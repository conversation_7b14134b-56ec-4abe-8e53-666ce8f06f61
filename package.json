{"private": true, "workspaces": {"packages": ["addons/core/*", "addons/packages/*", "addons/plugins/*", "addons/themes/*"]}, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production", "format": "npx prettier addons/**/resources/assets/**/*.{js,scss,vue} addons/**/resources/views/**/*.blade.php addons/themes/*/{views,partials}/**/*.blade.php addons/themes/*/assets/**/*.{js,scss} --write"}, "devDependencies": {"@fullhuman/postcss-purgecss": "^5.0.0", "@shufo/prettier-plugin-blade": "^1.11.1", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "glob": "^10.3.10", "laravel-mix": "^6.0.49", "postcss": "^8.4.31", "prettier": "^3.0.3", "resolve-url-loader": "^5.0.0", "sass": "^1.69.0", "sass-loader": "^13.3.2", "vue-loader": "^17.2.2"}, "dependencies": {"@popperjs/core": "^2.11.8", "axios": "^1.5.1", "bootstrap": "^5.3.2", "cropperjs": "^1.6.1", "epic-spinners": "^2.0.0", "jquery": "^3.7.1", "lodash": "^4.17.21", "moment": "^2.29.4", "popper.js": "^1.16.1", "sanitize-html": "^2.11.0", "tiny-emitter": "^2.1.0", "vue": "^3.3.4"}}
{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0|^8.1|^8.2", "ext-curl": "*", "ext-gd": "*", "ext-json": "*", "ext-zip": "*", "addons/kernel": "*@dev", "addons/api": "*@dev", "addons/get-started": "*@dev", "addons/installer": "*@dev", "addons/menu": "*@dev", "addons/optimize": "*@dev", "addons/page": "*@dev", "addons/addons": "*@dev", "addons/plugin-management": "*@dev", "addons/revision": "*@dev", "addons/seo-helper": "*@dev", "addons/shortcode": "*@dev", "addons/sitemap": "*@dev", "addons/slug": "*@dev", "addons/theme": "*@dev", "addons/widget": "*@dev", "doctrine/dbal": "^3.6", "guzzlehttp/guzzle": "^7.7", "laravel/framework": "^9.52", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.8", "predis/predis": "^2.2", "wikimedia/composer-merge-plugin": "^2.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.8", "addons/dev-tool": "*@dev", "addons/git-commit-checker": "*@dev", "fakerphp/faker": "^1.23", "knuckleswtf/scribe": "^4.22", "laravel/pint": "^1.5", "laravel/sail": "^1.23", "mockery/mockery": "^1.6", "nunomaduro/collision": "^6.4", "nunomaduro/larastan": "^2.6", "phpunit/phpunit": "^9.6.10", "spatie/laravel-ignition": "^1.6"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "Aws\\Script\\Composer\\Composer::removeUnusedServices", "Google\\Task\\Composer::cleanup", "@php artisan cms:publish:assets"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}, "merge-plugin": {"include": ["./addons/plugins/*/composer.json", "./addons/themes/*/composer.json"], "recurse": false, "replace": false, "ignore-duplicates": false, "merge-dev": false, "merge-extra": false, "merge-extra-deep": false, "merge-scripts": false}, "google/apiclient-services": ["Analytics", "Sheets", "Drive", "DriveActivity"], "aws/aws-sdk-php": ["S3", "Ses", "Translate"]}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "pestphp/pest-plugin": true, "wikimedia/composer-merge-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true, "repositories": [{"type": "path", "url": "./addons/kernel"}, {"type": "path", "url": "./addons/core"}, {"type": "path", "url": "./addons/packages/*"}]}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('tinhthanhpho')) {
            Schema::create('tinhthanhpho', function (Blueprint $table) {
                $table->string('matp', 5)->primary();
                $table->string('name', 100);
                $table->string('type', 30);
                $table->tinyInteger('order')->default(1);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tinhthanhpho');
    }
};

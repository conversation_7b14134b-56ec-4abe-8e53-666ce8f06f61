<?php

namespace Database\Seeders;

use Addons\Base\Supports\BaseSeeder;
use Addons\Page\Models\Page;
use Addons\Setting\Facades\Setting;
use Addons\Theme\Facades\ThemeOption;
use Carbon\Carbon;

class ThemeOptionSeeder extends BaseSeeder
{
    public function run(): void
    {
        Setting::newQuery()->where('key', 'LIKE', ThemeOption::getOptionKey('%'))->delete();

        $data = [
            'site_title' => 'Just another Laravel CMS site',
            'seo_description' => 'With experience, we make sure to get every project done very fast and in time with high quality using our Laravel CMS https://fsofts.com',
            'copyright' => sprintf('©%s Laravel Technologies. All right reserved.', Carbon::now()->format('Y')),
            'favicon' => $this->imagePath('general/favicon.png'),
            'logo' => $this->imagePath('general/logo.png'),
            'website' => 'mailto:<EMAIL>',
            'contact_email' => '<EMAIL>',
            'site_description' => 'With experience, we make sure to get every project done very fast and in time with high quality using our Laravel CMS https://fsofts.com',
            'phone' => '+(*************',
            'address' => '214 West Arnold St. New York, NY 10002',
            'cookie_consent_message' => 'Your experience on this site will be improved by allowing cookies ',
            'cookie_consent_learn_more_url' => '/cookie-policy',
            'cookie_consent_learn_more_text' => 'Cookie Policy',
            'homepage_id' => Page::query()->value('id'),
            'blog_page_id' => Page::query()->skip(1)->value('id'),
            'primary_color' => '#AF0F26',
            'primary_font' => 'Roboto',
        ];

        Setting::set($this->prepareThemeOptions($data));

        Setting::set(
            ThemeOption::getOptionKey('social_links'),
            json_encode([
                [
                    [
                        'key' => 'social-name',
                        'value' => 'Facebook',
                    ],
                    [
                        'key' => 'social-icon',
                        'value' => 'fab fa-facebook',
                    ],
                    [
                        'key' => 'social-url',
                        'value' => 'https://facebook.com',
                    ],
                ],
                [
                    [
                        'key' => 'social-name',
                        'value' => 'Twitter',
                    ],
                    [
                        'key' => 'social-icon',
                        'value' => 'fab fa-twitter',
                    ],
                    [
                        'key' => 'social-url',
                        'value' => 'https://twitter.com',
                    ],
                ],
                [
                    [
                        'key' => 'social-name',
                        'value' => 'Youtube',
                    ],
                    [
                        'key' => 'social-icon',
                        'value' => 'fab fa-youtube',
                    ],
                    [
                        'key' => 'social-url',
                        'value' => 'https://youtube.com',
                    ],
                ],
            ])
        );

        Setting::save();
    }
}

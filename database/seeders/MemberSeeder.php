<?php

namespace Database\Seeders;

use Addons\Base\Supports\BaseSeeder;
use Addons\Member\Models\Member;
use Addons\Member\Models\MemberActivityLog;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;

class MemberSeeder extends BaseSeeder
{
    public function run(): void
    {
        $files = $this->uploadFiles('members');

        Member::query()->truncate();
        MemberActivityLog::query()->truncate();

        $faker = $this->fake();

        Member::query()->create([
            'first_name' => '<PERSON>',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'password' => Hash::make('12345678'),
            'dob' => $faker->dateTime(),
            'phone' => $faker->phoneNumber(),
            'avatar_id' => ! $files[0]['error'] ? $files[0]['data']->id : 0,
            'description' => $faker->realText(30),
            'confirmed_at' => Carbon::now(),
        ]);

        for ($i = 0; $i < 9; $i++) {
            Member::query()->create([
                'first_name' => $faker->firstName(),
                'last_name' => $faker->lastName(),
                'email' => $faker->email(),
                'password' => Hash::make('12345678'),
                'dob' => $faker->dateTime(),
                'phone' => $faker->phoneNumber(),
                'avatar_id' => ! $files[$i + 1]['error'] ? $files[$i + 1]['data']->id : 0,
                'description' => $faker->realText(30),
                'confirmed_at' => Carbon::now(),
            ]);
        }
    }
}

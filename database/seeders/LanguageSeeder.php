<?php

namespace Database\Seeders;

use Addons\Base\Supports\BaseSeeder;
use Addons\Language\Models\Language;
use Addons\Language\Models\LanguageMeta;
use Addons\LanguageAdvanced\Supports\LanguageAdvancedManager;
use Addons\Setting\Facades\Setting;
use Illuminate\Support\Facades\DB;

class LanguageSeeder extends BaseSeeder
{
    public function run(): void
    {
        Language::query()->truncate();
        LanguageMeta::query()->truncate();

        foreach (LanguageAdvancedManager::supportedModels() as $model) {
            DB::table((new $model())->getModel()->getTable() . '_translations')->truncate();
        }

        Language::query()->create([
            'lang_name' => 'English',
            'lang_locale' => 'en',
            'lang_is_default' => true,
            'lang_code' => 'en_US',
            'lang_is_rtl' => false,
            'lang_flag' => 'us',
            'lang_order' => 0,
        ]);

        Setting::set([
            'language_hide_default' => '1',
            'language_switcher_display' => 'dropdown',
            'language_display' => 'all',
            'language_hide_languages' => '[]',
        ])->save();
    }
}

<?php

namespace Database\Seeders;

use Addons\Base\Supports\BaseSeeder;
use Addons\Gallery\Models\Gallery;
use Addons\Gallery\Models\GalleryMeta;
use Addons\Slug\Facades\SlugHelper;

class GallerySeeder extends BaseSeeder
{
    public function run(): void
    {
        $this->uploadFiles('galleries');

        Gallery::query()->truncate();
        GalleryMeta::query()->truncate();

        $galleries = [
            [
                'name' => 'Perfect',
            ],
            [
                'name' => 'New Day',
            ],
            [
                'name' => 'Happy Day',
            ],
            [
                'name' => 'Nature',
            ],
            [
                'name' => 'Morning',
            ],
            [
                'name' => 'Photography',
            ],
        ];

        $faker = $this->fake();

        $images = [];
        for ($i = 0; $i < 10; $i++) {
            $images[] = [
                'img' => $this->imagePath('galleries/' . ($i + 1) . '.jpg'),
                'description' => $faker->text(150),
            ];
        }

        foreach ($galleries as $index => $item) {
            $item['description'] = $faker->text(150);
            $item['image'] = $this->imagePath('galleries/' . ($index + 1) . '.jpg');
            $item['is_featured'] = true;

            $gallery = Gallery::query()->create($item);

            SlugHelper::createSlug($gallery);

            GalleryMeta::query()->create([
                'images' => $images,
                'reference_id' => $gallery->id,
                'reference_type' => Gallery::class,
            ]);
        }
    }
}

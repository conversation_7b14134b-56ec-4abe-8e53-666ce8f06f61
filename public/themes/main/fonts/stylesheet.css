@font-face {
    font-family: 'VN-Peugeot New';
    src: url('VN-PeugeotNew-Regular.eot');
    src: url('VN-PeugeotNew-Regular.eot?#iefix') format('embedded-opentype'),
        url('VN-PeugeotNew-Regular.woff') format('woff'),
        url('VN-PeugeotNew-Regular.ttf') format('truetype'),
        url('VN-PeugeotNew-Regular.svg#VN-PeugeotNew-Regular') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'VN-Peugeot New';
    src: url('VN-PeugeotNew-Bold.eot');
    src: url('VN-PeugeotNew-Bold.eot?#iefix') format('embedded-opentype'),
        url('VN-PeugeotNew-Bold.woff') format('woff'),
        url('VN-PeugeotNew-Bold.ttf') format('truetype'),
        url('VN-PeugeotNew-Bold.svg#VN-PeugeotNew-Bold') format('svg');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'VN-Peugeot New';
    src: url('VN-PeugeotNew-Italic.eot');
    src: url('VN-PeugeotNew-Italic.eot?#iefix') format('embedded-opentype'),
        url('VN-PeugeotNew-Italic.woff') format('woff'),
        url('VN-PeugeotNew-Italic.ttf') format('truetype'),
        url('VN-PeugeotNew-Italic.svg#VN-PeugeotNew-Italic') format('svg');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}


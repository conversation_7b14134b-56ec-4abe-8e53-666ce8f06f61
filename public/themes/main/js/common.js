try {
    const targetTime = new Date('2025-06-23T00:00:00+07:00');
    const popupOverlay = document.getElementById('popup-overlay');
    const btnClose = document.getElementById('popup-close');

    function updateTimer() {
        const now = new Date();
        let distance = targetTime - now;

        if (distance < 0) distance = 0;

        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

            document.getElementById('timer-days').textContent = days;
            document.getElementById('timer-hours').textContent = String(hours).padStart(2, '0');
            document.getElementById('timer-minutes').textContent = String(minutes).padStart(2, '0');
            document.getElementById('timer-seconds').textContent = String(seconds).padStart(2, '0');




        if (distance <= 0) {
            clearInterval(timerInterval);
            if (popupOverlay) popupOverlay.style.display = 'none';
        }
    }

    const timerInterval = setInterval(updateTimer, 1000);
    updateTimer();

    if (btnClose) {
        btnClose.onclick = function() {
            if (popupOverlay) popupOverlay.style.display = 'none';
            clearInterval(timerInterval);
        };
    }
} catch (e) {}


/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./addons/themes/main/assets/js/common.js":
/*!************************************************!*\
  !*** ./addons/themes/main/assets/js/common.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _modules_home__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./modules/home */ "./addons/themes/main/assets/js/modules/home.js");
/* harmony import */ var _modules_swiper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modules/swiper */ "./addons/themes/main/assets/js/modules/swiper.js");
/* harmony import */ var _modules_section_equipment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./modules/section-equipment */ "./addons/themes/main/assets/js/modules/section-equipment.js");
/* harmony import */ var _modules_section4__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./modules/section4 */ "./addons/themes/main/assets/js/modules/section4.js");
/* harmony import */ var _modules_LoadingShowRoom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./modules/LoadingShowRoom */ "./addons/themes/main/assets/js/modules/LoadingShowRoom.js");





(function () {
  if (!!document.querySelector(".container-custom")) {
    var init = function init() {
      return document.documentElement.style.setProperty("--container-padding", "".concat((document.querySelector("body").offsetWidth - document.querySelector(".container-custom").offsetWidth) / 2, "px"));
    };
    init();
    window.addEventListener("resize", init);
  }
})();
$(document).ready(function () {
  new _modules_home__WEBPACK_IMPORTED_MODULE_0__["default"]();
  new _modules_swiper__WEBPACK_IMPORTED_MODULE_1__["default"]();
  new _modules_section_equipment__WEBPACK_IMPORTED_MODULE_2__["default"]();
  new _modules_section4__WEBPACK_IMPORTED_MODULE_3__["default"]();
  new _modules_LoadingShowRoom__WEBPACK_IMPORTED_MODULE_4__.LoadingShowRoom();
});
$(document).on("click", '.menu__wapper a[href^="#"]', function (event) {
  event.preventDefault();
  $("html, body").animate({
    scrollTop: $($.attr(this, "href")).offset().top
  }, 500);
});
$("a.scroll-top").click(function (event) {
  event.preventDefault();
  $("html, body").animate({
    scrollTop: 0
  }, 500);
});

/***/ }),

/***/ "./addons/themes/main/assets/js/modules/LoadingShowRoom.js":
/*!*****************************************************************!*\
  !*** ./addons/themes/main/assets/js/modules/LoadingShowRoom.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LoadingShowRoom: () => (/* binding */ LoadingShowRoom)
/* harmony export */ });
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var LoadingShowRoom = /*#__PURE__*/function () {
  function LoadingShowRoom() {
    _classCallCheck(this, LoadingShowRoom);
    this.events();
  }
  return _createClass(LoadingShowRoom, [{
    key: "events",
    value: function events() {
      this.loadingShowRoom();
    }
  }, {
    key: "loadingShowRoom",
    value: function loadingShowRoom() {
      $(document).ready(function () {
        $(document).on("change", "#matp_id", function () {
          var url = $(this).data("url");
          $.ajax({
            headers: {
              "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content")
            },
            url: url,
            data: {
              matp_id: $(this).val()
            },
            method: "GET",
            dataType: "json",
            success: function success(data) {
              $('#change-show-room').html(data.data.template);
              $('.change-show-room').html(data.data.template);
              if (data.data.type) {
                $(".btn-booking").removeClass('button-booking-disable');
                $(".btn-booking").removeAttr("disabled");
              } else {
                $(".btn-booking").addClass('button-booking-disable');
                $(".btn-booking").attr("disabled", "disabled");
              }
            },
            error: function error(xhr, thrownError) {}
          });
        });

        // show_room_id

        $(document).on("change", "#matp_id_test", function () {
          var url = $(this).data("url");
          $.ajax({
            headers: {
              "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content")
            },
            url: url,
            data: {
              matp_id: $(this).val()
            },
            method: "GET",
            dataType: "json",
            success: function success(data) {
              $('#change-show-room').html(data.data.template);
              $('.change-show-room').html(data.data.template);

              // button submit mổ khi check
              if (data.data.type) {
                console.log();
                if ($('[name="car_id"]').val() !== 'Chọn phiên bản') {
                  $(".btn-booking").removeClass('button-booking-disable').removeAttr("disabled");
                } else {
                  $(".btn-booking").addClass('button-booking-disable').attr("disabled", "disabled");
                }
              } else {
                $(".btn-booking").addClass('button-booking-disable').attr("disabled", "disabled");
              }
            },
            error: function error(xhr, thrownError) {}
          });
        });
        $(document).on("change", ".show_room_id", function () {
          var url = $(this).data("url");
          $.ajax({
            headers: {
              "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content")
            },
            url: url,
            data: {
              show_room_id: $(this).val()
            },
            method: "GET",
            dataType: "json",
            success: function success(data) {
              // $('.agency-list.checkbox-custom').append(data.data.template);
              $('#change-google-map').html(data.data.templateGoogleMap);
            },
            error: function error(xhr, thrownError) {}
          });
        });
        function formatPrice(number) {
          return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
        }
        $(document).on("change", "#change_car", function () {
          var url = $(this).data("url");
          $.ajax({
            headers: {
              "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content")
            },
            url: url,
            data: {
              car_id: $(this).val(),
              matp_id: $('#matp_id').val()
            },
            method: "GET",
            dataType: "json",
            success: function success(data) {
              if (data.type === 'success') {
                $('.price_car').text(formatPrice(data.price) + ' VNĐ');
                $('input#price_car').val(data.price);
                var PhanTramPhiTruocBa = data.phi_truoc_ba;
                var PhiDangKyBienSo = data.phi_dang_ky_bien_so;
                var PhiDangKiem = data.phi_dang_kiem_xe;
                var priceCar = $('input#price_car').val();
                var PhiTruocBa = priceCar * PhanTramPhiTruocBa / 100;
                var totalPrice = parseFloat(PhiTruocBa + Number(priceCar) + PhiDangKiem + PhiDangKyBienSo);
                // var formattedPriceCar = formatNumber(priceCar); //
                var formattedPriceLicensePlate = formatNumber(PhiTruocBa); // phí trước bạ
                var formattedPriceEntry = formatNumber(PhiDangKyBienSo);
                var formattedPriceInspection = formatNumber(PhiDangKiem);
                var formattedTotalPrice = formatNumber(totalPrice);
                $('.price_license_plate').text(formattedPriceLicensePlate + ' VNĐ');
                $('.price_entry').text(formattedPriceEntry + ' VNĐ');
                $('.price_inspection').text(formattedPriceInspection + ' VNĐ');
                $('.total_price').text(formattedTotalPrice + ' VNĐ');
                $("input[name='price_license_plate']").val(PhiTruocBa);
                $("input[name='price_entry']").val(PhiDangKyBienSo);
                $("input[name='price_inspection']").val(PhiDangKiem);
                $("input[name='total_price']").val(totalPrice);
              } else {
                $('.price_car').text(formatPrice(data.data.price) + ' VNĐ');
                $('input#price_car').val(data.data.price);
              }
            },
            error: function error(xhr, thrownError) {}
          });
        });
        $(document).on("change", "#matp_id", function () {
          $.ajax({
            headers: {
              "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content")
            },
            url: carPriceUrl,
            data: {
              matp_id: $(this).val()
            },
            method: "GET",
            dataType: "json",
            success: function success(data) {
              if (data.type === 'success') {
                var PhanTramPhiTruocBa = data.phi_truoc_ba;
                var PhiDangKyBienSo = data.phi_dang_ky_bien_so;
                var PhiDangKiem = data.phi_dang_kiem_xe;
                var priceCar = $('input#price_car').val();
                var PhiTruocBa = priceCar * PhanTramPhiTruocBa / 100;
                var totalPrice = parseFloat(PhiTruocBa + Number(priceCar) + PhiDangKiem + PhiDangKyBienSo);
                var formattedPriceLicensePlate = formatNumber(PhiTruocBa); // phí trước bạ
                var formattedPriceEntry = formatNumber(PhiDangKyBienSo);
                var formattedPriceInspection = formatNumber(PhiDangKiem);
                var formattedTotalPrice = formatNumber(totalPrice);
                $('.price_license_plate').text(formattedPriceLicensePlate + ' VNĐ');
                $('.price_entry').text(formattedPriceEntry + ' VNĐ');
                $('.price_inspection').text(formattedPriceInspection + ' VNĐ');
                $('.total_price').text(formattedTotalPrice + ' VNĐ');
                $("input[name='price_license_plate']").val(PhiTruocBa);
                $("input[name='price_entry']").val(PhiDangKyBienSo);
                $("input[name='price_inspection']").val(PhiDangKiem);
                $("input[name='total_price']").val(totalPrice);
              }
            },
            error: function error(xhr, thrownError) {}
          });
        });
        $(document).on("change", "#matp_id", function () {
          $.ajax({
            headers: {
              "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content")
            },
            url: carPriceUrl,
            data: {
              matp_id: $(this).val()
            },
            method: "GET",
            dataType: "json",
            success: function success(data) {
              if (data.type === 'success') {
                var PhanTramPhiTruocBa = data.phi_truoc_ba;
                var PhiDangKyBienSo = data.phi_dang_ky_bien_so;
                var PhiDangKiem = data.phi_dang_kiem_xe;
                var priceCar = $('input#price_car').val();
                var PhiTruocBa = priceCar * PhanTramPhiTruocBa / 100;
                var totalPrice = parseFloat(PhiTruocBa + Number(priceCar) + PhiDangKiem + PhiDangKyBienSo);

                // var formattedPriceCar = formatNumber(priceCar); //
                var formattedPriceLicensePlate = formatNumber(PhiTruocBa); // phí trước bạ
                var formattedPriceEntry = formatNumber(PhiDangKyBienSo);
                var formattedPriceInspection = formatNumber(PhiDangKiem);
                var formattedTotalPrice = formatNumber(totalPrice);
                $('.price_license_plate').text(formattedPriceLicensePlate + ' VNĐ');
                $('.price_entry').text(formattedPriceEntry + ' VNĐ');
                $('.price_inspection').text(formattedPriceInspection + ' VNĐ');
                $('.total_price').text(formattedTotalPrice + ' VNĐ');
                $("input[name='price_license_plate']").val(PhiTruocBa);
                $("input[name='price_entry']").val(PhiDangKyBienSo);
                $("input[name='price_inspection']").val(PhiDangKiem);
                $("input[name='total_price']").val(totalPrice);
              }
            },
            error: function error(xhr, thrownError) {}
          });
        });
        function formatNumber(number) {
          return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
        }
        $(document).on('click', '.close-popup', function () {
          $('.fixed-bg').hide();
          $('.animated-modal-tk').hide();
          $('.camon').hide();
          $('body').removeClass('active-popup');
        });
        $(document).on('click', '.active-popup', function () {
          $('.fixed-bg').hide();
          $('.animated-modal-tk').hide();
          $('.camon').hide();
          $('body').removeClass('active-popup');
        });
        $('[name="car_id"]').on('change', function () {
          if ($(this).val() !== 'Chọn phiên bản') {
            if (!blank($('.show_room_id').val())) {
              console.log('ok next');
              $(".btn-booking").removeClass('button-booking-disable').removeAttr("disabled");
            } else {
              $(".btn-booking").addClass('button-booking-disable').attr("disabled", "disabled");
            }
          } else {
            $(".btn-booking").addClass('button-booking-disable').attr("disabled", "disabled");
          }
        });
        function blank(value) {
          return value == null || value.trim() === '';
        }
      });
    }
  }]);
}();

/***/ }),

/***/ "./addons/themes/main/assets/js/modules/home.js":
/*!******************************************************!*\
  !*** ./addons/themes/main/assets/js/modules/home.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var Home = /*#__PURE__*/function () {
  function Home() {
    _classCallCheck(this, Home);
    this.events();
    $('.dropdown').dropdown();
    $('#dob-picker').datepicker({});
  }
  return _createClass(Home, [{
    key: "events",
    value: function events() {
      this.handleChangMenu();
      this.loadingInit();
    }
  }, {
    key: "handleChangMenu",
    value: function handleChangMenu() {
      var navbarMenu = document.getElementById("menu");
      var burgerMenu = document.getElementById("burger");
      var overlayMenu = document.querySelector(".overlay");

      // Open Close Navbar Menu on Click Burger
      if (burgerMenu && navbarMenu) {
        burgerMenu.addEventListener("click", function () {
          burgerMenu.classList.toggle("menu-active");
          navbarMenu.classList.toggle("menu-active");
          document.body.classList.toggle("overflow-hidden");
        });
      }

      // Fixed Navbar Menu on Window Resize
      window.addEventListener("resize", function () {
        if (window.innerWidth >= 1024) {
          if (navbarMenu.classList.contains("menu-active")) {
            navbarMenu.classList.remove("menu-active");
            overlayMenu.classList.remove("menu-active");
          }
        }
      });
    }
  }, {
    key: "loadingInit",
    value: function loadingInit() {
      (function () {
        $(window).on("load", function () {
          $('#overlay').addClass('loaded');
        });
        setTimeout(function () {
          $('#overlay').addClass('loaded');
        }, 4500);
      })();
    }
  }]);
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Home);

/***/ }),

/***/ "./addons/themes/main/assets/js/modules/section-equipment.js":
/*!*******************************************************************!*\
  !*** ./addons/themes/main/assets/js/modules/section-equipment.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var Equipment = /*#__PURE__*/function () {
  function Equipment() {
    _classCallCheck(this, Equipment);
    this.events();
  }
  return _createClass(Equipment, [{
    key: "events",
    value: function events() {
      this.slideEquipment();
      this.slideSafe();
    }
  }, {
    key: "slideEquipment",
    value: function slideEquipment() {
      var updSwiperNumericPagination = function updSwiperNumericPagination(e) {
        if ($(".section-equipment-slider-wrapper .slide-pagination-custom .pagination-custom").length) {
          $(".section-equipment-slider-wrapper .slide-pagination-custom .pagination-custom").html('<span class="count">' + (e.realIndex + 1) + '</span>/<span class="total">' +
          // (e?.slides?.length || 1) +
          4 + "</span>");
        }
      };
      var EquipmentSlider = new Swiper(".section-equipment-slider", {
        slidesPerView: 2,
        loop: true,
        spaceBetween: 30,
        autoplay: {
          delay: 5000,
          disableOnInteraction: true
        },
        centeredSlides: true,
        navigation: {
          nextEl: ".section-equipment-slider-wrapper .slide-pagination-custom .button-next",
          prevEl: ".section-equipment-slider-wrapper .slide-pagination-custom .button-prev"
        },
        // pagination: {
        //     el: ".peugeot-fade-swiper-pagination",
        //     type: "fraction",
        // },
        pagination: {
          el: document.querySelector(".section-equipment-slider .slide-pagination-custom")
        },
        on: {
          init: updSwiperNumericPagination,
          slideChange: updSwiperNumericPagination
        },
        breakpoints: {
          1240: {
            slidesPerView: 2,
            spaceBetween: 30
          },
          1024: {
            slidesPerView: 1.7,
            spaceBetween: 20
          },
          768: {
            slidesPerView: 1.5,
            spaceBetween: 20
          },
          1: {
            slidesPerView: 1,
            spaceBetween: 0
          }
        }
      });
    }
  }, {
    key: "slideSafe",
    value: function slideSafe() {
      var updSwiperNumericPaginationSafe = function updSwiperNumericPaginationSafe(e) {
        if ($(".section-safe-slider-wrapper .slide-pagination-custom .pagination-custom").length) {
          $(".section-safe-slider-wrapper .slide-pagination-custom .pagination-custom").html('<span class="count">' + (e.realIndex + 1) + '</span>/<span class="total">' +
          // (e?.slides?.length || 1) +
          3 + "</span>");
        }
      };
      var EquipmentSlider = new Swiper(".section-safe-slider", {
        // slidesPerView: 1,
        spaceBetween: 30,
        observeParents: true,
        autoplay: {
          delay: 5000,
          disableOnInteraction: true
        },
        centeredSlides: true,
        loop: true,
        navigation: {
          nextEl: ".section-safe-slider-wrapper .slide-pagination-custom .button-next",
          prevEl: ".section-safe-slider-wrapper .slide-pagination-custom .button-prev"
        },
        // pagination: {
        //     el: ".peugeot-fade-swiper-pagination",
        //     type: "fraction",
        // },
        pagination: {
          el: document.querySelector(".section-safe-slider .slide-pagination-custom")
        },
        on: {
          init: updSwiperNumericPaginationSafe,
          slideChange: updSwiperNumericPaginationSafe
        },
        breakpoints: {
          1240: {
            slidesPerView: 2,
            spaceBetween: 30
          },
          1024: {
            slidesPerView: 1.7,
            spaceBetween: 20
          },
          768: {
            slidesPerView: 1.5,
            spaceBetween: 20
          },
          1: {
            slidesPerView: 1,
            spaceBetween: 0
          }
        }
      });
    }
  }]);
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Equipment);

/***/ }),

/***/ "./addons/themes/main/assets/js/modules/section4.js":
/*!**********************************************************!*\
  !*** ./addons/themes/main/assets/js/modules/section4.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var Section4 = /*#__PURE__*/function () {
  function Section4() {
    _classCallCheck(this, Section4);
    this.events();
  }
  return _createClass(Section4, [{
    key: "events",
    value: function events() {
      this.handleChangMenu();
    }
  }, {
    key: "handleChangMenu",
    value: function handleChangMenu() {
      window.addEventListener('load', videoScroll);
      window.addEventListener('scroll', videoScroll);
      function videoScroll() {
        if (document.querySelectorAll('video[autoplay]').length > 0) {
          var windowHeight = window.innerHeight,
            videoEl = document.querySelectorAll('video[autoplay]');
          for (var i = 0; i < videoEl.length; i++) {
            var thisVideoEl = videoEl[i],
              videoHeight = thisVideoEl.clientHeight,
              videoClientRect = thisVideoEl.getBoundingClientRect().top;
            if (videoClientRect <= windowHeight - videoHeight * .5 && videoClientRect >= 0 - videoHeight * .5) {
              thisVideoEl.play();
            } else {
              thisVideoEl.pause();
            }
          }
        }
      }
    }
  }]);
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Section4);

/***/ }),

/***/ "./addons/themes/main/assets/js/modules/swiper.js":
/*!********************************************************!*\
  !*** ./addons/themes/main/assets/js/modules/swiper.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var SwiperSlide = /*#__PURE__*/function () {
  function SwiperSlide() {
    _classCallCheck(this, SwiperSlide);
    this.events();
  }
  return _createClass(SwiperSlide, [{
    key: "events",
    value: function events() {
      this.slideSection3();
      this.slideSectionTechnology();
      this.slideEngine();
    }
  }, {
    key: "slideSection3",
    value: function slideSection3() {
      // var menu = ['CUỐN HÚT', 'CẢM XÚC', 'HOÀN HẢO' ,'MẠNH MẼ','PHONG CÁCH']

      var menu = JSON.parse($('#tabs_slide_custom').val());

      // $data = $('#tabs_slide_custom').val();

      var slideSection3Thumbs = new Swiper('.slide-feature-thumb', {
        spaceBetween: 20,
        speed: 800,
        allowTouchMove: false,
        pagination: {
          el: '.swiper-pagination',
          clickable: true,
          renderBullet: function renderBullet(index, className) {
            return '<span class="' + className + '">' + menu[index] + '</span>';
          }
        }
        //   autoplay: {
        //     delay: 5000,
        //     },
      });
      var slideSection3Top = new Swiper('.slide-feature-top', {
        speed: 800,
        slidesPerView: 1,
        navigation: {
          nextEl: '.slide-feature-top .swiper-button-next',
          prevEl: '.slide-feature-top .swiper-button-prev'
        }
      });
      slideSection3Thumbs.controller.control = slideSection3Top;
      slideSection3Top.controller.control = slideSection3Thumbs;
    }
  }, {
    key: "slideSectionTechnology",
    value: function slideSectionTechnology() {
      var updSwiperNumericPagination = function updSwiperNumericPagination(e) {
        if ($('.slide-technology .slide-pagination-custom .pagination-custom').length) {
          var _e$slides;
          $('.slide-technology .slide-pagination-custom .pagination-custom').html('<span class="count">' + (e.realIndex + 1) + '</span>/<span class="total">' + ((e === null || e === void 0 || (_e$slides = e.slides) === null || _e$slides === void 0 ? void 0 : _e$slides.length) || 1) + "</span>");
        }
      };
      var slideSectionTechnologyThumbs = new Swiper('.slide-technology-thumb', {
        spaceBetween: 15,
        speed: 800,
        allowTouchMove: false,
        navigation: {
          nextEl: '.slide-technology .slide-pagination-custom .button-next',
          prevEl: '.slide-technology .slide-pagination-custom .button-prev'
        },
        pagination: {
          el: document.querySelector('.slide-technology-thumb .slide-pagination-custom')
        },
        autoplay: {
          delay: 5000,
          disableOnInteraction: true
        },
        on: {
          init: updSwiperNumericPagination,
          slideChange: updSwiperNumericPagination
        }
      });
      var slideSectionTechnologyTop = new Swiper('.slide-technology-top', {
        speed: 800,
        slidesPerView: 1,
        spaceBetween: 15,
        autoplay: {
          delay: 5000,
          disableOnInteraction: true
        }
      });
      slideSectionTechnologyThumbs.controller.control = slideSectionTechnologyTop;
      slideSectionTechnologyTop.controller.control = slideSectionTechnologyThumbs;
    }
  }, {
    key: "slideEngine",
    value: function slideEngine() {
      var updSwiperNumericPagination = function updSwiperNumericPagination(e) {
        if ($('.slide-engine .slide-pagination-custom .pagination-custom').length) {
          var _e$slides2;
          $('.slide-engine .slide-pagination-custom .pagination-custom').html('<span class="count">' + (e.realIndex + 1) + '</span>/<span class="total">' + ((e === null || e === void 0 || (_e$slides2 = e.slides) === null || _e$slides2 === void 0 ? void 0 : _e$slides2.length) || 1) + "</span>");
        }
      };
      var slideEngineThumbs = new Swiper('.slide-engine-thumb', {
        speed: 800,
        slidesPerView: 1,
        spaceBetween: 15,
        allowTouchMove: false,
        navigation: {
          nextEl: '.slide-engine .slide-pagination-custom .button-next',
          prevEl: '.slide-engine .slide-pagination-custom .button-prev'
        },
        autoplay: {
          delay: 5000,
          disableOnInteraction: true
        }
      });
      var slideEngineTop = new Swiper('.slide-engine-top', {
        speed: 800,
        slidesPerView: 1,
        spaceBetween: 15,
        navigation: {
          nextEl: '.slide-engine-top .swiper-button-next',
          prevEl: '.slide-engine-top .swiper-button-prev'
        },
        autoplay: {
          delay: 5000,
          disableOnInteraction: true
        },
        pagination: {
          el: document.querySelector('.slide-engine-top .slide-pagination-custom')
        },
        on: {
          init: updSwiperNumericPagination,
          slideChange: updSwiperNumericPagination
        }
      });
      slideEngineThumbs.controller.control = slideEngineTop;
      slideEngineTop.controller.control = slideEngineThumbs;
    }
  }]);
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SwiperSlide);

/***/ }),

/***/ "./addons/themes/main/assets/sass/common.scss":
/*!****************************************************!*\
  !*** ./addons/themes/main/assets/sass/common.scss ***!
  \****************************************************/
/***/ (() => {

throw new Error("Module build failed (from ./node_modules/mini-css-extract-plugin/dist/loader.js):\nModuleBuildError: Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\nUndefined variable.\n   ╷\n31 │     outline-color: $color-primary;\r\n   │                    ^^^^^^^^^^^^^^\n   ╵\n  addons\\themes\\main\\assets\\sass\\_reset.scss 31:20  @use\n  addons\\themes\\main\\assets\\sass\\common.scss 4:1    root stylesheet\n    at processResult (C:\\project\\peu\\node_modules\\webpack\\lib\\NormalModule.js:936:19)\n    at C:\\project\\peu\\node_modules\\webpack\\lib\\NormalModule.js:1084:5\n    at C:\\project\\peu\\node_modules\\loader-runner\\lib\\LoaderRunner.js:400:11\n    at C:\\project\\peu\\node_modules\\loader-runner\\lib\\LoaderRunner.js:252:18\n    at context.callback (C:\\project\\peu\\node_modules\\loader-runner\\lib\\LoaderRunner.js:124:13)\n    at Object.loader (C:\\project\\peu\\node_modules\\sass-loader\\dist\\index.js:63:5)");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/
/************************************************************************/
/******/
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	__webpack_require__("./addons/themes/main/assets/js/common.js");
/******/ 	// This entry module doesn't tell about it's top-level declarations so it can't be inlined
/******/ 	var __webpack_exports__ = __webpack_require__("./addons/themes/main/assets/sass/common.scss");
/******/
/******/ })()
;

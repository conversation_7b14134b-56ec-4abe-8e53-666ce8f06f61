<!DOCTYPE html>
<html>
<head>
    <title>Test Register Form</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test Register Form</h1>
    
    <h2>Test 1: Simple Route Test</h2>
    <button onclick="testRoute()">Test Route</button>
    
    <h2>Test 2: Database Test</h2>
    <form id="test-db-form">
        <div>
            <label>Name:</label>
            <input type="text" name="name" value="Test User" required>
        </div>
        <div>
            <label>Email:</label>
            <input type="email" name="email" value="<EMAIL>" required>
        </div>
        <div>
            <label>Phone:</label>
            <input type="tel" name="phone" value="0123456789" required>
        </div>
        <button type="submit">Test Database</button>
    </form>

    <h2>Test 3: Full Register Test</h2>
    <form id="register-test-form">
        <div>
            <label>Name:</label>
            <input type="text" name="name" value="Real User" required>
        </div>
        <div>
            <label>Email:</label>
            <input type="email" name="email" value="<EMAIL>" required>
        </div>
        <div>
            <label>Phone:</label>
            <input type="tel" name="phone" value="0987654321" required>
        </div>
        <button type="submit">Test Full Register</button>
    </form>

    <div id="results" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>

    <script>
        function testRoute() {
            $.get('/register/test')
                .done(function(response) {
                    $('#results').html('<h3>Route Test Success:</h3><pre>' + JSON.stringify(response, null, 2) + '</pre>');
                })
                .fail(function(xhr) {
                    $('#results').html('<h3>Route Test Failed:</h3><pre>' + xhr.responseText + '</pre>');
                });
        }

        $('#test-db-form').on('submit', function(e) {
            e.preventDefault();
            
            $.ajax({
                url: '/register/test-db',
                type: 'POST',
                data: $(this).serialize(),
                success: function(response) {
                    $('#results').html('<h3>Database Test Success:</h3><pre>' + JSON.stringify(response, null, 2) + '</pre>');
                },
                error: function(xhr) {
                    $('#results').html('<h3>Database Test Failed:</h3><pre>' + xhr.responseText + '</pre>');
                }
            });
        });

        $('#register-test-form').on('submit', function(e) {
            e.preventDefault();
            
            $.ajax({
                url: '/register/send',
                type: 'POST',
                data: $(this).serialize(),
                success: function(response) {
                    $('#results').html('<h3>Register Test Success:</h3><pre>' + JSON.stringify(response, null, 2) + '</pre>');
                },
                error: function(xhr) {
                    $('#results').html('<h3>Register Test Failed:</h3><pre>' + xhr.responseText + '</pre>');
                }
            });
        });
    </script>
</body>
</html>

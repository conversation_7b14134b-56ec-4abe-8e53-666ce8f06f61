<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Wed Jun 23 16:35:11 2021
 By <PERSON><PERSON>sey,,,
Copyright (c) 2019 by Peugeot. All rights reserved.
</metadata>
<defs>
<font id="VN-PeugeotNew-Bold" horiz-adv-x="760" >
  <font-face 
    font-family="VN-Peugeot New"
    font-weight="700"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 0 0 0 0 0 0 0 0"
    ascent="802"
    descent="-198"
    x-height="572"
    cap-height="802"
    bbox="-277 -246 1666 1227"
    underline-thickness="138"
    underline-position="-267"
    unicode-range="U+0020-FEFF"
  />
<missing-glyph horiz-adv-x="615" 
d="M0 802h615v-1000h-615v1000zM339 195l15 85q105 2 149.5 41t44.5 115q0 38 -15 68t-48.5 50t-88 30.5t-132.5 10.5q-23 0 -48.5 -1.5t-51 -4t-49 -6t-43.5 -7.5v-124q48 9 96.5 14.5t93.5 5.5q42 0 67.5 -2t39 -7.5t17.5 -14.5t4 -23q0 -12 -2.5 -21t-11.5 -14
t-25.5 -7.5t-44.5 -2.5h-114v-70l21 -115h126zM357 0v153h-162v-153h162z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="883" 
d="M115 430h-81v108l81 34v9q0 58 12.5 102t41 74t75 45.5t115.5 15.5q31 0 53.5 -2t49.5 -5v-147q-20 2 -39 3t-39 1q-25 0 -41 -3.5t-25 -12t-12.5 -23.5t-3.5 -38v-19h214v9q0 58 12.5 102t42 74t78 45.5t121.5 15.5q32 0 56.5 -2t53.5 -5v-147q-22 2 -42.5 3t-41.5 1
q-54 0 -74 -15.5t-20 -61.5v-19h172v-142h-172v-430h-186v430h-214v-430h-187v430z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="1178" 
d="M111 430h-82v108l82 34v9q0 58 12 102t40.5 74t75.5 45.5t116 15.5q31 0 53.5 -2t49.5 -5v-147q-20 2 -39 3t-39 1q-25 0 -41 -3.5t-25 -12t-12.5 -23.5t-3.5 -38v-19h212v9q0 58 12 102t39.5 74t72.5 45.5t112 15.5q24 0 41.5 -1.5t40.5 -4.5v-147q-16 2 -29.5 2.5
t-28.5 0.5q-22 0 -36 -3.5t-22.5 -12t-11.5 -23.5t-3 -38v-19h400v-572h-187v430h-213v-430h-187v430h-212v-430h-187v430zM909 797h189v-145h-189v145z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="1197" 
d="M111 430h-82v108l82 34v9q0 58 12 102t40.5 74t75.5 45.5t116 15.5q31 0 53.5 -2t49.5 -5v-147q-20 2 -39 3t-39 1q-25 0 -41 -3.5t-25 -12t-12.5 -23.5t-3.5 -38v-19h213v9q0 58 11.5 102t39.5 74t74 45.5t115 15.5q34 0 54.5 -1.5t44.5 -4.5v-147q-34 3 -65 3
q-27 0 -44 -3.5t-26.5 -12t-13 -23.5t-3.5 -38v-19h146v-142h-146v-430h-187v430h-213v-430h-187v430zM929 802h187v-802h-187v802z" />
    <glyph glyph-name="f_b" unicode="fb" horiz-adv-x="1251" 
d="M111 430h-82v108l82 34v9q0 58 11.5 102t39.5 74t74.5 45.5t116.5 15.5q33 0 53.5 -1.5t43.5 -4.5v-147q-33 3 -63 3q-27 0 -44.5 -3.5t-27.5 -12t-13.5 -23.5t-3.5 -38v-19h146v-142h-146v-430h-187v430zM529 802h187v-251q48 18 93 27.5t93 9.5q68 0 123.5 -16
t94.5 -52t60.5 -94t21.5 -141q0 -82 -21.5 -139t-60.5 -93t-94.5 -52.5t-123.5 -16.5q-56 0 -109 14t-111 37l-25 -35h-128v802zM856 437q-36 0 -71 -7.5t-69 -21.5v-242q34 -14 69 -22t71 -8q42 0 71.5 7.5t48 24.5t26.5 46t8 72t-8 72t-26.5 46.5t-48 25t-71.5 7.5z" />
    <glyph glyph-name="f_h" unicode="fh" horiz-adv-x="1264" 
d="M111 430h-82v108l82 34v9q0 58 11.5 102t39.5 74t74.5 45.5t116.5 15.5q33 0 53.5 -1.5t43.5 -4.5v-147q-33 3 -63 3q-27 0 -44.5 -3.5t-27.5 -12t-13.5 -23.5t-3.5 -38v-19h146v-142h-146v-430h-187v430zM529 802h187v-258q48 21 95 32.5t105 11.5q140 0 205.5 -61.5
t65.5 -168.5v-358h-187v338q0 48 -26.5 69t-85.5 21q-45 0 -87.5 -12t-84.5 -31v-385h-187v802z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="779" 
d="M111 430h-82v108l82 34v9q0 58 12 102t40 74t73 45.5t112 15.5q24 0 42 -1.5t39 -4.5v-147q-28 3 -57 3q-22 0 -36.5 -3.5t-23 -12t-11.5 -23.5t-3 -38v-19h400v-572h-188v430h-212v-430h-187v430zM509 797h190v-145h-190v145z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="778" 
d="M111 430h-82v108l82 34v9q0 58 12 102t40 74t73 45.5t112 15.5q24 0 42 -1.5t39 -4.5v-147q-28 3 -57 3q-22 0 -36.5 -3.5t-23 -12t-11.5 -23.5t-3 -38v-19h399v-557q0 -59 -10 -102t-35.5 -71t-68.5 -42t-110 -14q-29 0 -50 1t-46 5v147q14 -2 31.5 -2.5t31.5 -0.5
q38 0 54 15.5t16 56.5v422h-212v-430h-187v430zM510 797h189v-145h-189v145z" />
    <glyph glyph-name="f_k" unicode="fk" horiz-adv-x="1215" 
d="M111 430h-82v108l82 34v9q0 58 11.5 102t39.5 74t74.5 45.5t116.5 15.5q33 0 53.5 -1.5t43.5 -4.5v-147q-33 3 -63 3q-27 0 -44.5 -3.5t-27.5 -12t-13.5 -23.5t-3.5 -38v-19h146v-142h-146v-430h-187v430zM529 802h187v-469l237 239h231l-267 -263l280 -309h-241
l-240 285v-285h-187v802z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="797" 
d="M111 430h-82v108l82 34v9q0 58 11.5 102t39.5 74t74.5 45.5t116.5 15.5q33 0 53.5 -1.5t43.5 -4.5v-147q-33 3 -63 3q-27 0 -44.5 -3.5t-27.5 -12t-13.5 -23.5t-3.5 -38v-19h146v-142h-146v-430h-187v430zM529 802h187v-802h-187v802z" />
    <glyph glyph-name="f_f_b" unicode="ffb" horiz-adv-x="1651" 
d="M111 430h-82v108l82 34v9q0 58 12 102t40.5 74t75.5 45.5t116 15.5q31 0 53.5 -2t49.5 -5v-147q-20 2 -39 3t-39 1q-25 0 -41 -3.5t-25 -12t-12.5 -23.5t-3.5 -38v-19h213v9q0 58 11.5 102t39.5 74t74 45.5t115 15.5q34 0 54.5 -1.5t44.5 -4.5v-147q-34 3 -65 3
q-27 0 -44 -3.5t-26.5 -12t-13 -23.5t-3.5 -38v-19h146v-142h-146v-430h-187v430h-213v-430h-187v430zM929 802h187v-251q48 18 93.5 27.5t93.5 9.5q68 0 123.5 -16t94.5 -52t60.5 -94t21.5 -141q0 -82 -21.5 -139t-60.5 -93t-94.5 -52.5t-123.5 -16.5q-56 0 -109 14
t-112 37l-25 -35h-128v802zM1257 437q-37 0 -72 -7.5t-69 -21.5v-242q34 -14 69 -22t72 -8q42 0 71.5 7.5t47.5 24.5t26.5 46t8.5 72t-8.5 72t-26.5 46.5t-47.5 25t-71.5 7.5z" />
    <glyph glyph-name="f_f_h" unicode="ffh" horiz-adv-x="1665" 
d="M111 430h-82v108l82 34v9q0 58 12 102t40.5 74t75.5 45.5t116 15.5q31 0 53.5 -2t49.5 -5v-147q-20 2 -39 3t-39 1q-25 0 -41 -3.5t-25 -12t-12.5 -23.5t-3.5 -38v-19h213v9q0 58 11.5 102t39.5 74t74 45.5t115 15.5q34 0 54.5 -1.5t44.5 -4.5v-147q-34 3 -65 3
q-27 0 -44 -3.5t-26.5 -12t-13 -23.5t-3.5 -38v-19h146v-142h-146v-430h-187v430h-213v-430h-187v430zM929 802h187v-258q48 21 95 32.5t105 11.5q140 0 205.5 -61.5t65.5 -168.5v-358h-187v338q0 48 -26.5 69t-85.5 21q-45 0 -87.5 -12t-84.5 -32v-384h-187v802z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="1178" 
d="M111 430h-82v108l82 34v9q0 58 12 102t40.5 74t75.5 45.5t116 15.5q31 0 53.5 -2t49.5 -5v-147q-20 2 -39 3t-39 1q-25 0 -41 -3.5t-25 -12t-12.5 -23.5t-3.5 -38v-19h213v9q0 58 12 102t39.5 74t72.5 45.5t111 15.5q25 0 43 -1.5t40 -4.5v-147q-15 2 -29 2.5t-29 0.5
q-22 0 -36.5 -3.5t-22.5 -12t-11 -23.5t-3 -38v-19h399v-557q0 -59 -10 -102t-35.5 -71t-68.5 -42t-110 -14q-29 0 -50 1t-46 5v147q14 -2 31.5 -2.5t31.5 -0.5q38 0 54 15.5t16 56.5v422h-212v-430h-187v430h-213v-430h-187v430zM910 797h189v-145h-189v145z" />
    <glyph glyph-name="f_f_k" unicode="ffk" horiz-adv-x="1611" 
d="M111 430h-82v108l82 34v9q0 58 12 102t40.5 74t75.5 45.5t116 15.5q31 0 53.5 -2t49.5 -5v-147q-20 2 -39 3t-39 1q-25 0 -41 -3.5t-25 -12t-12.5 -23.5t-3.5 -38v-19h213v9q0 58 11.5 102t39.5 74t74 45.5t115 15.5q34 0 54.5 -1.5t44.5 -4.5v-147q-34 3 -65 3
q-27 0 -44 -3.5t-26.5 -12t-13 -23.5t-3.5 -38v-19h146v-142h-146v-430h-187v430h-213v-430h-187v430zM929 802h187v-470l237 240h232l-268 -263l280 -309h-240l-241 286v-286h-187v802z" />
    <glyph glyph-name=".notdef" horiz-adv-x="615" 
d="M0 802h615v-1000h-615v1000zM339 195l15 85q105 2 149.5 41t44.5 115q0 38 -15 68t-48.5 50t-88 30.5t-132.5 10.5q-23 0 -48.5 -1.5t-51 -4t-49 -6t-43.5 -7.5v-124q48 9 96.5 14.5t93.5 5.5q42 0 67.5 -2t39 -7.5t17.5 -14.5t4 -23q0 -12 -2.5 -21t-11.5 -14
t-25.5 -7.5t-44.5 -2.5h-114v-70l21 -115h126zM357 0v153h-162v-153h162z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni000D" horiz-adv-x="338" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="300" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="380" 
d="M94 579v223h191v-223l-22 -311h-147zM93 183h194v-183h-194v183z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="640" 
d="M90 802h201l-22 -336h-158zM350 802h201l-22 -336h-158z" />
    <glyph glyph-name="numbersign" unicode="#" 
d="M97 186h-90l47 137h92l55 156h-90l47 137h92l67 186h142l-67 -186h129l66 186h143l-67 -186h90l-48 -137h-91l-55 -156h89l-47 -137h-91l-67 -186h-142l66 186h-128l-67 -186h-142zM416 323l56 156h-129l-55 -156h128z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="752" 
d="M77 224q70 -15 147 -27t151 -12q45 0 73 3t43.5 10.5t21.5 20.5t6 32t-5 31t-20.5 19.5t-45 13t-78.5 10.5q-87 9 -145.5 22.5t-94.5 38.5t-51.5 65t-15.5 100q0 47 12 84.5t40.5 64.5t74.5 44t114 23v95h152v-93q49 -3 97.5 -9.5t99.5 -17.5v-158q-73 14 -135 22t-127 8
q-45 0 -74.5 -2t-46.5 -8t-23.5 -16t-6.5 -26q0 -18 5 -29.5t21 -19t45.5 -12t78.5 -10.5q87 -9 146 -23.5t95 -40.5t51.5 -66.5t15.5 -102.5q0 -100 -55.5 -156t-186.5 -68v-94h-152v93q-60 3 -119 12t-108 23v156z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="951" 
d="M227 466q-105 0 -155 39.5t-50 136.5q0 49 12.5 82.5t37.5 54.5t64 30t91 9q53 0 92 -9t64.5 -30t38 -54.5t12.5 -82.5q0 -97 -50 -136.5t-157 -39.5zM242 0h-177l645 802h178zM227 571q38 0 50.5 15.5t12.5 55.5t-12.5 55.5t-50.5 15.5q-37 0 -49.5 -15.5t-12.5 -55.5
t12.5 -55.5t49.5 -15.5zM723 -16q-105 0 -155 39t-50 136q0 49 12.5 82.5t37.5 54.5t64 30.5t91 9.5q53 0 92 -9.5t64.5 -30.5t38 -54.5t12.5 -82.5q0 -97 -50 -136t-157 -39zM723 88q38 0 50.5 16t12.5 55q0 41 -12.5 56.5t-50.5 15.5q-37 0 -49.5 -15.5t-12.5 -56.5
q0 -39 12.5 -55t49.5 -16z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="841" 
d="M773 76l-92 -69l-429 328q-14 -8 -23.5 -16t-14.5 -25q-8 -21 -8 -51q0 -29 9 -49q7 -19 28 -31q12 -7 29.5 -11.5t37 -7t38.5 -3t32 -0.5q20 0 41 0.5t41 4.5l148 -108q-15 -7 -32.5 -12.5t-38.5 -10.5q-6 -2 -10 -2q-63 -12 -154 -12q-38 0 -86.5 2.5t-95 14t-85.5 35
t-57 64.5q-22 41 -22 105q0 68 27.5 117.5t97.5 66.5l-31 22l-37 30q-45 48 -45 140q0 36 9 66q2 8 3.5 15t3.5 13q17 37 50 59t73.5 33t83.5 14.5t79 4.5q35 -1 77.5 -4t83 -14.5t73.5 -33t50 -59.5q9 -18 13 -46t4 -48q0 -92 -45 -140v-2l-10 -8q-8 -7 -21.5 -17
t-33 -20.5t-44 -19.5t-54.5 -15l-92 68q55 14 83 36.5t39 43.5q14 24 14 51q0 26 -8 44t-31 32q-22 11 -48 14.5t-50 5.5q-26 -1 -51 -5t-44.5 -13.5t-31 -28t-11.5 -49.5v-7q1 -3 1.5 -7t1.5 -9q1 -3 3 -10t7 -15.5t12.5 -18t19.5 -16.5l76 -53l91 -67l167 -138
q9 14 19.5 33t19.5 41t15.5 46.5t9.5 48.5h164q-8 -43 -22 -89q-12 -40 -32 -88t-50 -96z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="380" 
d="M90 802h201l-22 -336h-158z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="427" 
d="M224 -146q-37 45 -67 94t-51.5 106.5t-33.5 125t-12 148.5q0 83 12.5 151t34.5 125.5t52 106t65 91.5h177q-31 -39 -59 -89t-49.5 -110.5t-34 -129.5t-12.5 -145t12.5 -144.5t33.5 -128.5t49 -110.5t60 -90.5h-177z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="427" 
d="M203 802q37 -45 67 -94t51.5 -106.5t33.5 -125t12 -148.5q0 -82 -12.5 -150.5t-34.5 -126t-52 -106t-65 -91.5h-177q31 39 59 89t49.5 110.5t34 129.5t12.5 145t-12.5 144.5t-33.5 128.5t-49 110.5t-60 90.5h177z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="533" 
d="M351 330l-84 152l-85 -152l-111 80l118 128l-171 33l42 132l159 -74l-22 173h139l-22 -173l159 74l42 -132l-171 -33l120 -128z" />
    <glyph glyph-name="plus" unicode="+" 
d="M96 361h208v211h151v-211h209v-150h-209v-211h-151v211h-208v150z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="380" 
d="M145 186h197l-140 -337h-164z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="380" 
d="M24 209v155h332v-155h-332z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="380" 
d="M93 183h194v-183h-194v183z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="490" 
d="M298 802h182l-288 -948h-180z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="752" 
d="M375 -16q-91 0 -156 25t-106.5 76.5t-61 130t-19.5 185.5t19.5 185.5t61 130t106.5 76.5t156 25q92 0 157.5 -25t107 -76.5t61 -130t19.5 -185.5t-19.5 -185.5t-61 -130t-107 -76.5t-157.5 -25zM375 144q44 0 73 12.5t46 42t24 79t7 123.5t-7 123.5t-24 79t-46 42
t-73 12.5q-43 0 -72 -12.5t-45.5 -42t-23.5 -79t-7 -123.5t7 -123.5t23.5 -79t45.5 -42t72 -12.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="625" 
d="M330 802h187v-802h-191v617l-229 -112l-79 145z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="752" 
d="M359 818q90 0 154 -13t104.5 -40.5t59 -70t18.5 -102.5q0 -47 -10 -80t-31 -58.5t-54 -47t-80 -47.5l-212 -115q-16 -9 -26 -15.5t-15.5 -14t-7.5 -17.5t-2 -25v-15h437v-157h-629v174q0 39 7.5 68t25 52.5t46.5 44.5t72 43l223 119q18 10 29.5 17.5t19 15.5t10.5 19
t3 27q0 22 -6 36.5t-23.5 22.5t-48 11.5t-78.5 3.5q-63 0 -132 -8t-123 -18v163q54 11 118 19t151 8z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="752" 
d="M60 167q32 -6 60.5 -10.5t59 -7.5t64 -5t74.5 -2q58 0 95.5 4.5t59 15t29.5 28t8 43.5q0 28 -6.5 46.5t-22 29.5t-41.5 15.5t-64 4.5h-224v157h224q66 0 94 17t28 71q0 26 -7.5 43t-26.5 27t-52 13.5t-84 3.5q-40 0 -73.5 -2t-64 -5t-59 -7.5t-60.5 -10.5v155
q45 10 112 18.5t152 8.5q93 0 160.5 -11t111 -36t64.5 -66t21 -102q0 -85 -33.5 -130.5t-94.5 -67.5q69 -20 105 -65t36 -130q0 -64 -22 -107.5t-68 -69.5t-118 -37.5t-171 -11.5q-85 0 -153 8.5t-113 18.5v156z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="752" 
d="M695 456v-456h-188v153h-476v147l283 502h199l-278 -494h272v148h188z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="752" 
d="M59 173q32 -6 61 -10.5t59.5 -8t64.5 -5t75 -1.5q58 0 95.5 5t59 16.5t29.5 30.5t8 47q0 27 -7.5 46t-25 30t-46.5 16t-73 5h-273v458h572v-158h-381v-142h114q87 0 147.5 -16t97.5 -47.5t53.5 -79.5t16.5 -111q0 -78 -22 -129t-68.5 -81t-118.5 -42t-171 -12
q-85 0 -153 8.5t-114 18.5v162z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="752" 
d="M382 -16q-96 0 -162.5 24.5t-107.5 75.5t-59 130t-18 187q0 115 22 194.5t70 129t124 71.5t184 22q69 0 122 -8t89 -19v-162q-25 6 -47.5 10.5t-46.5 7.5t-50.5 5t-59.5 2q-58 0 -98 -6.5t-65 -25.5t-36 -53t-13 -89q42 16 89 26t108 10q78 0 131.5 -16.5t87 -49.5
t48.5 -81.5t15 -112.5q0 -75 -23 -126.5t-65.5 -84t-103 -47t-135.5 -14.5zM384 358q-45 0 -81.5 -7.5t-71.5 -19.5q2 -55 10 -91.5t25.5 -59t46.5 -32t73 -9.5q68 0 98.5 24.5t30.5 84.5q0 33 -6 54t-21 33.5t-40 17.5t-64 5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="752" 
d="M42 802h668v-147l-385 -655h-220l393 644h-456v158z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="752" 
d="M376 -16q-173 0 -259.5 52t-86.5 174q0 84 36.5 130t102.5 65q-59 21 -92.5 67t-33.5 129q0 116 83 166.5t250 50.5q166 0 249.5 -50.5t83.5 -166.5q0 -83 -33.5 -129t-92.5 -67q65 -19 101.5 -65t36.5 -130q0 -122 -86 -174t-259 -52zM376 477q39 0 65.5 5t43 15.5
t23.5 28.5t7 43q0 24 -7 41t-22.5 28.5t-42 16.5t-67.5 5q-40 0 -67 -5t-42.5 -16.5t-22.5 -28.5t-7 -41q0 -25 7 -43t23.5 -28.5t43 -15.5t65.5 -5zM376 141q83 0 116.5 22.5t33.5 74.5q0 56 -35 78t-115 22q-81 0 -116 -21.5t-35 -78.5q0 -52 33.5 -74.5t117.5 -22.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="752" 
d="M364 818q97 0 163 -24.5t107 -75.5t59 -130t18 -187q0 -115 -22 -194.5t-70 -129t-124 -71.5t-184 -22q-69 0 -121.5 8t-89.5 19v162q26 -6 48.5 -10.5t46 -7.5t50 -5t59.5 -2q59 0 99 6.5t64.5 25.5t36 53t12.5 89q-42 -16 -89 -26t-108 -10q-78 0 -131.5 16.5t-87 49.5
t-48.5 81.5t-15 112.5q0 75 23 126.5t65.5 84t103 47t135.5 14.5zM231 554q0 -33 6 -54t21 -33.5t40.5 -17.5t63.5 -5q45 0 81.5 7.5t71.5 19.5q-2 55 -10 91.5t-25.5 59t-46 32t-72.5 9.5q-68 0 -99 -24t-31 -85z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="380" 
d="M93 571h194v-183h-194v183zM93 183h194v-183h-194v183z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="381" 
d="M115 542h194v-183h-194v183zM125 186h197l-140 -337h-164z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M96 385l568 169v-156l-382 -111l382 -112v-156l-568 169v197z" />
    <glyph glyph-name="equal" unicode="=" 
d="M96 496h568v-150h-568v150zM96 228h568v-151h-568v151z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M96 175l383 112l-383 111v156l568 -169v-197l-568 -169v156z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="699" 
d="M162 402v94h148q54 0 88 4.5t53 14.5t26 26t7 39q0 24 -7 40.5t-27.5 26.5t-58.5 14.5t-100 4.5q-65 0 -133 -7.5t-131 -21.5v154q27 6 59 11t66.5 8.5t70.5 5.5t70 2q104 0 176 -14.5t116.5 -43t64 -70.5t19.5 -96q0 -57 -17.5 -99.5t-55.5 -70.5t-98.5 -41.5
t-146.5 -13.5l-18 -101h-147zM162 183h194v-183h-194v183z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="977" 
d="M455 79q-82 0 -131 32t-49 105q0 64 35.5 93.5t123.5 29.5h131v5q0 12 -3.5 20.5t-14 14.5t-29.5 8.5t-50 2.5q-40 0 -80 -3.5t-85 -9.5v108q24 4 43.5 6.5t39 4t41.5 2t52 0.5q66 0 109 -9t69 -27t36.5 -45.5t10.5 -64.5v-225q0 -26 11 -41t40 -15q27 0 37.5 15t10.5 41
v162q0 86 -16 144.5t-52.5 94t-96.5 50.5t-148 15t-147.5 -15t-96 -50.5t-52.5 -94t-16 -144.5q0 -87 16.5 -145t54 -93.5t97 -50.5t144.5 -15q17 0 37 1t37 3v-111q-31 -6 -74 -6q-123 0 -207 24t-135.5 75t-74 130t-22.5 188q0 107 24.5 185t77.5 129.5t136 76.5t201 25
q117 0 199.5 -25t135 -76.5t77 -129.5t24.5 -185v-142q0 -54 -11.5 -88.5t-34 -55t-55 -28.5t-74.5 -8q-74 0 -123 30.5t-60 101.5q-49 -20 -113 -20zM477 175q50 0 88 16v59h-99q-31 0 -43 -8.5t-12 -28.5t14 -29t52 -9z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="943" 
d="M315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="899" 
d="M92 802h434q78 0 134 -12t92.5 -38t53.5 -67t17 -99q0 -81 -28.5 -125t-82.5 -63q30 -8 54.5 -21.5t42.5 -34.5t28 -50.5t10 -69.5q0 -65 -20 -107.5t-61 -68t-104 -36t-149 -10.5h-421v802zM287 636v-163h233q57 0 81 16.5t24 65.5q0 25 -5.5 40.5t-18.5 24.5
t-33.5 12.5t-49.5 3.5h-231zM287 333v-167h226q37 0 62.5 4t41.5 14t23 26.5t7 41.5q0 48 -27 64.5t-83 16.5h-250z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="894" 
d="M462 -16q-109 0 -187 25t-128 76.5t-73.5 130t-23.5 185.5t23.5 185.5t73.5 130t128 76.5t187 25q178 0 274.5 -67.5t125.5 -208.5h-205q-21 56 -66.5 80.5t-128.5 24.5q-63 0 -104 -14t-65.5 -43.5t-34.5 -76t-10 -112.5t10 -112.5t34.5 -76t65.5 -43.5t104 -14
q83 0 128.5 25t66.5 80h205q-29 -141 -125.5 -208.5t-274.5 -67.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="937" 
d="M92 802h363q117 0 199.5 -24.5t134.5 -74t75.5 -125t23.5 -177.5q0 -105 -23.5 -181t-75.5 -124.5t-134.5 -71.5t-199.5 -23h-363v801zM287 633v-463h168q69 0 114 13t72 41t37.5 72t10.5 105t-10.5 105t-37.5 72t-72 41.5t-114 13.5h-168z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="814" 
d="M92 802h662v-167h-467v-147h428v-167h-428v-154h467v-167h-662v802z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="780" 
d="M92 802h650v-169h-455v-166h417v-169h-417v-298h-195v802z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="956" 
d="M485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t134 76.5t200 25q96 0 168 -16.5t123 -51t82 -86t45 -122.5h-204q-22 56 -72.5 80.5t-141.5 24.5q-69 0 -114 -14t-72 -44t-38 -76.5t-11 -111.5q0 -66 11 -112.5t38 -76t72.5 -43.5
t115.5 -14q110 0 164.5 32.5t59.5 104.5h-255v164h451v-155q0 -68 -21.5 -126t-71 -100.5t-130 -66.5t-197.5 -24z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="991" 
d="M92 802h195v-313h416v313h195v-802h-195v320h-416v-320h-195v802z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="380" 
d="M92 802h195v-802h-195v802z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="488" 
d="M130 -14q-31 0 -52.5 2t-50.5 5v164q41 -3 80 -3q57 0 79 18t22 62v568h195v-571q0 -62 -11.5 -108t-42.5 -76.5t-83.5 -45.5t-135.5 -15z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="896" 
d="M92 802h195v-335l331 335h244l-383 -382l391 -420h-246l-337 370v-370h-195v802z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="691" 
d="M92 802h195v-633h371v-169h-566v802z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1144" 
d="M92 802h285l189 -444h12l188 444h286v-802h-196v562l-189 -426h-191l-189 426v-562h-195v802z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="998" 
d="M92 802h242l377 -547v547h195v-802h-248l-371 533v-533h-195v802z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="969" 
d="M485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5t75.5 -130t23.5 -185.5t-23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76
t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="891" 
d="M92 802h433q90 0 154 -16.5t105 -51.5t60 -91t19 -135t-19 -135.5t-60 -91.5t-105 -51.5t-154 -16.5h-238v-213h-195v802zM287 639v-263h236q37 0 63.5 5.5t44 20.5t25.5 40.5t8 64.5q0 40 -8 65.5t-25.5 40.5t-44 20.5t-63.5 5.5h-236z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="969" 
d="M485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5t75.5 -130t23.5 -185.5q0 -100 -20.5 -175t-65.5 -126l61 -77l-126 -98l-71 88q-44 -15 -96.5 -22t-115.5 -7zM485 155q26 0 48 1.5t42 6.5l-91 112
l128 101l90 -113q11 27 15 61.5t4 76.5q0 66 -11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="921" 
d="M92 802h433q90 0 154 -16t105 -51.5t60 -91t19 -133.5q0 -109 -35.5 -173t-114.5 -93l139 -244h-220l-113 217h-232v-217h-195v802zM287 639v-259h236q37 0 63.5 5.5t44 20t25.5 39.5t8 64t-8 64.5t-25.5 40t-44 20t-63.5 5.5h-236z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="864" 
d="M82 199q81 -18 169.5 -32.5t173.5 -14.5q55 0 89.5 3.5t54 12.5t26.5 24t7 37q0 23 -6 37.5t-24.5 24t-53.5 15.5t-92 12q-99 8 -166 23t-108.5 43t-59 72.5t-17.5 111.5q0 62 19 108.5t63 78t115.5 47.5t175.5 16q80 0 155 -8.5t157 -27.5v-170q-88 19 -161.5 28.5
t-148.5 9.5q-55 0 -89.5 -3t-54 -11t-26 -22t-6.5 -36q0 -21 6 -34t25 -21t53.5 -13.5t92.5 -11.5q99 -9 166.5 -24.5t108.5 -44.5t58.5 -74.5t17.5 -113.5q0 -64 -20 -112.5t-64.5 -80.5t-116 -48t-174.5 -16q-89 0 -179.5 12.5t-165.5 32.5v170z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="836" 
d="M320 633h-288v169h772v-169h-288v-633h-196v633z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="966" 
d="M483 -16q-111 0 -188 18.5t-125 61.5t-69 113.5t-21 175.5v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8t102.5 8t63.5 30t32 60.5t9 98.5v449h196v-449q0 -105 -21.5 -175.5t-69 -113.5t-124.5 -61.5t-188 -18.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="945" 
d="M18 802h210l228 -637h33l228 637h210l-296 -802h-316z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1398" 
d="M27 802h208l151 -638h16l131 638h331l130 -638h17l151 638h209l-197 -802h-328l-138 637h-18l-138 -637h-329z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="945" 
d="M328 409l-288 393h239l194 -269l194 269h238l-288 -393l300 -409h-239l-205 284l-205 -284h-240z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="891" 
d="M348 311l-336 491h224l213 -313l213 313h217l-336 -492v-310h-195v311z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="823" 
d="M40 168l467 465h-447v169h704v-169l-465 -464h484v-169h-743v168z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="427" 
d="M69 802h326v-146h-150v-656h150v-146h-326v948z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="490" 
d="M12 802h181l287 -948h-181z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="427" 
d="M358 -146h-325v146h149v656h-149v146h325v-948z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M281 802h198l211 -510h-157l-153 365l-153 -365h-157z" />
    <glyph glyph-name="underscore" unicode="_" 
d="M0 -198v138h760v-138h-760z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="600" 
d="M124 802h206l147 -170h-173z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="758" 
d="M319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91
v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="803" 
d="M81 802h187v-251q48 18 93 27.5t93 9.5q68 0 123.5 -16t94.5 -52t60.5 -94t21.5 -141q0 -82 -21.5 -139t-60.5 -93t-94.5 -52.5t-123.5 -16.5q-56 0 -109 14t-111 37l-25 -35h-128v802zM408 437q-36 0 -71 -7.5t-69 -21.5v-242q34 -14 69 -22t71 -8q42 0 71.5 7.5
t48 24.5t27 46t8.5 72q0 44 -8.5 73t-27 46t-48 24.5t-71.5 7.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="681" 
d="M393 -16q-88 0 -153 15.5t-107.5 51.5t-63.5 93.5t-21 141.5t21 141.5t63.5 93.5t107.5 51.5t153 15.5q59 0 113.5 -6.5t111.5 -20.5v-148q-32 5 -59.5 9t-53 6.5t-50.5 3.5t-52 1q-48 0 -79.5 -7t-50 -24t-25.5 -45t-7 -71t7 -71t25.5 -45t50 -24t79.5 -7q56 0 107 4.5
t118 14.5v-147q-60 -15 -117 -21t-118 -6z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="803" 
d="M348 -16q-68 0 -123.5 16t-94.5 52t-60.5 94t-21.5 141q0 81 21.5 138.5t60.5 93.5t94.5 52.5t123.5 16.5q48 0 93.5 -10.5t93.5 -27.5v252h187v-802h-128l-25 35q-58 -24 -111.5 -37.5t-109.5 -13.5zM241 286q0 -43 8.5 -72.5t26.5 -46.5t47.5 -24.5t71.5 -7.5
q36 0 71 7.5t69 21.5v243q-34 14 -69 22t-71 8q-42 0 -71.5 -7.5t-47.5 -25t-26.5 -46.5t-8.5 -72z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="732" 
d="M395 -16q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5q69 0 125.5 -15t96.5 -47.5t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3t63 5.5t72 9.5v-139q-67 -17 -131.5 -22t-134.5 -5z
M376 449q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="483" 
d="M115 430h-81v108l81 34v9q0 58 12.5 102t42 74t78 45.5t121.5 15.5q32 0 57 -2t54 -5v-147q-22 2 -43 3t-42 1q-27 0 -45 -3.5t-28.5 -12t-15 -23.5t-4.5 -38v-19h172v-142h-172v-430h-187v430z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="795" 
d="M348 7q-68 0 -123.5 15.5t-94.5 50.5t-60.5 91t-21.5 137q0 78 21.5 132t60.5 88.5t94.5 50t123.5 16.5q54 -1 105.5 -14.5t107.5 -36.5l25 35h128v-529q0 -75 -22.5 -124t-65 -78t-104 -41t-139.5 -12q-61 0 -125.5 2.5t-147.5 16.5v145q47 -7 84 -11t67.5 -6.5t56.5 -3
t50 -0.5q51 0 82 6.5t48.5 19.5t23 32t5.5 44v9q-46 -17 -89.5 -26t-89.5 -9zM241 300q0 -41 8.5 -68t26.5 -43.5t47.5 -23.5t71.5 -7q35 0 67.5 7t64.5 19v223q-32 13 -64.5 21t-67.5 9q-42 -1 -71.5 -8t-48 -22.5t-26.5 -41.5t-8 -65z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="816" 
d="M81 802h187v-258q48 21 95 32.5t105 11.5q140 0 205.5 -61.5t65.5 -168.5v-358h-187v338q0 48 -26.5 69t-85.5 21q-45 0 -87.5 -12t-84.5 -31v-385h-187v802z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="350" 
d="M86 802h178v-150h-178v150zM82 572h187v-572h-187v572z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="349" 
d="M80 797h190v-145h-190v145zM-52 -61q14 -2 31 -2.5t31 -0.5q39 0 55 15.5t16 56.5v564h187v-557q0 -59 -10 -102t-35.5 -71t-69 -42t-110.5 -14q-28 0 -49 1t-46 5v147z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="767" 
d="M81 802h187v-469l237 239h231l-267 -263l280 -309h-240l-241 285v-285h-187v802z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="349" 
d="M81 802h187v-802h-187v802z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1232" 
d="M81 572h128l29 -41q53 26 103.5 41.5t113.5 15.5q71 0 120.5 -17.5t81.5 -51.5q59 32 115 50.5t126 18.5q133 0 194.5 -61.5t61.5 -168.5v-358h-186v338q0 48 -23.5 69t-75.5 21q-42 0 -81.5 -11.5t-77.5 -30.5q1 -7 1 -13v-373h-187v338q0 48 -23.5 69t-74.5 21
q-42 0 -81 -11.5t-77 -30.5v-386h-187v572z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="816" 
d="M238 531q56 26 109.5 41.5t120.5 15.5q140 0 205.5 -61.5t65.5 -168.5v-358h-187v338q0 48 -26.5 69t-85.5 21q-45 0 -87.5 -11.5t-84.5 -31.5v-385h-187v572h128z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="774" 
d="M387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t149.5 -16t105.5 -52t62.5 -93.5t20.5 -140.5q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5zM387 131q41 0 69 8.5t45 27t24.5 48t7.5 71.5
q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="803" 
d="M81 572h128l25 -35q58 23 111 37t109 14q68 0 123.5 -16t94.5 -52t60.5 -94t21.5 -141q0 -81 -21.5 -138.5t-60.5 -93.5t-94.5 -52.5t-123.5 -16.5q-48 0 -93 10.5t-93 27.5v-220h-187v770zM408 437q-36 0 -71 -8t-69 -22v-241q34 -14 69 -22.5t71 -8.5q42 0 71.5 7.5
t48 25t27 46.5t8.5 72q0 44 -8.5 73t-27 46t-48 24.5t-71.5 7.5z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="803" 
d="M348 -16q-68 0 -123.5 16t-94.5 52t-60.5 94t-21.5 141q0 82 21.5 139t60.5 93t94.5 52.5t123.5 16.5q56 0 109 -14t112 -38l25 36h128v-770h-187v219q-48 -17 -93.5 -27t-93.5 -10zM241 286q0 -43 8.5 -72.5t26.5 -46.5t47.5 -24.5t71.5 -7.5q36 0 71 8t69 22v241
q-34 14 -69 22.5t-71 8.5q-42 0 -71.5 -7.5t-47.5 -25t-26.5 -46.5t-8.5 -72z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="483" 
d="M81 572h128l28 -40q51 26 102 39.5t112 14.5v-164q-97 -4 -183 -30v-392h-187v572z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="712" 
d="M66 155q68 -14 144.5 -24t149.5 -10q37 0 60.5 1.5t37.5 6t19.5 12t5.5 19.5q0 13 -4.5 21t-18.5 13.5t-39.5 9.5t-67.5 7q-83 6 -138.5 17.5t-89 33t-47.5 55t-14 83.5q0 47 14.5 82t48.5 58.5t91 35.5t142 12q67 0 134.5 -6t137.5 -21v-138q-74 14 -136.5 21t-127.5 7
q-38 0 -62.5 -2t-38 -6.5t-18.5 -12t-5 -18.5q0 -12 4.5 -19.5t18.5 -12.5t39 -8.5t67 -7.5q84 -6 139.5 -17.5t89.5 -33t48 -56t14 -85.5q0 -47 -15 -82.5t-49.5 -59t-91.5 -35t-141 -11.5q-37 0 -77 2t-79 6.5t-76 10.5t-69 14v138z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="508" 
d="M357 -9q-79 0 -128.5 14t-77.5 41t-38 66.5t-10 91.5v226h-82v108l82 34v145h187v-145h172v-142h-172v-213q0 -24 4.5 -39t15 -23t28.5 -11t45 -3q20 0 40.5 1t40.5 4v-146q-52 -9 -107 -9z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="816" 
d="M348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5v384h186v-572h-127l-29 41q-56 -26 -110 -41.5t-121 -15.5z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="751" 
d="M12 572h200l157 -419h12l158 419h200l-219 -572h-290z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1132" 
d="M25 572h197l89 -420h9l109 420h275l107 -420l9 1l85 419h202l-138 -572h-283l-115 419h-10l-115 -419h-283z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="745" 
d="M258 293l-235 279h222l128 -157l126 157h223l-233 -279l244 -293h-222l-138 170l-139 -170h-223z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="747" 
d="M12 572h198l153 -407h24l151 407h197l-230 -594q-20 -53 -43.5 -89.5t-56 -59t-77.5 -33t-107 -10.5q-38 0 -64.5 1t-61.5 5v148q21 -2 41.5 -3t45.5 -1q38 0 62 3t39.5 9t24 15.5t13.5 22.5l10 27h-103z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="696" 
d="M48 138l329 292h-315v142h571v-137l-331 -293h345v-142h-599v138z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="427" 
d="M395 -146h-108q-46 0 -82 8.5t-61.5 29.5t-39 56.5t-13.5 89.5v159q0 32 -11 45.5t-48 13.5h-24v145h24q37 0 48 13t11 45v160q0 53 13.5 88.5t39 56.5t61.5 29.5t82 8.5h108v-146h-64q-37 0 -50.5 -11t-13.5 -46v-128q0 -54 -18.5 -89.5t-59.5 -53.5q41 -18 59.5 -53.5
t18.5 -89.5v-127q0 -35 13.5 -46.5t50.5 -11.5h64v-146z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="380" 
d="M103 802h174v-948h-174v948z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="427" 
d="M336 38q0 -54 -13.5 -89.5t-38.5 -56.5t-61 -29.5t-82 -8.5h-108v146h55q42 0 57 11.5t15 46.5v127q0 54 18.5 89.5t59.5 53.5q-41 18 -59.5 53.5t-18.5 89.5v128q0 35 -13 46t-51 11h-63v146h108q46 0 82 -8.5t61 -29.5t38.5 -56.5t13.5 -88.5v-160q0 -32 11.5 -45
t48.5 -13h24v-145h-24q-37 0 -48.5 -13.5t-11.5 -45.5v-159z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M42 213q0 109 46 161t137 52q53 0 96 -18t79 -39t66 -39t56 -18q33 0 44.5 23.5t11.5 69.5v13h140v-35q0 -213 -184 -213q-52 0 -95 18t-79 39.5t-65.5 39.5t-55.5 18q-34 0 -45.5 -23.5t-11.5 -69.5v-14h-140v35z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="300" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="380" 
d="M287 572v-183h-194v183h194zM263 304l22 -311v-223h-191v223l22 311h147z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="752" 
d="M512 802v-100q40 -3 79.5 -8.5t79.5 -15.5v-157q-38 6 -70.5 10t-63 6t-60 3t-61.5 1q-45 0 -74 -6.5t-46 -22.5t-23.5 -43t-6.5 -67t6.5 -66.5t23.5 -42.5t46 -22.5t74 -6.5q33 0 64 0.5t62.5 3t65.5 6t73 9.5v-156q-43 -11 -84.5 -16t-84.5 -8v-103h-152v102
q-74 4 -128.5 22t-90 54t-53 91t-17.5 133t17.5 133.5t53 91.5t90 54.5t128.5 22.5v98h152z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="752" 
d="M104 156q21 0 36 4.5t24 16.5t13 32.5t4 51.5v69h-137v147h137v97q0 62 16 107.5t50.5 76t89.5 45.5t132 15q69 0 124 -8t92 -15v-161q-43 10 -85 16t-99 6q-75 0 -105.5 -20.5t-30.5 -69.5v-89h294v-147h-294v-71q0 -63 -37 -103h378v-156h-662v156h60z" />
    <glyph glyph-name="currency" unicode="&#xa4;" 
d="M79 142l106 106q-19 41 -19 108t19 108l-106 106l87 86l112 -112q40 13 101 13t101 -13l113 113l87 -87l-105 -106q19 -41 19 -108t-19 -108l105 -106l-87 -87l-113 113q-43 -12 -101 -12t-100 12l-113 -112zM379 262q53 0 73.5 21t20.5 73q0 54 -20.5 75t-73.5 21
q-52 0 -72 -21t-20 -75q0 -53 19.5 -73.5t72.5 -20.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="752" 
d="M68 366h212v74h-212v132h116l-167 230h203l156 -219l157 219h202l-168 -230h117v-132h-213v-74h213v-132h-213v-234h-191v234h-212v132z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="380" 
d="M103 432v370h174v-370h-174zM103 224h174v-370h-174v370z" />
    <glyph glyph-name="section" unicode="&#xa7;" 
d="M85 -3q69 -20 142 -33.5t145 -13.5q37 0 62 2.5t40 9.5t21.5 19t6.5 31q0 20 -4.5 32t-18.5 19.5t-41 12.5t-71 9q-89 8 -149.5 21t-97 36t-52.5 58.5t-16 87.5q0 44 20.5 83t55.5 67q-29 26 -41.5 66t-12.5 97q0 55 15.5 96t52 68t96 40t147.5 13q66 0 126.5 -11
t127.5 -26v-166q-72 17 -130.5 28t-121.5 11q-37 0 -62 -2t-40 -8t-21.5 -17t-6.5 -29q0 -19 4.5 -30t19 -18t41 -11t70.5 -9q89 -8 149.5 -21t97.5 -36.5t53 -60t16 -91.5q0 -42 -20 -80.5t-52 -70.5q27 -26 38.5 -64t11.5 -95q0 -56 -17 -98.5t-54 -70.5t-96.5 -42
t-144.5 -14q-75 0 -150.5 14t-138.5 31v166zM392 241q48 -5 87 -10.5t70 -14.5q10 20 10 44q0 26 -6.5 44t-26.5 30.5t-58 20t-101 13.5q-48 4 -86 10t-69 16q-13 -23 -13 -52q0 -27 6.5 -44t26.5 -27.5t58 -17t102 -12.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="600" 
d="M117 791h142v-146h-142v146zM341 791h142v-146h-142v146z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="969" 
d="M485 -16q-119 0 -202 25t-135 77t-75.5 130.5t-23.5 184.5t23.5 184.5t75.5 130t135 77t202 25.5t202 -25.5t134.5 -77t75 -130t23.5 -184.5t-23.5 -184.5t-75 -130.5t-134.5 -77t-202 -25zM485 83q87 0 148.5 18.5t100.5 57.5t57 99t18 143t-18 143t-57 99t-100.5 57.5
t-148.5 18.5q-88 0 -149.5 -18.5t-100.5 -57.5t-56.5 -99t-17.5 -143t17.5 -143t56.5 -99t100.5 -57.5t149.5 -18.5zM485 149q-132 0 -191 61t-59 191t59 191t191 61q108 0 166 -41t75 -127h-139q-13 28 -35.5 40.5t-66.5 12.5q-34 0 -56 -8t-35 -24.5t-18.5 -42.5t-5.5 -62
t5.5 -62t18.5 -42.5t35 -24.5t56 -8q43 0 66 12.5t36 40.5h140q-18 -86 -76.5 -127t-165.5 -41z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="442" 
d="M180 466q-78 0 -116.5 27.5t-38.5 87.5q0 26 7 45.5t23 32.5t42.5 19t66.5 6h103v3q0 8 -2 14t-10 10t-23.5 5.5t-42.5 1.5q-32 0 -67 -3t-73 -8v100q22 3 39.5 5.5t34.5 3.5t36 1.5t43 0.5q60 0 100 -8t63 -24t32.5 -39.5t9.5 -54.5v-217h-93l-15 18q-23 -13 -52 -20
t-67 -7zM200 551q40 0 67 13v41h-74q-17 0 -24 -6.5t-7 -20.5t8 -20.5t30 -6.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="687" 
d="M177 519h190l-151 -232l151 -232h-190l-152 232zM472 519h190l-151 -232l151 -232h-190l-152 232z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M96 496h568v-419h-152v269h-416v150z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="485" 
d="M242 401q-118 0 -167.5 51.5t-49.5 157.5q0 53 11.5 92t37.5 65t67.5 38.5t100.5 12.5q119 0 168.5 -51t49.5 -157t-49.5 -157.5t-168.5 -51.5zM242 451q88 0 125.5 36.5t37.5 122.5t-37.5 122.5t-125.5 36.5t-124.5 -36.5t-36.5 -122.5t37 -122.5t124 -36.5zM134 730
h123q55 0 78.5 -20t23.5 -68q0 -63 -44 -80l41 -73h-73l-31 65h-50v-65h-68v241zM202 676v-67h54q19 0 27.5 6.5t8.5 26.5t-8.5 27t-27.5 7h-54z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="600" 
d="M93 780h414v-124h-414v124z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="453" 
d="M227 466q-105 0 -155 39.5t-50 136.5q0 49 12.5 82.5t37.5 54.5t64 30t91 9q53 0 92 -9t64.5 -30t38 -54.5t12.5 -82.5q0 -97 -50 -136.5t-157 -39.5zM227 571q38 0 50.5 15.5t12.5 55.5t-12.5 55.5t-50.5 15.5q-37 0 -49.5 -15.5t-12.5 -55.5t12.5 -55.5t49.5 -15.5z
" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M96 457h208v115h151v-115h209v-150h-209v-115h-151v115h-208v150zM96 151h568v-151h-568v151z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="436" 
d="M210 1009q107 0 155.5 -31.5t48.5 -106.5q0 -28 -6 -47.5t-19 -34.5t-32.5 -27.5t-46.5 -26.5l-117 -60q-11 -6 -14 -9.5t-3 -12.5h234v-113h-378v112q0 23 5 39.5t16 30t29 25.5t43 25l123 63q12 7 16.5 12t4.5 17q0 8 -2.5 13t-10 8t-21 4t-36.5 1q-35 0 -77.5 -5
t-74.5 -10v118q33 6 71.5 11t91.5 5z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="436" 
d="M29 663q39 -7 74 -11t77 -4q57 0 75 6.5t18 27.5t-10.5 28.5t-42.5 7.5h-139v112h139q26 0 37 6t11 25q0 20 -16.5 26t-66.5 6q-46 0 -79 -4t-72 -11v115q24 5 65 10.5t92 5.5q57 0 98.5 -5.5t68.5 -19.5t40.5 -38t13.5 -60q0 -48 -19.5 -74t-54.5 -40q39 -12 59.5 -37.5
t20.5 -72.5q0 -39 -14 -64.5t-42 -40t-71 -20.5t-100 -6q-51 0 -94 5.5t-68 10.5v116z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="600" 
d="M270 802h206l-180 -170h-173z" />
    <glyph glyph-name="uni00B5" unicode="&#xb5;" 
d="M83 572h186v-325q0 -55 25 -80t81 -25q61 0 116 28v402h186v-572h-127l-18 26q-35 -22 -70 -32t-78 -10q-70 0 -115 34v-216h-186v770z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" 
d="M269 234q-119 6 -172 73.5t-53 210.5q0 76 15 130t46.5 88t82 50t120.5 16h356v-948h-138v820h-119v-820h-138v380z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="380" 
d="M93 377h194v-183h-194v183z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="600" 
d="M255 3h86l-49 -45q67 0 98 -18.5t31 -63.5q0 -51 -35 -70.5t-113 -19.5q-24 0 -47 1t-48 6v65q28 -3 48 -4t43 -1q39 0 50.5 4.5t11.5 18.5t-11.5 19t-50.5 5q-18 0 -34 -1.5t-37 -3.5v57z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="365" 
d="M185 1000h141v-460h-143v322l-118 -59l-59 108z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="455" 
d="M227 466q-105 0 -155 39.5t-50 136.5q0 49 12.5 82.5t37.5 54.5t64 30t91 9q53 0 92 -9t64.5 -30t38 -54.5t12.5 -82.5q0 -97 -50 -136.5t-157 -39.5zM227 571q38 0 50.5 15.5t12.5 55.5t-12.5 55.5t-50.5 15.5q-37 0 -49.5 -15.5t-12.5 -55.5t12.5 -55.5t49.5 -15.5z
" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="687" 
d="M216 55h-191l152 232l-152 232h190l152 -232zM511 55h-191l152 232l-152 232h190l152 -232z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1071" 
d="M185 802h141v-460h-143v322l-118 -59l-59 108zM265 0h-177l645 802h178zM1052 262v-262h-140v77h-262v110l154 273h149l-154 -272h113v74h140z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1071" 
d="M185 802h141v-460h-143v322l-118 -59l-59 108zM265 0h-177l645 802h178zM844 469q107 0 155.5 -31.5t48.5 -106.5q0 -28 -6 -47.5t-19 -34.5t-32.5 -27.5t-46.5 -26.5l-117 -60q-11 -6 -14 -9.5t-3 -12.5h234v-113h-378v112q0 23 5 39.5t16 30t29 25.5t43 25l123 63
q12 7 16.5 12t4.5 17q0 8 -2.5 13t-10 8t-21 4t-36.5 1q-35 0 -77.5 -5t-74.5 -10v118q33 6 71.5 11t91.5 5z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1143" 
d="M29 465q39 -7 74 -11t77 -4q57 0 75 6.5t18 27.5t-10.5 28.5t-42.5 7.5h-139v112h139q26 0 37 6t11 25q0 20 -16.5 26t-66.5 6q-46 0 -79 -4t-72 -11v115q24 5 65 10.5t92 5.5q57 0 98.5 -5.5t68.5 -19.5t40.5 -38t13.5 -60q0 -48 -19.5 -74t-54.5 -40q39 -12 59.5 -37.5
t20.5 -72.5q0 -39 -14 -64.5t-42 -40t-71 -20.5t-100 -6q-51 0 -94 5.5t-68 10.5v116zM336 0h-177l645 802h178zM1124 262v-262h-140v77h-262v110l154 273h149l-154 -272h113v74h140z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="699" 
d="M537 389h-194v183h194v-183zM537 170v-94h-148q-54 0 -88 -4.5t-53 -14.5t-26 -26t-7 -39q0 -24 7 -40.5t27.5 -26.5t58.5 -14.5t100 -4.5q65 0 133 7.5t131 21.5v-154q-27 -6 -59 -11t-66.5 -8.5t-70.5 -5.5t-70 -2q-104 0 -176 14.5t-116.5 43t-64 70.5t-19.5 96
q0 57 17.5 99.5t55.5 70.5t98.5 41.5t146.5 13.5l18 101h147z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="943" 
d="M249 987h206l119 -140h-181zM315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="943" 
d="M489 987h206l-144 -140h-181zM315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="943" 
d="M358 987h230l133 -140h-166l-82 73l-83 -73h-166zM315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="943" 
d="M246 863q0 63 32 93.5t93 30.5q32 0 58.5 -6t49 -13t41.5 -13t35 -6q17 0 23 6.5t6 20.5v6h117v-18q0 -63 -32 -93.5t-93 -30.5q-32 0 -58.5 6t-49 13.5t-41 13.5t-35.5 6t-23 -6.5t-6 -22.5v-6h-117v19zM315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350
l-104 292h-32l-104 -292h240z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="943" 
d="M275 978h150v-122h-150v122zM519 978h150v-122h-150v122zM315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="943" 
d="M315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1373" 
d="M651 181h-322l-106 -181h-229l475 802h843v-167h-465v-147h427v-167h-427v-154h465v-167h-661v181zM651 350v285h-57l-166 -285h223z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="894" 
d="M862 260q-25 -122 -100.5 -189t-211.5 -82l-121 -187h-164l124 185q-178 16 -258.5 117.5t-80.5 296.5q0 107 23.5 185.5t73.5 130t128 76.5t187 25q178 0 274.5 -67.5t125.5 -208.5h-205q-21 56 -66.5 80.5t-128.5 24.5q-63 0 -104 -14t-65.5 -43.5t-34.5 -76
t-10 -112.5t10 -112.5t34.5 -76t65.5 -43.5t104 -14q83 0 128.5 25t66.5 80h205z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="814" 
d="M203 987h206l119 -140h-181zM92 802h662v-167h-467v-147h428v-167h-428v-154h467v-167h-662v802z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="814" 
d="M443 987h206l-144 -140h-181zM92 802h662v-167h-467v-147h428v-167h-428v-154h467v-167h-662v802z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="814" 
d="M312 987h230l133 -140h-166l-82 73l-83 -73h-166zM92 802h662v-167h-467v-147h428v-167h-428v-154h467v-167h-662v802z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="814" 
d="M229 978h150v-122h-150v122zM473 978h150v-122h-150v122zM92 802h662v-167h-467v-147h428v-167h-428v-154h467v-167h-662v802z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="380" 
d="M-33 987h206l119 -140h-181zM92 802h195v-802h-195v802z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="380" 
d="M207 987h206l-144 -140h-181zM92 802h195v-802h-195v802z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="380" 
d="M76 987h230l133 -140h-166l-82 73l-83 -73h-166zM92 802h195v-802h-195v802z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="380" 
d="M-7 978h150v-122h-150v122zM237 978h150v-122h-150v122zM92 802h195v-802h-195v802z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="937" 
d="M92 335h-92v139h92v328h363q117 0 199.5 -24.5t134.5 -74t75.5 -125t23.5 -177.5q0 -105 -23.5 -181t-75.5 -124.5t-134.5 -71.5t-199.5 -23h-363v334zM287 335v-165h168q69 0 114 13t72 41t37.5 72t10.5 105t-10.5 105t-37.5 72t-72 41.5t-114 13.5h-168v-159h173v-139
h-173z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="998" 
d="M273 863q0 63 32 93.5t93 30.5q32 0 58.5 -6t49 -13t41.5 -13t35 -6q17 0 23 6.5t6 20.5v6h117v-18q0 -63 -32 -93.5t-93 -30.5q-32 0 -58.5 6t-49 13.5t-41 13.5t-35.5 6t-23 -6.5t-6 -22.5v-6h-117v19zM92 802h242l377 -547v547h195v-802h-248l-371 533v-533h-195v802z
" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="969" 
d="M262 987h206l119 -140h-181zM485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5t75.5 -130t23.5 -185.5t-23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155q69 0 114.5 14t72.5 43.5t38 76
t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="969" 
d="M502 987h206l-144 -140h-181zM485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5t75.5 -130t23.5 -185.5t-23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155q69 0 114.5 14t72.5 43.5t38 76
t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="969" 
d="M371 987h230l133 -140h-166l-82 73l-83 -73h-166zM485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5t75.5 -130t23.5 -185.5t-23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155
q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="969" 
d="M259 863q0 63 32 93.5t93 30.5q32 0 58.5 -6t49 -13t41.5 -13t35 -6q17 0 23 6.5t6 20.5v6h117v-18q0 -63 -32 -93.5t-93 -30.5q-32 0 -58.5 6t-49 13.5t-41 13.5t-35.5 6t-23 -6.5t-6 -22.5v-6h-117v19zM485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5
t23.5 185.5t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5t75.5 -130t23.5 -185.5t-23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5
t11 -112.5t38 -76t73 -43.5t115 -14z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="969" 
d="M288 978h150v-122h-150v122zM532 978h150v-122h-150v122zM485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5t75.5 -130t23.5 -185.5t-23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155
q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M108 122l165 165l-164 164l106 106l164 -164l166 165l106 -107l-165 -165l165 -165l-106 -105l-165 165l-165 -165z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="969" 
d="M42 83l63 60q-29 49 -42 113t-13 145q0 107 23.5 185.5t75.5 130t135 76.5t201 25q176 0 276 -57l63 57l99 -99l-61 -57q30 -50 43.5 -114.5t13.5 -146.5q0 -107 -23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25q-177 0 -278 58l-62 -58zM248 401q0 -33 2.5 -61
t8.5 -52l358 339q-49 20 -132 20q-69 0 -115 -14t-73 -43.5t-38 -76t-11 -112.5zM485 155q69 0 114.5 14t72.5 43.5t38 76t11 112.5q0 33 -2.5 61.5t-8.5 51.5l-358 -339q47 -20 133 -20z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="966" 
d="M260 987h206l119 -140h-181zM483 -16q-111 0 -188 18.5t-125 61.5t-69 113.5t-21 175.5v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8t102.5 8t63.5 30t32 60.5t9 98.5v449h196v-449q0 -105 -21.5 -175.5t-69 -113.5t-124.5 -61.5t-188 -18.5z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="966" 
d="M500 987h206l-144 -140h-181zM483 -16q-111 0 -188 18.5t-125 61.5t-69 113.5t-21 175.5v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8t102.5 8t63.5 30t32 60.5t9 98.5v449h196v-449q0 -105 -21.5 -175.5t-69 -113.5t-124.5 -61.5t-188 -18.5z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="966" 
d="M369 987h230l133 -140h-166l-82 73l-83 -73h-166zM483 -16q-111 0 -188 18.5t-125 61.5t-69 113.5t-21 175.5v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8t102.5 8t63.5 30t32 60.5t9 98.5v449h196v-449q0 -105 -21.5 -175.5t-69 -113.5t-124.5 -61.5
t-188 -18.5z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="966" 
d="M286 978h150v-122h-150v122zM530 978h150v-122h-150v122zM483 -16q-111 0 -188 18.5t-125 61.5t-69 113.5t-21 175.5v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8t102.5 8t63.5 30t32 60.5t9 98.5v449h196v-449q0 -105 -21.5 -175.5t-69 -113.5t-124.5 -61.5
t-188 -18.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="891" 
d="M463 987h206l-144 -140h-181zM348 311l-336 491h224l213 -313l213 313h217l-336 -492v-310h-195v311z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="891" 
d="M287 802v-105h238q90 0 154 -16t105 -51.5t60 -91t19 -133.5q0 -79 -19 -134.5t-60 -91t-105 -51.5t-154 -16h-238v-112h-195v802h195zM287 534v-259h236q37 0 63.5 5.5t44 20t25.5 39.5t8 64t-8 64t-25.5 40t-44 20.5t-63.5 5.5h-236z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="769" 
d="M449 -5q-32 0 -53.5 1t-47.5 6v141q17 -2 31.5 -3.5t28.5 -1.5q30 0 52.5 4.5t38.5 15.5t24 31.5t8 52.5q0 62 -31 83.5t-97 21.5h-55v123h56q54 0 80.5 22t26.5 79q0 58 -29.5 80.5t-91.5 22.5q-63 0 -92.5 -21.5t-29.5 -75.5v-577h-187v576q0 53 14 97t49.5 76.5
t95.5 50.5t151 18q154 0 230.5 -52.5t76.5 -167.5q0 -81 -29.5 -126t-92.5 -65q67 -16 106 -59.5t39 -129.5q0 -115 -66.5 -169t-205.5 -54z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="758" 
d="M111 802h206l147 -170h-173zM319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1q95 0 158 -13
t100.5 -39.5t53 -65.5t15.5 -91v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="758" 
d="M441 800h206l-180 -170h-173zM319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1q95 0 158 -13
t100.5 -39.5t53 -65.5t15.5 -91v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="758" 
d="M280 802h198l131 -170h-165l-65 88l-65 -88h-165zM319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3
t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="758" 
d="M152 660q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24zM319 -16q-60 0 -108 11t-82.5 35
t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91v-379h-126l-25 35q-41 -23 -93 -37
t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="758" 
d="M196 791h142v-146h-142v146zM420 791h142v-146h-142v146zM319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5
t60 3t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="758" 
d="M378 617q-86 0 -123.5 27t-37.5 93q0 65 37.5 92t123.5 27q87 0 124.5 -27t37.5 -92q0 -66 -38 -93t-124 -27zM378 688q41 0 56.5 10t15.5 39t-15.5 38.5t-56.5 9.5q-40 0 -55 -9.5t-15 -38.5t15 -39t55 -10zM319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5
q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112
q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1174" 
d="M320 -16q-59 0 -107.5 11t-83 34.5t-53.5 61t-19 89.5q0 45 12 78.5t39.5 55t71.5 32t108 10.5h206v16q0 18 -5.5 31.5t-22.5 21.5t-48 12t-81 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 62.5 9.5t53 5.5t57 3t74.5 1q98 0 159 -22.5t93 -60.5q36 42 93 63t139 21
q69 0 124 -15t93.5 -47.5t59 -84.5t20.5 -127v-89h-442q3 -29 12.5 -47.5t28 -29t48.5 -14.5t74 -4q32 0 61 1t59 3t63 5.5t72 9.5v-139q-67 -17 -131.5 -22t-134.5 -5q-91 0 -155.5 16t-106.5 52q-50 -31 -113 -49.5t-142 -18.5zM818 449q-65 0 -95 -21.5t-38 -78.5h264
q-8 57 -39 78.5t-92 21.5zM334 112q105 0 176 45q-5 17 -8.5 36t-5.5 40h-178q-44 0 -60 -13.5t-16 -45.5q0 -35 19 -48.5t73 -13.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="681" 
d="M628 11q-77 -19 -157 -25l-119 -184h-164l125 186q-67 6 -117 26t-83 55.5t-49 89t-16 127.5q0 84 21 141.5t63.5 93.5t107.5 51.5t153 15.5q59 0 113.5 -6.5t111.5 -20.5v-148q-32 5 -59.5 9t-53 6.5t-50.5 3.5t-52 1q-48 0 -79.5 -7t-50 -24t-25.5 -45t-7 -71t7 -71
t25.5 -45t50 -24t79.5 -7q56 0 107 4.5t118 14.5v-147z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="732" 
d="M109 802h206l147 -170h-173zM395 -16q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5q69 0 125.5 -15t96.5 -47.5t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3t63 5.5t72 9.5v-139
q-67 -17 -131.5 -22t-134.5 -5zM376 449q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="732" 
d="M439 800h206l-180 -170h-173zM395 -16q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5q69 0 125.5 -15t96.5 -47.5t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3t63 5.5t72 9.5v-139
q-67 -17 -131.5 -22t-134.5 -5zM376 449q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="732" 
d="M278 802h198l131 -170h-165l-65 88l-65 -88h-165zM395 -16q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5q69 0 125.5 -15t96.5 -47.5t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3t63 5.5
t72 9.5v-139q-67 -17 -131.5 -22t-134.5 -5zM376 449q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="732" 
d="M194 791h142v-146h-142v146zM418 791h142v-146h-142v146zM395 -16q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5q69 0 125.5 -15t96.5 -47.5t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3
t63 5.5t72 9.5v-139q-67 -17 -131.5 -22t-134.5 -5zM376 449q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="350" 
d="M-93 802h206l147 -170h-173zM82 572h187v-572h-187v572z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="350" 
d="M237 800h206l-180 -170h-173zM82 572h187v-572h-187v572z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="350" 
d="M76 802h198l131 -170h-165l-65 88l-65 -88h-165zM82 572h187v-572h-187v572z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="350" 
d="M-8 791h142v-146h-142v146zM216 791h142v-146h-142v146zM82 572h187v-572h-187v572z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="771" 
d="M164 697l114 24q-26 20 -55 40t-60 41h264q15 -10 29.5 -19t28.5 -20l187 39l25 -106l-111 -23q71 -78 104 -166t33 -186q0 -93 -20.5 -157t-62 -104t-104.5 -58t-149 -18t-150 16t-106 51t-62.5 91t-20.5 136q0 79 21.5 134.5t60.5 90.5t94.5 51t123.5 16q50 0 103 -14
q-30 39 -67 75l-194 -41zM531 322q0 21 -2 40.5t-6 39.5q-31 11 -63 17.5t-65 6.5q-42 0 -71.5 -7.5t-47.5 -24.5t-26.5 -45.5t-8.5 -70.5q0 -40 7.5 -68.5t24.5 -45.5t45 -25t69 -8q40 0 67.5 10t44.5 32.5t24.5 59t7.5 89.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="816" 
d="M181 660q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24zM238 531q56 26 109.5 41.5t120.5 15.5
q140 0 205.5 -61.5t65.5 -168.5v-358h-187v338q0 48 -26.5 69t-85.5 21q-45 0 -87.5 -11.5t-84.5 -31.5v-385h-187v572h128z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="774" 
d="M119 802h206l147 -170h-173zM387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t149.5 -16t105.5 -52t62.5 -93.5t20.5 -140.5q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5zM387 131q41 0 69 8.5t45 27
t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="774" 
d="M449 800h206l-180 -170h-173zM387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t149.5 -16t105.5 -52t62.5 -93.5t20.5 -140.5q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5zM387 131q41 0 69 8.5
t45 27t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="774" 
d="M288 802h198l131 -170h-165l-65 88l-65 -88h-165zM387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t149.5 -16t105.5 -52t62.5 -93.5t20.5 -140.5q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5z
M387 131q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="774" 
d="M160 660q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24zM387 -16q-86 0 -150 16.5t-106 52.5
t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t149.5 -16t105.5 -52t62.5 -93.5t20.5 -140.5q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5zM387 131q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8
t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="774" 
d="M204 791h142v-146h-142v146zM428 791h142v-146h-142v146zM387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t149.5 -16t105.5 -52t62.5 -93.5t20.5 -140.5q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5
zM387 131q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M305 562h150v-142h-150v142zM96 361h568v-150h-568v150zM305 152h150v-142h-150v142z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="774" 
d="M46 90l34 29q-32 66 -32 167q0 83 20.5 140.5t62.5 93.5t106 52t150 16q63 0 114 -8.5t90 -27.5l44 36l93 -106l-35 -29q32 -67 32 -167q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5q-63 0 -114 8.5t-91 27.5l-43 -36zM241 286q0 -12 0.5 -23t1.5 -21
l221 188q-29 11 -77 11q-41 0 -69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5zM387 131q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 12 -0.5 23t-1.5 21l-221 -188q28 -11 77 -11z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="816" 
d="M140 802h206l147 -170h-173zM348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5v384h186v-572h-127l-29 41q-56 -26 -110 -41.5t-121 -15.5z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="816" 
d="M470 800h206l-180 -170h-173zM348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5v384h186v-572h-127l-29 41q-56 -26 -110 -41.5t-121 -15.5z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="816" 
d="M309 802h198l131 -170h-165l-65 88l-65 -88h-165zM348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5v384h186v-572h-127l-29 41q-56 -26 -110 -41.5t-121 -15.5z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="816" 
d="M225 791h142v-146h-142v146zM449 791h142v-146h-142v146zM348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5v384h186v-572h-127l-29 41q-56 -26 -110 -41.5t-121 -15.5z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="747" 
d="M436 800h206l-180 -170h-173zM12 572h198l153 -407h24l151 407h197l-230 -594q-20 -53 -43.5 -89.5t-56 -59t-77.5 -33t-107 -10.5q-38 0 -64.5 1t-61.5 5v148q21 -2 41.5 -3t45.5 -1q38 0 62 3t39.5 9t24 15.5t13.5 22.5l10 27h-103z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="803" 
d="M81 802h187v-252q48 18 93 28t93 10q68 0 123.5 -16t94.5 -52t60.5 -94t21.5 -141q0 -82 -21.5 -139t-60.5 -93t-94.5 -52.5t-123.5 -16.5q-48 0 -93 10.5t-93 27.5v-220h-187v1000zM408 435q-36 0 -71 -7.5t-69 -21.5v-239q34 -14 69 -22t71 -8q42 0 71.5 7.5t48 24.5
t27 45.5t8.5 71.5t-8.5 71.5t-27 46t-48 24.5t-71.5 7z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="747" 
d="M191 791h142v-146h-142v146zM415 791h142v-146h-142v146zM12 572h198l153 -407h24l151 407h197l-230 -594q-20 -53 -43.5 -89.5t-56 -59t-77.5 -33t-107 -10.5q-38 0 -64.5 1t-61.5 5v148q21 -2 41.5 -3t45.5 -1q38 0 62 3t39.5 9t24 15.5t13.5 22.5l10 27h-103z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="943" 
d="M261 974h422v-116h-422v116zM315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="758" 
d="M172 780h414v-124h-414v124zM319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1q95 0 158 -13
t100.5 -39.5t53 -65.5t15.5 -91v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="943" 
d="M473 842q-69 0 -115 8t-73.5 25.5t-39 45t-11.5 66.5h140q0 -22 20 -32t79 -10t79 10t20 32h141q0 -39 -11.5 -66.5t-39 -45t-74 -25.5t-115.5 -8zM315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="758" 
d="M379 632q-64 0 -106.5 9t-68 29t-36 51t-10.5 76h140q0 -36 16 -49t65 -13t65.5 13t16.5 49h140q0 -45 -10.5 -76t-36 -51t-68 -29t-107.5 -9zM319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5
t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5
t27.5 -11.5t44.5 -4z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="943" 
d="M315 802h313l296 -802h-59q-31 -14 -49 -23.5t-28 -17t-13 -15t-3 -17.5q0 -17 13 -23.5t46 -6.5q20 0 39.5 2.5t39.5 4.5v-106q-26 -5 -52.5 -8.5t-58.5 -3.5q-89 0 -124.5 24.5t-35.5 75.5q0 36 16.5 62t60.5 53l-64 180h-361l-64 -181h-209zM591 350l-104 292h-32
l-104 -292h240z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="758" 
d="M319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91
v-379h-33q-31 -14 -49 -23.5t-28 -17t-13 -15t-3 -17.5q0 -17 13 -23.5t46 -6.5q20 0 39.5 2.5t39.5 4.5v-106q-26 -5 -52.5 -8.5t-58.5 -3.5q-89 0 -124.5 24.5t-35.5 75.5q0 22 5.5 39.5t18.5 33.5t36 31t57 32l-9 13q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91
h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="894" 
d="M479 987h206l-144 -140h-181zM462 -16q-109 0 -187 25t-128 76.5t-73.5 130t-23.5 185.5t23.5 185.5t73.5 130t128 76.5t187 25q178 0 274.5 -67.5t125.5 -208.5h-205q-21 56 -66.5 80.5t-128.5 24.5q-63 0 -104 -14t-65.5 -43.5t-34.5 -76t-10 -112.5t10 -112.5
t34.5 -76t65.5 -43.5t104 -14q83 0 128.5 25t66.5 80h205q-29 -141 -125.5 -208.5t-274.5 -67.5z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="681" 
d="M423 800h206l-180 -170h-173zM393 -16q-88 0 -153 15.5t-107.5 51.5t-63.5 93.5t-21 141.5t21 141.5t63.5 93.5t107.5 51.5t153 15.5q59 0 113.5 -6.5t111.5 -20.5v-148q-32 5 -59.5 9t-53 6.5t-50.5 3.5t-52 1q-48 0 -79.5 -7t-50 -24t-25.5 -45t-7 -71t7 -71t25.5 -45
t50 -24t79.5 -7q56 0 107 4.5t118 14.5v-147q-60 -15 -117 -21t-118 -6z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="894" 
d="M348 987h230l133 -140h-166l-82 73l-83 -73h-166zM462 -16q-109 0 -187 25t-128 76.5t-73.5 130t-23.5 185.5t23.5 185.5t73.5 130t128 76.5t187 25q178 0 274.5 -67.5t125.5 -208.5h-205q-21 56 -66.5 80.5t-128.5 24.5q-63 0 -104 -14t-65.5 -43.5t-34.5 -76
t-10 -112.5t10 -112.5t34.5 -76t65.5 -43.5t104 -14q83 0 128.5 25t66.5 80h205q-29 -141 -125.5 -208.5t-274.5 -67.5z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="681" 
d="M262 802h198l131 -170h-165l-65 88l-65 -88h-165zM393 -16q-88 0 -153 15.5t-107.5 51.5t-63.5 93.5t-21 141.5t21 141.5t63.5 93.5t107.5 51.5t153 15.5q59 0 113.5 -6.5t111.5 -20.5v-148q-32 5 -59.5 9t-53 6.5t-50.5 3.5t-52 1q-48 0 -79.5 -7t-50 -24t-25.5 -45
t-7 -71t7 -71t25.5 -45t50 -24t79.5 -7q56 0 107 4.5t118 14.5v-147q-60 -15 -117 -21t-118 -6z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="894" 
d="M367 978h190v-122h-190v122zM462 -16q-109 0 -187 25t-128 76.5t-73.5 130t-23.5 185.5t23.5 185.5t73.5 130t128 76.5t187 25q178 0 274.5 -67.5t125.5 -208.5h-205q-21 56 -66.5 80.5t-128.5 24.5q-63 0 -104 -14t-65.5 -43.5t-34.5 -76t-10 -112.5t10 -112.5t34.5 -76
t65.5 -43.5t104 -14q83 0 128.5 25t66.5 80h205q-29 -141 -125.5 -208.5t-274.5 -67.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="681" 
d="M266 791h190v-146h-190v146zM393 -16q-88 0 -153 15.5t-107.5 51.5t-63.5 93.5t-21 141.5t21 141.5t63.5 93.5t107.5 51.5t153 15.5q59 0 113.5 -6.5t111.5 -20.5v-148q-32 5 -59.5 9t-53 6.5t-50.5 3.5t-52 1q-48 0 -79.5 -7t-50 -24t-25.5 -45t-7 -71t7 -71t25.5 -45
t50 -24t79.5 -7q56 0 107 4.5t118 14.5v-147q-60 -15 -117 -21t-118 -6z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="894" 
d="M214 987h166l83 -74l82 74h166l-133 -140h-230zM462 -16q-109 0 -187 25t-128 76.5t-73.5 130t-23.5 185.5t23.5 185.5t73.5 130t128 76.5t187 25q178 0 274.5 -67.5t125.5 -208.5h-205q-21 56 -66.5 80.5t-128.5 24.5q-63 0 -104 -14t-65.5 -43.5t-34.5 -76t-10 -112.5
t10 -112.5t34.5 -76t65.5 -43.5t104 -14q83 0 128.5 25t66.5 80h205q-29 -141 -125.5 -208.5t-274.5 -67.5z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="681" 
d="M131 802h165l65 -88l65 88h165l-131 -170h-198zM393 -16q-88 0 -153 15.5t-107.5 51.5t-63.5 93.5t-21 141.5t21 141.5t63.5 93.5t107.5 51.5t153 15.5q59 0 113.5 -6.5t111.5 -20.5v-148q-32 5 -59.5 9t-53 6.5t-50.5 3.5t-52 1q-48 0 -79.5 -7t-50 -24t-25.5 -45
t-7 -71t7 -71t25.5 -45t50 -24t79.5 -7q56 0 107 4.5t118 14.5v-147q-60 -15 -117 -21t-118 -6z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="937" 
d="M221 987h166l83 -74l82 74h166l-133 -140h-230zM92 802h363q117 0 199.5 -24.5t134.5 -74t75.5 -125t23.5 -177.5q0 -105 -23.5 -181t-75.5 -124.5t-134.5 -71.5t-199.5 -23h-363v801zM287 633v-463h168q69 0 114 13t72 41t37.5 72t10.5 105t-10.5 105t-37.5 72t-72 41.5
t-114 13.5h-168z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="850" 
d="M348 -16q-68 0 -123.5 16t-94.5 52t-60.5 94t-21.5 141q0 81 21.5 138.5t60.5 93.5t94.5 52.5t123.5 16.5q48 0 93.5 -10.5t93.5 -27.5v252h187v-802h-128l-25 35q-58 -24 -111.5 -37.5t-109.5 -13.5zM819 802h164l-114 -252h-99zM241 286q0 -43 8.5 -72.5t26.5 -46.5
t47.5 -24.5t71.5 -7.5q36 0 71 7.5t69 21.5v243q-34 14 -69 22t-71 8q-42 0 -71.5 -7.5t-47.5 -25t-26.5 -46.5t-8.5 -72z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="937" 
d="M92 335h-92v139h92v328h363q117 0 199.5 -24.5t134.5 -74t75.5 -125t23.5 -177.5q0 -105 -23.5 -181t-75.5 -124.5t-134.5 -71.5t-199.5 -23h-363v334zM287 335v-165h168q69 0 114 13t72 41t37.5 72t10.5 105t-10.5 105t-37.5 72t-72 41.5t-114 13.5h-168v-159h173v-139
h-173z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="803" 
d="M348 -16q-68 0 -123.5 16t-94.5 52t-60.5 94t-21.5 141q0 81 21.5 138.5t60.5 93.5t94.5 52.5t123.5 16.5q48 0 93.5 -10.5t93.5 -27.5v73h-185v132h185v47h187v-47h81v-132h-81v-623h-128l-25 35q-58 -24 -111.5 -37.5t-109.5 -13.5zM241 286q0 -43 8.5 -72.5
t26.5 -46.5t47.5 -24.5t71.5 -7.5q36 0 71 7.5t69 21.5v243q-34 14 -69 22t-71 8q-42 0 -71.5 -7.5t-47.5 -25t-26.5 -46.5t-8.5 -72z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="814" 
d="M215 974h422v-116h-422v116zM92 802h662v-167h-467v-147h428v-167h-428v-154h467v-167h-662v802z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="732" 
d="M170 780h414v-124h-414v124zM395 -16q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5q69 0 125.5 -15t96.5 -47.5t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3t63 5.5t72 9.5v-139
q-67 -17 -131.5 -22t-134.5 -5zM376 449q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="814" 
d="M427 842q-69 0 -115 8t-73.5 25.5t-39 45t-11.5 66.5h140q0 -22 20 -32t79 -10t79 10t20 32h141q0 -39 -11.5 -66.5t-39 -45t-74 -25.5t-115.5 -8zM92 802h662v-167h-467v-147h428v-167h-428v-154h467v-167h-662v802z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="732" 
d="M377 632q-64 0 -106.5 9t-68 29t-36 51t-10.5 76h140q0 -36 16 -49t65 -13t65.5 13t16.5 49h140q0 -45 -10.5 -76t-36 -51t-68 -29t-107.5 -9zM395 -16q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5q69 0 125.5 -15t96.5 -47.5
t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3t63 5.5t72 9.5v-139q-67 -17 -131.5 -22t-134.5 -5zM376 449q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="814" 
d="M331 978h190v-122h-190v122zM92 802h662v-167h-467v-147h428v-167h-428v-154h467v-167h-662v802z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="732" 
d="M282 791h190v-146h-190v146zM395 -16q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5q69 0 125.5 -15t96.5 -47.5t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3t63 5.5t72 9.5v-139
q-67 -17 -131.5 -22t-134.5 -5zM376 449q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="814" 
d="M92 802h662v-167h-467v-147h428v-167h-428v-154h467v-167h-45q-31 -14 -49 -23.5t-28 -17t-13 -15t-3 -17.5q0 -17 13 -23.5t46 -6.5q20 0 39.5 2.5t39.5 4.5v-106q-26 -5 -52.5 -8.5t-58.5 -3.5q-89 0 -124.5 24.5t-35.5 75.5q0 36 16 62t60 52h-467v802z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="732" 
d="M395 -16q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5q69 0 125.5 -15t96.5 -47.5t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3t63 5.5t72 9.5v-139l-36 -8q-33 -15 -53 -25t-30.5 -18
t-13.5 -15.5t-3 -17.5q0 -17 13 -23.5t46 -6.5q20 0 39.5 2.5t39.5 4.5v-106q-26 -5 -52.5 -8.5t-58.5 -3.5q-89 0 -124.5 24.5t-35.5 75.5q0 31 11.5 53.5t40.5 45.5q-12 -1 -24 -1h-25zM376 449q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21z
" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="814" 
d="M178 987h166l83 -74l82 74h166l-133 -140h-230zM92 802h662v-167h-467v-147h428v-167h-428v-154h467v-167h-662v802z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="732" 
d="M147 802h165l65 -88l65 88h165l-131 -170h-198zM395 -16q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5q69 0 125.5 -15t96.5 -47.5t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3t63 5.5
t72 9.5v-139q-67 -17 -131.5 -22t-134.5 -5zM376 449q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="956" 
d="M369 987h230l133 -140h-166l-82 73l-83 -73h-166zM485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t134 76.5t200 25q96 0 168 -16.5t123 -51t82 -86t45 -122.5h-204q-22 56 -72.5 80.5t-141.5 24.5q-69 0 -114 -14t-72 -44t-38 -76.5
t-11 -111.5q0 -66 11 -112.5t38 -76t72.5 -43.5t115.5 -14q110 0 164.5 32.5t59.5 104.5h-255v164h451v-155q0 -68 -21.5 -126t-71 -100.5t-130 -66.5t-197.5 -24z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="795" 
d="M309 802h198l131 -170h-165l-65 88l-65 -88h-165zM348 7q-68 0 -123.5 15.5t-94.5 50.5t-60.5 91t-21.5 137q0 78 21.5 132t60.5 88.5t94.5 50t123.5 16.5q54 -1 105.5 -14.5t107.5 -36.5l25 35h128v-529q0 -75 -22.5 -124t-65 -78t-104 -41t-139.5 -12q-61 0 -125.5 2.5
t-147.5 16.5v145q47 -7 84 -11t67.5 -6.5t56.5 -3t50 -0.5q51 0 82 6.5t48.5 19.5t23 32t5.5 44v9q-46 -17 -89.5 -26t-89.5 -9zM241 300q0 -41 8.5 -68t26.5 -43.5t47.5 -23.5t71.5 -7q35 0 67.5 7t64.5 19v223q-32 13 -64.5 21t-67.5 9q-42 -1 -71.5 -8t-48 -22.5
t-26.5 -41.5t-8 -65z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="956" 
d="M484 842q-69 0 -115 8t-73.5 25.5t-39 45t-11.5 66.5h140q0 -22 20 -32t79 -10t79 10t20 32h141q0 -39 -11.5 -66.5t-39 -45t-74 -25.5t-115.5 -8zM485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t134 76.5t200 25q96 0 168 -16.5t123 -51
t82 -86t45 -122.5h-204q-22 56 -72.5 80.5t-141.5 24.5q-69 0 -114 -14t-72 -44t-38 -76.5t-11 -111.5q0 -66 11 -112.5t38 -76t72.5 -43.5t115.5 -14q110 0 164.5 32.5t59.5 104.5h-255v164h451v-155q0 -68 -21.5 -126t-71 -100.5t-130 -66.5t-197.5 -24z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="795" 
d="M408 632q-64 0 -106.5 9t-68 29t-36 51t-10.5 76h140q0 -36 16 -49t65 -13t65.5 13t16.5 49h140q0 -45 -10.5 -76t-36 -51t-68 -29t-107.5 -9zM348 7q-68 0 -123.5 15.5t-94.5 50.5t-60.5 91t-21.5 137q0 78 21.5 132t60.5 88.5t94.5 50t123.5 16.5q54 -1 105.5 -14.5
t107.5 -36.5l25 35h128v-529q0 -75 -22.5 -124t-65 -78t-104 -41t-139.5 -12q-61 0 -125.5 2.5t-147.5 16.5v145q47 -7 84 -11t67.5 -6.5t56.5 -3t50 -0.5q51 0 82 6.5t48.5 19.5t23 32t5.5 44v9q-46 -17 -89.5 -26t-89.5 -9zM241 300q0 -41 8.5 -68t26.5 -43.5t47.5 -23.5
t71.5 -7q35 0 67.5 7t64.5 19v223q-32 13 -64.5 21t-67.5 9q-42 -1 -71.5 -8t-48 -22.5t-26.5 -41.5t-8 -65z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="956" 
d="M388 978h190v-122h-190v122zM485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t134 76.5t200 25q96 0 168 -16.5t123 -51t82 -86t45 -122.5h-204q-22 56 -72.5 80.5t-141.5 24.5q-69 0 -114 -14t-72 -44t-38 -76.5t-11 -111.5
q0 -66 11 -112.5t38 -76t72.5 -43.5t115.5 -14q110 0 164.5 32.5t59.5 104.5h-255v164h451v-155q0 -68 -21.5 -126t-71 -100.5t-130 -66.5t-197.5 -24z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="795" 
d="M313 791h190v-146h-190v146zM348 7q-68 0 -123.5 15.5t-94.5 50.5t-60.5 91t-21.5 137q0 78 21.5 132t60.5 88.5t94.5 50t123.5 16.5q54 -1 105.5 -14.5t107.5 -36.5l25 35h128v-529q0 -75 -22.5 -124t-65 -78t-104 -41t-139.5 -12q-61 0 -125.5 2.5t-147.5 16.5v145
q47 -7 84 -11t67.5 -6.5t56.5 -3t50 -0.5q51 0 82 6.5t48.5 19.5t23 32t5.5 44v9q-46 -17 -89.5 -26t-89.5 -9zM241 300q0 -41 8.5 -68t26.5 -43.5t47.5 -23.5t71.5 -7q35 0 67.5 7t64.5 19v223q-32 13 -64.5 21t-67.5 9q-42 -1 -71.5 -8t-48 -22.5t-26.5 -41.5t-8 -65z" />
    <glyph glyph-name="uni0122" unicode="&#x122;" horiz-adv-x="956" 
d="M485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t134 76.5t200 25q96 0 168 -16.5t123 -51t82 -86t45 -122.5h-204q-22 56 -72.5 80.5t-141.5 24.5q-69 0 -114 -14t-72 -44t-38 -76.5t-11 -111.5q0 -66 11 -112.5t38 -76t72.5 -43.5
t115.5 -14q110 0 164.5 32.5t59.5 104.5h-255v164h451v-155q0 -68 -21.5 -126t-71 -100.5t-130 -66.5t-197.5 -24zM386 -53h207l-177 -145h-173z" />
    <glyph glyph-name="uni0123" unicode="&#x123;" horiz-adv-x="795" 
d="M511 632h-206l180 170h173zM348 7q-68 0 -123.5 15.5t-94.5 50.5t-60.5 91t-21.5 137q0 78 21.5 132t60.5 88.5t94.5 50t123.5 16.5q54 -1 105.5 -14.5t107.5 -36.5l25 35h128v-529q0 -75 -22.5 -124t-65 -78t-104 -41t-139.5 -12q-61 0 -125.5 2.5t-147.5 16.5v145
q47 -7 84 -11t67.5 -6.5t56.5 -3t50 -0.5q51 0 82 6.5t48.5 19.5t23 32t5.5 44v9q-46 -17 -89.5 -26t-89.5 -9zM241 300q0 -41 8.5 -68t26.5 -43.5t47.5 -23.5t71.5 -7q35 0 67.5 7t64.5 19v223q-32 13 -64.5 21t-67.5 9q-42 -1 -71.5 -8t-48 -22.5t-26.5 -41.5t-8 -65z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="991" 
d="M381 987h230l133 -140h-166l-82 73l-83 -73h-166zM92 802h195v-313h416v313h195v-802h-195v320h-416v-320h-195v802z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="816" 
d="M294 987h230l133 -140h-166l-82 73l-83 -73h-166zM81 802h187v-258q48 21 95 32.5t105 11.5q140 0 205.5 -61.5t65.5 -168.5v-358h-187v338q0 48 -26.5 69t-85.5 21q-45 0 -87.5 -12t-84.5 -31v-385h-187v802z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="991" 
d="M92 579h-92v140h92v83h195v-83h416v83h195v-83h93v-140h-93v-579h-195v320h-416v-320h-195v579zM703 489v90h-416v-90h416z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="816" 
d="M81 623h-81v132h81v47h187v-47h186v-132h-186v-79q48 21 95 32.5t105 11.5q140 0 205.5 -61.5t65.5 -168.5v-358h-187v338q0 48 -26.5 69t-85.5 21q-45 0 -87.5 -12t-84.5 -31v-385h-187v623z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="380" 
d="M-36 863q0 63 32 93.5t93 30.5q32 0 58.5 -6t49 -13t41.5 -13t35 -6q17 0 23 6.5t6 20.5v6h117v-18q0 -63 -32 -93.5t-93 -30.5q-32 0 -58.5 6t-49 13.5t-41 13.5t-35.5 6t-23 -6.5t-6 -22.5v-6h-117v19zM92 802h195v-802h-195v802z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="350" 
d="M-52 660q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24zM82 572h187v-572h-187v572z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="380" 
d="M-21 974h422v-116h-422v116zM92 802h195v-802h-195v802z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="350" 
d="M-32 780h414v-124h-414v124zM82 572h187v-572h-187v572z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="380" 
d="M191 842q-69 0 -115 8t-73.5 25.5t-39 45t-11.5 66.5h140q0 -22 20 -32t79 -10t79 10t20 32h141q0 -39 -11.5 -66.5t-39 -45t-74 -25.5t-115.5 -8zM92 802h195v-802h-195v802z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="350" 
d="M175 632q-64 0 -106.5 9t-68 29t-36 51t-10.5 76h140q0 -36 16 -49t65 -13t65.5 13t16.5 49h140q0 -45 -10.5 -76t-36 -51t-68 -29t-107.5 -9zM82 572h187v-572h-187v572z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="380" 
d="M92 802h195v-802h-38q-31 -14 -49 -23.5t-28 -17t-13 -15t-3 -17.5q0 -17 13 -23.5t46 -6.5q20 0 39.5 2.5t39.5 4.5v-106q-26 -5 -52.5 -8.5t-58.5 -3.5q-89 0 -124.5 24.5t-35.5 75.5q0 36 16 62t60 52h-7v802z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="350" 
d="M80 797h190v-145h-190v145zM138 -73q0 -17 13 -23.5t46 -6.5q20 0 39.5 2.5t39.5 4.5v-106q-26 -5 -52.5 -8.5t-58.5 -3.5q-89 0 -124.5 24.5t-35.5 75.5q0 36 16.5 62t60.5 53v571h187v-572h-38q-31 -14 -49 -23.5t-28 -17t-13 -15t-3 -17.5z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="380" 
d="M95 978h190v-122h-190v122zM92 802h195v-802h-195v802z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="350" 
d="M82 572h187v-572h-187v572z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="867" 
d="M92 802h195v-802h-195v802zM510 -14q-31 0 -52.5 2t-50.5 5v164q41 -3 80 -3q57 0 79 18t22 62v568h195v-571q0 -62 -11.5 -108t-42.5 -76.5t-83.5 -45.5t-135.5 -15z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="699" 
d="M80 797h190v-145h-190v145zM430 797h190v-145h-190v145zM82 572h187v-572h-187v572zM298 -61q14 -2 31 -2.5t31 -0.5q39 0 55 15.5t16 56.5v564h187v-557q0 -59 -10 -102t-35.5 -71t-69 -42t-110.5 -14q-28 0 -49 1t-46 5v147z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="488" 
d="M192 987h230l133 -140h-166l-82 73l-83 -73h-166zM130 -14q-31 0 -52.5 2t-50.5 5v164q41 -3 80 -3q57 0 79 18t22 62v568h195v-571q0 -62 -11.5 -108t-42.5 -76.5t-83.5 -45.5t-135.5 -15z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="349" 
d="M75 802h198l131 -170h-165l-65 88l-65 -88h-165zM-52 -67q14 -2 31 -3t31 -1q39 0 55 16.5t16 60.5v566h187v-557q0 -59 -10 -102t-35.5 -71t-69 -42t-110.5 -14q-28 0 -49 1t-46 5v141z" />
    <glyph glyph-name="uni0136" unicode="&#x136;" horiz-adv-x="896" 
d="M92 802h195v-335l331 335h244l-383 -382l391 -420h-246l-337 370v-370h-195v802zM343 -53h207l-177 -145h-173z" />
    <glyph glyph-name="uni0137" unicode="&#x137;" horiz-adv-x="767" 
d="M81 802h187v-469l237 239h231l-267 -263l280 -309h-240l-241 285v-285h-187v802zM278 -53h207l-177 -145h-173z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="763" 
d="M81 572h187v-239l237 239h231l-267 -263l280 -309h-240l-241 285v-285h-187v572z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="691" 
d="M374 987h206l-144 -140h-181zM92 802h195v-633h371v-169h-566v802z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="349" 
d="M191 987h206l-144 -140h-181zM81 802h187v-802h-187v802z" />
    <glyph glyph-name="uni013B" unicode="&#x13b;" horiz-adv-x="691" 
d="M92 802h195v-633h371v-169h-566v802zM291 -53h207l-177 -145h-173z" />
    <glyph glyph-name="uni013C" unicode="&#x13c;" horiz-adv-x="349" 
d="M81 802h187v-802h-187v802zM76 -53h207l-177 -145h-173z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="691" 
d="M92 802h195v-633h371v-169h-566v802zM389 802h164l-114 -252h-99z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="396" 
d="M81 802h187v-802h-187v802zM365 802h164l-114 -252h-99z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="691" 
d="M92 802h195v-633h371v-169h-566v802zM454 570h194v-183h-194v183z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="543" 
d="M81 802h187v-802h-187v802zM366 377h194v-183h-194v183z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="691" 
d="M92 281l-92 -43v160l92 43v361h195v-270l157 73v-159l-157 -74v-203h371v-169h-566v281z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="349" 
d="M81 190l-81 -38v152l81 38v460h187v-373l81 38v-151l-81 -38v-278h-187v190z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="998" 
d="M516 987h206l-144 -140h-181zM92 802h242l377 -547v547h195v-802h-248l-371 533v-533h-195v802z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="816" 
d="M470 800h206l-180 -170h-173zM238 531q56 26 109.5 41.5t120.5 15.5q140 0 205.5 -61.5t65.5 -168.5v-358h-187v338q0 48 -26.5 69t-85.5 21q-45 0 -87.5 -11.5t-84.5 -31.5v-385h-187v572h128z" />
    <glyph glyph-name="uni0145" unicode="&#x145;" horiz-adv-x="998" 
d="M92 802h242l377 -547v547h195v-802h-248l-371 533v-533h-195v802zM397 -53h207l-177 -145h-173z" />
    <glyph glyph-name="uni0146" unicode="&#x146;" horiz-adv-x="816" 
d="M238 531q56 26 109.5 41.5t120.5 15.5q140 0 205.5 -61.5t65.5 -168.5v-358h-187v338q0 48 -26.5 69t-85.5 21q-45 0 -87.5 -11.5t-84.5 -31.5v-385h-187v572h128zM310 -53h207l-177 -145h-173z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="998" 
d="M251 987h166l83 -74l82 74h166l-133 -140h-230zM92 802h242l377 -547v547h195v-802h-248l-371 533v-533h-195v802z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="816" 
d="M178 802h165l65 -88l65 88h165l-131 -170h-198zM238 531q56 26 109.5 41.5t120.5 15.5q140 0 205.5 -61.5t65.5 -168.5v-358h-187v338q0 48 -26.5 69t-85.5 21q-45 0 -87.5 -11.5t-84.5 -31.5v-385h-187v572h128z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="1049" 
d="M145 802h197l-140 -337h-164zM471 531q56 26 109.5 41.5t120.5 15.5q140 0 205.5 -61.5t65.5 -168.5v-358h-187v338q0 48 -26.5 69t-85.5 21q-45 0 -87.5 -11.5t-84.5 -31.5v-385h-187v572h128z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="998" 
d="M92 802h240l379 -492v492h195v-771q0 -62 -11.5 -108t-42.5 -76.5t-83.5 -45.5t-135.5 -15q-31 0 -53 2t-50 5v160q41 -3 80 -3q29 0 48 2.5t30.5 8t16.5 15t6 24.5l-424 544v-544h-195v802z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="816" 
d="M418 -61q15 -2 32 -2.5t31 -0.5q38 0 54.5 15.5t16.5 56.5v330q0 51 -26.5 74t-85.5 23q-45 0 -87.5 -12t-84.5 -31v-392h-187v572h128l29 -41q56 26 109.5 41.5t120.5 15.5q140 0 205.5 -61.5t65.5 -168.5v-343q0 -59 -10 -102t-35.5 -71t-69 -42t-110.5 -14
q-29 0 -50 1t-46 5v147z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="969" 
d="M274 974h422v-116h-422v116zM485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5t75.5 -130t23.5 -185.5t-23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155q69 0 114.5 14t72.5 43.5t38 76
t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="774" 
d="M180 780h414v-124h-414v124zM387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t149.5 -16t105.5 -52t62.5 -93.5t20.5 -140.5q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5zM387 131q41 0 69 8.5t45 27
t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="969" 
d="M486 842q-69 0 -115 8t-73.5 25.5t-39 45t-11.5 66.5h140q0 -22 20 -32t79 -10t79 10t20 32h141q0 -39 -11.5 -66.5t-39 -45t-74 -25.5t-115.5 -8zM485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5
t75.5 -130t23.5 -185.5t-23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="774" 
d="M387 632q-64 0 -106.5 9t-68 29t-36 51t-10.5 76h140q0 -36 16 -49t65 -13t65.5 13t16.5 49h140q0 -45 -10.5 -76t-36 -51t-68 -29t-107.5 -9zM387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t149.5 -16t105.5 -52
t62.5 -93.5t20.5 -140.5q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5zM387 131q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="969" 
d="M359 987h191l-121 -140h-165zM634 987h191l-120 -140h-166zM485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5t75.5 -130t23.5 -185.5t-23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155
q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="774" 
d="M280 802h190l-140 -170h-157zM532 802h190l-141 -170h-157zM387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t149.5 -16t105.5 -52t62.5 -93.5t20.5 -140.5q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5
t-149.5 -16.5zM387 131q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1372" 
d="M650 1h-167q-118 0 -200 24.5t-134 74t-75.5 125t-23.5 177.5q0 105 23.5 181t75.5 124.5t134 71.5t200 23h829v-167h-467v-147h428v-167h-428v-154h467v-167h-662v1zM650 170v463h-167q-69 0 -114.5 -13t-72 -41t-37.5 -72t-11 -105t11 -105t37.5 -72.5t72 -41.5
t114.5 -13h167z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1217" 
d="M378 -16q-84 0 -146 16.5t-103 52.5t-61 93.5t-20 139.5q0 83 20 140.5t61 93.5t103 52t146 16q177 0 251 -95q37 50 98 73t146 23q68 0 122.5 -15t93.5 -47.5t60 -84.5t21 -127v-88h-443q3 -30 12 -48.5t28 -29.5t49 -15t75 -4q32 0 61 1t59 3t63 6t72 10v-139
q-66 -16 -128 -21.5t-130 -5.5q-186 0 -260 95q-73 -95 -250 -95zM860 450q-65 0 -94.5 -22t-37.5 -78h264q-9 57 -40 78.5t-92 21.5zM387 131q41 0 69 8t45 26.5t24.5 48t7.5 72.5t-7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5
t24.5 -48t45 -27t69 -8.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="921" 
d="M477 987h206l-144 -140h-181zM92 802h433q90 0 154 -16t105 -51.5t60 -91t19 -133.5q0 -109 -35.5 -173t-114.5 -93l139 -244h-220l-113 217h-232v-217h-195v802zM287 639v-259h236q37 0 63.5 5.5t44 20t25.5 39.5t8 64t-8 64.5t-25.5 40t-44 20t-63.5 5.5h-236z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="483" 
d="M337 800h206l-180 -170h-173zM81 572h128l28 -40q51 26 102 39.5t112 14.5v-164q-97 -4 -183 -30v-392h-187v572z" />
    <glyph glyph-name="uni0156" unicode="&#x156;" horiz-adv-x="921" 
d="M92 802h433q90 0 154 -16t105 -51.5t60 -91t19 -133.5q0 -109 -35.5 -173t-114.5 -93l139 -244h-220l-113 217h-232v-217h-195v802zM287 639v-259h236q37 0 63.5 5.5t44 20t25.5 39.5t8 64t-8 64.5t-25.5 40t-44 20t-63.5 5.5h-236zM352 -53h207l-177 -145h-173z" />
    <glyph glyph-name="uni0157" unicode="&#x157;" horiz-adv-x="483" 
d="M81 572h128l28 -40q51 26 102 39.5t112 14.5v-164q-97 -4 -183 -30v-392h-187v572zM74 -53h207l-177 -145h-173z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="921" 
d="M212 987h166l83 -74l82 74h166l-133 -140h-230zM92 802h433q90 0 154 -16t105 -51.5t60 -91t19 -133.5q0 -109 -35.5 -173t-114.5 -93l139 -244h-220l-113 217h-232v-217h-195v802zM287 639v-259h236q37 0 63.5 5.5t44 20t25.5 39.5t8 64t-8 64.5t-25.5 40t-44 20
t-63.5 5.5h-236z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="483" 
d="M45 802h165l65 -88l65 88h165l-131 -170h-198zM81 572h128l28 -40q51 26 102 39.5t112 14.5v-164q-97 -4 -183 -30v-392h-187v572z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="864" 
d="M458 987h206l-144 -140h-181zM82 199q81 -18 169.5 -32.5t173.5 -14.5q55 0 89.5 3.5t54 12.5t26.5 24t7 37q0 23 -6 37.5t-24.5 24t-53.5 15.5t-92 12q-99 8 -166 23t-108.5 43t-59 72.5t-17.5 111.5q0 62 19 108.5t63 78t115.5 47.5t175.5 16q80 0 155 -8.5t157 -27.5
v-170q-88 19 -161.5 28.5t-148.5 9.5q-55 0 -89.5 -3t-54 -11t-26 -22t-6.5 -36q0 -21 6 -34t25 -21t53.5 -13.5t92.5 -11.5q99 -9 166.5 -24.5t108.5 -44.5t58.5 -74.5t17.5 -113.5q0 -64 -20 -112.5t-64.5 -80.5t-116 -48t-174.5 -16q-89 0 -179.5 12.5t-165.5 32.5v170z
" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="712" 
d="M420 800h206l-180 -170h-173zM66 155q68 -14 144.5 -24t149.5 -10q37 0 60.5 1.5t37.5 6t19.5 12t5.5 19.5q0 13 -4.5 21t-18.5 13.5t-39.5 9.5t-67.5 7q-83 6 -138.5 17.5t-89 33t-47.5 55t-14 83.5q0 47 14.5 82t48.5 58.5t91 35.5t142 12q67 0 134.5 -6t137.5 -21
v-138q-74 14 -136.5 21t-127.5 7q-38 0 -62.5 -2t-38 -6.5t-18.5 -12t-5 -18.5q0 -12 4.5 -19.5t18.5 -12.5t39 -8.5t67 -7.5q84 -6 139.5 -17.5t89.5 -33t48 -56t14 -85.5q0 -47 -15 -82.5t-49.5 -59t-91.5 -35t-141 -11.5q-37 0 -77 2t-79 6.5t-76 10.5t-69 14v138z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="864" 
d="M327 987h230l133 -140h-166l-82 73l-83 -73h-166zM82 199q81 -18 169.5 -32.5t173.5 -14.5q55 0 89.5 3.5t54 12.5t26.5 24t7 37q0 23 -6 37.5t-24.5 24t-53.5 15.5t-92 12q-99 8 -166 23t-108.5 43t-59 72.5t-17.5 111.5q0 62 19 108.5t63 78t115.5 47.5t175.5 16
q80 0 155 -8.5t157 -27.5v-170q-88 19 -161.5 28.5t-148.5 9.5q-55 0 -89.5 -3t-54 -11t-26 -22t-6.5 -36q0 -21 6 -34t25 -21t53.5 -13.5t92.5 -11.5q99 -9 166.5 -24.5t108.5 -44.5t58.5 -74.5t17.5 -113.5q0 -64 -20 -112.5t-64.5 -80.5t-116 -48t-174.5 -16
q-89 0 -179.5 12.5t-165.5 32.5v170z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="712" 
d="M259 802h198l131 -170h-165l-65 88l-65 -88h-165zM66 155q68 -14 144.5 -24t149.5 -10q37 0 60.5 1.5t37.5 6t19.5 12t5.5 19.5q0 13 -4.5 21t-18.5 13.5t-39.5 9.5t-67.5 7q-83 6 -138.5 17.5t-89 33t-47.5 55t-14 83.5q0 47 14.5 82t48.5 58.5t91 35.5t142 12
q67 0 134.5 -6t137.5 -21v-138q-74 14 -136.5 21t-127.5 7q-38 0 -62.5 -2t-38 -6.5t-18.5 -12t-5 -18.5q0 -12 4.5 -19.5t18.5 -12.5t39 -8.5t67 -7.5q84 -6 139.5 -17.5t89.5 -33t48 -56t14 -85.5q0 -47 -15 -82.5t-49.5 -59t-91.5 -35t-141 -11.5q-37 0 -77 2t-79 6.5
t-76 10.5t-69 14v138z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="864" 
d="M82 199q81 -18 169.5 -32.5t173.5 -14.5q55 0 89.5 3.5t54 12.5t26.5 24t7 37q0 23 -6 37.5t-24.5 24t-53.5 15.5t-92 12q-99 8 -166 23t-108.5 43t-59 72.5t-17.5 111.5q0 62 19 108.5t63 78t115.5 47.5t175.5 16q80 0 155 -8.5t157 -27.5v-170q-88 19 -161.5 28.5
t-148.5 9.5q-55 0 -89.5 -3t-54 -11t-26 -22t-6.5 -36q0 -21 6 -34t25 -21t53.5 -13.5t92.5 -11.5q99 -9 166.5 -24.5t108.5 -44.5t58.5 -74.5t17.5 -113.5q0 -61 -17.5 -107t-56.5 -78t-101.5 -50t-152.5 -21l-29 -27q67 0 98 -18.5t31 -63.5q0 -51 -35 -70.5t-113 -19.5
q-24 0 -47 1t-48 6v65q28 -3 48 -4t43 -1q39 0 50.5 4.5t11.5 18.5t-11.5 19t-50.5 5q-18 0 -34 -1.5t-37 -3.5v57l37 33q-81 2 -160.5 14.5t-145.5 29.5v170z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="712" 
d="M66 155q68 -14 144.5 -24t149.5 -10q37 0 60.5 1.5t37.5 6t19.5 12t5.5 19.5q0 13 -4.5 21t-18.5 13.5t-39.5 9.5t-67.5 7q-83 6 -138.5 17.5t-89 33t-47.5 55t-14 83.5q0 47 14.5 82t48.5 58.5t91 35.5t142 12q67 0 134.5 -6t137.5 -21v-138q-74 14 -136.5 21t-127.5 7
q-38 0 -62.5 -2t-38 -6.5t-18.5 -12t-5 -18.5q0 -12 4.5 -19.5t18.5 -12.5t39 -8.5t67 -7.5q84 -6 139.5 -17.5t89.5 -33t48 -56t14 -85.5q0 -46 -13.5 -80.5t-45.5 -58t-84.5 -36t-130.5 -13.5l-28 -26q67 0 98 -18.5t31 -63.5q0 -51 -35 -70.5t-113 -19.5q-24 0 -47 1
t-48 6v65q28 -3 48 -4t43 -1q39 0 50.5 4.5t11.5 18.5t-11.5 19t-50.5 5q-18 0 -34 -1.5t-37 -3.5v57l37 33q-63 3 -125.5 11t-113.5 21v138z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="864" 
d="M193 987h166l83 -74l82 74h166l-133 -140h-230zM82 199q81 -18 169.5 -32.5t173.5 -14.5q55 0 89.5 3.5t54 12.5t26.5 24t7 37q0 23 -6 37.5t-24.5 24t-53.5 15.5t-92 12q-99 8 -166 23t-108.5 43t-59 72.5t-17.5 111.5q0 62 19 108.5t63 78t115.5 47.5t175.5 16
q80 0 155 -8.5t157 -27.5v-170q-88 19 -161.5 28.5t-148.5 9.5q-55 0 -89.5 -3t-54 -11t-26 -22t-6.5 -36q0 -21 6 -34t25 -21t53.5 -13.5t92.5 -11.5q99 -9 166.5 -24.5t108.5 -44.5t58.5 -74.5t17.5 -113.5q0 -64 -20 -112.5t-64.5 -80.5t-116 -48t-174.5 -16
q-89 0 -179.5 12.5t-165.5 32.5v170z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="712" 
d="M128 802h165l65 -88l65 88h165l-131 -170h-198zM66 155q68 -14 144.5 -24t149.5 -10q37 0 60.5 1.5t37.5 6t19.5 12t5.5 19.5q0 13 -4.5 21t-18.5 13.5t-39.5 9.5t-67.5 7q-83 6 -138.5 17.5t-89 33t-47.5 55t-14 83.5q0 47 14.5 82t48.5 58.5t91 35.5t142 12
q67 0 134.5 -6t137.5 -21v-138q-74 14 -136.5 21t-127.5 7q-38 0 -62.5 -2t-38 -6.5t-18.5 -12t-5 -18.5q0 -12 4.5 -19.5t18.5 -12.5t39 -8.5t67 -7.5q84 -6 139.5 -17.5t89.5 -33t48 -56t14 -85.5q0 -47 -15 -82.5t-49.5 -59t-91.5 -35t-141 -11.5q-37 0 -77 2t-79 6.5
t-76 10.5t-69 14v138z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="836" 
d="M320 633h-288v169h772v-169h-288v-633h-57l-46 -42q67 0 98 -18.5t31 -63.5q0 -51 -35 -70.5t-113 -19.5q-24 0 -47 1t-48 6v65q28 -3 48 -4t43 -1q39 0 50.5 4.5t11.5 18.5t-11.5 19t-50.5 5q-18 0 -34 -1.5t-37 -3.5v57l54 48h-53v633z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="508" 
d="M276 -4q-53 7 -86.5 23.5t-53 43t-26.5 62t-7 79.5v226h-82v108l82 34v145h187v-145h172v-142h-172v-213q0 -24 4.5 -39t15 -23t28.5 -11t45 -3q20 0 40.5 1t40.5 4v-146q-52 -9 -107 -9l-36 -33q67 0 98 -18.5t31 -63.5q0 -51 -35 -70.5t-113 -19.5q-24 0 -47 1t-48 6
v65q28 -3 48 -4t43 -1q39 0 50.5 4.5t11.5 18.5t-11.5 19t-50.5 5q-18 0 -34 -1.5t-37 -3.5v57z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="836" 
d="M170 987h166l83 -74l82 74h166l-133 -140h-230zM320 633h-288v169h772v-169h-288v-633h-196v633z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="508" 
d="M386 868h164l-114 -252h-99zM357 -9q-79 0 -128.5 14t-77.5 41t-38 66.5t-10 91.5v226h-82v108l82 34v145h187v-145h172v-142h-172v-213q0 -24 4.5 -39t15 -23t28.5 -11t45 -3q20 0 40.5 1t40.5 4v-146q-52 -9 -107 -9z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="836" 
d="M153 419h167v214h-288v169h772v-169h-288v-214h168v-140h-168v-279h-196v279h-167v140z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="508" 
d="M22 351h81v79h-82v108l82 34v145h187v-145h172v-142h-172v-79h130v-131h-130v-3q0 -24 4.5 -39t15 -23t28.5 -11t45 -3q20 0 40.5 1t40.5 4v-146q-52 -9 -107 -9q-79 0 -128.5 14t-77.5 41t-38 66.5t-10 91.5v16h-81v131z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="966" 
d="M257 863q0 63 32 93.5t93 30.5q32 0 58.5 -6t49 -13t41.5 -13t35 -6q17 0 23 6.5t6 20.5v6h117v-18q0 -63 -32 -93.5t-93 -30.5q-32 0 -58.5 6t-49 13.5t-41 13.5t-35.5 6t-23 -6.5t-6 -22.5v-6h-117v19zM483 -16q-111 0 -188 18.5t-125 61.5t-69 113.5t-21 175.5v449
h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8t102.5 8t63.5 30t32 60.5t9 98.5v449h196v-449q0 -105 -21.5 -175.5t-69 -113.5t-124.5 -61.5t-188 -18.5z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="816" 
d="M181 660q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24zM348 -16q-139 0 -204.5 61.5
t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5v384h186v-572h-127l-29 41q-56 -26 -110 -41.5t-121 -15.5z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="966" 
d="M272 974h422v-116h-422v116zM483 -16q-111 0 -188 18.5t-125 61.5t-69 113.5t-21 175.5v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8t102.5 8t63.5 30t32 60.5t9 98.5v449h196v-449q0 -105 -21.5 -175.5t-69 -113.5t-124.5 -61.5t-188 -18.5z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="816" 
d="M201 780h414v-124h-414v124zM348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5v384h186v-572h-127l-29 41q-56 -26 -110 -41.5t-121 -15.5z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="966" 
d="M484 842q-69 0 -115 8t-73.5 25.5t-39 45t-11.5 66.5h140q0 -22 20 -32t79 -10t79 10t20 32h141q0 -39 -11.5 -66.5t-39 -45t-74 -25.5t-115.5 -8zM483 -16q-111 0 -188 18.5t-125 61.5t-69 113.5t-21 175.5v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8
t102.5 8t63.5 30t32 60.5t9 98.5v449h196v-449q0 -105 -21.5 -175.5t-69 -113.5t-124.5 -61.5t-188 -18.5z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="816" 
d="M408 632q-64 0 -106.5 9t-68 29t-36 51t-10.5 76h140q0 -36 16 -49t65 -13t65.5 13t16.5 49h140q0 -45 -10.5 -76t-36 -51t-68 -29t-107.5 -9zM348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5v384h186v-572h-127
l-29 41q-56 -26 -110 -41.5t-121 -15.5z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="966" 
d="M483 -16q-111 0 -188 18.5t-125 61.5t-69 113.5t-21 175.5v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8t102.5 8t63.5 30t32 60.5t9 98.5v449h196v-449q0 -105 -21.5 -175.5t-69 -113.5t-124.5 -61.5t-188 -18.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="816" 
d="M407 617q-86 0 -123.5 27t-37.5 93q0 65 37.5 92t123.5 27q87 0 124.5 -27t37.5 -92q0 -66 -38 -93t-124 -27zM407 688q41 0 56.5 10t15.5 39t-15.5 38.5t-56.5 9.5q-40 0 -55 -9.5t-15 -38.5t15 -39t55 -10zM348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338
q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5v384h186v-572h-127l-29 41q-56 -26 -110 -41.5t-121 -15.5z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="966" 
d="M357 987h191l-121 -140h-165zM632 987h191l-120 -140h-166zM483 -16q-111 0 -188 18.5t-125 61.5t-69 113.5t-21 175.5v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8t102.5 8t63.5 30t32 60.5t9 98.5v449h196v-449q0 -105 -21.5 -175.5t-69 -113.5
t-124.5 -61.5t-188 -18.5z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="816" 
d="M301 802h190l-140 -170h-157zM553 802h190l-141 -170h-157zM348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5v384h186v-572h-127l-29 41q-56 -26 -110 -41.5t-121 -15.5z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="966" 
d="M486 -73q0 -17 13 -23.5t46 -6.5q20 0 39.5 2.5t39.5 4.5v-106q-26 -5 -52.5 -8.5t-58.5 -3.5q-89 0 -124.5 24.5t-35.5 75.5q0 32 11.5 54.5t43.5 45.5q-90 6 -152.5 28.5t-101.5 66t-56.5 110.5t-17.5 162v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8
t102.5 8t63.5 30t32 60.5t9 98.5v449h196v-449q0 -97 -18 -164.5t-58.5 -111t-104.5 -65t-157 -26.5q-20 -10 -32.5 -17t-19 -14t-8.5 -13.5t-2 -14.5z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="816" 
d="M348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5v384h186v-572h-31q-33 -15 -53 -25t-30.5 -18t-13.5 -15.5t-3 -17.5q0 -17 13 -23.5t46 -6.5q20 0 39.5 2.5t39.5 4.5v-106q-26 -5 -52.5 -8.5t-58.5 -3.5
q-89 0 -124.5 24.5t-35.5 75.5q0 22 5.5 40.5t19.5 34.5t37.5 31t59.5 32l-14 20q-56 -26 -110 -41.5t-121 -15.5z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="1398" 
d="M576 987h230l133 -140h-166l-82 73l-83 -73h-166zM27 802h208l151 -638h16l131 638h331l130 -638h17l151 638h209l-197 -802h-328l-138 637h-18l-138 -637h-329z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="1132" 
d="M463 802h198l131 -170h-165l-65 88l-65 -88h-165zM25 572h197l89 -420h9l109 420h275l107 -420l9 1l85 419h202l-138 -572h-283l-115 419h-10l-115 -419h-283z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="891" 
d="M332 987h230l133 -140h-166l-82 73l-83 -73h-166zM348 311l-336 491h224l213 -313l213 313h217l-336 -492v-310h-195v311z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="747" 
d="M275 802h198l131 -170h-165l-65 88l-65 -88h-165zM12 572h198l153 -407h24l151 407h197l-230 -594q-20 -53 -43.5 -89.5t-56 -59t-77.5 -33t-107 -10.5q-38 0 -64.5 1t-61.5 5v148q21 -2 41.5 -3t45.5 -1q38 0 62 3t39.5 9t24 15.5t13.5 22.5l10 27h-103z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="891" 
d="M249 978h150v-122h-150v122zM493 978h150v-122h-150v122zM348 311l-336 491h224l213 -313l213 313h217l-336 -492v-310h-195v311z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="823" 
d="M440 987h206l-144 -140h-181zM40 168l467 465h-447v169h704v-169l-465 -464h484v-169h-743v168z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="696" 
d="M418 800h206l-180 -170h-173zM48 138l329 292h-315v142h571v-137l-331 -293h345v-142h-599v138z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="823" 
d="M328 978h190v-122h-190v122zM40 168l467 465h-447v169h704v-169l-465 -464h484v-169h-743v168z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="696" 
d="M261 791h190v-146h-190v146zM48 138l329 292h-315v142h571v-137l-331 -293h345v-142h-599v138z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="823" 
d="M175 987h166l83 -74l82 74h166l-133 -140h-230zM40 168l467 465h-447v169h704v-169l-465 -464h484v-169h-743v168z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="696" 
d="M126 802h165l65 -88l65 88h165l-131 -170h-198zM48 138l329 292h-315v142h571v-137l-331 -293h345v-142h-599v138z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="466" 
d="M109 581q0 58 12.5 102t42 74t78 45.5t121.5 15.5q33 0 57.5 -2t53.5 -5v-147q-21 2 -42.5 3t-42.5 1q-27 0 -45 -3.5t-28.5 -12t-15 -23.5t-4.5 -38v-591h-187v581z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="752" 
d="M62 477h206v81q0 63 13.5 111.5t46 81.5t86 50t133.5 17q38 0 66 -1.5t61 -5.5v-150q-26 3 -50.5 3.5t-49.5 0.5q-34 0 -56 -4.5t-35.5 -15.5t-19 -29.5t-5.5 -47.5v-91h216v-147h-216v-284q0 -63 -13.5 -111.5t-45 -81.5t-84 -50t-130.5 -17q-36 0 -63 1.5t-60 5.5v150
q25 -3 49 -3.5t49 -0.5q33 0 54 4.5t33 15.5t16.5 30t4.5 47v294h-206v147z" />
    <glyph glyph-name="Ohorn" unicode="&#x1a0;" horiz-adv-x="969" 
d="M485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25q97 0 167 -16q13 -1 26 -1h20q45 0 65.5 27.5t20.5 71.5v8h150q0 -69 -27.5 -118.5t-87.5 -72.5q53 -52 76.5 -130t23.5 -186q0 -107 -23.5 -185.5t-75.5 -130
t-134.5 -76.5t-200.5 -25zM485 155q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14z" />
    <glyph glyph-name="ohorn" unicode="&#x1a1;" horiz-adv-x="774" 
d="M387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t150 -16q45 0 65 27.5t20 70.5v9h151q0 -66 -25 -114.5t-80 -72.5q57 -72 57 -206q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5zM387 131
q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5z" />
    <glyph glyph-name="Uhorn" unicode="&#x1af;" horiz-adv-x="966" 
d="M483 -16q-111 0 -188 18.5t-125 61.5t-69 113.5t-21 175.5v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8t102.5 8t63.5 30t32 60.5t9 98.5v449h122q29 9 42.5 35t13.5 64v6h151q0 -75 -32 -127.5t-101 -72.5v-354q0 -105 -21.5 -175.5t-69 -113.5
t-124.5 -61.5t-188 -18.5z" />
    <glyph glyph-name="uhorn" unicode="&#x1b0;" horiz-adv-x="816" 
d="M348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5v384h92q34 7 49 33.5t15 68.5v5h151q0 -72 -29 -123t-92 -73v-483h-127l-29 41q-56 -26 -110 -41.5t-121 -15.5z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="943" 
d="M489 987h206l-144 -140h-181zM315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="758" 
d="M441 1069h206l-180 -170h-173zM378 617q-86 0 -123.5 27t-37.5 93q0 65 37.5 92t123.5 27q87 0 124.5 -27t37.5 -92q0 -66 -38 -93t-124 -27zM378 688q41 0 56.5 10t15.5 39t-15.5 38.5t-56.5 9.5q-40 0 -55 -9.5t-15 -38.5t15 -39t55 -10zM319 -16q-60 0 -108 11
t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91v-379h-126l-25 35
q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="1373" 
d="M777 987h206l-144 -140h-181zM651 181h-322l-106 -181h-229l475 802h843v-167h-465v-147h427v-167h-427v-154h465v-167h-661v181zM651 350v285h-57l-166 -285h223z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="1174" 
d="M660 782h206l-180 -170h-173zM320 -16q-59 0 -107.5 11t-83 34.5t-53.5 61t-19 89.5q0 45 12 78.5t39.5 55t71.5 32t108 10.5h206v16q0 18 -5.5 31.5t-22.5 21.5t-48 12t-81 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 62.5 9.5t53 5.5t57 3t74.5 1q98 0 159 -22.5t93 -60.5
q36 42 93 63t139 21q69 0 124 -15t93.5 -47.5t59 -84.5t20.5 -127v-89h-442q3 -29 12.5 -47.5t28 -29t48.5 -14.5t74 -4q32 0 61 1t59 3t63 5.5t72 9.5v-139q-67 -17 -131.5 -22t-134.5 -5q-91 0 -155.5 16t-106.5 52q-50 -31 -113 -49.5t-142 -18.5zM818 449
q-65 0 -95 -21.5t-38 -78.5h264q-8 57 -39 78.5t-92 21.5zM334 112q105 0 176 45q-5 17 -8.5 36t-5.5 40h-178q-44 0 -60 -13.5t-16 -45.5q0 -35 19 -48.5t73 -13.5z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="969" 
d="M502 987h206l-144 -140h-181zM42 83l63 60q-29 49 -42 113t-13 145q0 107 23.5 185.5t75.5 130t135 76.5t201 25q176 0 276 -57l63 57l99 -99l-61 -57q30 -50 43.5 -114.5t13.5 -146.5q0 -107 -23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25q-177 0 -278 58l-62 -58z
M248 401q0 -33 2.5 -61t8.5 -52l358 339q-49 20 -132 20q-69 0 -115 -14t-73 -43.5t-38 -76t-11 -112.5zM485 155q69 0 114.5 14t72.5 43.5t38 76t11 112.5q0 33 -2.5 61.5t-8.5 51.5l-358 -339q47 -20 133 -20z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="774" 
d="M449 800h206l-180 -170h-173zM46 90l34 29q-32 66 -32 167q0 83 20.5 140.5t62.5 93.5t106 52t150 16q63 0 114 -8.5t90 -27.5l44 36l93 -106l-35 -29q32 -67 32 -167q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5q-63 0 -114 8.5t-91 27.5l-43 -36z
M241 286q0 -12 0.5 -23t1.5 -21l221 188q-29 11 -77 11q-41 0 -69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5zM387 131q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 12 -0.5 23t-1.5 21l-221 -188q28 -11 77 -11z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="864" 
d="M82 199q81 -18 169.5 -32.5t173.5 -14.5q55 0 89.5 3.5t54 12.5t26.5 24t7 37q0 23 -6 37.5t-24.5 24t-53.5 15.5t-92 12q-99 8 -166 23t-108.5 43t-59 72.5t-17.5 111.5q0 62 19 108.5t63 78t115.5 47.5t175.5 16q80 0 155 -8.5t157 -27.5v-170q-88 19 -161.5 28.5
t-148.5 9.5q-55 0 -89.5 -3t-54 -11t-26 -22t-6.5 -36q0 -21 6 -34t25 -21t53.5 -13.5t92.5 -11.5q99 -9 166.5 -24.5t108.5 -44.5t58.5 -74.5t17.5 -113.5q0 -64 -20 -112.5t-64.5 -80.5t-116 -48t-174.5 -16q-89 0 -179.5 12.5t-165.5 32.5v170zM353 -53h207l-177 -145
h-173z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="712" 
d="M66 155q68 -14 144.5 -24t149.5 -10q37 0 60.5 1.5t37.5 6t19.5 12t5.5 19.5q0 13 -4.5 21t-18.5 13.5t-39.5 9.5t-67.5 7q-83 6 -138.5 17.5t-89 33t-47.5 55t-14 83.5q0 47 14.5 82t48.5 58.5t91 35.5t142 12q67 0 134.5 -6t137.5 -21v-138q-74 14 -136.5 21t-127.5 7
q-38 0 -62.5 -2t-38 -6.5t-18.5 -12t-5 -18.5q0 -12 4.5 -19.5t18.5 -12.5t39 -8.5t67 -7.5q84 -6 139.5 -17.5t89.5 -33t48 -56t14 -85.5q0 -47 -15 -82.5t-49.5 -59t-91.5 -35t-141 -11.5q-37 0 -77 2t-79 6.5t-76 10.5t-69 14v138zM270 -53h207l-177 -145h-173z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="836" 
d="M320 633h-288v169h772v-169h-288v-633h-196v633zM321 -53h207l-177 -145h-173z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="508" 
d="M357 -9q-79 0 -128.5 14t-77.5 41t-38 66.5t-10 91.5v226h-82v108l82 34v145h187v-145h172v-142h-172v-213q0 -24 4.5 -39t15 -23t28.5 -11t45 -3q20 0 40.5 1t40.5 4v-146q-52 -9 -107 -9zM229 -53h207l-177 -145h-173z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="349" 
d="M-52 -67q14 -2 31 -3t31 -1q39 0 55 16.5t16 60.5v566h187v-557q0 -59 -10 -102t-35.5 -71t-69 -42t-110.5 -14q-28 0 -49 1t-46 5v141z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="600" 
d="M201 802h198l131 -170h-165l-65 88l-65 -88h-165z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="600" 
d="M70 802h165l65 -88l65 88h165l-131 -170h-198z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="600" 
d="M300 632q-64 0 -106.5 9t-68 29t-36 51t-10.5 76h140q0 -36 16 -49t65 -13t65.5 13t16.5 49h140q0 -45 -10.5 -76t-36 -51t-68 -29t-107.5 -9z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="600" 
d="M205 791h190v-146h-190v146z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="600" 
d="M300 617q-86 0 -123.5 27t-37.5 93q0 65 37.5 92t123.5 27q87 0 124.5 -27t37.5 -92q0 -66 -38 -93t-124 -27zM300 688q41 0 56.5 10t15.5 39t-15.5 38.5t-56.5 9.5q-40 0 -55 -9.5t-15 -38.5t15 -39t55 -10z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="600" 
d="M297 -73q0 -17 13 -23.5t46 -6.5q20 0 39.5 2.5t39.5 4.5v-106q-26 -5 -52.5 -8.5t-58.5 -3.5q-89 0 -124.5 24.5t-35.5 75.5q0 22 6 40.5t20 34.5t39 32t62 34l124 -16q-39 -17 -62.5 -28.5t-36 -20.5t-16 -17t-3.5 -18z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="600" 
d="M72 660q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="600" 
d="M132 802h190l-140 -170h-157zM384 802h190l-141 -170h-157z" />
    <glyph glyph-name="gravecomb" unicode="&#x300;" horiz-adv-x="0" 
d="M-268 802h206l147 -170h-173z" />
    <glyph glyph-name="acutecomb" unicode="&#x301;" horiz-adv-x="0" 
d="M62 802h206l-180 -170h-173z" />
    <glyph glyph-name="uni0302" unicode="&#x302;" horiz-adv-x="0" 
d="M-99 802h198l131 -170h-165l-65 88l-65 -88h-165z" />
    <glyph glyph-name="tildecomb" unicode="&#x303;" horiz-adv-x="0" 
d="M-227 660q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="uni0304" unicode="&#x304;" horiz-adv-x="0" 
d="M-207 780h414v-124h-414v124z" />
    <glyph glyph-name="uni0306" unicode="&#x306;" horiz-adv-x="0" 
d="M0 632q-64 0 -106.5 9t-68 29t-36 51t-10.5 76h140q0 -36 16 -49t65 -13t65.5 13t16.5 49h140q0 -45 -10.5 -76t-36 -51t-68 -29t-107.5 -9z" />
    <glyph glyph-name="uni0307" unicode="&#x307;" horiz-adv-x="0" 
d="M-95 791h190v-146h-190v146z" />
    <glyph glyph-name="uni0308" unicode="&#x308;" horiz-adv-x="0" 
d="M-183 791h142v-146h-142v146zM41 791h142v-146h-142v146z" />
    <glyph glyph-name="uni030A" unicode="&#x30a;" horiz-adv-x="0" 
d="M-1 617q-86 0 -123.5 27t-37.5 93q0 65 37.5 92t123.5 27q87 0 124.5 -27t37.5 -92q0 -66 -38 -93t-124 -27zM-1 688q41 0 56.5 10t15.5 39t-15.5 38.5t-56.5 9.5q-40 0 -55 -9.5t-15 -38.5t15 -39t55 -10z" />
    <glyph glyph-name="uni030B" unicode="&#x30b;" horiz-adv-x="0" 
d="M-107 802h190l-140 -170h-157zM145 802h190l-141 -170h-157z" />
    <glyph glyph-name="uni030C" unicode="&#x30c;" horiz-adv-x="0" 
d="M-230 802h165l65 -88l65 88h165l-131 -170h-198z" />
    <glyph glyph-name="uni0312" unicode="&#x312;" horiz-adv-x="0" 
d="M103 632h-206l180 170h173z" />
    <glyph glyph-name="uni0326" unicode="&#x326;" horiz-adv-x="0" 
d="M-98 -53h207l-177 -145h-173z" />
    <glyph glyph-name="uni0327" unicode="&#x327;" horiz-adv-x="0" 
d="M-43 3h86l-49 -45q67 0 98 -18.5t31 -63.5q0 -51 -35 -70.5t-113 -19.5q-24 0 -47 1t-48 6v65q28 -3 48 -4t43 -1q39 0 50.5 4.5t11.5 18.5t-11.5 19t-50.5 5q-18 0 -34 -1.5t-37 -3.5v57z" />
    <glyph glyph-name="uni0328" unicode="&#x328;" horiz-adv-x="0" 
d="M-47 -73q0 -17 13 -23.5t46 -6.5q20 0 39.5 2.5t39.5 4.5v-106q-26 -5 -52.5 -8.5t-58.5 -3.5q-89 0 -124.5 24.5t-35.5 75.5q0 22 6 40.5t20 34.5t38.5 32t62.5 34l124 -16q-39 -17 -62.5 -28.5t-36 -20.5t-16 -17t-3.5 -18z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" 
d="M30 149h99q-50 44 -73.5 114t-23.5 170q0 99 20 171.5t62 120t107.5 70.5t157.5 23q93 0 159 -23t108 -70.5t62 -120t20 -171.5q0 -100 -23.5 -170t-73.5 -114h99v-149h-314v133q36 9 59 31.5t37 57t19.5 79.5t5.5 100q0 75 -7 125t-25 80t-48.5 42.5t-77.5 12.5
q-46 0 -76 -12.5t-48 -42.5t-25 -80t-7 -125q0 -55 5.5 -100t19.5 -79.5t37 -57t59 -31.5v-133h-314v149z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" 
d="M92 403h-72v169h720v-169h-72v-403h-195v403h-186v-403h-195v403z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="1398" 
d="M467 987h206l119 -140h-181zM27 802h208l151 -638h16l131 638h331l130 -638h17l151 638h209l-197 -802h-328l-138 637h-18l-138 -637h-329z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="1132" 
d="M294 802h206l147 -170h-173zM25 572h197l89 -420h9l109 420h275l107 -420l9 1l85 419h202l-138 -572h-283l-115 419h-10l-115 -419h-283z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="1398" 
d="M707 987h206l-144 -140h-181zM27 802h208l151 -638h16l131 638h331l130 -638h17l151 638h209l-197 -802h-328l-138 637h-18l-138 -637h-329z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="1132" 
d="M624 800h206l-180 -170h-173zM25 572h197l89 -420h9l109 420h275l107 -420l9 1l85 419h202l-138 -572h-283l-115 419h-10l-115 -419h-283z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="1398" 
d="M493 978h150v-122h-150v122zM737 978h150v-122h-150v122zM27 802h208l151 -638h16l131 638h331l130 -638h17l151 638h209l-197 -802h-328l-138 637h-18l-138 -637h-329z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="1132" 
d="M379 791h142v-146h-142v146zM603 791h142v-146h-142v146zM25 572h197l89 -420h9l109 420h275l107 -420l9 1l85 419h202l-138 -572h-283l-115 419h-10l-115 -419h-283z" />
    <glyph glyph-name="uni1EA0" unicode="&#x1ea0;" horiz-adv-x="943" 
d="M315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240zM382 -76h178v-150h-178v150z" />
    <glyph glyph-name="uni1EA1" unicode="&#x1ea1;" horiz-adv-x="758" 
d="M319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91
v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4zM290 -76h178v-150h-178v150z" />
    <glyph glyph-name="uni1EA2" unicode="&#x1ea2;" horiz-adv-x="943" 
d="M412 864q7 18 17.5 29.5t20.5 20.5t17 18t7 22q0 15 -10.5 24t-27.5 9q-18 0 -33 -12.5t-23 -24.5l-60 32q23 37 59 60.5t93 23.5q26 0 48 -7.5t38.5 -20t26 -29t9.5 -34.5q0 -23 -10 -38.5t-24.5 -28.5t-30.5 -26.5t-29 -33.5zM315 802h313l296 -802h-208l-64 181h-361
l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240z" />
    <glyph glyph-name="uni1EA3" unicode="&#x1ea3;" horiz-adv-x="758" 
d="M336 708q32 1 49 8.5t17 20.5q0 16 -24 16q-11 0 -24.5 -1.5t-36.5 -20.5l-63 54q52 51 127 51h6q54 -1 88 -26.5t34 -68.5v-3q-1 -40 -25 -62t-57 -32l-2 -24h-83zM319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206
v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5
q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4z" />
    <glyph glyph-name="uni1EA4" unicode="&#x1ea4;" horiz-adv-x="943" 
d="M358 987h230l133 -140h-166l-82 73l-83 -73h-166zM315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240zM534 1185h206l-180 -170h-173z" />
    <glyph glyph-name="uni1EA5" unicode="&#x1ea5;" horiz-adv-x="758" 
d="M280 802h198l131 -170h-165l-65 88l-65 -88h-165zM319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3
t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4zM441 1000h206l-180 -170h-173z" />
    <glyph glyph-name="uni1EA6" unicode="&#x1ea6;" horiz-adv-x="943" 
d="M358 987h230l133 -140h-166l-82 73l-83 -73h-166zM315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240zM204 1187h206l147 -170h-173z" />
    <glyph glyph-name="uni1EA7" unicode="&#x1ea7;" horiz-adv-x="758" 
d="M280 802h198l131 -170h-165l-65 88l-65 -88h-165zM319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3
t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4zM111 1002h206l147 -170h-173z" />
    <glyph glyph-name="uni1EA8" unicode="&#x1ea8;" horiz-adv-x="943" 
d="M646 966q6 15 14.5 25t17 18t14.5 15.5t6 17.5q0 13 -9 21t-23 8q-15 0 -27.5 -10.5t-19.5 -21.5l-51 27q19 32 49.5 52t79.5 20q44 0 73.5 -23t29.5 -55q0 -19 -8.5 -32t-20.5 -24.5t-25.5 -23t-24.5 -28.5zM358 987h230l133 -140h-166l-82 73l-83 -73h-166zM315 802
h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240z" />
    <glyph glyph-name="uni1EA9" unicode="&#x1ea9;" horiz-adv-x="758" 
d="M562 760q6 15 14.5 25t17 18t14.5 15.5t6 17.5q0 13 -9 21t-23 8q-15 0 -27.5 -10.5t-19.5 -21.5l-51 27q19 32 49.5 52t79.5 20q44 0 73.5 -23t29.5 -55q0 -19 -8.5 -32t-20.5 -24.5t-25.5 -23t-24.5 -28.5zM280 802h198l131 -170h-165l-65 88l-65 -88h-165zM319 -16
q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91v-379h-126
l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4z" />
    <glyph glyph-name="uni1EAA" unicode="&#x1eaa;" horiz-adv-x="943" 
d="M358 987h230l133 -140h-166l-82 73l-83 -73h-166zM315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240zM245 1045q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24
q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="uni1EAB" unicode="&#x1eab;" horiz-adv-x="758" 
d="M280 802h198l131 -170h-165l-65 88l-65 -88h-165zM319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3
t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4zM152 860q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5
t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="uni1EAC" unicode="&#x1eac;" horiz-adv-x="943" 
d="M315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240zM382 -76h178v-150h-178v150zM373 1032h198l131 -170h-165l-65 88l-65 -88h-165z" />
    <glyph glyph-name="uni1EAD" unicode="&#x1ead;" horiz-adv-x="758" 
d="M319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91
v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4zM290 -76h178v-150h-178v150zM280 802h198l131 -170h-165l-65 88l-65 -88h-165z" />
    <glyph glyph-name="uni1EAE" unicode="&#x1eae;" horiz-adv-x="943" 
d="M473 842q-69 0 -115 8t-73.5 25.5t-39 45t-11.5 66.5h140q0 -22 20 -32t79 -10t79 10t20 32h141q0 -39 -11.5 -66.5t-39 -45t-74 -25.5t-115.5 -8zM315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240zM534 1185h206l-180 -170
h-173z" />
    <glyph glyph-name="uni1EAF" unicode="&#x1eaf;" horiz-adv-x="758" 
d="M379 632q-64 0 -106.5 9t-68 29t-36 51t-10.5 76h140q0 -36 16 -49t65 -13t65.5 13t16.5 49h140q0 -45 -10.5 -76t-36 -51t-68 -29t-107.5 -9zM319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5
t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5
t27.5 -11.5t44.5 -4zM441 995h206l-180 -170h-173z" />
    <glyph glyph-name="uni1EB0" unicode="&#x1eb0;" horiz-adv-x="943" 
d="M473 842q-69 0 -115 8t-73.5 25.5t-39 45t-11.5 66.5h140q0 -22 20 -32t79 -10t79 10t20 32h141q0 -39 -11.5 -66.5t-39 -45t-74 -25.5t-115.5 -8zM315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240zM204 1187h206l147 -170
h-173z" />
    <glyph glyph-name="uni1EB1" unicode="&#x1eb1;" horiz-adv-x="758" 
d="M379 632q-64 0 -106.5 9t-68 29t-36 51t-10.5 76h140q0 -36 16 -49t65 -13t65.5 13t16.5 49h140q0 -45 -10.5 -76t-36 -51t-68 -29t-107.5 -9zM319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5
t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5
t27.5 -11.5t44.5 -4zM111 997h206l147 -170h-173z" />
    <glyph glyph-name="uni1EB2" unicode="&#x1eb2;" horiz-adv-x="943" 
d="M421 1025q7 18 17.5 29.5t20.5 20.5t17 18t7 22q0 15 -10.5 24t-27.5 9q-18 0 -33 -12.5t-23 -24.5l-60 32q23 37 59 60.5t93 23.5q26 0 48 -7.5t38.5 -20t26 -29t9.5 -34.5q0 -23 -10 -38.5t-24.5 -28.5t-30.5 -26.5t-29 -33.5zM473 842q-69 0 -115 8t-73.5 25.5t-39 45
t-11.5 66.5h140q0 -22 20 -32t79 -10t79 10t20 32h141q0 -39 -11.5 -66.5t-39 -45t-74 -25.5t-115.5 -8zM315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240z" />
    <glyph glyph-name="uni1EB3" unicode="&#x1eb3;" horiz-adv-x="758" 
d="M332 899q32 1 49 8t17 21q0 16 -24 16q-11 0 -24.5 -1.5t-36.5 -20.5l-63 54q52 51 127 51h6q54 -1 88 -26.5t34 -68.5v-3q-1 -40 -25 -62.5t-57 -31.5l-2 -24h-83zM379 632q-64 0 -106.5 9t-68 29t-36 51t-10.5 76h140q0 -36 16 -49t65 -13t65.5 13t16.5 49h140
q0 -45 -10.5 -76t-36 -51t-68 -29t-107.5 -9zM319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1
q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4z" />
    <glyph glyph-name="uni1EB4" unicode="&#x1eb4;" horiz-adv-x="943" 
d="M473 842q-69 0 -115 8t-73.5 25.5t-39 45t-11.5 66.5h140q0 -22 20 -32t79 -10t79 10t20 32h141q0 -39 -11.5 -66.5t-39 -45t-74 -25.5t-115.5 -8zM315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240zM245 1045q0 72 32 107
t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="uni1EB5" unicode="&#x1eb5;" horiz-adv-x="758" 
d="M379 632q-64 0 -106.5 9t-68 29t-36 51t-10.5 76h140q0 -36 16 -49t65 -13t65.5 13t16.5 49h140q0 -45 -10.5 -76t-36 -51t-68 -29t-107.5 -9zM319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5
t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5
t27.5 -11.5t44.5 -4zM152 855q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="uni1EB6" unicode="&#x1eb6;" horiz-adv-x="943" 
d="M315 802h313l296 -802h-208l-64 181h-361l-64 -181h-209zM591 350l-104 292h-32l-104 -292h240zM382 -76h178v-150h-178v150zM472 862q-64 0 -106.5 9t-68 29t-36 51t-10.5 76h140q0 -36 16 -49t65 -13t65.5 13t16.5 49h140q0 -45 -10.5 -76t-36 -51t-68 -29t-107.5 -9z
" />
    <glyph glyph-name="uni1EB7" unicode="&#x1eb7;" horiz-adv-x="758" 
d="M319 -16q-60 0 -108 11t-82.5 35t-53 61.5t-18.5 90.5q0 46 12 79.5t39.5 55.5t71.5 32.5t108 10.5h206v12q0 18 -5.5 31.5t-22.5 21.5t-47.5 12t-81.5 4q-58 0 -116.5 -5t-121.5 -13v146q36 6 63.5 9.5t55.5 5.5t60 3t76 1q95 0 158 -13t100.5 -39.5t53 -65.5t15.5 -91
v-379h-126l-25 35q-41 -23 -93 -37t-118 -14zM332 112q96 0 162 34v91h-176q-44 0 -60 -14.5t-16 -47.5q0 -16 4 -28t14 -19.5t27.5 -11.5t44.5 -4zM290 -76h178v-150h-178v150zM379 632q-64 0 -106.5 9t-68 29t-36 51t-10.5 76h140q0 -36 16 -49t65 -13t65.5 13t16.5 49
h140q0 -45 -10.5 -76t-36 -51t-68 -29t-107.5 -9z" />
    <glyph glyph-name="uni1EB8" unicode="&#x1eb8;" horiz-adv-x="814" 
d="M92 802h662v-167h-467v-147h428v-167h-428v-154h467v-167h-662v802zM326 -76h178v-150h-178v150z" />
    <glyph glyph-name="uni1EB9" unicode="&#x1eb9;" horiz-adv-x="732" 
d="M395 -16q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5q69 0 125.5 -15t96.5 -47.5t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3t63 5.5t72 9.5v-139q-67 -17 -131.5 -22t-134.5 -5z
M376 449q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21z" />
    <glyph glyph-name="uni1EBA" unicode="&#x1eba;" horiz-adv-x="814" 
d="M364 877q7 18 17.5 29.5t20.5 20.5t17 18t7 22q0 15 -10.5 24t-27.5 9q-18 0 -33 -12.5t-23 -24.5l-60 32q23 37 59 60.5t93 23.5q26 0 48 -7.5t38.5 -20t26 -29t9.5 -34.5q0 -23 -10 -38.5t-24.5 -28.5t-30.5 -26.5t-29 -33.5zM92 802h662v-167h-467v-147h428v-167h-428
v-154h467v-167h-662v802z" />
    <glyph glyph-name="uni1EBB" unicode="&#x1ebb;" horiz-adv-x="732" 
d="M336 708q32 1 49 8t17 21q0 16 -24 16q-11 0 -24.5 -1.5t-36.5 -20.5l-63 54q52 51 127 51h6q54 -1 88 -26.5t34 -68.5v-3q-1 -40 -25 -62.5t-57 -31.5l-2 -24h-83zM395 -16q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5
q69 0 125.5 -15t96.5 -47.5t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3t63 5.5t72 9.5v-139q-67 -17 -131.5 -22t-134.5 -5zM376 449q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21z" />
    <glyph glyph-name="uni1EBC" unicode="&#x1ebc;" horiz-adv-x="814" 
d="M92 802h662v-167h-467v-147h428v-167h-428v-154h467v-167h-662v802zM199 890q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5
q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="uni1EBD" unicode="&#x1ebd;" horiz-adv-x="732" 
d="M395 -16q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5q69 0 125.5 -15t96.5 -47.5t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3t63 5.5t72 9.5v-139q-67 -17 -131.5 -22t-134.5 -5z
M376 449q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21zM150 660q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16
t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="uni1EBE" unicode="&#x1ebe;" horiz-adv-x="814" 
d="M312 987h230l133 -140h-166l-82 73l-83 -73h-166zM92 802h662v-167h-467v-147h428v-167h-428v-154h467v-167h-662v802zM488 1185h206l-180 -170h-173z" />
    <glyph glyph-name="uni1EBF" unicode="&#x1ebf;" horiz-adv-x="732" 
d="M278 802h198l131 -170h-165l-65 88l-65 -88h-165zM395 -16q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5q69 0 125.5 -15t96.5 -47.5t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3t63 5.5
t72 9.5v-139q-67 -17 -131.5 -22t-134.5 -5zM376 449q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21zM439 1000h206l-180 -170h-173z" />
    <glyph glyph-name="uni1EC0" unicode="&#x1ec0;" horiz-adv-x="814" 
d="M312 987h230l133 -140h-166l-82 73l-83 -73h-166zM92 802h662v-167h-467v-147h428v-167h-428v-154h467v-167h-662v802zM158 1187h206l147 -170h-173z" />
    <glyph glyph-name="uni1EC1" unicode="&#x1ec1;" horiz-adv-x="732" 
d="M278 802h198l131 -170h-165l-65 88l-65 -88h-165zM395 -16q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5q69 0 125.5 -15t96.5 -47.5t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3t63 5.5
t72 9.5v-139q-67 -17 -131.5 -22t-134.5 -5zM376 449q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21zM109 1002h206l147 -170h-173z" />
    <glyph glyph-name="uni1EC2" unicode="&#x1ec2;" horiz-adv-x="814" 
d="M612 966q6 15 14.5 25t17 18t14.5 15.5t6 17.5q0 13 -9 21t-23 8q-15 0 -27.5 -10.5t-19.5 -21.5l-51 27q19 32 49.5 52t79.5 20q44 0 73.5 -23t29.5 -55q0 -19 -8.5 -32t-20.5 -24.5t-25.5 -23t-24.5 -28.5zM312 987h230l133 -140h-166l-82 73l-83 -73h-166zM92 802h662
v-167h-467v-147h428v-167h-428v-154h467v-167h-662v802z" />
    <glyph glyph-name="uni1EC3" unicode="&#x1ec3;" horiz-adv-x="732" 
d="M548 760q6 15 14.5 25t17 18t14.5 15.5t6 17.5q0 13 -9 21t-23 8q-15 0 -27.5 -10.5t-19.5 -21.5l-51 27q19 32 49.5 52t79.5 20q44 0 73.5 -23t29.5 -55q0 -19 -8.5 -32t-20.5 -24.5t-25.5 -23t-24.5 -28.5zM278 802h198l131 -170h-165l-65 88l-65 -88h-165zM395 -16
q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5q69 0 125.5 -15t96.5 -47.5t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3t63 5.5t72 9.5v-139q-67 -17 -131.5 -22t-134.5 -5zM376 449
q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21z" />
    <glyph glyph-name="uni1EC4" unicode="&#x1ec4;" horiz-adv-x="814" 
d="M312 987h230l133 -140h-166l-82 73l-83 -73h-166zM92 802h662v-167h-467v-147h428v-167h-428v-154h467v-167h-662v802zM199 1045q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5
q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="uni1EC5" unicode="&#x1ec5;" horiz-adv-x="732" 
d="M278 802h198l131 -170h-165l-65 88l-65 -88h-165zM395 -16q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5q69 0 125.5 -15t96.5 -47.5t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3t63 5.5
t72 9.5v-139q-67 -17 -131.5 -22t-134.5 -5zM376 449q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21zM150 860q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5
q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="uni1EC6" unicode="&#x1ec6;" horiz-adv-x="814" 
d="M92 802h662v-167h-467v-147h428v-167h-428v-154h467v-167h-662v802zM326 -76h178v-150h-178v150zM327 1032h198l131 -170h-165l-65 88l-65 -88h-165z" />
    <glyph glyph-name="uni1EC7" unicode="&#x1ec7;" horiz-adv-x="732" 
d="M278 802h198l131 -170h-165l-65 88l-65 -88h-165zM395 -16q-90 0 -155.5 15.5t-108 51t-63 93t-20.5 140.5q0 86 21 144t63 94t104 51.5t144 15.5q69 0 125.5 -15t96.5 -47.5t62 -84.5t22 -127v-88h-443q3 -29 12.5 -48t28 -29.5t48 -14.5t74.5 -4q32 0 61 1t59 3t63 5.5
t72 9.5v-139q-67 -17 -131.5 -22t-134.5 -5zM376 449q-33 0 -56 -5t-39 -16.5t-24.5 -30.5t-12.5 -47h260q-6 57 -36 78t-92 21zM290 -76h178v-150h-178v150z" />
    <glyph glyph-name="uni1EC8" unicode="&#x1ec8;" horiz-adv-x="380" 
d="M128 864q7 18 17.5 29.5t20.5 20.5t17 18t7 22q0 15 -10.5 24t-27.5 9q-18 0 -33 -12.5t-23 -24.5l-60 32q23 37 59 60.5t93 23.5q26 0 48 -7.5t38.5 -20t26 -29t9.5 -34.5q0 -23 -10 -38.5t-24.5 -28.5t-30.5 -26.5t-29 -33.5zM92 802h195v-802h-195v802z" />
    <glyph glyph-name="uni1EC9" unicode="&#x1ec9;" horiz-adv-x="350" 
d="M121 736q32 1 49 8t17 21q0 16 -24 16q-11 0 -24.5 -1.5t-36.5 -20.5l-63 54q52 51 127 51h6q54 -1 88 -26.5t34 -68.5v-3q-1 -40 -25 -62.5t-57 -31.5l-2 -24h-83zM82 572h187v-572h-187v572z" />
    <glyph glyph-name="uni1ECA" unicode="&#x1eca;" horiz-adv-x="380" 
d="M92 802h195v-802h-195v802zM100 -76h178v-150h-178v150z" />
    <glyph glyph-name="uni1ECB" unicode="&#x1ecb;" horiz-adv-x="350" 
d="M86 802h178v-150h-178v150zM82 572h187v-572h-187v572zM86 -76h178v-150h-178v150z" />
    <glyph glyph-name="uni1ECC" unicode="&#x1ecc;" horiz-adv-x="969" 
d="M485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5t75.5 -130t23.5 -185.5t-23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76
t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14zM396 -76h178v-150h-178v150z" />
    <glyph glyph-name="uni1ECD" unicode="&#x1ecd;" horiz-adv-x="774" 
d="M387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t149.5 -16t105.5 -52t62.5 -93.5t20.5 -140.5q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5zM387 131q41 0 69 8.5t45 27t24.5 48t7.5 71.5
q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5zM290 -76h178v-150h-178v150z" />
    <glyph glyph-name="uni1ECE" unicode="&#x1ece;" horiz-adv-x="969" 
d="M440 864q7 18 17.5 29.5t20.5 20.5t17 18t7 22q0 15 -10.5 24t-27.5 9q-18 0 -33 -12.5t-23 -24.5l-60 32q23 37 59 60.5t93 23.5q26 0 48 -7.5t38.5 -20t26 -29t9.5 -34.5q0 -23 -10 -38.5t-24.5 -28.5t-30.5 -26.5t-29 -33.5zM485 -16q-118 0 -201 25t-135 76.5
t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5t75.5 -130t23.5 -185.5t-23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14
t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14z" />
    <glyph glyph-name="uni1ECF" unicode="&#x1ecf;" horiz-adv-x="774" 
d="M336 708q32 1 49 8t17 21q0 16 -24 16q-11 0 -24.5 -1.5t-36.5 -20.5l-63 54q52 51 127 51h6q54 -1 88 -26.5t34 -68.5v-3q-1 -40 -25 -62.5t-57 -31.5l-2 -24h-83zM387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16
t149.5 -16t105.5 -52t62.5 -93.5t20.5 -140.5q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5zM387 131q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27
t69 -8.5z" />
    <glyph glyph-name="uni1ED0" unicode="&#x1ed0;" horiz-adv-x="969" 
d="M371 987h230l133 -140h-166l-82 73l-83 -73h-166zM485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5t75.5 -130t23.5 -185.5t-23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155
q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14zM547 1185h206l-180 -170h-173z" />
    <glyph glyph-name="uni1ED1" unicode="&#x1ed1;" horiz-adv-x="774" 
d="M288 802h198l131 -170h-165l-65 88l-65 -88h-165zM387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t149.5 -16t105.5 -52t62.5 -93.5t20.5 -140.5q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5z
M387 131q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5zM449 1000h206l-180 -170h-173z" />
    <glyph glyph-name="uni1ED2" unicode="&#x1ed2;" horiz-adv-x="969" 
d="M371 987h230l133 -140h-166l-82 73l-83 -73h-166zM485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5t75.5 -130t23.5 -185.5t-23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155
q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14zM217 1187h206l147 -170h-173z" />
    <glyph glyph-name="uni1ED3" unicode="&#x1ed3;" horiz-adv-x="774" 
d="M288 802h198l131 -170h-165l-65 88l-65 -88h-165zM387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t149.5 -16t105.5 -52t62.5 -93.5t20.5 -140.5q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5z
M387 131q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5zM119 1002h206l147 -170h-173z" />
    <glyph glyph-name="uni1ED4" unicode="&#x1ed4;" horiz-adv-x="969" 
d="M646 966q6 15 14.5 25t17 18t14.5 15.5t6 17.5q0 13 -9 21t-23 8q-15 0 -27.5 -10.5t-19.5 -21.5l-51 27q19 32 49.5 52t79.5 20q44 0 73.5 -23t29.5 -55q0 -19 -8.5 -32t-20.5 -24.5t-25.5 -23t-24.5 -28.5zM371 987h230l133 -140h-166l-82 73l-83 -73h-166zM485 -16
q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5t75.5 -130t23.5 -185.5t-23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76t-72.5 43.5
t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14z" />
    <glyph glyph-name="uni1ED5" unicode="&#x1ed5;" horiz-adv-x="774" 
d="M552 760q6 15 14.5 25t17 18t14.5 15.5t6 17.5q0 13 -9 21t-23 8q-15 0 -27.5 -10.5t-19.5 -21.5l-51 27q19 32 49.5 52t79.5 20q44 0 73.5 -23t29.5 -55q0 -19 -8.5 -32t-20.5 -24.5t-25.5 -23t-24.5 -28.5zM288 802h198l131 -170h-165l-65 88l-65 -88h-165zM387 -16
q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t149.5 -16t105.5 -52t62.5 -93.5t20.5 -140.5q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5zM387 131q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 43 -7.5 72.5
t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5z" />
    <glyph glyph-name="uni1ED6" unicode="&#x1ed6;" horiz-adv-x="969" 
d="M371 987h230l133 -140h-166l-82 73l-83 -73h-166zM485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5t75.5 -130t23.5 -185.5t-23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155
q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14zM258 1045q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24
q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="uni1ED7" unicode="&#x1ed7;" horiz-adv-x="774" 
d="M288 802h198l131 -170h-165l-65 88l-65 -88h-165zM387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t149.5 -16t105.5 -52t62.5 -93.5t20.5 -140.5q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5z
M387 131q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5zM160 860q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116
v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="uni1ED8" unicode="&#x1ed8;" horiz-adv-x="969" 
d="M485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5t75.5 -130t23.5 -185.5t-23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76
t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14zM396 -76h178v-150h-178v150zM386 1032h198l131 -170h-165l-65 88l-65 -88h-165z" />
    <glyph glyph-name="uni1ED9" unicode="&#x1ed9;" horiz-adv-x="774" 
d="M387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t149.5 -16t105.5 -52t62.5 -93.5t20.5 -140.5q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5zM387 131q41 0 69 8.5t45 27t24.5 48t7.5 71.5
q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5zM290 -76h178v-150h-178v150zM288 802h198l131 -170h-165l-65 88l-65 -88h-165z" />
    <glyph glyph-name="uni1EDA" unicode="&#x1eda;" horiz-adv-x="969" 
d="M485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25q97 0 167 -16q13 -1 26 -1h20q45 0 65.5 27.5t20.5 71.5v8h150q0 -69 -27.5 -118.5t-87.5 -72.5q53 -52 76.5 -130t23.5 -186q0 -107 -23.5 -185.5t-75.5 -130
t-134.5 -76.5t-200.5 -25zM485 155q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14zM547 1030h206l-180 -170h-173z" />
    <glyph glyph-name="uni1EDB" unicode="&#x1edb;" horiz-adv-x="774" 
d="M387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t150 -16q45 0 65 27.5t20 70.5v9h151q0 -66 -25 -114.5t-80 -72.5q57 -72 57 -206q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5zM387 131
q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5zM449 800h206l-180 -170h-173z" />
    <glyph glyph-name="uni1EDC" unicode="&#x1edc;" horiz-adv-x="969" 
d="M485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25q97 0 167 -16q13 -1 26 -1h20q45 0 65.5 27.5t20.5 71.5v8h150q0 -69 -27.5 -118.5t-87.5 -72.5q53 -52 76.5 -130t23.5 -186q0 -107 -23.5 -185.5t-75.5 -130
t-134.5 -76.5t-200.5 -25zM485 155q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14zM217 1032h206l147 -170h-173z" />
    <glyph glyph-name="uni1EDD" unicode="&#x1edd;" horiz-adv-x="774" 
d="M387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t150 -16q45 0 65 27.5t20 70.5v9h151q0 -66 -25 -114.5t-80 -72.5q57 -72 57 -206q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5zM387 131
q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5zM119 802h206l147 -170h-173z" />
    <glyph glyph-name="uni1EDE" unicode="&#x1ede;" horiz-adv-x="969" 
d="M440 864q7 18 17.5 29.5t20.5 20.5t17 18t7 22q0 15 -10.5 24t-27.5 9q-18 0 -33 -12.5t-23 -24.5l-60 32q23 37 59 60.5t93 23.5q26 0 48 -7.5t38.5 -20t26 -29t9.5 -34.5q0 -23 -10 -38.5t-24.5 -28.5t-30.5 -26.5t-29 -33.5zM485 -16q-118 0 -201 25t-135 76.5
t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25q97 0 167 -16q13 -1 26 -1h20q45 0 65.5 27.5t20.5 71.5v8h150q0 -69 -27.5 -118.5t-87.5 -72.5q53 -52 76.5 -130t23.5 -186q0 -107 -23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155q69 0 114.5 14
t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14z" />
    <glyph glyph-name="uni1EDF" unicode="&#x1edf;" horiz-adv-x="774" 
d="M336 708q32 1 49 8t17 21q0 16 -24 16q-11 0 -24.5 -1.5t-36.5 -20.5l-63 54q52 51 127 51h6q54 -1 88 -26.5t34 -68.5v-3q-1 -40 -25 -62.5t-57 -31.5l-2 -24h-83zM387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16
t150 -16q45 0 65 27.5t20 70.5v9h151q0 -66 -25 -114.5t-80 -72.5q57 -72 57 -206q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5zM387 131q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48
t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5z" />
    <glyph glyph-name="uni1EE0" unicode="&#x1ee0;" horiz-adv-x="969" 
d="M485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25q97 0 167 -16q13 -1 26 -1h20q45 0 65.5 27.5t20.5 71.5v8h150q0 -69 -27.5 -118.5t-87.5 -72.5q53 -52 76.5 -130t23.5 -186q0 -107 -23.5 -185.5t-75.5 -130
t-134.5 -76.5t-200.5 -25zM485 155q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14zM258 890q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5
t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="uni1EE1" unicode="&#x1ee1;" horiz-adv-x="774" 
d="M387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t150 -16q45 0 65 27.5t20 70.5v9h151q0 -66 -25 -114.5t-80 -72.5q57 -72 57 -206q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5zM387 131
q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5zM160 660q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24
q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="uni1EE2" unicode="&#x1ee2;" horiz-adv-x="969" 
d="M485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5t75.5 130t135 76.5t201 25q97 0 167 -16q13 -1 26 -1h20q45 0 65.5 27.5t20.5 71.5v8h150q0 -69 -27.5 -118.5t-87.5 -72.5q53 -52 76.5 -130t23.5 -186q0 -107 -23.5 -185.5t-75.5 -130
t-134.5 -76.5t-200.5 -25zM485 155q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5t38 -76t73 -43.5t115 -14zM485 -16q-118 0 -201 25t-135 76.5t-75.5 130t-23.5 185.5t23.5 185.5
t75.5 130t135 76.5t201 25t200.5 -25t134.5 -76.5t75.5 -130t23.5 -185.5t-23.5 -185.5t-75.5 -130t-134.5 -76.5t-200.5 -25zM485 155q69 0 114.5 14t72.5 43.5t38 76t11 112.5t-11 112.5t-38 76t-72.5 43.5t-114.5 14t-115 -14t-73 -43.5t-38 -76t-11 -112.5t11 -112.5
t38 -76t73 -43.5t115 -14zM396 -76h178v-150h-178v150z" />
    <glyph glyph-name="uni1EE3" unicode="&#x1ee3;" horiz-adv-x="774" 
d="M387 -16q-86 0 -150 16.5t-106 52.5t-62.5 93.5t-20.5 139.5q0 83 20.5 140.5t62.5 93.5t106 52t150 16t150 -16q45 0 65 27.5t20 70.5v9h151q0 -66 -25 -114.5t-80 -72.5q57 -72 57 -206q0 -82 -20.5 -139.5t-62.5 -93.5t-105.5 -52.5t-149.5 -16.5zM387 131
q41 0 69 8.5t45 27t24.5 48t7.5 71.5q0 43 -7.5 72.5t-24.5 48t-45 26.5t-69 8t-69 -8.5t-45 -27t-24.5 -48t-7.5 -71.5t7.5 -71.5t24.5 -48t45 -27t69 -8.5zM290 -76h178v-150h-178v150z" />
    <glyph glyph-name="uni1EE4" unicode="&#x1ee4;" horiz-adv-x="966" 
d="M483 -16q-111 0 -188 18.5t-125 61.5t-69 113.5t-21 175.5v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8t102.5 8t63.5 30t32 60.5t9 98.5v449h196v-449q0 -105 -21.5 -175.5t-69 -113.5t-124.5 -61.5t-188 -18.5zM394 -76h178v-150h-178v150z" />
    <glyph glyph-name="uni1EE5" unicode="&#x1ee5;" horiz-adv-x="816" 
d="M348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5v384h186v-572h-127l-29 41q-56 -26 -110 -41.5t-121 -15.5zM319 -76h178v-150h-178v150z" />
    <glyph glyph-name="uni1EE6" unicode="&#x1ee6;" horiz-adv-x="966" 
d="M438 864q7 18 17.5 29.5t20.5 20.5t17 18t7 22q0 15 -10.5 24t-27.5 9q-18 0 -33 -12.5t-23 -24.5l-60 32q23 37 59 60.5t93 23.5q26 0 48 -7.5t38.5 -20t26 -29t9.5 -34.5q0 -23 -10 -38.5t-24.5 -28.5t-30.5 -26.5t-29 -33.5zM483 -16q-111 0 -188 18.5t-125 61.5
t-69 113.5t-21 175.5v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8t102.5 8t63.5 30t32 60.5t9 98.5v449h196v-449q0 -105 -21.5 -175.5t-69 -113.5t-124.5 -61.5t-188 -18.5z" />
    <glyph glyph-name="uni1EE7" unicode="&#x1ee7;" horiz-adv-x="816" 
d="M336 708q32 1 49 8t17 21q0 16 -24 16q-11 0 -24.5 -1.5t-36.5 -20.5l-63 54q52 51 127 51h6q54 -1 88 -26.5t34 -68.5v-3q-1 -40 -25 -62.5t-57 -31.5l-2 -24h-83zM348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5
v384h186v-572h-127l-29 41q-56 -26 -110 -41.5t-121 -15.5z" />
    <glyph glyph-name="uni1EE8" unicode="&#x1ee8;" horiz-adv-x="966" 
d="M483 -16q-111 0 -188 18.5t-125 61.5t-69 113.5t-21 175.5v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8t102.5 8t63.5 30t32 60.5t9 98.5v449h122q29 9 42.5 35t13.5 64v6h151q0 -75 -32 -127.5t-101 -72.5v-354q0 -105 -21.5 -175.5t-69 -113.5
t-124.5 -61.5t-188 -18.5zM545 1030h206l-180 -170h-173z" />
    <glyph glyph-name="uni1EE9" unicode="&#x1ee9;" horiz-adv-x="816" 
d="M348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5v384h92q34 7 49 33.5t15 68.5v5h151q0 -72 -29 -123t-92 -73v-483h-127l-29 41q-56 -26 -110 -41.5t-121 -15.5zM470 800h206l-180 -170h-173z" />
    <glyph glyph-name="uni1EEA" unicode="&#x1eea;" horiz-adv-x="966" 
d="M483 -16q-111 0 -188 18.5t-125 61.5t-69 113.5t-21 175.5v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8t102.5 8t63.5 30t32 60.5t9 98.5v449h122q29 9 42.5 35t13.5 64v6h151q0 -75 -32 -127.5t-101 -72.5v-354q0 -105 -21.5 -175.5t-69 -113.5
t-124.5 -61.5t-188 -18.5zM215 1032h206l147 -170h-173z" />
    <glyph glyph-name="uni1EEB" unicode="&#x1eeb;" horiz-adv-x="816" 
d="M348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5v384h92q34 7 49 33.5t15 68.5v5h151q0 -72 -29 -123t-92 -73v-483h-127l-29 41q-56 -26 -110 -41.5t-121 -15.5zM140 802h206l147 -170h-173z" />
    <glyph glyph-name="uni1EEC" unicode="&#x1eec;" horiz-adv-x="966" 
d="M438 864q7 18 17.5 29.5t20.5 20.5t17 18t7 22q0 15 -10.5 24t-27.5 9q-18 0 -33 -12.5t-23 -24.5l-60 32q23 37 59 60.5t93 23.5q26 0 48 -7.5t38.5 -20t26 -29t9.5 -34.5q0 -23 -10 -38.5t-24.5 -28.5t-30.5 -26.5t-29 -33.5zM483 -16q-111 0 -188 18.5t-125 61.5
t-69 113.5t-21 175.5v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8t102.5 8t63.5 30t32 60.5t9 98.5v449h122q29 9 42.5 35t13.5 64v6h151q0 -75 -32 -127.5t-101 -72.5v-354q0 -105 -21.5 -175.5t-69 -113.5t-124.5 -61.5t-188 -18.5z" />
    <glyph glyph-name="uni1EED" unicode="&#x1eed;" horiz-adv-x="816" 
d="M336 708q32 1 49 8t17 21q0 16 -24 16q-11 0 -24.5 -1.5t-36.5 -20.5l-63 54q52 51 127 51h6q54 -1 88 -26.5t34 -68.5v-3q-1 -40 -25 -62.5t-57 -31.5l-2 -24h-83zM348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5
v384h92q34 7 49 33.5t15 68.5v5h151q0 -72 -29 -123t-92 -73v-483h-127l-29 41q-56 -26 -110 -41.5t-121 -15.5z" />
    <glyph glyph-name="uni1EEE" unicode="&#x1eee;" horiz-adv-x="966" 
d="M483 -16q-111 0 -188 18.5t-125 61.5t-69 113.5t-21 175.5v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8t102.5 8t63.5 30t32 60.5t9 98.5v449h122q29 9 42.5 35t13.5 64v6h151q0 -75 -32 -127.5t-101 -72.5v-354q0 -105 -21.5 -175.5t-69 -113.5
t-124.5 -61.5t-188 -18.5zM256 890q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="uni1EEF" unicode="&#x1eef;" horiz-adv-x="816" 
d="M348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5v384h92q34 7 49 33.5t15 68.5v5h151q0 -72 -29 -123t-92 -73v-483h-127l-29 41q-56 -26 -110 -41.5t-121 -15.5zM181 660q0 72 32 107t92 35q32 0 58.5 -7.5
t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="uni1EF0" unicode="&#x1ef0;" horiz-adv-x="966" 
d="M483 -16q-111 0 -188 18.5t-125 61.5t-69 113.5t-21 175.5v449h196v-449q0 -60 9 -98.5t32.5 -60.5t63.5 -30t102 -8t102.5 8t63.5 30t32 60.5t9 98.5v449h122q29 9 42.5 35t13.5 64v6h151q0 -75 -32 -127.5t-101 -72.5v-354q0 -105 -21.5 -175.5t-69 -113.5
t-124.5 -61.5t-188 -18.5zM394 -76h178v-150h-178v150z" />
    <glyph glyph-name="uni1EF1" unicode="&#x1ef1;" horiz-adv-x="816" 
d="M348 -16q-139 0 -204.5 61.5t-65.5 168.5v358h187v-338q0 -48 26 -69t86 -21q45 0 87.5 12.5t84.5 31.5v384h92q34 7 49 33.5t15 68.5v5h151q0 -72 -29 -123t-92 -73v-483h-127l-29 41q-56 -26 -110 -41.5t-121 -15.5zM290 -76h178v-150h-178v150z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="891" 
d="M223 987h206l119 -140h-181zM348 311l-336 491h224l213 -313l213 313h217l-336 -492v-310h-195v311z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="747" 
d="M106 802h206l147 -170h-173zM12 572h198l153 -407h24l151 407h197l-230 -594q-20 -53 -43.5 -89.5t-56 -59t-77.5 -33t-107 -10.5q-38 0 -64.5 1t-61.5 5v148q21 -2 41.5 -3t45.5 -1q38 0 62 3t39.5 9t24 15.5t13.5 22.5l10 27h-103z" />
    <glyph glyph-name="uni1EF4" unicode="&#x1ef4;" horiz-adv-x="891" 
d="M348 311l-336 491h224l213 -313l213 313h217l-336 -492v-310h-195v311zM361 -76h178v-150h-178v150z" />
    <glyph glyph-name="uni1EF5" unicode="&#x1ef5;" horiz-adv-x="747" 
d="M12 572h198l153 -407h24l151 407h197l-230 -594q-20 -53 -43.5 -89.5t-56 -59t-77.5 -33t-107 -10.5q-38 0 -64.5 1t-61.5 5v148q21 -2 41.5 -3t45.5 -1q38 0 62 3t39.5 9t24 15.5t13.5 22.5l10 27h-103zM507 -76h178v-150h-178v150z" />
    <glyph glyph-name="uni1EF6" unicode="&#x1ef6;" horiz-adv-x="891" 
d="M401 864q7 18 17.5 29.5t20.5 20.5t17 18t7 22q0 15 -10.5 24t-27.5 9q-18 0 -33 -12.5t-23 -24.5l-60 32q23 37 59 60.5t93 23.5q26 0 48 -7.5t38.5 -20t26 -29t9.5 -34.5q0 -23 -10 -38.5t-24.5 -28.5t-30.5 -26.5t-29 -33.5zM348 311l-336 491h224l213 -313l213 313
h217l-336 -492v-310h-195v311z" />
    <glyph glyph-name="uni1EF7" unicode="&#x1ef7;" horiz-adv-x="747" 
d="M328 708q32 1 49 8t17 21q0 16 -24 16q-11 0 -24.5 -1.5t-36.5 -20.5l-63 54q52 51 127 51h6q54 -1 88 -26.5t34 -68.5v-3q-1 -40 -25 -62.5t-57 -31.5l-2 -24h-83zM12 572h198l153 -407h24l151 407h197l-230 -594q-20 -53 -43.5 -89.5t-56 -59t-77.5 -33t-107 -10.5
q-38 0 -64.5 1t-61.5 5v148q21 -2 41.5 -3t45.5 -1q38 0 62 3t39.5 9t24 15.5t13.5 22.5l10 27h-103z" />
    <glyph glyph-name="uni1EF8" unicode="&#x1ef8;" horiz-adv-x="891" 
d="M348 311l-336 491h224l213 -313l213 313h217l-336 -492v-310h-195v311zM219 890q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5
q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="uni1EF9" unicode="&#x1ef9;" horiz-adv-x="747" 
d="M12 572h198l153 -407h24l151 407h197l-230 -594q-20 -53 -43.5 -89.5t-56 -59t-77.5 -33t-107 -10.5q-38 0 -64.5 1t-61.5 5v148q21 -2 41.5 -3t45.5 -1q38 0 62 3t39.5 9t24 15.5t13.5 22.5l10 27h-103zM147 660q0 72 32 107t92 35q32 0 58.5 -7.5t48.5 -16.5
t40.5 -16.5t33.5 -7.5q20 0 27 8.5t7 25.5v9h116v-24q0 -72 -31.5 -106.5t-91.5 -34.5q-32 0 -58.5 7.5t-48.5 16t-40.5 16t-33.5 7.5q-20 0 -27 -8.5t-7 -25.5v-9h-117v24z" />
    <glyph glyph-name="uni2000" unicode="&#x2000;" 
 />
    <glyph glyph-name="uni2001" unicode="&#x2001;" horiz-adv-x="1520" 
 />
    <glyph glyph-name="uni2002" unicode="&#x2002;" 
 />
    <glyph glyph-name="uni2003" unicode="&#x2003;" horiz-adv-x="1520" 
 />
    <glyph glyph-name="uni2004" unicode="&#x2004;" horiz-adv-x="507" 
 />
    <glyph glyph-name="uni2005" unicode="&#x2005;" horiz-adv-x="380" 
 />
    <glyph glyph-name="uni2006" unicode="&#x2006;" horiz-adv-x="253" 
 />
    <glyph glyph-name="uni2007" unicode="&#x2007;" horiz-adv-x="752" 
 />
    <glyph glyph-name="uni2008" unicode="&#x2008;" horiz-adv-x="380" 
 />
    <glyph glyph-name="uni2009" unicode="&#x2009;" horiz-adv-x="190" 
 />
    <glyph glyph-name="uni200A" unicode="&#x200a;" horiz-adv-x="152" 
 />
    <glyph glyph-name="uni200B" unicode="&#x200b;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni200C" unicode="&#x200c;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni200D" unicode="&#x200d;" horiz-adv-x="0" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" 
d="M0 209v155h760v-155h-760z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1520" 
d="M0 209v155h1520v-155h-1520z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="380" 
d="M235 465h-197l140 337h164z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="380" 
d="M145 802h197l-140 -337h-164z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="380" 
d="M145 186h197l-140 -337h-164z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="655" 
d="M235 465h-197l140 337h164zM510 465h-197l140 337h164z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="655" 
d="M145 802h197l-140 -337h-164zM420 802h197l-140 -337h-164z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="655" 
d="M145 186h197l-140 -337h-164zM420 186h197l-140 -337h-164z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" 
d="M286 309v94h-214v169h214v230h188v-230h213v-169h-213v-94l-21 -455h-147z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" 
d="M72 241h214v174h-214v157h214v230h188v-230h213v-157h-213v-174h213v-157h-213v-230h-188v230h-214v157z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="522" 
d="M262 101q-93 0 -140.5 43t-47.5 143q0 98 47.5 140.5t140.5 42.5q91 0 138.5 -42.5t47.5 -140.5q0 -100 -47.5 -143t-138.5 -43z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="935" 
d="M93 183h194v-183h-194v183zM370 183h194v-183h-194v183zM648 183h194v-183h-194v183z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1399" 
d="M227 466q-105 0 -155 39.5t-50 136.5q0 49 12.5 82.5t37.5 54.5t64 30t91 9q53 0 92 -9t64.5 -30t38 -54.5t12.5 -82.5q0 -97 -50 -136.5t-157 -39.5zM242 0h-177l645 802h178zM227 571q38 0 50.5 15.5t12.5 55.5t-12.5 55.5t-50.5 15.5q-37 0 -49.5 -15.5t-12.5 -55.5
t12.5 -55.5t49.5 -15.5zM723 -16q-105 0 -155 39t-50 136q0 49 12.5 82.5t37.5 54.5t64 30.5t91 9.5q53 0 92 -9.5t64.5 -30.5t38 -54.5t12.5 -82.5q0 -97 -50 -136t-157 -39zM1172 -16q-105 0 -155.5 39t-50.5 136q0 49 12.5 82.5t38 54.5t64.5 30.5t91 9.5q53 0 91.5 -9.5
t64 -30.5t38 -54.5t12.5 -82.5q0 -97 -50 -136t-156 -39zM723 88q38 0 50.5 16t12.5 55q0 41 -12.5 56.5t-50.5 15.5q-37 0 -49.5 -15.5t-12.5 -56.5q0 -39 12.5 -55t49.5 -16zM1172 88q38 0 50.5 16t12.5 55q0 41 -12.5 56.5t-50.5 15.5t-50 -15.5t-12 -56.5q0 -39 12 -55
t50 -16z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="392" 
d="M177 519h190l-151 -232l151 -232h-190l-152 232z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="392" 
d="M216 55h-191l152 232l-152 232h190l152 -232z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="270" 
d="M-100 0h-177l645 802h178z" />
    <glyph glyph-name="uni2060" unicode="&#x2060;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni2070" unicode="&#x2070;" horiz-adv-x="436" 
d="M218 531q-109 0 -157 59t-48 180t48 180t157 59q110 0 158.5 -59.5t48.5 -179.5t-48.5 -179.5t-158.5 -59.5zM218 645q20 0 31.5 5.5t18.5 19.5t9 38.5t2 61.5q0 38 -2 62t-9 38t-18.5 19.5t-31.5 5.5q-19 0 -30.5 -5.5t-18 -19.5t-9 -38t-2.5 -62q0 -37 2.5 -61.5
t9 -38.5t18 -19.5t30.5 -5.5z" />
    <glyph glyph-name="uni2074" unicode="&#x2074;" horiz-adv-x="436" 
d="M418 802v-262h-140v77h-262v110l154 273h149l-154 -272h113v74h140z" />
    <glyph glyph-name="uni2075" unicode="&#x2075;" horiz-adv-x="436" 
d="M29 665q39 -8 74 -12t77 -4q30 0 48.5 1.5t29.5 6t15 12t4 19.5q0 22 -13.5 29.5t-55.5 7.5h-166v275h348v-113h-206v-49h49q53 0 89.5 -10t58.5 -28.5t31.5 -46.5t9.5 -64q0 -49 -14 -79.5t-43.5 -48t-74 -24t-105.5 -6.5q-50 0 -90.5 5.5t-65.5 10.5v118z" />
    <glyph glyph-name="uni2076" unicode="&#x2076;" horiz-adv-x="436" 
d="M220 531q-57 0 -97 15t-64.5 45t-35.5 74.5t-11 104.5q0 66 13.5 112t42.5 74t75 40.5t112 12.5q42 0 75 -5t53 -11v-118q-32 8 -60.5 12t-65.5 4q-27 0 -45.5 -2.5t-30 -10t-17 -20t-6.5 -33.5q20 7 43.5 11t52.5 4q92 0 129 -38t37 -109q0 -88 -54 -125t-146 -37z
M220 733q-17 0 -32 -2.5t-29 -7.5q2 -45 14.5 -62.5t49.5 -17.5q29 0 40.5 10.5t11.5 34.5q0 26 -12 35.5t-43 9.5z" />
    <glyph glyph-name="uni2077" unicode="&#x2077;" horiz-adv-x="436" 
d="M19 1000h400v-110l-207 -350h-172l219 347h-240v113z" />
    <glyph glyph-name="uni2078" unicode="&#x2078;" horiz-adv-x="436" 
d="M219 531q-101 0 -155 29t-54 102q0 46 20 72t57 38q-33 14 -52 40t-19 73q0 68 52.5 96t150.5 28q97 0 149.5 -28t52.5 -96q0 -47 -19 -73t-52 -40q37 -12 57 -38t20 -72q0 -73 -53.5 -102t-154.5 -29zM219 822q34 0 46 8.5t12 28.5t-12 28.5t-46 8.5t-46 -8.5t-12 -28.5
t12.5 -28.5t45.5 -8.5zM219 643q34 0 48.5 9t14.5 33t-14.5 33t-48.5 9q-35 0 -49.5 -9t-14.5 -33t14.5 -33t49.5 -9z" />
    <glyph glyph-name="uni2079" unicode="&#x2079;" horiz-adv-x="436" 
d="M212 1009q57 0 97 -15t64.5 -45t35.5 -74.5t11 -104.5q0 -66 -13 -112t-42.5 -74t-75.5 -40.5t-112 -12.5q-42 0 -74.5 5t-53.5 11v118q32 -8 61 -12t65 -4q27 0 46 2.5t30.5 10t17 20.5t6.5 34q-20 -8 -43.5 -12t-52.5 -4q-46 0 -78 10t-51.5 28.5t-28 46t-8.5 62.5
q0 88 53.5 125t145.5 37zM157 852q0 -26 12 -35.5t43 -9.5q17 0 32.5 2.5t29.5 7.5q-2 45 -15 62.5t-50 17.5q-28 0 -40 -10.5t-12 -34.5z" />
    <glyph glyph-name="uni2080" unicode="&#x2080;" horiz-adv-x="436" 
d="M218 -207q-109 0 -157 59t-48 180t48 180t157 59q110 0 158.5 -59.5t48.5 -179.5t-48.5 -179.5t-158.5 -59.5zM218 -93q20 0 31.5 5.5t18.5 19.5t9 38t2 62t-2 62t-9 38t-18.5 19.5t-31.5 5.5q-19 0 -30.5 -5.5t-18 -19.5t-9 -38t-2.5 -62t2.5 -62t9 -38t18 -19.5
t30.5 -5.5z" />
    <glyph glyph-name="uni2081" unicode="&#x2081;" horiz-adv-x="365" 
d="M185 262h141v-460h-143v322l-118 -59l-59 108z" />
    <glyph glyph-name="uni2082" unicode="&#x2082;" horiz-adv-x="436" 
d="M210 271q107 0 155.5 -31.5t48.5 -106.5q0 -28 -6 -47.5t-19 -34.5t-32.5 -27.5t-46.5 -26.5l-117 -60q-11 -6 -14 -9.5t-3 -12.5h234v-113h-378v112q0 22 5 39t16 30.5t29 25.5t43 25l123 63q12 7 16.5 12t4.5 17q0 8 -2.5 13t-10 8t-21 4t-36.5 1q-35 0 -77.5 -5
t-74.5 -10v118q33 6 71.5 11t91.5 5z" />
    <glyph glyph-name="uni2083" unicode="&#x2083;" horiz-adv-x="436" 
d="M29 -75q39 -7 74 -11t77 -4q57 0 75 6.5t18 27.5t-10.5 28.5t-42.5 7.5h-139v112h139q26 0 37 6t11 25q0 20 -16.5 26t-66.5 6q-46 0 -79 -4t-72 -11v115q24 5 65 10.5t92 5.5q57 0 98.5 -5.5t68.5 -19.5t40.5 -38t13.5 -60q0 -48 -19.5 -74t-54.5 -40q39 -12 59.5 -37.5
t20.5 -72.5q0 -39 -14 -64.5t-42 -40t-71 -20.5t-100 -6q-51 0 -94 5.5t-68 10.5v116z" />
    <glyph glyph-name="uni2084" unicode="&#x2084;" horiz-adv-x="436" 
d="M418 64v-262h-140v77h-262v110l154 273h149l-154 -272h113v74h140z" />
    <glyph glyph-name="uni2085" unicode="&#x2085;" horiz-adv-x="436" 
d="M29 -73q39 -8 74 -12t77 -4q30 0 48.5 1.5t29.5 6t15 12t4 19.5q0 22 -13.5 29.5t-55.5 7.5h-166v275h348v-113h-206v-49h49q53 0 89.5 -10t58.5 -28.5t31.5 -46.5t9.5 -64q0 -49 -14 -79.5t-43.5 -48t-74 -24t-105.5 -6.5q-50 0 -90.5 5.5t-65.5 10.5v118z" />
    <glyph glyph-name="uni2086" unicode="&#x2086;" horiz-adv-x="436" 
d="M220 -207q-57 0 -97 15t-64.5 45t-35.5 74.5t-11 104.5q0 66 13.5 112t42.5 74t75 40.5t112 12.5q42 0 75 -5t53 -11v-118q-32 8 -60.5 12t-65.5 4q-27 0 -45.5 -2.5t-30 -10t-17 -20t-6.5 -33.5q20 7 43.5 11t52.5 4q92 0 129 -38t37 -109q0 -88 -54 -125t-146 -37z
M220 -5q-17 0 -32 -2.5t-29 -7.5q2 -45 14.5 -62.5t49.5 -17.5q29 0 40.5 10.5t11.5 34.5q0 26 -12 35.5t-43 9.5z" />
    <glyph glyph-name="uni2087" unicode="&#x2087;" horiz-adv-x="436" 
d="M19 262h400v-110l-207 -350h-172l219 347h-240v113z" />
    <glyph glyph-name="uni2088" unicode="&#x2088;" horiz-adv-x="436" 
d="M219 -207q-101 0 -155 29t-54 102q0 46 20 72t57 38q-33 14 -52 40t-19 73q0 68 52.5 96t150.5 28q97 0 149.5 -28t52.5 -96q0 -47 -19 -73t-52 -40q37 -12 57 -38t20 -72q0 -73 -53.5 -102t-154.5 -29zM219 84q34 0 46 8.5t12 28.5t-12 28.5t-46 8.5t-46 -8.5t-12 -28.5
t12.5 -28.5t45.5 -8.5zM219 -95q34 0 48.5 9t14.5 33t-14.5 33t-48.5 9q-35 0 -49.5 -9t-14.5 -33t14.5 -33t49.5 -9z" />
    <glyph glyph-name="uni2089" unicode="&#x2089;" horiz-adv-x="436" 
d="M212 271q57 0 97 -15t64.5 -45t35.5 -74.5t11 -104.5q0 -66 -13 -112t-42.5 -74t-75.5 -40.5t-112 -12.5q-42 0 -74.5 5t-53.5 11v118q32 -8 61 -12t65 -4q27 0 46 2.5t30.5 10t17 20.5t6.5 34q-20 -8 -43.5 -12t-52.5 -4q-46 0 -78 9.5t-51.5 28.5t-28 46.5t-8.5 62.5
q0 88 53.5 125t145.5 37zM157 114q0 -26 12 -35.5t43 -9.5q17 0 32.5 2.5t29.5 7.5q-2 45 -15 62.5t-50 17.5q-28 0 -40 -10.5t-12 -34.5z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="752" 
d="M32 366h92v55q0 9 1 19h-93v132h106q13 63 38.5 109t66 76.5t98 45.5t134.5 15q73 0 131 -7.5t97 -15.5v-155q-27 5 -52 9t-50.5 7t-54 4.5t-63.5 1.5q-69 0 -104.5 -18t-49.5 -72h319v-132h-333v-74h333v-132h-320q7 -28 19 -46t30.5 -28.5t44.5 -14.5t61 -4t63.5 1.5
t54 4.5t50.5 7t52 10v-156q-39 -8 -97 -16t-131 -8q-77 0 -135 15t-98.5 46t-66 78t-37.5 111h-106v132z" />
    <glyph glyph-name="uni2113" unicode="&#x2113;" 
d="M422 -15q-46 0 -85.5 8t-70 27.5t-51 51t-30.5 78.5q-8 -4 -17 -9l-33 -19q-8 -4 -17 -9l-61 117q30 15 60 31.5t59 33.5v273q0 64 12 111t39 77.5t70 45.5t104 15q109 0 169.5 -45.5t60.5 -140.5q0 -49 -21 -98t-60 -98t-94 -97.5t-124 -95.5q0 -64 20.5 -90.5
t76.5 -26.5q49 0 83 23t66 65l109 -84q-51 -72 -113.5 -108t-151.5 -36zM332 398q62 49 100 100t38 104q0 75 -69 75q-39 0 -54 -23t-15 -67v-189z" />
    <glyph glyph-name="uni2116" unicode="&#x2116;" horiz-adv-x="1548" 
d="M92 802h241l290 -495v495h195v-802h-247l-284 482v-482h-195v802zM1198 219q-75 0 -130 15t-91.5 48t-54.5 86t-18 129t18 129t54.5 86t91.5 48t130 15t130.5 -15t92 -48t54.5 -86t18 -129t-18 -129t-54.5 -86t-92 -48t-130.5 -15zM1198 369q59 0 83 29t24 99
q0 69 -24 98t-83 29q-57 0 -80.5 -29t-23.5 -98q0 -71 23.5 -99.5t80.5 -28.5zM926 161h545v-161h-545v161z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="969" 
d="M133 698h-104v104h328v-104h-104v-297h-120v297zM426 802h158l83 -196l82 196h158v-401h-120v214l-68 -153h-105l-67 153v-214h-121v401z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="969" 
d="M274 347v-218q76 -46 211 -46q108 0 176 28t103 88h127q-39 -109 -136.5 -162t-269.5 -53q-119 0 -202 25t-135 77t-75.5 130.5t-23.5 184.5t23.5 184.5t75.5 130t135 77t202 25.5t202 -25.5t134.5 -77t75 -130t23.5 -184.5v-54h-646zM485 719q-135 0 -211 -46v-229h422
v229q-76 46 -211 46z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" 
d="M34 286l319 320l103 -103l-136 -133h440v-167h-442l138 -135l-103 -102z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" 
d="M60 323l320 320l320 -320l-103 -103l-134 137l1 -357h-168l1 358l-134 -138z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" 
d="M310 63l148 144h-458v159h457l-147 143l96 97l320 -320l-320 -320z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" 
d="M300 200v372h160v-372l144 147l96 -97l-320 -320l-320 320l97 97z" />
    <glyph glyph-name="uni2196" unicode="&#x2196;" 
d="M91 563h452v-137l-203 1l321 -321l-113 -113l-321 321v-204h-136v453z" />
    <glyph glyph-name="uni2197" unicode="&#x2197;" 
d="M532 120v202l-320 -320l-113 113l321 321h-203v136h452v-452h-137z" />
    <glyph glyph-name="uni2198" unicode="&#x2198;" 
d="M669 0h-452v136h203l-321 321l113 113l320 -320v202h137v-452z" />
    <glyph glyph-name="uni2199" unicode="&#x2199;" 
d="M227 452v-203l321 321l113 -113l-321 -321h203v-136h-452v452h136z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="783" 
d="M392 -16q-86 0 -149.5 16t-105 51t-62 91t-20.5 136q0 78 21.5 132t60.5 88.5t94.5 50t123.5 15.5q28 0 54.5 -4t52.5 -12q-48 67 -122 127.5t-172 126.5h265q155 -96 225.5 -217.5t70.5 -263.5q0 -93 -20.5 -157t-62 -104t-105 -58t-149.5 -18zM246 278q0 -41 7.5 -69.5
t25 -46t45.5 -25.5t68 -8q41 0 68.5 10t44.5 33t24.5 60t7.5 90q0 41 -7 75q-32 11 -64 18t-65 7q-42 0 -71.5 -7t-48 -24t-27 -44.5t-8.5 -68.5z" />
    <glyph glyph-name="uni2206" unicode="&#x2206;" 
d="M248 802h265l243 -802h-752zM519 163l-129 483h-18l-131 -483h278z" />
    <glyph glyph-name="product" unicode="&#x220f;" 
d="M92 632h-72v170h720v-170h-72v-741h-195v741h-186v-741h-195v741z" />
    <glyph glyph-name="summation" unicode="&#x2211;" 
d="M44 26l289 320l-287 321v135h670v-166h-398l259 -290l-258 -289h397v-166h-672v135z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M96 361h568v-150h-568v150z" />
    <glyph glyph-name="radical" unicode="&#x221a;" 
d="M34 572h257l89 -374l208 789h162l-265 -987h-213l-102 427h-136v145z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" 
d="M195 159q-38 0 -71.5 9t-58.5 33.5t-39.5 67.5t-14.5 110q0 68 14.5 111t39.5 67t58.5 33t71.5 9q56 0 105 -29t86 -84q36 54 81.5 83.5t97.5 29.5q38 0 71.5 -9t58.5 -33t39.5 -67t14.5 -111q0 -67 -14.5 -110t-39.5 -67.5t-58.5 -33.5t-71.5 -9q-56 0 -106 30t-85 84
q-37 -54 -82 -84t-97 -30zM195 278q28 0 50.5 15t37.5 39l25 38l-39 59q-14 22 -32 36.5t-42 14.5q-61 0 -61 -101t61 -101zM489 332q15 -23 33.5 -38.5t42.5 -15.5q61 0 61 101t-61 101q-28 0 -50 -14t-37 -37l-27 -41z" />
    <glyph glyph-name="integral" unicode="&#x222b;" 
d="M80 -58q21 -2 42 -3t42 -1q27 0 44 3.5t27.5 12t15.5 23t7 36.5l58 568q6 57 19 101t41 74t74.5 46t119.5 16q32 0 56.5 -2t53.5 -5v-148q-21 2 -42 3t-42 1q-27 0 -44 -3.5t-27.5 -12.5t-15.5 -23.5t-7 -36.5l-58 -568q-6 -57 -19 -101t-41 -74t-74.5 -45.5
t-119.5 -15.5q-32 0 -56.5 2t-53.5 4v149z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M96 353q0 87 38.5 129.5t113.5 42.5q40 0 74.5 -10.5t64 -22.5t54.5 -22.5t44 -10.5q23 0 31 12.5t8 35.5v11h140v-29q0 -172 -151 -172q-40 0 -74.5 10.5t-64 22.5t-54 22.5t-44.5 10.5q-23 0 -31 -12t-8 -36v-11h-141v29zM96 101q0 87 38.5 129.5t113.5 42.5
q40 0 74.5 -10.5t64 -22.5t54.5 -22.5t44 -10.5q23 0 31 12.5t8 35.5v11h140v-29q0 -172 -151 -172q-40 0 -74.5 10.5t-64 22.5t-54 22.5t-44.5 10.5q-23 0 -31 -12t-8 -36v-11h-141v29z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M96 228h170l56 118h-226v150h298l36 76h174l-36 -76h96v-150h-168l-57 -118h225v-151h-297l-37 -77h-173l37 77h-98v151z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M96 522l568 98v-150l-434 -63v-13l434 -64v-150l-568 97v245zM96 151h568v-151h-568v151z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M96 330l435 64v13l-435 63v150l568 -98v-245l-568 -97v150zM96 151h568v-151h-568v151z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" 
d="M62 401l219 401h198l219 -401l-219 -401h-198zM380 153l136 248l-136 249l-136 -249z" />
    <glyph glyph-name="uni25CC" unicode="&#x25cc;" horiz-adv-x="672" 
d="M336 -10q-61 0 -115 23.5t-94 63.5t-63.5 94t-23.5 115t23.5 115t63.5 94t94 63.5t115 23.5t115 -23.5t94 -63.5t63.5 -94t23.5 -115t-23.5 -115t-63.5 -94t-94 -63.5t-115 -23.5zM336 572q-59 0 -111 -22.5t-91 -61.5t-61.5 -91t-22.5 -111t22.5 -111t61.5 -91t91 -61.5
t111 -22.5t111 22.5t91 61.5t61.5 91t22.5 111t-22.5 111t-61.5 91t-91 61.5t-111 22.5z" />
    <glyph glyph-name="uniFEFF" unicode="&#xfeff;" horiz-adv-x="0" 
 />
    <glyph glyph-name="glyph1" horiz-adv-x="0" 
 />
    <glyph glyph-name="ampersand.1" horiz-adv-x="892" 
d="M576 79q-40 -43 -97 -68.5t-146 -25.5q-147 0 -215 56.5t-68 181.5q0 91 45 146t139 69l-10 10q-23 24 -38 44.5t-23.5 40t-12 40.5t-3.5 46q0 104 68.5 151.5t215.5 47.5q67 0 121 -5t89 -11v-157q-45 8 -91 12t-94 4q-37 0 -59.5 -3t-35 -10t-17 -18.5t-4.5 -28.5
q0 -9 1.5 -16t5.5 -14.5t11.5 -16.5t19.5 -21l185 -195l100 146h202l-187 -267l207 -217h-233zM239 222q0 -55 23 -78.5t83 -23.5q45 0 70 18.5t51 56.5l-141 147q-52 -5 -69 -32.5t-17 -87.5z" />
    <glyph glyph-name="commaaccentbelow" horiz-adv-x="600" 
d="M268 -53h207l-177 -145h-173z" />
    <glyph glyph-name="commaturnedabove" horiz-adv-x="600" 
d="M329 632h-206l180 170h173z" />
    <glyph glyph-name="gravecomb.case" horiz-adv-x="0" 
d="M-223 987h206l119 -140h-181z" />
    <glyph glyph-name="acutecomb.case" horiz-adv-x="0" 
d="M17 987h206l-144 -140h-181z" />
    <glyph glyph-name="uni030B.case" horiz-adv-x="0" 
d="M-126 987h191l-121 -140h-165zM149 987h191l-120 -140h-166z" />
    <glyph glyph-name="uni0302.case" horiz-adv-x="0" 
d="M-114 987h230l133 -140h-166l-82 73l-83 -73h-166z" />
    <glyph glyph-name="uni030C.case" horiz-adv-x="0" 
d="M-248 987h166l83 -74l82 74h166l-133 -140h-230z" />
    <glyph glyph-name="uni0306.case" horiz-adv-x="0" 
d="M1 842q-69 0 -115 8t-73.5 25.5t-39 45t-11.5 66.5h140q0 -22 20 -32t79 -10t79 10t20 32h141q0 -39 -11.5 -66.5t-39 -45t-74 -25.5t-115.5 -8z" />
    <glyph glyph-name="uni030A.case" horiz-adv-x="0" 
d="M0 832q-86 0 -124 25t-38 87q0 60 38 85t124 25t124 -25t38 -85q0 -62 -38 -87t-124 -25zM0 903q40 0 55.5 8.5t15.5 32.5q0 23 -15.5 31t-55.5 8q-41 0 -56 -8t-15 -31q0 -24 15 -32.5t56 -8.5z" />
    <glyph glyph-name="tildecomb.case" horiz-adv-x="0" 
d="M-226 863q0 63 32 93.5t93 30.5q32 0 58.5 -6t49 -13t41.5 -13t35 -6q17 0 23 6.5t6 20.5v6h117v-18q0 -63 -32 -93.5t-93 -30.5q-32 0 -58.5 6t-49 13.5t-41.5 13.5t-35 6q-17 0 -23 -6.5t-6 -22.5v-6h-117v19z" />
    <glyph glyph-name="uni0307.case" horiz-adv-x="0" 
d="M-95 978h190v-122h-190v122z" />
    <glyph glyph-name="uni0308.case" horiz-adv-x="0" 
d="M-197 978h150v-122h-150v122zM47 978h150v-122h-150v122z" />
    <glyph glyph-name="uni0304.case" horiz-adv-x="0" 
d="M-211 974h422v-116h-422v116z" />
    <glyph glyph-name="uni030C.alt" horiz-adv-x="0" 
d="M97 802h164l-114 -252h-99z" />
    <glyph glyph-name="uni0327.1" horiz-adv-x="0" 
d="M-40 -198h-164l137 204h159z" />
    <glyph glyph-name="Ccedilla.1" horiz-adv-x="894" 
d="M407 -14q-188 12 -272.5 114t-84.5 301q0 107 23.5 185.5t73.5 130t128 76.5t187 25q178 0 274.5 -67.5t125.5 -208.5h-205q-21 56 -66.5 80.5t-128.5 24.5q-63 0 -104 -14t-65.5 -43.5t-34.5 -76t-10 -112.5t10 -112.5t34.5 -76t65.5 -43.5t104 -14q83 0 128.5 25
t66.5 80h205q-27 -134 -117 -202t-253 -74l-29 -26q67 0 98 -18.5t31 -63.5q0 -51 -35 -70.5t-113 -19.5q-24 0 -47 1t-48 6v65q28 -3 48 -4t43 -1q39 0 50.5 4.5t11.5 18.5t-11.5 19t-50.5 5q-18 0 -34 -1.5t-37 -3.5v57z" />
    <glyph glyph-name="germandbls.cap" horiz-adv-x="1727" 
d="M82 199q81 -18 169.5 -32.5t173.5 -14.5q55 0 89.5 3.5t54 12.5t26.5 24t7 37q0 23 -6 37.5t-24.5 24t-53.5 15.5t-92 12q-99 8 -166 23t-108.5 43t-59 72.5t-17.5 111.5q0 62 19 108.5t63 78t115.5 47.5t175.5 16q80 0 155 -8.5t157 -27.5v-170q-88 19 -161.5 28.5
t-148.5 9.5q-55 0 -89.5 -3t-54 -11t-26 -22t-6.5 -36q0 -21 6 -34t25 -21t53.5 -13.5t92.5 -11.5q99 -9 166.5 -24.5t108.5 -44.5t58.5 -74.5t17.5 -113.5q0 -64 -20 -112.5t-64.5 -80.5t-116 -48t-174.5 -16q-89 0 -179.5 12.5t-165.5 32.5v170zM946 199
q81 -18 169.5 -32.5t173.5 -14.5q55 0 89.5 3.5t54 12.5t26.5 24t7 37q0 23 -6 37.5t-24.5 24t-53.5 15.5t-92 12q-99 8 -166 23t-108.5 43t-59 72.5t-17.5 111.5q0 62 19 108.5t63 78t115.5 47.5t175.5 16q80 0 155 -8.5t157 -27.5v-170q-88 19 -161.5 28.5t-148.5 9.5
q-55 0 -89.5 -3t-54 -11t-26 -22t-6.5 -36q0 -21 6 -34t25 -21t53.5 -13.5t92.5 -11.5q99 -9 166.5 -24.5t108.5 -44.5t58.5 -74.5t17.5 -113.5q0 -64 -20 -112.5t-64.5 -80.5t-116 -48t-174.5 -16q-89 0 -179.5 12.5t-165.5 32.5v170z" />
    <glyph glyph-name="ccedilla.1" horiz-adv-x="681" 
d="M330 -14q-72 5 -125 24t-88 55t-52 90.5t-17 130.5q0 84 21 141.5t63.5 93.5t107.5 51.5t153 15.5q59 0 113.5 -6.5t111.5 -20.5v-148q-32 5 -59.5 9t-53 6.5t-50.5 3.5t-52 1q-48 0 -79.5 -7t-50 -24t-25.5 -45t-7 -71t7 -71t25.5 -45t50 -24t79.5 -7q56 0 107 4.5
t118 14.5v-147q-54 -14 -106.5 -20t-107.5 -7l-28 -26q67 0 98 -18.5t31 -63.5q0 -51 -35 -70.5t-113 -19.5q-24 0 -47 1t-48 6v65q28 -3 48 -4t43 -1q39 0 50.5 4.5t11.5 18.5t-11.5 19t-50.5 5q-18 0 -34 -1.5t-37 -3.5v57z" />
    <glyph glyph-name="i.trk" horiz-adv-x="350" 
d="M80 791h190v-146h-190v146zM82 572h187v-572h-187v572z" />
    <glyph glyph-name="exclamdown.case" horiz-adv-x="380" 
d="M287 802v-183h-194v183h194zM263 534l22 -311v-223h-191v223l22 311h147z" />
    <glyph glyph-name="questiondown.case" horiz-adv-x="699" 
d="M537 627h-194v183h194v-183zM537 408v-94h-148q-54 0 -88 -4.5t-53 -14.5t-26 -26t-7 -39q0 -24 7 -40.5t27.5 -26.5t58.5 -14.5t100 -4.5q65 0 133 7.5t131 21.5v-154q-27 -6 -59 -11t-66.5 -8.5t-70.5 -5.5t-70 -2q-104 0 -176 14.5t-116.5 43t-64 70.5t-19.5 96
q0 57 17.5 99.5t55.5 70.5t98.5 41.5t146.5 13.5l18 101h147z" />
    <glyph glyph-name="hyphen.case" horiz-adv-x="380" 
d="M24 324v155h332v-155h-332z" />
    <glyph glyph-name="guilsinglleft.case" horiz-adv-x="392" 
d="M177 633h190l-151 -232l151 -232h-190l-152 232z" />
    <glyph glyph-name="guilsinglright.case" horiz-adv-x="392" 
d="M216 170h-191l152 232l-152 232h190l152 -232z" />
    <glyph glyph-name="guillemotleft.case" horiz-adv-x="687" 
d="M177 633h190l-151 -232l151 -232h-190l-152 232zM472 633h190l-151 -232l151 -232h-190l-152 232z" />
    <glyph glyph-name="guillemotright.case" horiz-adv-x="687" 
d="M216 170h-191l152 232l-152 232h190l152 -232zM511 170h-191l152 232l-152 232h190l152 -232z" />
    <glyph glyph-name="parenleft.case" horiz-adv-x="427" 
d="M224 -73q-37 45 -67 94t-51.5 106.5t-33.5 125t-12 148.5q0 83 12.5 151t34.5 125.5t52 106t65 91.5h177q-31 -39 -59 -89t-49.5 -110.5t-34 -129.5t-12.5 -145t12.5 -144.5t33.5 -128.5t49 -110.5t60 -90.5h-177z" />
    <glyph glyph-name="parenright.case" horiz-adv-x="427" 
d="M203 875q37 -45 67 -94t51.5 -106.5t33.5 -125t12 -148.5q0 -82 -12.5 -150.5t-34.5 -126t-52 -106t-65 -91.5h-177q31 39 59 89t49.5 110.5t34 129.5t12.5 145t-12.5 144.5t-33.5 128.5t-49 110.5t-60 90.5h177z" />
    <glyph glyph-name="braceleft.case" horiz-adv-x="427" 
d="M395 -73h-108q-46 0 -82 8.5t-61.5 29.5t-39 56.5t-13.5 89.5v159q0 32 -11 45.5t-48 13.5h-24v145h24q37 0 48 13t11 45v160q0 53 13.5 88.5t39 56.5t61.5 29.5t82 8.5h108v-146h-64q-37 0 -50.5 -11t-13.5 -46v-128q0 -54 -18.5 -89.5t-59.5 -53.5q41 -18 59.5 -53.5
t18.5 -89.5v-127q0 -35 13.5 -46.5t50.5 -11.5h64v-146z" />
    <glyph glyph-name="braceright.case" horiz-adv-x="427" 
d="M336 111q0 -54 -13.5 -89.5t-38.5 -56.5t-61 -29.5t-82 -8.5h-108v146h55q42 0 57 11.5t15 46.5v127q0 54 18.5 89.5t59.5 53.5q-41 18 -59.5 53.5t-18.5 89.5v128q0 35 -13 46t-51 11h-63v146h108q46 0 82 -8.5t61 -29.5t38.5 -56.5t13.5 -88.5v-160q0 -32 11.5 -45
t48.5 -13h24v-145h-24q-37 0 -48.5 -13.5t-11.5 -45.5v-159z" />
    <glyph glyph-name="bracketleft.case" horiz-adv-x="427" 
d="M69 875h326v-146h-150v-656h150v-146h-326v948z" />
    <glyph glyph-name="bracketright.case" horiz-adv-x="427" 
d="M358 -73h-325v146h149v656h-149v146h325v-948z" />
    <glyph glyph-name="endash.case" 
d="M0 324v155h760v-155h-760z" />
    <glyph glyph-name="emdash.case" horiz-adv-x="1520" 
d="M0 324v155h1520v-155h-1520z" />
    <glyph glyph-name="bullet.case" horiz-adv-x="522" 
d="M262 217q-93 0 -140.5 43t-47.5 143q0 98 47.5 140.5t140.5 42.5q91 0 138.5 -42.5t47.5 -140.5q0 -100 -47.5 -143t-138.5 -43z" />
    <glyph glyph-name="periodcentered.case" horiz-adv-x="380" 
d="M93 492h194v-183h-194v183z" />
    <glyph glyph-name="at.case" horiz-adv-x="977" 
d="M490 -15q-123 0 -207 24t-135.5 75t-74 130t-22.5 188q0 107 24.5 185t77.5 129.5t136 76.5t201 25q117 0 199.5 -25t135 -76.5t77 -129.5t24.5 -185v-142q0 -54 -11.5 -88.5t-34 -55t-55 -28.5t-74.5 -8q-74 0 -123 30.5t-60 101.5q-49 -20 -113 -20q-82 0 -131 32
t-49 105q0 64 35.5 93.5t123.5 29.5h131v5q0 12 -3.5 20.5t-14 14.5t-29.5 8.5t-50 2.5q-40 0 -80 -3.5t-85 -9.5v108q24 4 43.5 6.5t39 4t41.5 2t52 0.5q66 0 109 -9t69 -27t36.5 -45.5t10.5 -64.5v-225q0 -26 11 -41t40 -15q27 0 37.5 15t10.5 41v162q0 86 -16 144.5
t-52.5 94t-96.5 50.5t-148 15t-147.5 -15t-96 -50.5t-52.5 -94t-16 -144.5q0 -87 16.5 -145t54 -93.5t97 -50.5t144.5 -15q17 0 37 1t37 3v-111q-31 -6 -74 -6zM477 288q50 0 88 16v59h-99q-31 0 -43 -8.5t-12 -28.5t14 -29t52 -9z" />
    <glyph glyph-name="dollar.weight" horiz-adv-x="752" 
d="M77 224q54 -11 111.5 -21t115.5 -15v145q-67 9 -113.5 24t-74.5 40t-40.5 62.5t-12.5 91.5q0 47 12 84.5t40.5 64.5t74.5 44t114 23v95h152v-93q49 -3 97.5 -9.5t99.5 -17.5v-158q-54 11 -101.5 18t-95.5 10v-129q68 -10 114 -25.5t74.5 -41.5t41 -64t12.5 -94
q0 -100 -55.5 -156t-186.5 -68v-94h-152v93q-60 3 -119 12t-108 23v156zM240 562q0 -27 12 -39t52 -20v108q-37 -5 -50.5 -15.5t-13.5 -33.5zM519 251q0 26 -11.5 40t-51.5 22v-124q38 5 50.5 19.5t12.5 42.5z" />
    <glyph glyph-name="cent.weight" horiz-adv-x="752" 
d="M512 802v-99q45 -3 87.5 -8.5t87.5 -16.5v-157q-51 8 -93 12t-82 6v-274q42 2 86.5 5.5t98.5 12.5v-156q-47 -11 -92.5 -16.5t-92.5 -7.5v-103h-152v102q-78 4 -135.5 22t-95 53.5t-56 91t-18.5 133.5q0 79 18.5 134.5t56 91.5t95 54t135.5 22v98h152zM250 402
q0 -33 5 -57t17.5 -40.5t33.5 -25.5t54 -12v271q-33 -3 -54 -12t-33.5 -25.5t-17.5 -40.5t-5 -58z" />
    <glyph glyph-name="zeroslash" horiz-adv-x="752" 
d="M375 -16q-91 0 -156 25t-106.5 76.5t-61 130t-19.5 185.5t19.5 185.5t61 130t106.5 76.5t156 25q92 0 157.5 -25t107 -76.5t61 -130t19.5 -185.5t-19.5 -185.5t-61 -130t-107 -76.5t-157.5 -25zM227 401q0 -20 0.5 -37.5t1.5 -33.5l237 306q-17 12 -39.5 17t-51.5 5
q-43 0 -72 -12.5t-45.5 -42t-23.5 -79t-7 -123.5zM375 144q44 0 73 12.5t46 42t24 79t7 123.5q0 20 -0.5 37.5t-1.5 33.5l-237 -306q17 -12 38.5 -17t50.5 -5z" />
    <glyph glyph-name="dollar.lt" 
d="M81 224q70 -15 147 -27t151 -12q45 0 73 3t43.5 10.5t21.5 20.5t6 32t-5 31t-20.5 19.5t-45 13t-78.5 10.5q-87 9 -145.5 22.5t-94.5 38.5t-51.5 65t-15.5 100q0 47 12 84.5t40.5 64.5t74.5 44t114 23v95h152v-93q49 -3 97.5 -9.5t99.5 -17.5v-158q-73 14 -135 22t-127 8
q-45 0 -74.5 -2t-46.5 -8t-23.5 -16t-6.5 -26q0 -18 5 -29.5t21 -19t45.5 -12t78.5 -10.5q87 -9 146 -23.5t95 -40.5t51.5 -66.5t15.5 -102.5q0 -100 -55.5 -156t-186.5 -68v-94h-152v93q-60 3 -119 12t-108 23v156z" />
    <glyph glyph-name="dollar.lt.weight" 
d="M81 224q54 -11 111.5 -21t115.5 -15v145q-67 9 -113.5 24t-74.5 40t-40.5 62.5t-12.5 91.5q0 47 12 84.5t40.5 64.5t74.5 44t114 23v95h152v-93q49 -3 97.5 -9.5t99.5 -17.5v-158q-54 11 -101.5 18t-95.5 10v-129q68 -10 114 -25.5t74.5 -41.5t41 -64t12.5 -94
q0 -100 -55.5 -156t-186.5 -68v-94h-152v93q-60 3 -119 12t-108 23v156zM244 562q0 -27 12 -39t52 -20v108q-37 -5 -50.5 -15.5t-13.5 -33.5zM523 251q0 26 -11.5 40t-51.5 22v-124q38 5 50.5 19.5t12.5 42.5z" />
    <glyph glyph-name="Euro.lt" 
d="M36 366h92v55q0 9 1 19h-93v132h106q13 63 38.5 109t66 76.5t98 45.5t134.5 15q73 0 131 -7.5t97 -15.5v-155q-27 5 -52 9t-50.5 7t-54 4.5t-63.5 1.5q-69 0 -104.5 -18t-49.5 -72h319v-132h-333v-74h333v-132h-320q7 -28 19 -46t30.5 -28.5t44.5 -14.5t61 -4t63.5 1.5
t54 4.5t50.5 7t52 10v-156q-39 -8 -97 -16t-131 -8q-77 0 -135 15t-98.5 46t-66 78t-37.5 111h-106v132z" />
    <glyph glyph-name="cent.lt" 
d="M516 802v-100q40 -3 79.5 -8.5t79.5 -15.5v-157q-38 6 -70.5 10t-63 6t-60 3t-61.5 1q-45 0 -74 -6.5t-46 -22.5t-23.5 -43t-6.5 -67t6.5 -66.5t23.5 -42.5t46 -22.5t74 -6.5q33 0 64 0.5t62.5 3t65.5 6t73 9.5v-156q-43 -11 -84.5 -16t-84.5 -8v-103h-152v102
q-74 4 -128.5 22t-90 54t-53 91t-17.5 133t17.5 133.5t53 91.5t90 54.5t128.5 22.5v98h152z" />
    <glyph glyph-name="cent.lt.weight" 
d="M516 802v-99q45 -3 87.5 -8.5t87.5 -16.5v-157q-51 8 -93 12t-82 6v-274q42 2 86.5 5.5t98.5 12.5v-156q-47 -11 -92.5 -16.5t-92.5 -7.5v-103h-152v102q-78 4 -135.5 22t-95 53.5t-56 91t-18.5 133.5q0 79 18.5 134.5t56 91.5t95 54t135.5 22v98h152zM254 402
q0 -33 5 -57t17.5 -40.5t33.5 -25.5t54 -12v271q-33 -3 -54 -12t-33.5 -25.5t-17.5 -40.5t-5 -58z" />
    <glyph glyph-name="sterling.lt" 
d="M108 156q21 0 36 4.5t24 16.5t13 32.5t4 51.5v69h-137v147h137v97q0 62 16 107.5t50.5 76t89.5 45.5t132 15q69 0 124 -8t92 -15v-161q-43 10 -85 16t-99 6q-75 0 -105.5 -20.5t-30.5 -69.5v-89h294v-147h-294v-71q0 -63 -37 -103h378v-156h-662v156h60z" />
    <glyph glyph-name="yen.lt" 
d="M72 366h212v74h-212v132h116l-167 230h203l156 -219l157 219h202l-168 -230h117v-132h-213v-74h213v-132h-213v-234h-191v234h-212v132z" />
    <glyph glyph-name="florin.lt" 
d="M66 477h206v81q0 63 13.5 111.5t46 81.5t86 50t133.5 17q38 0 66 -1.5t61 -5.5v-150q-26 3 -50.5 3.5t-49.5 0.5q-34 0 -56 -4.5t-35.5 -15.5t-19 -29.5t-5.5 -47.5v-91h216v-147h-216v-284q0 -63 -13.5 -111.5t-45 -81.5t-84 -50t-130.5 -17q-36 0 -63 1.5t-60 5.5v150
q25 -3 49 -3.5t49 -0.5q33 0 54 4.5t33 15.5t16.5 30t4.5 47v294h-206v147z" />
    <glyph glyph-name="zeroslash.lt" 
d="M379 -16q-91 0 -156 25t-106.5 76.5t-61 130t-19.5 185.5t19.5 185.5t61 130t106.5 76.5t156 25q92 0 157.5 -25t107 -76.5t61 -130t19.5 -185.5t-19.5 -185.5t-61 -130t-107 -76.5t-157.5 -25zM231 401q0 -20 0.5 -37.5t1.5 -33.5l237 306q-17 12 -39.5 17t-51.5 5
q-43 0 -72 -12.5t-45.5 -42t-23.5 -79t-7 -123.5zM379 144q44 0 73 12.5t46 42t24 79t7 123.5q0 20 -0.5 37.5t-1.5 33.5l-237 -306q17 -12 38.5 -17t50.5 -5z" />
    <glyph glyph-name="zero.lt" 
d="M379 -16q-91 0 -156 25t-106.5 76.5t-61 130t-19.5 185.5t19.5 185.5t61 130t106.5 76.5t156 25q92 0 157.5 -25t107 -76.5t61 -130t19.5 -185.5t-19.5 -185.5t-61 -130t-107 -76.5t-157.5 -25zM379 144q44 0 73 12.5t46 42t24 79t7 123.5t-7 123.5t-24 79t-46 42
t-73 12.5q-43 0 -72 -12.5t-45.5 -42t-23.5 -79t-7 -123.5t7 -123.5t23.5 -79t45.5 -42t72 -12.5z" />
    <glyph glyph-name="one.lt" 
d="M86 157h237v455l-199 -107l-78 145l281 152h187v-645h206v-157h-634v157z" />
    <glyph glyph-name="two.lt" 
d="M365 818q90 0 154 -13t104.5 -40.5t59 -70t18.5 -102.5q0 -47 -10 -80t-31 -58.5t-54 -47t-80 -47.5l-212 -115q-16 -9 -26 -15.5t-15.5 -14t-7.5 -17.5t-2 -25v-15h437v-157h-629v174q0 39 7.5 68t25 52.5t46.5 44.5t72 43l223 119q18 10 29.5 17.5t19 15.5t10.5 19
t3 27q0 22 -6 36.5t-23.5 22.5t-48 11.5t-78.5 3.5q-63 0 -132 -8t-123 -18v163q54 11 118 19t151 8z" />
    <glyph glyph-name="three.lt" 
d="M64 167q32 -6 60.5 -10.5t59 -7.5t64 -5t74.5 -2q58 0 95.5 4.5t59 15t29.5 28t8 43.5q0 28 -6.5 46.5t-22 29.5t-41.5 15.5t-64 4.5h-224v157h224q66 0 94 17t28 71q0 26 -7.5 43t-26.5 27t-52 13.5t-84 3.5q-40 0 -73.5 -2t-64 -5t-59 -7.5t-60.5 -10.5v155
q45 10 112 18.5t152 8.5q93 0 160.5 -11t111 -36t64.5 -66t21 -102q0 -85 -33.5 -130.5t-94.5 -67.5q69 -20 105 -65t36 -130q0 -64 -22 -107.5t-68 -69.5t-118 -37.5t-171 -11.5q-85 0 -153 8.5t-113 18.5v156z" />
    <glyph glyph-name="four.lt" 
d="M699 456v-456h-188v153h-476v147l283 502h199l-278 -494h272v148h188z" />
    <glyph glyph-name="five.lt" 
d="M63 173q32 -6 61 -10.5t59.5 -8t64.5 -5t75 -1.5q58 0 95.5 5t59 16.5t29.5 30.5t8 47q0 27 -7.5 46t-25 30t-46.5 16t-73 5h-273v458h572v-158h-381v-142h114q87 0 147.5 -16t97.5 -47.5t53.5 -79.5t16.5 -111q0 -78 -22 -129t-68.5 -81t-118.5 -42t-171 -12
q-85 0 -153 8.5t-114 18.5v162z" />
    <glyph glyph-name="six.lt" 
d="M386 -16q-96 0 -162.5 24.5t-107.5 75.5t-59 130t-18 187q0 115 22 194.5t70 129t124 71.5t184 22q69 0 122 -8t89 -19v-162q-25 6 -47.5 10.5t-46.5 7.5t-50.5 5t-59.5 2q-58 0 -98 -6.5t-65 -25.5t-36 -53t-13 -89q42 16 89 26t108 10q78 0 131.5 -16.5t87 -49.5
t48.5 -81.5t15 -112.5q0 -75 -23 -126.5t-65.5 -84t-103 -47t-135.5 -14.5zM388 358q-45 0 -81.5 -7.5t-71.5 -19.5q2 -55 10 -91.5t25.5 -59t46.5 -32t73 -9.5q68 0 98.5 24.5t30.5 84.5q0 33 -6 54t-21 33.5t-40 17.5t-64 5z" />
    <glyph glyph-name="seven.lt" 
d="M46 802h668v-147l-385 -655h-220l393 644h-456v158z" />
    <glyph glyph-name="eight.lt" 
d="M380 -16q-173 0 -259.5 52t-86.5 174q0 84 36.5 130t102.5 65q-59 21 -92.5 67t-33.5 129q0 116 83 166.5t250 50.5q166 0 249.5 -50.5t83.5 -166.5q0 -83 -33.5 -129t-92.5 -67q65 -19 101.5 -65t36.5 -130q0 -122 -86 -174t-259 -52zM380 477q39 0 65.5 5t43 15.5
t23.5 28.5t7 43q0 24 -7 41t-22.5 28.5t-42 16.5t-67.5 5q-40 0 -67 -5t-42.5 -16.5t-22.5 -28.5t-7 -41q0 -25 7 -43t23.5 -28.5t43 -15.5t65.5 -5zM380 141q83 0 116.5 22.5t33.5 74.5q0 56 -35 78t-115 22q-81 0 -116 -21.5t-35 -78.5q0 -52 33.5 -74.5t117.5 -22.5z" />
    <glyph glyph-name="nine.lt" 
d="M366 818q97 0 163 -24.5t107 -75.5t59 -130t18 -187q0 -115 -22 -194.5t-70 -129t-124 -71.5t-184 -22q-69 0 -121.5 8t-89.5 19v162q26 -6 48.5 -10.5t46 -7.5t50 -5t59.5 -2q59 0 99 6.5t64.5 25.5t36 53t12.5 89q-42 -16 -89 -26t-108 -10q-78 0 -131.5 16.5t-87 49.5
t-48.5 81.5t-15 112.5q0 75 23 126.5t65.5 84t103 47t135.5 14.5zM233 554q0 -33 6 -54t21 -33.5t40.5 -17.5t63.5 -5q45 0 81.5 7.5t71.5 19.5q-2 55 -10 91.5t-25.5 59t-46 32t-72.5 9.5q-68 0 -99 -24t-31 -85z" />
    <glyph glyph-name="dollar.ot" 
d="M81 222q72 -17 149.5 -28t150.5 -11q45 0 73 2t43.5 7t21.5 13t6 21t-4 21t-19.5 14t-45.5 10.5t-82 10.5q-87 9 -146 21t-94.5 34t-51 56t-15.5 87q0 44 12 77.5t40.5 56.5t74.5 37t114 19v92h152v-91q49 -3 97.5 -9.5t99.5 -16.5v-152q-74 14 -138 22t-134 8
q-45 0 -73 -1.5t-43.5 -5t-21 -10t-5.5 -16.5q0 -11 4.5 -18t20 -12.5t45.5 -10t82 -10.5q87 -9 146 -21.5t95 -35t51.5 -57t15.5 -88.5q0 -91 -55.5 -140.5t-186.5 -60.5v-96h-152v95q-60 4 -119 12.5t-108 22.5v152z" />
    <glyph glyph-name="dollar.ot.weight" 
d="M81 222q54 -13 112 -22t115 -14v103q-67 9 -113.5 22t-74.5 35t-40.5 54t-12.5 80q0 44 12 77.5t40.5 56.5t74.5 37t114 19v92h152v-91q49 -3 97.5 -9.5t99.5 -16.5v-152q-54 11 -101.5 17.5t-95.5 10.5v-90q68 -9 114 -22.5t74.5 -35.5t41 -55t12.5 -81
q0 -91 -55.5 -140.5t-186.5 -60.5v-96h-152v95q-60 4 -119 12.5t-108 22.5v152zM242 490q0 -8 2 -14t9 -10.5t20 -8.5t35 -7v71q-41 -2 -53.5 -9.5t-12.5 -21.5zM525 226q0 10 -2 16.5t-8.5 12t-19.5 9t-35 7.5v-86q39 4 52 13t13 28z" />
    <glyph glyph-name="Euro.ot" 
d="M36 427h95q6 75 28 129.5t63.5 90.5t104.5 53.5t152 17.5q73 0 131 -7.5t97 -15.5v-155q-27 5 -52 9t-50.5 7t-54 4.5t-63.5 1.5q-46 0 -76.5 -5.5t-50 -20.5t-29.5 -41.5t-13 -67.5h334v-147h-335q3 -42 13 -69t29.5 -42.5t50.5 -21.5t77 -6q35 0 63.5 1.5t54 4.5
t50.5 7t52 10v-156q-39 -8 -97 -16t-131 -8q-90 0 -153 18t-104.5 54.5t-63 92t-27.5 131.5h-95v147z" />
    <glyph glyph-name="Euro.ot.weight" 
d="M36 320h92v49q0 9 1 17h-93v132h111q28 102 106 151t226 49q73 0 131 -7.5t97 -15.5v-155q-27 5 -52 9t-50.5 7t-54 4.5t-63.5 1.5q-57 0 -91.5 -9t-52.5 -35h309v-132h-337v-66h337v-131h-312q18 -28 52.5 -38t94.5 -10q35 0 63.5 1.5t54 4.5t50.5 7t52 10v-156
q-39 -8 -97 -16t-131 -8q-75 0 -131 12.5t-96.5 38t-66.5 64t-40 90.5h-109v131z" />
    <glyph glyph-name="cent.ot" 
d="M516 702v-78q45 -2 87.5 -8t87.5 -17v-156q-40 6 -75 10t-67 6.5t-63.5 3t-65.5 0.5q-50 0 -82 -5.5t-51 -18t-26 -34t-7 -53.5q0 -31 7 -52.5t26 -34t51 -18t82 -5.5q35 0 67.5 0.5t66 2.5t69.5 6t78 10v-156q-47 -11 -92.5 -17t-92.5 -8v-80h-152v79q-78 3 -135.5 19.5
t-95 49t-56 83t-18.5 121.5t18.5 121.5t56 83t95 49t135.5 19.5v77h152z" />
    <glyph glyph-name="cent.ot.weight" 
d="M516 702v-78q45 -2 87.5 -8t87.5 -17v-156q-51 8 -93 12t-82 6v-218q42 2 86.5 6t98.5 12v-156q-47 -11 -92.5 -17t-92.5 -8v-80h-152v79q-78 3 -135.5 19.5t-95 49t-56 83t-18.5 121.5t18.5 121.5t56 83t95 49t135.5 19.5v77h152zM254 352q0 -27 5 -46t17.5 -31.5
t33.5 -20t54 -10.5v217q-33 -3 -54 -10.5t-33.5 -20t-17.5 -32t-5 -46.5z" />
    <glyph glyph-name="sterling.ot" 
d="M108 152q21 0 36 4.5t24 16.5t13 32.5t4 51.5v23h-137v147h137v55q0 61 16 107t50.5 76.5t89.5 45.5t132 15q69 0 123.5 -8t92.5 -16v-161q-43 11 -85 17t-99 6q-74 0 -105 -20.5t-31 -69.5v-47h294v-147h-294v-25q0 -64 -41 -103h382v-152h-662v152h60z" />
    <glyph glyph-name="yen.ot" 
d="M72 320h212v66h-212v132h109l-160 184h203l156 -173l157 173h202l-161 -184h110v-132h-213v-66h213v-131h-213v-189h-191v189h-212v131z" />
    <glyph glyph-name="florin.ot" 
d="M66 427h206v35q0 63 13.5 112t46 82t86 50t133.5 17q38 0 66 -1.5t61 -5.5v-150q-26 3 -50.5 3.5t-49.5 0.5q-34 0 -56 -4.5t-35.5 -15.5t-19 -29.5t-5.5 -47.5v-46h216v-147h-216v-234q0 -63 -13.5 -111.5t-45 -81.5t-84 -50t-130.5 -17q-36 0 -63 1.5t-60 5.5v150
q25 -3 49 -3.5t49 -0.5q33 0 54 4.5t33 15.5t16.5 30t4.5 47v244h-206v147z" />
    <glyph glyph-name="zeroslash.ot" 
d="M379 -16q-91 0 -156 22t-106.5 67.5t-61 114.5t-19.5 163t19.5 163t61 114.5t106.5 67.5t156 22q92 0 157 -22t107 -67.5t61.5 -114.5t19.5 -163t-19.5 -163t-61 -114.5t-107 -67.5t-157.5 -22zM231 351v-26.5t1 -24.5l227 248q-31 12 -80 12q-43 0 -72 -10t-45.5 -34
t-23.5 -64.5t-7 -100.5zM379 142q44 0 73 10t46 34t24 64.5t7 100.5v26.5t-1 24.5l-227 -248q32 -12 78 -12z" />
    <glyph glyph-name="zero.ot" 
d="M379 -16q-91 0 -156 22t-106.5 67.5t-61 114.5t-19.5 163t19.5 163t61 114.5t106.5 67.5t156 22q92 0 157 -22t107 -67.5t61.5 -114.5t19.5 -163t-19.5 -163t-61 -114.5t-107 -67.5t-157.5 -22zM379 142q44 0 73 10t46 34t24 64.5t7 100.5t-7 100.5t-24 64.5t-46 34
t-73 10q-43 0 -72 -10t-45.5 -34t-23.5 -64.5t-7 -100.5t7 -100.5t23.5 -64.5t45.5 -34t72 -10z" />
    <glyph glyph-name="one.ot" 
d="M86 151h237v361l-199 -107l-78 145l281 152h187v-551h206v-151h-634v151z" />
    <glyph glyph-name="two.ot" 
d="M363 718q89 0 152 -10.5t103.5 -35t59.5 -64.5t19 -99q0 -44 -8 -77.5t-28.5 -59t-56 -44.5t-90.5 -35l-211 -63q-30 -10 -39 -21.5t-9 -38.5v-19h442v-151h-634v159q0 39 6.5 71t23.5 57.5t46 44.5t75 32l232 66q38 13 47.5 28t9.5 40q0 17 -6.5 28t-22 17.5t-43 9
t-69.5 2.5q-75 0 -149 -8t-131 -18v162q57 11 124 19t157 8z" />
    <glyph glyph-name="three.ot" 
d="M64 67q32 -6 60.5 -10.5t59 -7.5t64 -5t74.5 -2q58 0 95.5 4.5t59 15t29.5 28t8 43.5q0 28 -6.5 46.5t-22 29.5t-41.5 15.5t-64 4.5h-224v157h224q66 0 94 17t28 71q0 26 -7.5 43t-26.5 27t-52 13.5t-84 3.5q-40 0 -73.5 -2t-64 -5t-59 -7.5t-60.5 -10.5v155
q45 10 112 18.5t152 8.5q93 0 160.5 -11t111 -36t64.5 -66t21 -102q0 -85 -33.5 -130.5t-94.5 -67.5q69 -20 105 -65t36 -130q0 -64 -22 -107.5t-68 -69.5t-118 -37.5t-171 -11.5q-85 0 -153 8.5t-113 18.5v156z" />
    <glyph glyph-name="four.ot" 
d="M699 356v-456h-188v153h-476v147l283 502h199l-278 -494h272v148h188z" />
    <glyph glyph-name="five.ot" 
d="M63 73q32 -6 61 -10.5t59.5 -8t64.5 -5t75 -1.5q58 0 95.5 5t59 16.5t29.5 30.5t8 47q0 27 -7.5 46t-25 30t-46.5 16t-73 5h-273v458h572v-158h-381v-142h114q87 0 147.5 -16t97.5 -47.5t53.5 -79.5t16.5 -111q0 -78 -22 -129t-68.5 -81t-118.5 -42t-171 -12
q-85 0 -153 8.5t-114 18.5v162z" />
    <glyph glyph-name="six.ot" 
d="M386 -16q-96 0 -162.5 24.5t-107.5 75.5t-59 130t-18 187q0 115 22 194.5t70 129t124 71.5t184 22q69 0 122 -8t89 -19v-162q-25 6 -47.5 10.5t-46.5 7.5t-50.5 5t-59.5 2q-58 0 -98 -6.5t-65 -25.5t-36 -53t-13 -89q42 16 89 26t108 10q78 0 131.5 -16.5t87 -49.5
t48.5 -81.5t15 -112.5q0 -75 -23 -126.5t-65.5 -84t-103 -47t-135.5 -14.5zM388 358q-45 0 -81.5 -7.5t-71.5 -19.5q2 -55 10 -91.5t25.5 -59t46.5 -32t73 -9.5q68 0 98.5 24.5t30.5 84.5q0 33 -6 54t-21 33.5t-40 17.5t-64 5z" />
    <glyph glyph-name="seven.ot" 
d="M46 702h668v-147l-385 -655h-220l393 644h-456v158z" />
    <glyph glyph-name="eight.ot" 
d="M380 -16q-173 0 -259.5 52t-86.5 174q0 84 36.5 130t102.5 65q-59 21 -92.5 67t-33.5 129q0 116 83 166.5t250 50.5q166 0 249.5 -50.5t83.5 -166.5q0 -83 -33.5 -129t-92.5 -67q65 -19 101.5 -65t36.5 -130q0 -122 -86 -174t-259 -52zM380 477q39 0 65.5 5t43 15.5
t23.5 28.5t7 43q0 24 -7 41t-22.5 28.5t-42 16.5t-67.5 5q-40 0 -67 -5t-42.5 -16.5t-22.5 -28.5t-7 -41q0 -25 7 -43t23.5 -28.5t43 -15.5t65.5 -5zM380 141q83 0 116.5 22.5t33.5 74.5q0 56 -35 78t-115 22q-81 0 -116 -21.5t-35 -78.5q0 -52 33.5 -74.5t117.5 -22.5z" />
    <glyph glyph-name="nine.ot" 
d="M366 718q97 0 163 -24.5t107 -75.5t59 -130t18 -187q0 -115 -22 -194.5t-70 -129t-124 -71.5t-184 -22q-69 0 -121.5 8t-89.5 19v162q26 -6 48.5 -10.5t46 -7.5t50 -5t59.5 -2q59 0 99 6.5t64.5 25.5t36 53t12.5 89q-42 -16 -89 -26t-108 -10q-78 0 -131.5 16.5t-87 49.5
t-48.5 81.5t-15 112.5q0 75 23 126.5t65.5 84t103 47t135.5 14.5zM233 454q0 -33 6 -54t21 -33.5t40.5 -17.5t63.5 -5q45 0 81.5 7.5t71.5 19.5q-2 55 -10 91.5t-25.5 59t-46 32t-72.5 9.5q-68 0 -99 -24t-31 -85z" />
    <glyph glyph-name="dollar.op" horiz-adv-x="752" 
d="M77 222q72 -17 149.5 -28t150.5 -11q45 0 73 2t43.5 7t21.5 13t6 21t-4 21t-19.5 14t-45.5 10.5t-82 10.5q-87 9 -146 21t-94.5 34t-51 56t-15.5 87q0 44 12 77.5t40.5 56.5t74.5 37t114 19v92h152v-91q49 -3 97.5 -9.5t99.5 -16.5v-152q-74 14 -138 22t-134 8
q-45 0 -73 -1.5t-43.5 -5t-21 -10t-5.5 -16.5q0 -11 4.5 -18t20 -12.5t45.5 -10t82 -10.5q87 -9 146 -21.5t95 -35t51.5 -57t15.5 -88.5q0 -91 -55.5 -140.5t-186.5 -60.5v-96h-152v95q-60 4 -119 12.5t-108 22.5v152z" />
    <glyph glyph-name="dollar.op.weight" horiz-adv-x="752" 
d="M77 222q54 -13 112 -22t115 -14v103q-67 9 -113.5 22t-74.5 35t-40.5 54t-12.5 80q0 44 12 77.5t40.5 56.5t74.5 37t114 19v92h152v-91q49 -3 97.5 -9.5t99.5 -16.5v-152q-54 11 -101.5 17.5t-95.5 10.5v-90q68 -9 114 -22.5t74.5 -35.5t41 -55t12.5 -81
q0 -91 -55.5 -140.5t-186.5 -60.5v-96h-152v95q-60 4 -119 12.5t-108 22.5v152zM238 490q0 -8 2 -14t9 -10.5t20 -8.5t35 -7v71q-41 -2 -53.5 -9.5t-12.5 -21.5zM521 226q0 10 -2 16.5t-8.5 12t-19.5 9t-35 7.5v-86q39 4 52 13t13 28z" />
    <glyph glyph-name="Euro.op" horiz-adv-x="752" 
d="M32 427h95q6 75 28 129.5t63.5 90.5t104.5 53.5t152 17.5q73 0 131 -7.5t97 -15.5v-155q-27 5 -52 9t-50.5 7t-54 4.5t-63.5 1.5q-46 0 -76.5 -5.5t-50 -20.5t-29.5 -41.5t-13 -67.5h334v-147h-335q3 -42 13 -69t29.5 -42.5t50.5 -21.5t77 -6q35 0 63.5 1.5t54 4.5
t50.5 7t52 10v-156q-39 -8 -97 -16t-131 -8q-90 0 -153 18t-104.5 54.5t-63 92t-27.5 131.5h-95v147z" />
    <glyph glyph-name="Euro.op.weight" horiz-adv-x="752" 
d="M32 320h92v49q0 9 1 17h-93v132h111q28 102 106 151t226 49q73 0 131 -7.5t97 -15.5v-155q-27 5 -52 9t-50.5 7t-54 4.5t-63.5 1.5q-57 0 -91.5 -9t-52.5 -35h309v-132h-337v-66h337v-131h-312q18 -28 52.5 -38t94.5 -10q35 0 63.5 1.5t54 4.5t50.5 7t52 10v-156
q-39 -8 -97 -16t-131 -8q-75 0 -131 12.5t-96.5 38t-66.5 64t-40 90.5h-109v131z" />
    <glyph glyph-name="cent.op" horiz-adv-x="752" 
d="M512 702v-78q45 -2 87.5 -8t87.5 -17v-156q-40 6 -75 10t-67 6.5t-63.5 3t-65.5 0.5q-50 0 -82 -5.5t-51 -18t-26 -34t-7 -53.5q0 -31 7 -52.5t26 -34t51 -18t82 -5.5q35 0 67.5 0.5t66 2.5t69.5 6t78 10v-156q-47 -11 -92.5 -17t-92.5 -8v-80h-152v79q-78 3 -135.5 19.5
t-95 49t-56 83t-18.5 121.5t18.5 121.5t56 83t95 49t135.5 19.5v77h152z" />
    <glyph glyph-name="cent.op.weight" horiz-adv-x="752" 
d="M512 702v-78q45 -2 87.5 -8t87.5 -17v-156q-51 8 -93 12t-82 6v-218q42 2 86.5 6t98.5 12v-156q-47 -11 -92.5 -17t-92.5 -8v-80h-152v79q-78 3 -135.5 19.5t-95 49t-56 83t-18.5 121.5t18.5 121.5t56 83t95 49t135.5 19.5v77h152zM250 352q0 -27 5 -46t17.5 -31.5
t33.5 -20t54 -10.5v217q-33 -3 -54 -10.5t-33.5 -20t-17.5 -32t-5 -46.5z" />
    <glyph glyph-name="sterling.op" horiz-adv-x="752" 
d="M104 152q21 0 36 4.5t24 16.5t13 32.5t4 51.5v23h-137v147h137v55q0 61 16 107t50.5 76.5t89.5 45.5t132 15q69 0 123.5 -8t92.5 -16v-161q-43 11 -85 17t-99 6q-74 0 -105 -20.5t-31 -69.5v-47h294v-147h-294v-25q0 -64 -41 -103h382v-152h-662v152h60z" />
    <glyph glyph-name="yen.op" horiz-adv-x="752" 
d="M68 320h212v66h-212v132h109l-160 184h203l156 -173l157 173h202l-161 -184h110v-132h-213v-66h213v-131h-213v-189h-191v189h-212v131z" />
    <glyph glyph-name="florin.op" horiz-adv-x="752" 
d="M62 427h206v35q0 63 13.5 112t46 82t86 50t133.5 17q38 0 66 -1.5t61 -5.5v-150q-26 3 -50.5 3.5t-49.5 0.5q-34 0 -56 -4.5t-35.5 -15.5t-19 -29.5t-5.5 -47.5v-46h216v-147h-216v-234q0 -63 -13.5 -111.5t-45 -81.5t-84 -50t-130.5 -17q-36 0 -63 1.5t-60 5.5v150
q25 -3 49 -3.5t49 -0.5q33 0 54 4.5t33 15.5t16.5 30t4.5 47v244h-206v147z" />
    <glyph glyph-name="zeroslash.op" horiz-adv-x="752" 
d="M375 -16q-91 0 -156 22t-106.5 67.5t-61 114.5t-19.5 163t19.5 163t61 114.5t106.5 67.5t156 22q92 0 157 -22t107 -67.5t61.5 -114.5t19.5 -163t-19.5 -163t-61 -114.5t-107 -67.5t-157.5 -22zM227 351v-26.5t1 -24.5l227 248q-31 12 -80 12q-43 0 -72 -10t-45.5 -34
t-23.5 -64.5t-7 -100.5zM375 142q44 0 73 10t46 34t24 64.5t7 100.5v26.5t-1 24.5l-227 -248q32 -12 78 -12z" />
    <glyph glyph-name="zero.op" horiz-adv-x="752" 
d="M375 -16q-91 0 -156 22t-106.5 67.5t-61 114.5t-19.5 163t19.5 163t61 114.5t106.5 67.5t156 22q92 0 157 -22t107 -67.5t61.5 -114.5t19.5 -163t-19.5 -163t-61 -114.5t-107 -67.5t-157.5 -22zM375 142q44 0 73 10t46 34t24 64.5t7 100.5t-7 100.5t-24 64.5t-46 34
t-73 10q-43 0 -72 -10t-45.5 -34t-23.5 -64.5t-7 -100.5t7 -100.5t23.5 -64.5t45.5 -34t72 -10z" />
    <glyph glyph-name="one.op" horiz-adv-x="613" 
d="M330 702h187v-702h-191v512l-227 -111l-81 149z" />
    <glyph glyph-name="two.op" horiz-adv-x="752" 
d="M359 718q89 0 152 -10.5t103.5 -35t59.5 -64.5t19 -99q0 -44 -8 -77.5t-28.5 -59t-56 -44.5t-90.5 -35l-211 -63q-30 -10 -39 -21.5t-9 -38.5v-19h442v-151h-634v159q0 39 6.5 71t23.5 57.5t46 44.5t75 32l232 66q38 13 47.5 28t9.5 40q0 17 -6.5 28t-22 17.5t-43 9
t-69.5 2.5q-75 0 -149 -8t-131 -18v162q57 11 124 19t157 8z" />
    <glyph glyph-name="three.op" horiz-adv-x="752" 
d="M60 67q32 -6 60.5 -10.5t59 -7.5t64 -5t74.5 -2q58 0 95.5 4.5t59 15t29.5 28t8 43.5q0 28 -6.5 46.5t-22 29.5t-41.5 15.5t-64 4.5h-224v157h224q66 0 94 17t28 71q0 26 -7.5 43t-26.5 27t-52 13.5t-84 3.5q-40 0 -73.5 -2t-64 -5t-59 -7.5t-60.5 -10.5v155
q45 10 112 18.5t152 8.5q93 0 160.5 -11t111 -36t64.5 -66t21 -102q0 -85 -33.5 -130.5t-94.5 -67.5q69 -20 105 -65t36 -130q0 -64 -22 -107.5t-68 -69.5t-118 -37.5t-171 -11.5q-85 0 -153 8.5t-113 18.5v156z" />
    <glyph glyph-name="four.op" horiz-adv-x="752" 
d="M695 356v-456h-188v153h-476v147l283 502h199l-278 -494h272v148h188z" />
    <glyph glyph-name="five.op" horiz-adv-x="752" 
d="M59 73q32 -6 61 -10.5t59.5 -8t64.5 -5t75 -1.5q58 0 95.5 5t59 16.5t29.5 30.5t8 47q0 27 -7.5 46t-25 30t-46.5 16t-73 5h-273v458h572v-158h-381v-142h114q87 0 147.5 -16t97.5 -47.5t53.5 -79.5t16.5 -111q0 -78 -22 -129t-68.5 -81t-118.5 -42t-171 -12
q-85 0 -153 8.5t-114 18.5v162z" />
    <glyph glyph-name="six.op" horiz-adv-x="752" 
d="M382 -16q-96 0 -162.5 24.5t-107.5 75.5t-59 130t-18 187q0 115 22 194.5t70 129t124 71.5t184 22q69 0 122 -8t89 -19v-162q-25 6 -47.5 10.5t-46.5 7.5t-50.5 5t-59.5 2q-58 0 -98 -6.5t-65 -25.5t-36 -53t-13 -89q42 16 89 26t108 10q78 0 131.5 -16.5t87 -49.5
t48.5 -81.5t15 -112.5q0 -75 -23 -126.5t-65.5 -84t-103 -47t-135.5 -14.5zM384 358q-45 0 -81.5 -7.5t-71.5 -19.5q2 -55 10 -91.5t25.5 -59t46.5 -32t73 -9.5q68 0 98.5 24.5t30.5 84.5q0 33 -6 54t-21 33.5t-40 17.5t-64 5z" />
    <glyph glyph-name="seven.op" horiz-adv-x="752" 
d="M42 702h668v-147l-385 -655h-220l393 644h-456v158z" />
    <glyph glyph-name="eight.op" horiz-adv-x="752" 
d="M376 -16q-173 0 -259.5 52t-86.5 174q0 84 36.5 130t102.5 65q-59 21 -92.5 67t-33.5 129q0 116 83 166.5t250 50.5q166 0 249.5 -50.5t83.5 -166.5q0 -83 -33.5 -129t-92.5 -67q65 -19 101.5 -65t36.5 -130q0 -122 -86 -174t-259 -52zM376 477q39 0 65.5 5t43 15.5
t23.5 28.5t7 43q0 24 -7 41t-22.5 28.5t-42 16.5t-67.5 5q-40 0 -67 -5t-42.5 -16.5t-22.5 -28.5t-7 -41q0 -25 7 -43t23.5 -28.5t43 -15.5t65.5 -5zM376 141q83 0 116.5 22.5t33.5 74.5q0 56 -35 78t-115 22q-81 0 -116 -21.5t-35 -78.5q0 -52 33.5 -74.5t117.5 -22.5z" />
    <glyph glyph-name="nine.op" horiz-adv-x="752" 
d="M364 718q97 0 163 -24.5t107 -75.5t59 -130t18 -187q0 -115 -22 -194.5t-70 -129t-124 -71.5t-184 -22q-69 0 -121.5 8t-89.5 19v162q26 -6 48.5 -10.5t46 -7.5t50 -5t59.5 -2q59 0 99 6.5t64.5 25.5t36 53t12.5 89q-42 -16 -89 -26t-108 -10q-78 0 -131.5 16.5t-87 49.5
t-48.5 81.5t-15 112.5q0 75 23 126.5t65.5 84t103 47t135.5 14.5zM231 454q0 -33 6 -54t21 -33.5t40.5 -17.5t63.5 -5q45 0 81.5 7.5t71.5 19.5q-2 55 -10 91.5t-25.5 59t-46 32t-72.5 9.5q-68 0 -99 -24t-31 -85z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="436" 
d="M218 333q-109 0 -157 59t-48 180t48 180t157 59q110 0 158.5 -59.5t48.5 -179.5t-48.5 -179.5t-158.5 -59.5zM218 447q20 0 31.5 5.5t18.5 19.5t9 38.5t2 61.5q0 38 -2 62t-9 38t-18.5 19.5t-31.5 5.5q-19 0 -30.5 -5.5t-18 -19.5t-9 -38t-2.5 -62q0 -37 2.5 -61.5
t9 -38.5t18 -19.5t30.5 -5.5z" />
    <glyph glyph-name="one.numr" horiz-adv-x="365" 
d="M185 802h141v-460h-143v322l-118 -59l-59 108z" />
    <glyph glyph-name="two.numr" horiz-adv-x="436" 
d="M210 811q107 0 155.5 -31.5t48.5 -106.5q0 -28 -6 -47.5t-19 -34.5t-32.5 -27.5t-46.5 -26.5l-117 -60q-11 -6 -14 -9.5t-3 -12.5h234v-113h-378v112q0 23 5 39.5t16 30t29 25.5t43 25l123 63q12 7 16.5 12t4.5 17q0 8 -2.5 13t-10 8t-21 4t-36.5 1q-35 0 -77.5 -5
t-74.5 -10v118q33 6 71.5 11t91.5 5z" />
    <glyph glyph-name="three.numr" horiz-adv-x="436" 
d="M29 465q39 -7 74 -11t77 -4q57 0 75 6.5t18 27.5t-10.5 28.5t-42.5 7.5h-139v112h139q26 0 37 6t11 25q0 20 -16.5 26t-66.5 6q-46 0 -79 -4t-72 -11v115q24 5 65 10.5t92 5.5q57 0 98.5 -5.5t68.5 -19.5t40.5 -38t13.5 -60q0 -48 -19.5 -74t-54.5 -40q39 -12 59.5 -37.5
t20.5 -72.5q0 -39 -14 -64.5t-42 -40t-71 -20.5t-100 -6q-51 0 -94 5.5t-68 10.5v116z" />
    <glyph glyph-name="four.numr" horiz-adv-x="436" 
d="M418 604v-262h-140v77h-262v110l154 273h149l-154 -272h113v74h140z" />
    <glyph glyph-name="five.numr" horiz-adv-x="436" 
d="M29 467q39 -8 74 -12t77 -4q30 0 48.5 1.5t29.5 6t15 12t4 19.5q0 22 -13.5 29.5t-55.5 7.5h-166v275h348v-113h-206v-49h49q53 0 89.5 -10t58.5 -28.5t31.5 -46.5t9.5 -64q0 -49 -14 -79.5t-43.5 -48t-74 -24t-105.5 -6.5q-50 0 -90.5 5.5t-65.5 10.5v118z" />
    <glyph glyph-name="six.numr" horiz-adv-x="436" 
d="M220 333q-57 0 -97 15t-64.5 45t-35.5 74.5t-11 104.5q0 66 13.5 112t42.5 74t75 40.5t112 12.5q42 0 75 -5t53 -11v-118q-32 8 -60.5 12t-65.5 4q-27 0 -45.5 -2.5t-30 -10t-17 -20t-6.5 -33.5q20 7 43.5 11t52.5 4q92 0 129 -38t37 -109q0 -88 -54 -125t-146 -37z
M220 535q-17 0 -32 -2.5t-29 -7.5q2 -45 14.5 -62.5t49.5 -17.5q29 0 40.5 10.5t11.5 34.5q0 26 -12 35.5t-43 9.5z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="436" 
d="M19 802h400v-110l-207 -350h-172l219 347h-240v113z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="436" 
d="M219 333q-101 0 -155 29t-54 102q0 46 20 72t57 38q-33 14 -52 40t-19 73q0 68 52.5 96t150.5 28q97 0 149.5 -28t52.5 -96q0 -47 -19 -73t-52 -40q37 -12 57 -38t20 -72q0 -73 -53.5 -102t-154.5 -29zM219 624q34 0 46 8.5t12 28.5t-12 28.5t-46 8.5t-46 -8.5t-12 -28.5
t12.5 -28.5t45.5 -8.5zM219 445q34 0 48.5 9t14.5 33t-14.5 33t-48.5 9q-35 0 -49.5 -9t-14.5 -33t14.5 -33t49.5 -9z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="436" 
d="M212 811q57 0 97 -15t64.5 -45t35.5 -74.5t11 -104.5q0 -66 -13 -112t-42.5 -74t-75.5 -40.5t-112 -12.5q-42 0 -74.5 5t-53.5 11v118q32 -8 61 -12t65 -4q27 0 46 2.5t30.5 10t17 20.5t6.5 34q-20 -8 -43.5 -12t-52.5 -4q-46 0 -78 10t-51.5 28.5t-28 46t-8.5 62.5
q0 88 53.5 125t145.5 37zM157 654q0 -26 12 -35.5t43 -9.5q17 0 32.5 2.5t29.5 7.5q-2 45 -15 62.5t-50 17.5q-28 0 -40 -10.5t-12 -34.5z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="438" 
d="M218 -9q-109 0 -157 59t-48 180t48 180t157 59q110 0 158.5 -59.5t48.5 -179.5t-48.5 -179.5t-158.5 -59.5zM218 105q20 0 31.5 5.5t18.5 19.5t9 38.5t2 61.5q0 38 -2 62t-9 38t-18.5 19.5t-31.5 5.5q-19 0 -30.5 -5.5t-18 -19.5t-9 -38t-2.5 -62q0 -37 2.5 -61.5
t9 -38.5t18 -19.5t30.5 -5.5z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="368" 
d="M185 460h141v-460h-143v322l-118 -59l-59 108z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="438" 
d="M210 469q107 0 155.5 -31.5t48.5 -106.5q0 -28 -6 -47.5t-19 -34.5t-32.5 -27.5t-46.5 -26.5l-117 -60q-11 -6 -14 -9.5t-3 -12.5h234v-113h-378v112q0 23 5 39.5t16 30t29 25.5t43 25l123 63q12 7 16.5 12t4.5 17q0 8 -2.5 13t-10 8t-21 4t-36.5 1q-35 0 -77.5 -5
t-74.5 -10v118q33 6 71.5 11t91.5 5z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="438" 
d="M29 123q39 -7 74 -11t77 -4q57 0 75 6.5t18 27.5t-10.5 28.5t-42.5 7.5h-139v112h139q26 0 37 6t11 25q0 20 -16.5 26t-66.5 6q-46 0 -79 -4t-72 -11v115q24 5 65 10.5t92 5.5q57 0 98.5 -5.5t68.5 -19.5t40.5 -38t13.5 -60q0 -48 -19.5 -74t-54.5 -40q39 -12 59.5 -37.5
t20.5 -72.5q0 -39 -14 -64.5t-42 -40t-71 -20.5t-100 -6q-51 0 -94 5.5t-68 10.5v116z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="438" 
d="M418 262v-262h-140v77h-262v110l154 273h149l-154 -272h113v74h140z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="438" 
d="M29 125q39 -8 74 -12t77 -4q30 0 48.5 1.5t29.5 6t15 12t4 19.5q0 22 -13.5 29.5t-55.5 7.5h-166v275h348v-113h-206v-49h49q53 0 89.5 -10t58.5 -28.5t31.5 -46.5t9.5 -64q0 -49 -14 -79.5t-43.5 -48t-74 -24t-105.5 -6.5q-50 0 -90.5 5.5t-65.5 10.5v118z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="438" 
d="M220 -9q-57 0 -97 15t-64.5 45t-35.5 74.5t-11 104.5q0 66 13.5 112t42.5 74t75 40.5t112 12.5q42 0 75 -5t53 -11v-118q-32 8 -60.5 12t-65.5 4q-27 0 -45.5 -2.5t-30 -10t-17 -20t-6.5 -33.5q20 7 43.5 11t52.5 4q92 0 129 -38t37 -109q0 -88 -54 -125t-146 -37z
M220 193q-17 0 -32 -2.5t-29 -7.5q2 -45 14.5 -62.5t49.5 -17.5q29 0 40.5 10.5t11.5 34.5q0 26 -12 35.5t-43 9.5z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="438" 
d="M19 460h400v-110l-207 -350h-172l219 347h-240v113z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="438" 
d="M219 -9q-101 0 -155 29t-54 102q0 46 20 72t57 38q-33 14 -52 40t-19 73q0 68 52.5 96t150.5 28q97 0 149.5 -28t52.5 -96q0 -47 -19 -73t-52 -40q37 -12 57 -38t20 -72q0 -73 -53.5 -102t-154.5 -29zM219 282q34 0 46 8.5t12 28.5t-12 28.5t-46 8.5t-46 -8.5t-12 -28.5
t12.5 -28.5t45.5 -8.5zM219 103q34 0 48.5 9t14.5 33t-14.5 33t-48.5 9q-35 0 -49.5 -9t-14.5 -33t14.5 -33t49.5 -9z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="438" 
d="M212 469q57 0 97 -15t64.5 -45t35.5 -74.5t11 -104.5q0 -66 -13 -112t-42.5 -74t-75.5 -40.5t-112 -12.5q-42 0 -74.5 5t-53.5 11v118q32 -8 61 -12t65 -4q27 0 46 2.5t30.5 10t17 20.5t6.5 34q-20 -8 -43.5 -12t-52.5 -4q-46 0 -78 10t-51.5 28.5t-28 46t-8.5 62.5
q0 88 53.5 125t145.5 37zM157 312q0 -26 12 -35.5t43 -9.5q17 0 32.5 2.5t29.5 7.5q-2 45 -15 62.5t-50 17.5q-28 0 -40 -10.5t-12 -34.5z" />
    <hkern u1="&#x23;" g2="nine.op" k="10" />
    <hkern u1="&#x23;" g2="four.op" k="80" />
    <hkern u1="&#x23;" u2="&#x37;" k="-30" />
    <hkern u1="&#x23;" u2="&#x34;" k="60" />
    <hkern u1="&#x28;" u2="&#x237;" k="-50" />
    <hkern u1="&#x28;" u2="j" k="-50" />
    <hkern u1="&#x2a;" u2="X" k="44" />
    <hkern u1="&#x2a;" u2="&#x2f;" k="110" />
    <hkern u1="&#x2f;" u2="&#xdf;" k="20" />
    <hkern u1="&#x2f;" u2="x" k="20" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="120" />
    <hkern u1="&#x31;" u2="&#x20ac;" k="8" />
    <hkern u1="&#x31;" u2="&#x192;" k="20" />
    <hkern u1="&#x31;" u2="&#xb0;" k="20" />
    <hkern u1="&#x31;" u2="&#x33;" k="20" />
    <hkern u1="&#x31;" u2="&#x31;" k="60" />
    <hkern u1="&#x32;" u2="&#x192;" k="60" />
    <hkern u1="&#x32;" u2="&#xa5;" k="16" />
    <hkern u1="&#x32;" u2="&#x37;" k="10" />
    <hkern u1="&#x32;" u2="&#x34;" k="10" />
    <hkern u1="&#x32;" u2="&#x33;" k="4" />
    <hkern u1="&#x32;" u2="&#x32;" k="10" />
    <hkern u1="&#x32;" u2="&#x31;" k="10" />
    <hkern u1="&#x33;" u2="&#x3e;" k="30" />
    <hkern u1="&#x33;" u2="&#x39;" k="10" />
    <hkern u1="&#x33;" u2="&#x37;" k="10" />
    <hkern u1="&#x33;" u2="&#x34;" k="-8" />
    <hkern u1="&#x33;" u2="&#x33;" k="20" />
    <hkern u1="&#x33;" u2="&#x32;" k="10" />
    <hkern u1="&#x33;" u2="&#x31;" k="20" />
    <hkern u1="&#x34;" u2="&#x37;" k="4" />
    <hkern u1="&#x34;" u2="&#x33;" k="8" />
    <hkern u1="&#x34;" u2="&#x31;" k="108" />
    <hkern u1="&#x35;" u2="&#xb0;" k="30" />
    <hkern u1="&#x35;" u2="&#x39;" k="10" />
    <hkern u1="&#x35;" u2="&#x37;" k="30" />
    <hkern u1="&#x35;" u2="&#x35;" k="10" />
    <hkern u1="&#x35;" u2="&#x33;" k="30" />
    <hkern u1="&#x35;" u2="&#x32;" k="20" />
    <hkern u1="&#x35;" u2="&#x31;" k="70" />
    <hkern u1="&#x37;" u2="&#x192;" k="70" />
    <hkern u1="&#x37;" u2="&#xb0;" k="-20" />
    <hkern u1="&#x37;" u2="&#xa3;" k="50" />
    <hkern u1="&#x37;" u2="&#x3e;" k="90" />
    <hkern u1="&#x37;" u2="&#x3c;" k="140" />
    <hkern u1="&#x37;" u2="&#x35;" k="20" />
    <hkern u1="&#x37;" u2="&#x34;" k="100" />
    <hkern u1="&#x37;" u2="&#x33;" k="10" />
    <hkern u1="&#x37;" u2="&#x32;" k="40" />
    <hkern u1="&#x37;" u2="&#x23;" k="90" />
    <hkern u1="&#x3c;" g2="seven.op" k="10" />
    <hkern u1="&#x3c;" u2="&#x37;" k="40" />
    <hkern u1="&#x3c;" u2="&#x31;" k="90" />
    <hkern u1="&#x3e;" g2="seven.op" k="110" />
    <hkern u1="&#x3e;" u2="&#x37;" k="100" />
    <hkern u1="&#x3f;" u2="X" k="30" />
    <hkern u1="&#x40;" u2="X" k="20" />
    <hkern u1="&#x40;" u2="V" k="50" />
    <hkern u1="B" g2="bullet.case" k="10" />
    <hkern u1="B" u2="&#x2122;" k="40" />
    <hkern u1="B" u2="&#xae;" k="26" />
    <hkern u1="B" u2="v" k="18" />
    <hkern u1="B" u2="X" k="20" />
    <hkern u1="B" u2="V" k="30" />
    <hkern u1="F" u2="&#xbf;" k="200" />
    <hkern u1="F" u2="&#xb0;" k="-20" />
    <hkern u1="F" u2="x" k="20" />
    <hkern u1="F" u2="v" k="10" />
    <hkern u1="F" u2="&#x3f;" k="-40" />
    <hkern u1="F" u2="&#x2f;" k="118" />
    <hkern u1="P" g2="at.case" k="-20" />
    <hkern u1="P" g2="bullet.case" k="-20" />
    <hkern u1="P" u2="v" k="-16" />
    <hkern u1="P" u2="X" k="58" />
    <hkern u1="P" u2="&#x2f;" k="80" />
    <hkern u1="P" u2="&#x2a;" k="-40" />
    <hkern u1="V" g2="at.case" k="40" />
    <hkern u1="V" g2="bullet.case" k="60" />
    <hkern u1="V" g2="questiondown.case" k="96" />
    <hkern u1="V" u2="&#x2022;" k="70" />
    <hkern u1="V" u2="&#xdf;" k="30" />
    <hkern u1="V" u2="&#xbf;" k="140" />
    <hkern u1="V" u2="&#xa9;" k="30" />
    <hkern u1="V" u2="&#xa1;" k="50" />
    <hkern u1="V" u2="x" k="40" />
    <hkern u1="V" u2="v" k="40" />
    <hkern u1="V" u2="V" k="-16" />
    <hkern u1="V" u2="&#x40;" k="50" />
    <hkern u1="V" u2="&#x2f;" k="80" />
    <hkern u1="V" u2="&#x23;" k="94" />
    <hkern u1="X" g2="at.case" k="70" />
    <hkern u1="X" g2="bullet.case" k="110" />
    <hkern u1="X" u2="&#x2022;" k="40" />
    <hkern u1="X" u2="&#xae;" k="18" />
    <hkern u1="X" u2="&#xa9;" k="40" />
    <hkern u1="X" u2="x" k="-8" />
    <hkern u1="X" u2="v" k="50" />
    <hkern u1="X" u2="&#x40;" k="20" />
    <hkern u1="X" u2="&#x2a;" k="44" />
    <hkern u1="\" u2="&#xdf;" k="20" />
    <hkern u1="\" u2="v" k="56" />
    <hkern u1="\" u2="V" k="80" />
    <hkern u1="v" u2="&#x2022;" k="20" />
    <hkern u1="v" u2="&#xbf;" k="8" />
    <hkern u1="v" u2="v" k="-20" />
    <hkern u1="v" u2="X" k="50" />
    <hkern u1="v" u2="V" k="40" />
    <hkern u1="v" u2="&#x2f;" k="56" />
    <hkern u1="x" u2="&#x2122;" k="40" />
    <hkern u1="x" u2="&#x2022;" k="40" />
    <hkern u1="x" u2="x" k="-16" />
    <hkern u1="x" u2="\" k="20" />
    <hkern u1="x" u2="X" k="-8" />
    <hkern u1="x" u2="V" k="40" />
    <hkern u1="&#xa1;" u2="V" k="50" />
    <hkern u1="&#xa3;" u2="&#x31;" k="28" />
    <hkern u1="&#xa5;" u2="&#x34;" k="18" />
    <hkern u1="&#xa5;" u2="&#x32;" k="28" />
    <hkern u1="&#xa5;" u2="&#x31;" k="24" />
    <hkern u1="&#xa9;" u2="X" k="40" />
    <hkern u1="&#xa9;" u2="V" k="30" />
    <hkern u1="&#xae;" u2="X" k="18" />
    <hkern u1="&#xb0;" g2="seven.op" k="-20" />
    <hkern u1="&#xb0;" g2="four.op" k="110" />
    <hkern u1="&#xb0;" u2="&#x37;" k="-10" />
    <hkern u1="&#xb0;" u2="&#x34;" k="80" />
    <hkern u1="&#xb0;" u2="&#x32;" k="20" />
    <hkern u1="&#xbf;" u2="&#xdf;" k="50" />
    <hkern u1="&#xbf;" u2="x" k="48" />
    <hkern u1="&#xbf;" u2="v" k="68" />
    <hkern u1="&#xbf;" u2="X" k="40" />
    <hkern u1="&#xbf;" u2="V" k="130" />
    <hkern u1="&#xbf;" u2="&#x237;" k="-100" />
    <hkern u1="&#xbf;" u2="j" k="-90" />
    <hkern u1="&#xde;" g2="at.case" k="-30" />
    <hkern u1="&#xde;" g2="bullet.case" k="-20" />
    <hkern u1="&#xde;" g2="parenleft.case" k="-30" />
    <hkern u1="&#xde;" u2="v" k="-16" />
    <hkern u1="&#xde;" u2="X" k="78" />
    <hkern u1="&#xde;" u2="V" k="30" />
    <hkern u1="&#xde;" u2="&#x2f;" k="50" />
    <hkern u1="&#xde;" u2="&#x2a;" k="-10" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="30" />
    <hkern u1="&#xdf;" u2="&#x2022;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xb0;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xae;" k="20" />
    <hkern u1="&#xdf;" u2="x" k="10" />
    <hkern u1="&#xdf;" u2="v" k="10" />
    <hkern u1="&#xdf;" u2="\" k="20" />
    <hkern u1="&#xdf;" u2="X" k="24" />
    <hkern u1="&#xdf;" u2="V" k="30" />
    <hkern u1="&#xdf;" u2="&#x2f;" k="20" />
    <hkern u1="&#xf0;" u2="&#xae;" k="8" />
    <hkern u1="&#xf0;" u2="x" k="36" />
    <hkern u1="&#xf0;" u2="v" k="20" />
    <hkern u1="&#xf0;" u2="X" k="30" />
    <hkern u1="&#xf0;" u2="V" k="35" />
    <hkern u1="&#xf0;" u2="&#x2a;" k="8" />
    <hkern u1="&#x13d;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x13d;" u2="&#x21a;" k="80" />
    <hkern u1="&#x13d;" u2="&#x17d;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x17b;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x179;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x178;" k="80" />
    <hkern u1="&#x13d;" u2="&#x176;" k="80" />
    <hkern u1="&#x13d;" u2="&#x166;" k="80" />
    <hkern u1="&#x13d;" u2="&#x164;" k="80" />
    <hkern u1="&#x13d;" u2="&#x162;" k="80" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="80" />
    <hkern u1="&#x13d;" u2="Z" k="-20" />
    <hkern u1="&#x13d;" u2="Y" k="80" />
    <hkern u1="&#x13d;" u2="V" k="60" />
    <hkern u1="&#x13d;" u2="T" k="80" />
    <hkern u1="&#x192;" u2="&#x37;" k="20" />
    <hkern u1="&#x192;" u2="&#x34;" k="48" />
    <hkern u1="&#x192;" u2="&#x32;" k="30" />
    <hkern u1="&#x192;" u2="&#x31;" k="36" />
    <hkern u1="&#x2022;" u2="x" k="40" />
    <hkern u1="&#x2022;" u2="v" k="20" />
    <hkern u1="&#x2022;" u2="X" k="40" />
    <hkern u1="&#x2022;" u2="V" k="70" />
    <hkern u1="&#x2044;" g2="seven.dnom" k="-20" />
    <hkern u1="&#x20ac;" u2="&#x31;" k="8" />
    <hkern g1="questiondown.case" u2="V" k="78" />
    <hkern g1="bullet.case" u2="X" k="110" />
    <hkern g1="bullet.case" u2="V" k="60" />
    <hkern g1="at.case" u2="X" k="70" />
    <hkern g1="at.case" u2="V" k="35" />
    <hkern g1="sterling.op" g2="nine.op" k="10" />
    <hkern g1="sterling.op" g2="three.op" k="20" />
    <hkern g1="sterling.op" g2="one.op" k="20" />
    <hkern g1="yen.op" g2="nine.op" k="10" />
    <hkern g1="yen.op" g2="four.op" k="20" />
    <hkern g1="florin.op" g2="nine.op" k="10" />
    <hkern g1="florin.op" g2="seven.op" k="30" />
    <hkern g1="florin.op" g2="five.op" k="30" />
    <hkern g1="florin.op" g2="four.op" k="38" />
    <hkern g1="florin.op" g2="three.op" k="20" />
    <hkern g1="florin.op" g2="two.op" k="20" />
    <hkern g1="florin.op" g2="one.op" k="30" />
    <hkern g1="one.op" g2="three.op" k="30" />
    <hkern g1="one.op" g2="one.op" k="30" />
    <hkern g1="two.op" g2="nine.op" k="2" />
    <hkern g1="two.op" g2="seven.op" k="6" />
    <hkern g1="two.op" g2="three.op" k="8" />
    <hkern g1="three.op" g2="nine.op" k="4" />
    <hkern g1="three.op" g2="one.op" k="20" />
    <hkern g1="three.op" g2="florin.op" k="20" />
    <hkern g1="three.op" g2="yen.op" k="20" />
    <hkern g1="four.op" g2="nine.op" k="10" />
    <hkern g1="four.op" g2="seven.op" k="14" />
    <hkern g1="four.op" g2="one.op" k="110" />
    <hkern g1="four.op" u2="&#xb0;" k="70" />
    <hkern g1="five.op" g2="nine.op" k="22" />
    <hkern g1="five.op" g2="seven.op" k="40" />
    <hkern g1="five.op" g2="three.op" k="8" />
    <hkern g1="five.op" g2="one.op" k="50" />
    <hkern g1="five.op" g2="florin.op" k="10" />
    <hkern g1="five.op" u2="&#xb0;" k="10" />
    <hkern g1="five.op" u2="&#x23;" k="-30" />
    <hkern g1="seven.op" g2="five.op" k="20" />
    <hkern g1="seven.op" g2="four.op" k="80" />
    <hkern g1="seven.op" g2="florin.op" k="70" />
    <hkern g1="seven.op" u2="&#xb0;" k="-20" />
    <hkern g1="seven.op" u2="&#x3e;" k="30" />
    <hkern g1="seven.op" u2="&#x3c;" k="70" />
    <hkern g1="nine.op" g2="nine.op" k="18" />
    <hkern g1="nine.op" g2="seven.op" k="50" />
    <hkern g1="nine.op" g2="five.op" k="16" />
    <hkern g1="nine.op" g2="three.op" k="16" />
    <hkern g1="nine.op" g2="one.op" k="20" />
    <hkern g1="nine.op" u2="&#xb0;" k="20" />
    <hkern g1="four.numr" u2="&#x2044;" k="-30" />
    <hkern g1="seven.numr" u2="&#x2044;" k="60" />
    <hkern g1="seven.op"
	g2="period,ellipsis"
	k="180" />
    <hkern g1="seven.op"
	g2="zeroslash.op,zero.op"
	k="10" />
    <hkern g1="seven.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="190" />
    <hkern g1="seven.op"
	g2="plus,divide,minus"
	k="90" />
    <hkern g1="seven.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="30" />
    <hkern g1="seven.op"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="30" />
    <hkern g1="F"
	g2="period,ellipsis"
	k="130" />
    <hkern g1="F"
	g2="AE,AEacute"
	k="110" />
    <hkern g1="F"
	g2="J,Jcircumflex"
	k="86" />
    <hkern g1="F"
	g2="comma,quotesinglbase,quotedblbase"
	k="130" />
    <hkern g1="F"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="F"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-16" />
    <hkern g1="F"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-10" />
    <hkern g1="F"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-32" />
    <hkern g1="F"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="F"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="F"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="4" />
    <hkern g1="F"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="F"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="4" />
    <hkern g1="F"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="32" />
    <hkern g1="F"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="10" />
    <hkern g1="F"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="4" />
    <hkern g1="F"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="X"
	k="100" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="V"
	k="60" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="96" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="P"
	g2="period,ellipsis"
	k="100" />
    <hkern g1="P"
	g2="AE,AEacute"
	k="120" />
    <hkern g1="P"
	g2="J,Jcircumflex"
	k="64" />
    <hkern g1="P"
	g2="guilsinglright.case,guillemotright.case"
	k="-10" />
    <hkern g1="P"
	g2="guillemotright,guilsinglright"
	k="-20" />
    <hkern g1="P"
	g2="comma,quotesinglbase,quotedblbase"
	k="170" />
    <hkern g1="P"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="P"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-30" />
    <hkern g1="P"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="P"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="38" />
    <hkern g1="P"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-8" />
    <hkern g1="P"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-40" />
    <hkern g1="P"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-32" />
    <hkern g1="P"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-16" />
    <hkern g1="P"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="-10" />
    <hkern g1="P"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="68" />
    <hkern g1="P"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-10" />
    <hkern g1="P"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="6" />
    <hkern g1="P"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-16" />
    <hkern g1="P"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-15" />
    <hkern g1="P"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-8" />
    <hkern g1="six,six.op"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="six,six.op"
	g2="zeroslash.op,zero.op"
	k="4" />
    <hkern g1="six,six.op"
	g2="degree"
	k="40" />
    <hkern g1="six,six.op"
	g2="three.op"
	k="60" />
    <hkern g1="six,six.op"
	g2="five.op"
	k="20" />
    <hkern g1="six,six.op"
	g2="nine.op"
	k="20" />
    <hkern g1="six,six.op"
	g2="two"
	k="20" />
    <hkern g1="six,six.op"
	g2="three"
	k="34" />
    <hkern g1="six,six.op"
	g2="seven"
	k="70" />
    <hkern g1="six,six.op"
	g2="one"
	k="68" />
    <hkern g1="six,six.op"
	g2="five"
	k="20" />
    <hkern g1="six,six.op"
	g2="quotedbl,quotesingle"
	k="70" />
    <hkern g1="six,six.op"
	g2="nine"
	k="40" />
    <hkern g1="six,six.op"
	g2="Euro"
	k="8" />
    <hkern g1="six,six.op"
	g2="florin"
	k="20" />
    <hkern g1="six,six.op"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="six,six.op"
	g2="yen"
	k="26" />
    <hkern g1="six,six.op"
	g2="seven.op"
	k="40" />
    <hkern g1="six,six.op"
	g2="one.op"
	k="50" />
    <hkern g1="six,six.op"
	g2="ordfeminine,ordmasculine"
	k="40" />
    <hkern g1="six,six.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="70" />
    <hkern g1="six,six.op"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="six,six.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-16" />
    <hkern g1="six,six.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="Thorn"
	g2="period,ellipsis"
	k="60" />
    <hkern g1="Thorn"
	g2="AE,AEacute"
	k="60" />
    <hkern g1="Thorn"
	g2="J,Jcircumflex"
	k="30" />
    <hkern g1="Thorn"
	g2="guilsinglright.case,guillemotright.case"
	k="-20" />
    <hkern g1="Thorn"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="Thorn"
	g2="guilsinglleft.case,guillemotleft.case"
	k="-30" />
    <hkern g1="Thorn"
	g2="comma,quotesinglbase,quotedblbase"
	k="100" />
    <hkern g1="Thorn"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="Thorn"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-60" />
    <hkern g1="Thorn"
	g2="hyphen,periodcentered,endash,emdash"
	k="-30" />
    <hkern g1="Thorn"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="10" />
    <hkern g1="Thorn"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="70" />
    <hkern g1="Thorn"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="Thorn"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-16" />
    <hkern g1="Thorn"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="Thorn"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-24" />
    <hkern g1="Thorn"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-16" />
    <hkern g1="Thorn"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="30" />
    <hkern g1="Thorn"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-10" />
    <hkern g1="Thorn"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-16" />
    <hkern g1="Thorn"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-15" />
    <hkern g1="Thorn"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-16" />
    <hkern g1="guillemotright,guilsinglright"
	g2="X"
	k="30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="V"
	k="70" />
    <hkern g1="guillemotright,guilsinglright"
	g2="v"
	k="18" />
    <hkern g1="guillemotright,guilsinglright"
	g2="x"
	k="48" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="100" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="122" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="guillemotright,guilsinglright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="16" />
    <hkern g1="two"
	g2="dollar,dollar.weight"
	k="10" />
    <hkern g1="two"
	g2="cent,cent.weight"
	k="30" />
    <hkern g1="two"
	g2="plus,divide,minus"
	k="60" />
    <hkern g1="two"
	g2="zero,six,zeroslash,six.op"
	k="10" />
    <hkern g1="two"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="10" />
    <hkern g1="V"
	g2="period,ellipsis"
	k="140" />
    <hkern g1="V"
	g2="AE,AEacute"
	k="90" />
    <hkern g1="V"
	g2="J,Jcircumflex"
	k="70" />
    <hkern g1="V"
	g2="guilsinglright.case,guillemotright.case"
	k="38" />
    <hkern g1="V"
	g2="guillemotright,guilsinglright"
	k="50" />
    <hkern g1="V"
	g2="guilsinglleft.case,guillemotleft.case"
	k="60" />
    <hkern g1="V"
	g2="guillemotleft,guilsinglleft"
	k="70" />
    <hkern g1="V"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="V"
	g2="comma,quotesinglbase,quotedblbase"
	k="160" />
    <hkern g1="V"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="V"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="80" />
    <hkern g1="V"
	g2="hyphen,periodcentered,endash,emdash"
	k="70" />
    <hkern g1="V"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-8" />
    <hkern g1="V"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-8" />
    <hkern g1="V"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-8" />
    <hkern g1="V"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="V"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="V"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="V"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="40" />
    <hkern g1="V"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="80" />
    <hkern g1="V"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="30" />
    <hkern g1="V"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="V"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="50" />
    <hkern g1="V"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="50" />
    <hkern g1="V"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="70" />
    <hkern g1="V"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="40" />
    <hkern g1="V"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="V"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="70" />
    <hkern g1="asterisk"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="asterisk"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="asterisk"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="16" />
    <hkern g1="asterisk"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="asterisk"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="62" />
    <hkern g1="asterisk"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="asterisk"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="three.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="three.op"
	g2="cent.op,cent.op.weight"
	k="20" />
    <hkern g1="three.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="three.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="uni2075"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2075"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="germandbls"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="germandbls"
	g2="quotedbl,quotesingle"
	k="16" />
    <hkern g1="germandbls"
	g2="comma,quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="germandbls"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="germandbls"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="germandbls"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="germandbls"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="74" />
    <hkern g1="germandbls"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="germandbls"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="germandbls"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="germandbls"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="4" />
    <hkern g1="germandbls"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="18" />
    <hkern g1="germandbls"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="germandbls"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="5" />
    <hkern g1="germandbls"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="5" />
    <hkern g1="dollar.op,dollar.op.weight"
	g2="nine.op"
	k="10" />
    <hkern g1="dollar.op,dollar.op.weight"
	g2="one.op"
	k="70" />
    <hkern g1="five.op"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="five.op"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="five.op"
	g2="cent.op,cent.op.weight"
	k="30" />
    <hkern g1="five.op"
	g2="ordfeminine,ordmasculine"
	k="30" />
    <hkern g1="five.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="five.op"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="five.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="five.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="30" />
    <hkern g1="dcaron,lcaron"
	g2="AE,AEacute"
	k="60" />
    <hkern g1="dcaron,lcaron"
	g2="X"
	k="-120" />
    <hkern g1="dcaron,lcaron"
	g2="V"
	k="-150" />
    <hkern g1="dcaron,lcaron"
	g2="v"
	k="-30" />
    <hkern g1="dcaron,lcaron"
	g2="x"
	k="-20" />
    <hkern g1="dcaron,lcaron"
	g2="parenright"
	k="-110" />
    <hkern g1="dcaron,lcaron"
	g2="germandbls"
	k="-30" />
    <hkern g1="dcaron,lcaron"
	g2="asterisk"
	k="-64" />
    <hkern g1="dcaron,lcaron"
	g2="bracketright,braceright"
	k="-110" />
    <hkern g1="dcaron,lcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-100" />
    <hkern g1="dcaron,lcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-120" />
    <hkern g1="dcaron,lcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-120" />
    <hkern g1="dcaron,lcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-140" />
    <hkern g1="dcaron,lcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-20" />
    <hkern g1="dcaron,lcaron"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-50" />
    <hkern g1="dcaron,lcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-50" />
    <hkern g1="dcaron,lcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-30" />
    <hkern g1="dcaron,lcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="16" />
    <hkern g1="dcaron,lcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="-40" />
    <hkern g1="dcaron,lcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="dcaron,lcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-90" />
    <hkern g1="dcaron,lcaron"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-40" />
    <hkern g1="dcaron,lcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-40" />
    <hkern g1="dcaron,lcaron"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="-80" />
    <hkern g1="dcaron,lcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="48" />
    <hkern g1="dcaron,lcaron"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,uni2116"
	k="-80" />
    <hkern g1="four"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="four"
	g2="cent,cent.weight"
	k="16" />
    <hkern g1="four"
	g2="ordfeminine,ordmasculine"
	k="40" />
    <hkern g1="four"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="one"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="one"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="one"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="one"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="20" />
    <hkern g1="seven"
	g2="period,ellipsis"
	k="160" />
    <hkern g1="seven"
	g2="dollar,dollar.weight"
	k="30" />
    <hkern g1="seven"
	g2="cent,cent.weight"
	k="30" />
    <hkern g1="seven"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="seven"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="seven"
	g2="comma,quotesinglbase,quotedblbase"
	k="180" />
    <hkern g1="seven"
	g2="plus,divide,minus"
	k="160" />
    <hkern g1="seven"
	g2="zero,six,zeroslash,six.op"
	k="2" />
    <hkern g1="seven"
	g2="hyphen,periodcentered,endash,emdash"
	k="60" />
    <hkern g1="seven"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="100" />
    <hkern g1="five"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="five"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="five"
	g2="ordfeminine,ordmasculine"
	k="30" />
    <hkern g1="five"
	g2="comma,quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="five"
	g2="hyphen,periodcentered,endash,emdash"
	k="-16" />
    <hkern g1="onesuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="onesuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="two"
	k="20" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="seven"
	k="-10" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="four"
	k="80" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="four.op"
	k="110" />
    <hkern g1="nine.op"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="nine.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="nine.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="nine.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="B"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="B"
	g2="AE,AEacute"
	k="10" />
    <hkern g1="B"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="B"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="B"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="48" />
    <hkern g1="B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="B"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="B"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="14" />
    <hkern g1="B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="K,uni0136"
	g2="guilsinglright.case,guillemotright.case"
	k="90" />
    <hkern g1="K,uni0136"
	g2="degree"
	k="26" />
    <hkern g1="K,uni0136"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="K,uni0136"
	g2="guilsinglleft.case,guillemotleft.case"
	k="138" />
    <hkern g1="K,uni0136"
	g2="v"
	k="100" />
    <hkern g1="K,uni0136"
	g2="guillemotleft,guilsinglleft"
	k="50" />
    <hkern g1="K,uni0136"
	g2="germandbls"
	k="20" />
    <hkern g1="K,uni0136"
	g2="asterisk"
	k="48" />
    <hkern g1="K,uni0136"
	g2="at.case"
	k="90" />
    <hkern g1="K,uni0136"
	g2="bullet"
	k="100" />
    <hkern g1="K,uni0136"
	g2="bullet.case"
	k="120" />
    <hkern g1="K,uni0136"
	g2="copyright"
	k="70" />
    <hkern g1="K,uni0136"
	g2="registered"
	k="30" />
    <hkern g1="K,uni0136"
	g2="at"
	k="20" />
    <hkern g1="K,uni0136"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="140" />
    <hkern g1="K,uni0136"
	g2="hyphen,periodcentered,endash,emdash"
	k="110" />
    <hkern g1="K,uni0136"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="K,uni0136"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="70" />
    <hkern g1="K,uni0136"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="64" />
    <hkern g1="K,uni0136"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="60" />
    <hkern g1="K,uni0136"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="100" />
    <hkern g1="K,uni0136"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="K,uni0136"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="38" />
    <hkern g1="K,uni0136"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="75" />
    <hkern g1="K,uni0136"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="40" />
    <hkern g1="K,uni0136"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="60" />
    <hkern g1="K,uni0136"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="50" />
    <hkern g1="K,uni0136"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="78" />
    <hkern g1="K,uni0136"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="20" />
    <hkern g1="K,uni0136"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="70" />
    <hkern g1="threesuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="threesuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="three"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="three"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="three"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="three"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="X"
	g2="AE,AEacute"
	k="-8" />
    <hkern g1="X"
	g2="guilsinglright.case,guillemotright.case"
	k="70" />
    <hkern g1="X"
	g2="guilsinglleft.case,guillemotleft.case"
	k="100" />
    <hkern g1="X"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="X"
	g2="z,zacute,zdotaccent,zcaron"
	k="4" />
    <hkern g1="X"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="90" />
    <hkern g1="X"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="X"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="4" />
    <hkern g1="X"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="X"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="66" />
    <hkern g1="X"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="X"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="50" />
    <hkern g1="X"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="X"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-8" />
    <hkern g1="X"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="X"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="X"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="30" />
    <hkern g1="X"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="X"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="50" />
    <hkern g1="X"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="40" />
    <hkern g1="uni2077"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2077"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="twosuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="twosuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="V"
	k="50" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="122" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="8" />
    <hkern g1="eth"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="eth"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="eth"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="eth"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="eth"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="eth"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="eth"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="30" />
    <hkern g1="eth"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="72" />
    <hkern g1="eth"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="34" />
    <hkern g1="eth"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="eth"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="eth"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="eth"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="5" />
    <hkern g1="eth"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="24" />
    <hkern g1="eth"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="eth"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-4" />
    <hkern g1="eth"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="4" />
    <hkern g1="two.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="two.op"
	g2="plus,divide,minus"
	k="30" />
    <hkern g1="period,ellipsis"
	g2="zeroslash.op,zero.op"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="V"
	k="140" />
    <hkern g1="period,ellipsis"
	g2="three.op"
	k="10" />
    <hkern g1="period,ellipsis"
	g2="five.op"
	k="30" />
    <hkern g1="period,ellipsis"
	g2="nine.op"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="seven"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="one"
	k="216" />
    <hkern g1="period,ellipsis"
	g2="quotedbl,quotesingle"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="v"
	k="60" />
    <hkern g1="period,ellipsis"
	g2="seven.op"
	k="120" />
    <hkern g1="period,ellipsis"
	g2="one.op"
	k="210" />
    <hkern g1="period,ellipsis"
	g2="asterisk"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="four"
	k="10" />
    <hkern g1="period,ellipsis"
	g2="eight,eight.op"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-16" />
    <hkern g1="period,ellipsis"
	g2="zero,six,zeroslash,six.op"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="150" />
    <hkern g1="period,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="180" />
    <hkern g1="period,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="period,ellipsis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="50" />
    <hkern g1="period,ellipsis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="colon,semicolon"
	g2="V"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="seven"
	k="20" />
    <hkern g1="colon,semicolon"
	g2="eight,eight.op"
	k="8" />
    <hkern g1="colon,semicolon"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="100" />
    <hkern g1="colon,semicolon"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="quotedbl,quotesingle"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="quotedbl,quotesingle"
	g2="zeroslash.op,zero.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="three.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="five.op"
	k="30" />
    <hkern g1="quotedbl,quotesingle"
	g2="nine.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="two"
	k="30" />
    <hkern g1="quotedbl,quotesingle"
	g2="three"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="x"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="four"
	k="70" />
    <hkern g1="quotedbl,quotesingle"
	g2="four.op"
	k="130" />
    <hkern g1="quotedbl,quotesingle"
	g2="slash"
	k="180" />
    <hkern g1="quotedbl,quotesingle"
	g2="two.op"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="quotedbl,quotesingle"
	g2="eight,eight.op"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="60" />
    <hkern g1="quotedbl,quotesingle"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="6" />
    <hkern g1="quotedbl,quotesingle"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="x"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="x"
	g2="guillemotleft,guilsinglleft"
	k="48" />
    <hkern g1="x"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="x"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="x"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="x"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="106" />
    <hkern g1="x"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="x"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="10" />
    <hkern g1="x"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-4" />
    <hkern g1="x"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="5" />
    <hkern g1="x"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-16" />
    <hkern g1="x"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="8" />
    <hkern g1="x"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="x"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="x"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="15" />
    <hkern g1="x"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="25" />
    <hkern g1="slash"
	g2="period,ellipsis"
	k="130" />
    <hkern g1="slash"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="slash"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="slash"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="slash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="slash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="slash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="slash"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="slash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="slash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="slash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="slash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="28" />
    <hkern g1="slash"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="eight,eight.op"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="eight,eight.op"
	g2="three.op"
	k="6" />
    <hkern g1="eight,eight.op"
	g2="nine.op"
	k="4" />
    <hkern g1="eight,eight.op"
	g2="one"
	k="10" />
    <hkern g1="eight,eight.op"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="eight,eight.op"
	g2="colon,semicolon"
	k="8" />
    <hkern g1="eight,eight.op"
	g2="cent.op,cent.op.weight"
	k="8" />
    <hkern g1="eight,eight.op"
	g2="numbersign"
	k="-10" />
    <hkern g1="eight,eight.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="eight,eight.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="cent.op,cent.op.weight"
	g2="three.op"
	k="20" />
    <hkern g1="cent.op,cent.op.weight"
	g2="one.op"
	k="10" />
    <hkern g1="v"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="v"
	g2="guillemotleft,guilsinglleft"
	k="18" />
    <hkern g1="v"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="v"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="v"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="v"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="v"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="102" />
    <hkern g1="v"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="v"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-4" />
    <hkern g1="v"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-8" />
    <hkern g1="v"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="v"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-16" />
    <hkern g1="v"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="v"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="v"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="15" />
    <hkern g1="v"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-4" />
    <hkern g1="v"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="15" />
    <hkern g1="cent,cent.weight"
	g2="seven"
	k="10" />
    <hkern g1="cent,cent.weight"
	g2="one"
	k="8" />
    <hkern g1="uni2074"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2074"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="zeroslash.op,zero.op"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="zeroslash.op,zero.op"
	g2="three.op"
	k="40" />
    <hkern g1="zeroslash.op,zero.op"
	g2="five.op"
	k="16" />
    <hkern g1="zeroslash.op,zero.op"
	g2="nine.op"
	k="4" />
    <hkern g1="zeroslash.op,zero.op"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="zeroslash.op,zero.op"
	g2="seven.op"
	k="20" />
    <hkern g1="zeroslash.op,zero.op"
	g2="one.op"
	k="8" />
    <hkern g1="zeroslash.op,zero.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="70" />
    <hkern g1="zeroslash.op,zero.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="X"
	k="70" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="V"
	k="40" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="92" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="uni2076"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2076"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="four.op"
	g2="quotedbl,quotesingle"
	k="60" />
    <hkern g1="four.op"
	g2="ordfeminine,ordmasculine"
	k="50" />
    <hkern g1="four.op"
	g2="plus,divide,minus"
	k="40" />
    <hkern g1="four.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="four.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="uni2078"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2078"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="plus,divide,minus"
	g2="three.op"
	k="50" />
    <hkern g1="plus,divide,minus"
	g2="five.op"
	k="30" />
    <hkern g1="plus,divide,minus"
	g2="two"
	k="20" />
    <hkern g1="plus,divide,minus"
	g2="three"
	k="50" />
    <hkern g1="plus,divide,minus"
	g2="seven"
	k="110" />
    <hkern g1="plus,divide,minus"
	g2="one"
	k="172" />
    <hkern g1="plus,divide,minus"
	g2="seven.op"
	k="140" />
    <hkern g1="plus,divide,minus"
	g2="four.op"
	k="30" />
    <hkern g1="plus,divide,minus"
	g2="zero,six,zeroslash,six.op"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="zero,nine,zeroslash"
	g2="two"
	k="10" />
    <hkern g1="zero,nine,zeroslash"
	g2="three"
	k="22" />
    <hkern g1="zero,nine,zeroslash"
	g2="seven"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="one"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="zero,nine,zeroslash"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="hyphen,periodcentered,endash,emdash"
	k="-16" />
    <hkern g1="zero,nine,zeroslash"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-16" />
    <hkern g1="zero,nine,zeroslash"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="20" />
    <hkern g1="f,longs,f_f"
	g2="period,ellipsis"
	k="100" />
    <hkern g1="f,longs,f_f"
	g2="V"
	k="-14" />
    <hkern g1="f,longs,f_f"
	g2="degree"
	k="-32" />
    <hkern g1="f,longs,f_f"
	g2="quotedbl,quotesingle"
	k="-16" />
    <hkern g1="f,longs,f_f"
	g2="v"
	k="-30" />
    <hkern g1="f,longs,f_f"
	g2="x"
	k="-10" />
    <hkern g1="f,longs,f_f"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="f,longs,f_f"
	g2="asterisk"
	k="-30" />
    <hkern g1="f,longs,f_f"
	g2="bullet"
	k="20" />
    <hkern g1="f,longs,f_f"
	g2="registered"
	k="-28" />
    <hkern g1="f,longs,f_f"
	g2="slash"
	k="66" />
    <hkern g1="f,longs,f_f"
	g2="numbersign"
	k="16" />
    <hkern g1="f,longs,f_f"
	g2="comma,quotesinglbase,quotedblbase"
	k="100" />
    <hkern g1="f,longs,f_f"
	g2="backslash"
	k="-42" />
    <hkern g1="f,longs,f_f"
	g2="question"
	k="-10" />
    <hkern g1="f,longs,f_f"
	g2="trademark"
	k="-12" />
    <hkern g1="f,longs,f_f"
	g2="questiondown"
	k="12" />
    <hkern g1="f,longs,f_f"
	g2="z,zacute,zdotaccent,zcaron"
	k="-4" />
    <hkern g1="f,longs,f_f"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-20" />
    <hkern g1="f,longs,f_f"
	g2="hyphen,periodcentered,endash,emdash"
	k="20" />
    <hkern g1="f,longs,f_f"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-50" />
    <hkern g1="f,longs,f_f"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-12" />
    <hkern g1="f,longs,f_f"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-12" />
    <hkern g1="f,longs,f_f"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-12" />
    <hkern g1="f,longs,f_f"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-30" />
    <hkern g1="f,longs,f_f"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-34" />
    <hkern g1="f,longs,f_f"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-24" />
    <hkern g1="f,longs,f_f"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="f,longs,f_f"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-16" />
    <hkern g1="f,longs,f_f"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="f,longs,f_f"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-32" />
    <hkern g1="f,longs,f_f"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-18" />
    <hkern g1="uni2070,uni2079"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2070,uni2079"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="X"
	k="16" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="V"
	k="120" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="three.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="five.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="nine.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="seven"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="one"
	k="158" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="quotedbl,quotesingle"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="v"
	k="40" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="seven.op"
	k="90" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="one.op"
	k="160" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="asterisk"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-16" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="zero,six,zeroslash,six.op"
	k="10" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="130" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="180" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="70" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="60" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="30" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="J,Jcircumflex"
	k="18" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="X"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="V"
	k="28" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="bullet"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="bullet.case"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="registered"
	k="18" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="trademark"
	k="40" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="54" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-8" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="4" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="7" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-5" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="zeroslash.op,zero.op"
	k="70" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="X"
	k="2" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="V"
	k="-16" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="three.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="five.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="nine.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="two"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="v"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="one.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="four"
	k="110" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="four.op"
	k="160" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="slash"
	k="150" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="two.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="eight,eight.op"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="z,zacute,zdotaccent,zcaron"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="zero,six,zeroslash,six.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="4" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="70" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="period,ellipsis"
	k="130" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="X"
	k="58" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="V"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="degree"
	k="-20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="v"
	k="-8" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="germandbls"
	k="8" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="asterisk"
	k="-20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="bullet"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="registered"
	k="-10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="slash"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="140" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="trademark"
	k="40" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="questiondown"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="28" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="128" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="35" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-16" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-8" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="15" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-16" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-4" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="15" />
    <hkern g1="Euro"
	g2="zero,six,zeroslash,six.op"
	k="10" />
    <hkern g1="questiondown"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="questiondown"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="140" />
    <hkern g1="questiondown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="170" />
    <hkern g1="questiondown"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="110" />
    <hkern g1="questiondown"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="66" />
    <hkern g1="questiondown"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="questiondown"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="38" />
    <hkern g1="questiondown"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="questiondown"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="30" />
    <hkern g1="questiondown"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="68" />
    <hkern g1="questiondown"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="40" />
    <hkern g1="questiondown"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="58" />
    <hkern g1="questiondown"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="60" />
    <hkern g1="questiondown"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="60" />
    <hkern g1="questiondown"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="50" />
    <hkern g1="questiondown"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="50" />
    <hkern g1="questiondown"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,uni2116"
	k="58" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="X"
	k="90" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="V"
	k="80" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="100" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="150" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-10" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="V"
	k="60" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="bullet"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="numbersign"
	k="-16" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="backslash"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="question"
	k="6" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="trademark"
	k="50" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="150" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="4" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="zeroslash.op,zero.op"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="X"
	k="50" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="V"
	k="70" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="three.op"
	k="60" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="nine.op"
	k="-10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="seven"
	k="70" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="one"
	k="164" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="v"
	k="10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="x"
	k="50" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="seven.op"
	k="100" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="one.op"
	k="20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="four"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="eight,eight.op"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="zero,six,zeroslash,six.op"
	k="-24" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="90" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="130" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-8" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-16" />
    <hkern g1="bullet.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="60" />
    <hkern g1="bullet.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="90" />
    <hkern g1="bullet.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="100" />
    <hkern g1="bullet.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="bullet.case"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="bullet.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="period,ellipsis"
	k="-16" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="J,Jcircumflex"
	k="-16" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="v"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="x"
	k="-8" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="bullet.case"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="numbersign"
	k="-20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="question"
	k="-28" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="-8" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="60" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-8" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="-8" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="numbersign"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="numbersign"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-10" />
    <hkern g1="numbersign"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-32" />
    <hkern g1="numbersign"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-16" />
    <hkern g1="numbersign"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="40" />
    <hkern g1="numbersign"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="102" />
    <hkern g1="numbersign"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="numbersign"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="40" />
    <hkern g1="questiondown.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-16" />
    <hkern g1="questiondown.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="questiondown.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="70" />
    <hkern g1="questiondown.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="questiondown.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-20" />
    <hkern g1="questiondown.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="questiondown.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="degree"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-10" />
    <hkern g1="degree"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-30" />
    <hkern g1="degree"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-10" />
    <hkern g1="degree"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-20" />
    <hkern g1="degree"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="degree"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="90" />
    <hkern g1="degree"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="degree"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="50" />
    <hkern g1="registered"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="registered"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="registered"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-8" />
    <hkern g1="registered"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="registered"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="90" />
    <hkern g1="registered"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="40" />
    <hkern g1="copyright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="copyright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="68" />
    <hkern g1="copyright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="copyright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="AE,AEacute"
	k="150" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="J,Jcircumflex"
	k="76" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="X"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="V"
	k="-8" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guillemotright,guilsinglright"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="degree"
	k="-30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guilsinglleft.case,guillemotleft.case"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="v"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="x"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guillemotleft,guilsinglleft"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="germandbls"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="at.case"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bullet"
	k="130" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bullet.case"
	k="90" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="copyright"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="registered"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="at"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="slash"
	k="140" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="numbersign"
	k="70" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bracketright,braceright"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="backslash"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="question"
	k="-50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="trademark"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="questiondown"
	k="160" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="z,zacute,zdotaccent,zcaron"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="hyphen,periodcentered,endash,emdash"
	k="90" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-24" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="exclamdown"
	k="70" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="questiondown.case"
	k="12" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-8" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="40" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="34" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="90" />
    <hkern g1="backslash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="140" />
    <hkern g1="backslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="118" />
    <hkern g1="backslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="60" />
    <hkern g1="backslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="38" />
    <hkern g1="backslash"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="40" />
    <hkern g1="backslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="36" />
    <hkern g1="backslash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="backslash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="backslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="backslash"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="40" />
    <hkern g1="backslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="68" />
    <hkern g1="backslash"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="AE,AEacute"
	k="90" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex"
	k="36" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="V"
	k="-8" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guilsinglright.case,guillemotright.case"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotright,guilsinglright"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guilsinglleft.case,guillemotleft.case"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="colon,semicolon"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="germandbls"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="at.case"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet.case"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="copyright"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="registered"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="at"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="slash"
	k="60" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="numbersign"
	k="60" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="90" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="trademark"
	k="-20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="questiondown"
	k="82" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,periodcentered,endash,emdash"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-8" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="exclamdown"
	k="8" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="45" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="45" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="X"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="V"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="degree"
	k="-10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="v"
	k="-4" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="bullet"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="slash"
	k="38" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="backslash"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="question"
	k="2" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="questiondown"
	k="4" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="50" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="100" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-8" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="5" />
    <hkern g1="bullet"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="130" />
    <hkern g1="bullet"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="150" />
    <hkern g1="bullet"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="bullet"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="bullet"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="bullet"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="bullet"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="exclamdown"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="70" />
    <hkern g1="exclamdown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="108" />
    <hkern g1="exclamdown"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="8" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="period,ellipsis"
	k="180" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="AE,AEacute"
	k="182" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J,Jcircumflex"
	k="78" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="V"
	k="-8" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guilsinglright.case,guillemotright.case"
	k="92" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotright,guilsinglright"
	k="122" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guilsinglleft.case,guillemotleft.case"
	k="96" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="v"
	k="102" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="x"
	k="106" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotleft,guilsinglleft"
	k="122" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="colon,semicolon"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="germandbls"
	k="58" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="asterisk"
	k="16" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at.case"
	k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="bullet"
	k="150" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="bullet.case"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="copyright"
	k="68" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at"
	k="110" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="slash"
	k="118" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="numbersign"
	k="110" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="180" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="questiondown"
	k="220" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="138" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="150" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="130" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-16" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="exclamdown"
	k="108" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="questiondown.case"
	k="8" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="84" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="104" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="136" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="56" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="116" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="132" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="132" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="128" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="82" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="64" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="16" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="152" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="period,ellipsis"
	k="-8" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="V"
	k="50" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="bullet"
	k="14" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="numbersign"
	k="-16" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="backslash"
	k="20" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="question"
	k="4" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-16" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="40" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="130" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="-4" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-8" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="4" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="X"
	k="50" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="V"
	k="40" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="degree"
	k="-20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="v"
	k="-16" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="x"
	k="-4" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="guillemotleft,guilsinglleft"
	k="16" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="bullet"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="at"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="slash"
	k="66" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="numbersign"
	k="26" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="100" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="backslash"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="trademark"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="questiondown"
	k="14" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="104" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-8" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-8" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="25" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-4" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="15" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="period,ellipsis"
	k="50" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="J,Jcircumflex"
	k="16" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="X"
	k="40" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="V"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="guillemotright,guilsinglright"
	k="-10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="x"
	k="4" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="asterisk"
	k="-20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="at.case"
	k="-10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="slash"
	k="50" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="backslash"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="trademark"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="questiondown"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="68" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-13" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-8" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="V"
	k="60" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="guillemotright,guilsinglright"
	k="8" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="x"
	k="-8" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="bullet"
	k="50" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="copyright"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="backslash"
	k="40" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="trademark"
	k="70" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="hyphen,periodcentered,endash,emdash"
	k="70" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="18" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="114" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-8" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="25" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="40" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="15" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="25" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="40" />
    <hkern g1="at.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="at.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="80" />
    <hkern g1="at.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="at.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="30" />
    <hkern g1="at.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="at"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="at"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="110" />
    <hkern g1="at"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="at"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="period,ellipsis"
	k="-20" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="AE,AEacute"
	k="-32" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="J,Jcircumflex"
	k="-30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="X"
	k="-24" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="V"
	k="92" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="degree"
	k="130" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="quotedbl,quotesingle"
	k="170" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="guilsinglleft.case,guillemotleft.case"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="v"
	k="50" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="asterisk"
	k="170" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="at.case"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="bullet"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="bullet.case"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="registered"
	k="150" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="numbersign"
	k="-40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="comma,quotesinglbase,quotedblbase"
	k="-30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="backslash"
	k="126" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="question"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="trademark"
	k="180" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="z,zacute,zdotaccent,zcaron"
	k="-20" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-24" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="100" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="hyphen,periodcentered,endash,emdash"
	k="-30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="170" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="148" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="114" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="45" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="-16" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-16" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="22" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="34" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="X"
	k="26" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="V"
	k="70" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="degree"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="v"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="x"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="germandbls"
	k="4" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="backslash"
	k="30" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="trademark"
	k="60" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="90" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="136" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="8" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="8" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="8" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="three.op"
	k="30" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="seven"
	k="130" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="one"
	k="110" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="seven.op"
	k="50" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="zero,six,zeroslash,six.op"
	k="20" />
    <hkern g1="bracketleft,braceleft"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="X"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="V"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="v"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="x"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="asterisk"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="bullet.case"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="registered"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="slash"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="backslash"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="trademark"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="50" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="4" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="X"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="V"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="degree"
	k="-20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="guillemotleft,guilsinglleft"
	k="-10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="slash"
	k="58" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="backslash"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="trademark"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="hyphen,periodcentered,endash,emdash"
	k="-40" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="4" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="40" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-8" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="2" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="V"
	k="60" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="registered"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="numbersign"
	k="-20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="trademark"
	k="20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="z,zacute,zdotaccent,zcaron"
	k="-4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="30" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="128" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="-4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-4" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="AE,AEacute"
	k="-24" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="J,Jcircumflex"
	k="-8" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="X"
	k="-8" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="V"
	k="80" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guilsinglright.case,guillemotright.case"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="degree"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="quotedbl,quotesingle"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guilsinglleft.case,guillemotleft.case"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="v"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="x"
	k="-16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="asterisk"
	k="62" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="at.case"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="bullet"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="bullet.case"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="copyright"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="registered"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="numbersign"
	k="-16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="backslash"
	k="80" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="question"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="trademark"
	k="100" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="100" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="116" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="45" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="4" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="2" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="trademark"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="question"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="78" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="X"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="V"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="degree"
	k="30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="v"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="x"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="asterisk"
	k="30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="registered"
	k="38" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="numbersign"
	k="-30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="backslash"
	k="50" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="trademark"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="152" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-4" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="AE,AEacute"
	k="-20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="J,Jcircumflex"
	k="-10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="v"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="x"
	k="-4" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="-16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="2" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="4" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="period,ellipsis"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="J,Jcircumflex"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="X"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="x"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="slash"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="hyphen,periodcentered,endash,emdash"
	k="-8" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="4" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="4" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="12" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="period,ellipsis"
	k="50" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="J,Jcircumflex"
	k="34" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="X"
	k="40" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="V"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="guillemotright,guilsinglright"
	k="8" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="degree"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="x"
	k="15" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="asterisk"
	k="-10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="at.case"
	k="-10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="slash"
	k="68" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="64" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="backslash"
	k="28" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="trademark"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="34" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="64" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="5" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-15" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-4" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="X"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="V"
	k="70" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="degree"
	k="50" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="v"
	k="15" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="x"
	k="25" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="asterisk"
	k="30" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="registered"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="numbersign"
	k="-20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="comma,quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="trademark"
	k="50" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="hyphen,periodcentered,endash,emdash"
	k="-16" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="90" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="152" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="4" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="4" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-4" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-4" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="V"
	k="40" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="trademark"
	k="30" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="132" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="V"
	k="55" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="degree"
	k="30" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="v"
	k="10" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="asterisk"
	k="20" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="registered"
	k="50" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="backslash"
	k="40" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="trademark"
	k="80" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="85" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="132" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="d,i,j,l,igrave,iacute,icircumflex,idieresis,dcroat,itilde,imacron,ibreve,iogonek,ij,jcircumflex,lacute,uni013C,lslash,f_f_i,f_f_l,i.trk,f_j,fl,f_f_j"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="16" />
  </font>
</defs></svg>

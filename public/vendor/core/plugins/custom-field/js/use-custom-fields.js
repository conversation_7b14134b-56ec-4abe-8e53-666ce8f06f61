(()=>{"use strict";var e={};function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,r){for(var a=0;a<r.length;a++){var i=r[a];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(n=i.key,l=void 0,l=function(e,r){if("object"!==t(e)||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var i=a.call(e,r||"default");if("object"!==t(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(n,"string"),"symbol"===t(l)?l:String(l)),i)}var n,l}function i(e,t,r){return t&&a(e.prototype,t),r&&a(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}e.d=(t,r)=>{for(var a in r)e.o(r,a)&&!e.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:r[a]})},e.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var n=function(){function e(){r(this,e)}return i(e,null,[{key:"wysiwyg",value:function(e,t){window.initializedEditor=window.initializedEditor||0;var r="ckeditor";"undefined"!=typeof tinymce&&(r="tinymce"),e.each((function(e,t){var a=$(t);a.attr("id","editor_initialized_"+window.initializedEditor),window.initializedEditor++,setTimeout((function(){(new EditorManagement).initEditor(a,{},r)}),100)}))}},{key:"wysiwygGetContent",value:function(e){return"undefined"!=typeof CKEDITOR?CKEDITOR[e.attr("id")].getData():"undefined"!=typeof tinymce?tinymce.editors[e.attr("id")].getContent():e.val()}},{key:"arrayGet",value:function(e,t){var r,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;try{r=e[t]}catch(e){return a}return null==r&&(r=a),r}},{key:"jsonEncode",value:function(e){return void 0===e&&(e=null),JSON.stringify(e)}},{key:"jsonDecode",value:function(e,t){if("string"==typeof e){var r;try{r=$.parseJSON(e)}catch(e){r=t}return r}return null}}]),e}(),l=function(){function e(){r(this,e),this.$body=$("body"),this.$_UPDATE_TO=$("#custom_fields_container"),this.$_EXPORT_TO=$("#custom_fields_json"),this.CURRENT_DATA=n.jsonDecode(this.base64Helper().decode(this.$_EXPORT_TO.text()),[]),this.CURRENT_DATA&&(this.handleCustomFields(),this.exportData())}return i(e,[{key:"base64Helper",value:function(){if(!this.base64){var e={_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",encode:function(t){var r,a,i,n,l,o,c,d="",u=0;for(t=e._utf8_encode(t);u<t.length;)n=(r=t.charCodeAt(u++))>>2,l=(3&r)<<4|(a=t.charCodeAt(u++))>>4,o=(15&a)<<2|(i=t.charCodeAt(u++))>>6,c=63&i,isNaN(a)?o=c=64:isNaN(i)&&(c=64),d=d+this._keyStr.charAt(n)+this._keyStr.charAt(l)+this._keyStr.charAt(o)+this._keyStr.charAt(c);return d},decode:function(t){var r,a,i,n,l,o,c="",d=0;for(t=t.replace(/[^A-Za-z0-9+/=]/g,"");d<t.length;)r=this._keyStr.indexOf(t.charAt(d++))<<2|(n=this._keyStr.indexOf(t.charAt(d++)))>>4,a=(15&n)<<4|(l=this._keyStr.indexOf(t.charAt(d++)))>>2,i=(3&l)<<6|(o=this._keyStr.indexOf(t.charAt(d++))),c+=String.fromCharCode(r),64!=l&&(c+=String.fromCharCode(a)),64!=o&&(c+=String.fromCharCode(i));return c=e._utf8_decode(c)},_utf8_encode:function(e){e=e.replace(/rn/g,"n");for(var t="",r=0;r<e.length;r++){var a=e.charCodeAt(r);a<128?t+=String.fromCharCode(a):a>127&&a<2048?(t+=String.fromCharCode(a>>6|192),t+=String.fromCharCode(63&a|128)):(t+=String.fromCharCode(a>>12|224),t+=String.fromCharCode(a>>6&63|128),t+=String.fromCharCode(63&a|128))}return t},_utf8_decode:function(e){for(var t="",r=0,a=0,i=0;r<e.length;)if((a=e.charCodeAt(r))<128)t+=String.fromCharCode(a),r++;else if(a>191&&a<224)i=e.charCodeAt(r+1),t+=String.fromCharCode((31&a)<<6|63&i),r+=2;else{i=e.charCodeAt(r+1);var n=e.charCodeAt(r+2);t+=String.fromCharCode((15&a)<<12|(63&i)<<6|63&n),r+=3}return t}};this.base64=e}return this.base64}},{key:"handleCustomFields",value:function(){var e=this,t=0,r={fieldGroup:$("#_render_custom_field_field_group_template").html(),globalSkeleton:$("#_render_custom_field_global_skeleton_template").html(),text:$("#_render_custom_field_text_template").html(),number:$("#_render_custom_field_number_template").html(),email:$("#_render_custom_field_email_template").html(),password:$("#_render_custom_field_password_template").html(),textarea:$("#_render_custom_field_textarea_template").html(),checkbox:$("#_render_custom_field_checkbox_template").html(),radio:$("#_render_custom_field_radio_template").html(),select:$("#_render_custom_field_select_template").html(),image:$("#_render_custom_field_image_template").html(),file:$("#_render_custom_field_file_template").html(),wysiwyg:$("#_render_custom_field_wysiswg_template").html(),repeater:$("#_render_custom_field_repeater_template").html(),repeaterItem:$("#_render_custom_field_repeater_item_template").html(),repeaterFieldLine:$("#_render_custom_field_repeater_line_template").html()},a=function(e){return n.wysiwyg(e),e},i=function(e){var a=r[e.type],i=$('<div class="lcf-'+e.type+'-wrapper"></div>');i.data("lcf-registered-data",e);var o=null,d=null;switch(e.type){case"text":case"number":case"email":case"password":a=(a=a.replace(/__placeholderText__/gi,e.options.placeholderText||"")).replace(/__value__/gi,e.value||e.options.defaultValue||"");break;case"textarea":a=(a=(a=a.replace(/__rows__/gi,e.options.rows||3)).replace(/__placeholderText__/gi,e.options.placeholderText||"")).replace(/__value__/gi,e.value||e.options.defaultValue||"");break;case"image":if(a=a.replace(/__value__/gi,e.value||e.options.defaultValue||""),e.value)a=a.replace("data-src","src").replace(/__image__/gi,e.thumb||e.options.defaultValue||"");else{var u=$(a).find("img").attr("data-default");a=a.replace("data-src","src").replace(/__image__/gi,u||e.options.defaultValue||"")}break;case"file":a=(a=a.replace(/__value__/gi,e.value||e.options.defaultValue||"")).replace(/__url__/gi,e.full_url||e.options.defaultValue||"");break;case"select":return d=$(a),(o=c(e.options.selectChoices)).forEach((function(e){d.append('<option value="'+e[0]+'">'+e[1]+"</option>")})),d.val(n.arrayGet(e,"value",e.options.defaultValue)),i.append(d),i;case"checkbox":o=c(e.options.selectChoices);var s=n.jsonDecode(e.value);return o.forEach((function(e){var t=a.replace(/__value__/gi,e[0]||"");t=(t=t.replace(/__title__/gi,e[1]||"")).replace(/__checked__/gi,-1!=$.inArray(e[0],s)?"checked":""),i.append($(t))})),i;case"radio":o=c(e.options.selectChoices);var f=!1;return o.forEach((function(r){var n=a.replace(/__value__/gi,r[0]||"");n=(n=(n=n.replace(/__id__/gi,e.id+e.slug+t)).replace(/__title__/gi,r[1]||"")).replace(/__checked__/gi,e.value===r[0]?"checked":""),i.append($(n)),e.value===r[0]&&(f=!0)})),!1===f&&i.find("input[type=radio]:first").prop("checked",!0),i;case"repeater":return(d=$(a)).data("lcf-registered-data",e),d.find("> .repeater-add-new-field").html(e.options.buttonLabel||"Add new item"),d.find("> .sortable-wrapper").sortable({handle:".ui-sortable-handle"}),l(e.items,e.value||[],d.find("> .field-group-items")),d;case"wysiwyg":a=a.replace(/__value__/gi,e.value||e.options.defaultValueTextarea||"")}return i.append($(a)),i},l=function(e,t,a){return a.data("lcf-registered-data",e),t.forEach((function(t){var i=a.find("> .ui-sortable-handle").length+1,n=r.repeaterItem;n=n.replace(/__position__/gi,i);var l=$(n);l.data("lcf-registered-data",e),o(e,t,l.find("> .field-line-wrapper > .field-group")),a.append(l)})),a},o=function(e,n,l){return n.forEach((function(e){t++;var n=r.repeaterFieldLine;n=(n=n.replace(/__title__/gi,e.title||"")).replace(/__instructions__/gi,e.instructions||"");var o=$(n),c=i(e);o.data("lcf-registered-data",e),o.find("> .repeater-item-input").append(c),l.append(o),"wysiwyg"===e.type&&a(o.find("> .repeater-item-input .wysiwyg-editor"))})),l},c=function(e){if(!e)return[];var t=[];return e.split("\n").forEach((function(e){var r=e.split(":");r[0]&&r[1]&&(r[0]=r[0].trim(),r[1]=r[1].trim()),t.push(r)})),t};this.$body.on("click",".remove-field-line",(function(e){e.preventDefault();var t=$(e.currentTarget);t.parent().animate({opacity:.1},300,(function(){t.parent().remove()}))})),this.$body.on("click",".collapse-field-line",(function(e){e.preventDefault(),$(e.currentTarget).toggleClass("collapsed-line")})),this.$body.on("click",".repeater-add-new-field",(function(e){e.preventDefault();var r=$.extend(!0,{},$(e.currentTarget).prev(".field-group-items")),a=r.data("lcf-registered-data");t++,l(a,[a],r),Apps.initMediaIntegrate()})),this.CURRENT_DATA.forEach((function(t){var n=r.fieldGroup;n=n.replace(/__title__/gi,t.title||"");var l,o,c=$(n);l=t.items,o=c.find(".meta-boxes-body"),l.forEach((function(e){var t=r.globalSkeleton;t=(t=(t=t.replace(/__type__/gi,e.type||"")).replace(/__title__/gi,e.title||"")).replace(/__instructions__/gi,e.instructions||"");var n=$(t),l=i(e);n.find(".meta-box-wrap").append(l),n.data("lcf-registered-data",e),o.append(n),"wysiwyg"===e.type&&a(n.find(".meta-box-wrap .wysiwyg-editor"))})),c.data("lcf-field-group",t),e.$_UPDATE_TO.append(c)})),Apps.initMediaIntegrate()}},{key:"exportData",value:function(){var e=this,t=function(e){var t=[];return e.each((function(e,a){t.push(r($(a)))})),t},r=function(e){var t=$.extend(!0,{},e.data("lcf-registered-data"));switch(t.type){case"text":case"number":case"email":case"password":case"image":case"file":t.value=e.find("> .meta-box-wrap input").val();break;case"wysiwyg":t.value=n.wysiwygGetContent(e.find("> .meta-box-wrap textarea"));break;case"textarea":t.value=e.find("> .meta-box-wrap textarea").val();break;case"checkbox":t.value=[],e.find("> .meta-box-wrap input:checked").each((function(e,r){t.value.push($(r).val())}));break;case"radio":t.value=e.find("> .meta-box-wrap input:checked").val();break;case"select":t.value=e.find("> .meta-box-wrap select").val();break;case"repeater":t.value=[],e.find("> .meta-box-wrap > .lcf-repeater > .field-group-items > li").each((function(e,r){var i=$(r).find("> .field-line-wrapper > .field-group");t.value.push(a(i.find("> li")))}));break;default:t=null}return t},a=function(e){var t=[];return e.each((function(e,r){var a=$(r);t.push(i(a))})),t},i=function(e){var t=$.extend(!0,{},e.data("lcf-registered-data"));switch(t.type){case"text":case"number":case"email":case"password":case"image":case"file":t.value=e.find("> .repeater-item-input input").val();break;case"wysiwyg":t.value=n.wysiwygGetContent(e.find("> .repeater-item-input > .lcf-wysiwyg-wrapper > .wysiwyg-editor"));break;case"textarea":t.value=e.find("> .repeater-item-input textarea").val();break;case"checkbox":t.value=[],e.find("> .repeater-item-input input:checked").each((function(e,r){t.value.push($(r).val())}));break;case"radio":t.value=e.find("> .repeater-item-input input:checked").val();break;case"select":t.value=e.find("> .repeater-item-input select").val();break;case"repeater":t.value=[],e.find("> .repeater-item-input > .lcf-repeater > .field-group-items > li").each((function(e,r){var i=$(r).find("> .field-line-wrapper > .field-group");t.value.push(a(i.find("> li")))}));break;default:t=null}return t};e.$_EXPORT_TO.closest("form").on("submit",(function(){var r;e.$_EXPORT_TO.val(n.jsonEncode((r=[],$("#custom_fields_container").find("> .meta-boxes").each((function(e,a){var i=$(a),n=i.data("lcf-field-group"),l=i.find("> .meta-boxes-body > .meta-box");n.items=t(l),r.push(n)})),r)))}))}}]),e}();jQuery(document).ready((function(){new l,document.addEventListener("core-init-resources",(function(){new l}))}))})();
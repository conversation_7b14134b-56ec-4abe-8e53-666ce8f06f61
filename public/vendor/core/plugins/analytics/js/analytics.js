(()=>{function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(e,a){for(var i=0;i<a.length;i++){var n=a[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(r=n.key,o=void 0,o=function(e,a){if("object"!==t(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var n=i.call(e,a||"default");if("object"!==t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(e)}(r,"string"),"symbol"===t(o)?o:String(o)),n)}var r,o}var a=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var a,i,n;return a=t,n=[{key:"initCharts",value:function(){var t=$("div[data-stats]").data("stats"),e=$("div[data-country-stats]").data("country-stats"),a=$("div[data-lang-pageviews]").data("lang-pageviews"),i=$("div[data-lang-visits]").data("lang-visits"),n=[];$.each(t,(function(t,e){n.push({axis:e.axis,visitors:e.visitors,pageViews:e.pageViews})})),$("#stats-chart").length&&new Morris.Area({element:"stats-chart",resize:!0,data:n,xkey:"axis",ykeys:["visitors","pageViews"],labels:[i,a],lineColors:["#dd4d37","#3c8dbc"],hideHover:"auto",parseTime:!1});var r={};$.each(e,(function(t,e){r[e[0]]=e[1]})),$(document).find("#world-map").vectorMap({map:"world_mill_en",backgroundColor:"transparent",regionStyle:{initial:{fill:"#e4e4e4","fill-opacity":1,stroke:"none","stroke-width":0,"stroke-opacity":1}},series:{regions:[{values:r,scale:["#c64333","#dd4b39"],normalizeFunction:"polynomial"}]},onRegionLabelShow:function(t,e,a){void 0!==r[a]&&e.html(e.html()+": "+r[a]+" "+i)}})}}],(i=null)&&e(a.prototype,i),n&&e(a,n),Object.defineProperty(a,"prototype",{writable:!1}),t}();$(document).ready((function(){BDashboard.loadWidget($("#widget_analytics_general").find(".widget-content"),route("analytics.general"),null,(function(){a.initCharts()})),BDashboard.loadWidget($("#widget_analytics_page").find(".widget-content"),route("analytics.page")),BDashboard.loadWidget($("#widget_analytics_browser").find(".widget-content"),route("analytics.browser")),BDashboard.loadWidget($("#widget_analytics_referrer").find(".widget-content"),route("analytics.referrer"))}))})();
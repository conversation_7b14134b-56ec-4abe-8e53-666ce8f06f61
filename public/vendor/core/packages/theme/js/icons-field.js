$(document).ready((function(){"use strict";var t=function(){var t=window.themeIcons||[];t&&$(document).find(".icon-select").each((function(e,n){var c=$(n);if(!c.data("check-initialized")||!c.hasClass("select2-hidden-accessible")){var i=c.children("option:selected").val(),o='<option value="">'+c.data("empty-value")+"</option>";t.forEach((function(t){o+='<option value="'+t+'">'+t+"</option>"})),c.html(o),c.val(i);var a=function(t){return t.id?$('<span><i class="'.concat(t.id,'"></i></span> ').concat(t.text,"</span>")):t.text},s={templateResult:function(t){return a(t)},width:"100%",templateSelection:function(t){return a(t)}},l=c.closest(".modal");l.length&&(s.dropdownParent=l),c.select2(s)}}))};t(),document.addEventListener("core-init-resources",(function(){t()}))}));
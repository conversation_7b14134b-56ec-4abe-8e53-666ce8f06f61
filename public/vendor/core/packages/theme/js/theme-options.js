$(document).ready((function(){$(document).find(".colorpicker-input").length>0&&$(document).find(".colorpicker-input").colorpicker(),$(document).find(".iconpicker-input").length>0&&$(document).find(".iconpicker-input").iconpicker({selected:!0,hideOnSelect:!0}),$(document).ready((function(){$(document).on("click",".button-save-theme-options",(function(e){e.preventDefault();var n=$(e.currentTarget);if(n.addClass("button-loading"),"undefined"!=typeof tinymce)for(var t in tinymce.editors)tinymce.editors[t].getContent&&$("#"+t).html(tinymce.editors[t].getContent());var o=n.closest("form");$httpClient.make().post(o.prop("action"),new FormData(o[0])).then((function(e){var n=e.data;Apps.showSuccess(n.message),o.removeClass("dirty")})).finally((function(){n.removeClass("button-loading")}))})),$('.theme-option-sidebar a[data-bs-toggle="tab"]').on("click",(function(){Apps.initResources(),"undefined"!=typeof EditorManagement&&(window.EDITOR=(new EditorManagement).init(),window.EditorManagement=window.EditorManagement||EditorManagement)}))}))}));
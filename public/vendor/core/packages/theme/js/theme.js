(()=>{function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(t,n){for(var o=0;o<n.length;o++){var r=n[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,(a=r.key,i=void 0,i=function(t,n){if("object"!==e(t)||null===t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var r=o.call(t,n||"default");if("object"!==e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(a,"string"),"symbol"===e(i)?i:String(i)),r)}var a,i}var n=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var n,o,r;return n=e,(o=[{key:"init",value:function(){$(document).on("click",".btn-trigger-active-theme",(function(e){e.preventDefault();var t=$(e.currentTarget);t.addClass("button-loading"),$httpClient.make().post(route("theme.active",{theme:t.data("theme")})).then((function(e){var t=e.data;Apps.showSuccess(t.message),window.location.reload()})).finally((function(){t.removeClass("button-loading")}))})),$(document).on("click",".btn-trigger-remove-theme",(function(e){e.preventDefault(),$("#confirm-remove-theme-button").data("theme",$(e.currentTarget).data("theme")),$("#remove-theme-modal").modal("show")})),$(document).on("click","#confirm-remove-theme-button",(function(e){e.preventDefault();var t=$(e.currentTarget);t.addClass("button-loading"),$httpClient.make().post(route("theme.remove",{theme:t.data("theme")})).then((function(e){var t=e.data;Apps.showSuccess(t.message),window.location.reload()})).finally((function(){t.removeClass("button-loading"),$("#remove-theme-modal").modal("hide")}))}))}}])&&t(n.prototype,o),r&&t(n,r),Object.defineProperty(n,"prototype",{writable:!1}),e}();$(document).ready((function(){(new n).init()}))})();
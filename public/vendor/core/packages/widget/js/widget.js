(()=>{function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(t,i){for(var o=0;o<i.length;o++){var a=i[o];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,(n=a.key,r=void 0,r=function(t,i){if("object"!==e(t)||null===t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var a=o.call(t,i||"default");if("object"!==e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===i?String:Number)(t)}(n,"string"),"symbol"===e(r)?r:String(r)),a)}var n,r}var i=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var i,o,a;return i=e,(o=[{key:"init",value:function(){var e=[{name:"wrap-widgets",pull:"clone",put:!1}];$.each($(".sidebar-item"),(function(){e.push({name:"wrap-widgets",pull:!0,put:!0})}));var t=function(e){if(e.length>0){var t=[];$.each(e.find("li[data-id]"),(function(e,i){t.push($(i).find("form").serialize())})),Apps.showNotice("info",AppVars.languages.notices_msg.processing_request),$httpClient.make().post(BWidget.routes.save_widgets_sidebar,{items:t,sidebar_id:e.data("id")}).then((function(t){var i=t.data;e.find("ul").html(i.data),Apps.callScroll($(".list-page-select-widget")),Apps.initResources(),Apps.initMediaIntegrate(),Apps.showSuccess(i.message)})).finally((function(){e.find(".widget_save i").remove()}))}};e.forEach((function(e,i){Sortable.create(document.getElementById("wrap-widget-"+(i+1)),{sort:0!==i,group:e,delay:0,disabled:!1,store:null,animation:150,handle:".widget-handle",ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dataIdAttr:"data-id",forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,scroll:!0,scrollSensitivity:30,scrollSpeed:10,onUpdate:function(e){e.from!==e.to&&t($(e.from).closest(".sidebar-item")),t($(e.item).closest(".sidebar-item"))},onAdd:function(e){e.from!==e.to&&t($(e.from).closest(".sidebar-item")),t($(e.item).closest(".sidebar-item"))}})}));var i=$("#wrap-widgets");i.on("click",".widget-control-delete",(function(e){e.preventDefault();var t=$(e.currentTarget),i=t.closest("li");t.addClass("button-loading"),Apps.showNotice("info",AppVars.languages.notices_msg.processing_request),$httpClient.make().delete(BWidget.routes.delete,{widget_id:i.data("id"),position:i.data("position"),sidebar_id:t.closest(".sidebar-item").data("id")}).then((function(e){var t=e.data;Apps.showSuccess(t.message),i.fadeOut().remove()})).finally((function(){i.find(".widget-control-delete").removeClass("button-loading")}))})),i.on("click","#added-widget .widget-handle",(function(e){var t=$(e.currentTarget);t.closest("li").find(".widget-content").slideToggle(300),t.find(".fa").toggleClass("fa-caret-up"),t.find(".fa").toggleClass("fa-caret-down")})),i.on("click","#added-widget .sidebar-header",(function(e){var t=$(e.currentTarget);t.closest(".sidebar-area").find("> ul").slideToggle(300),t.find(".fa").toggleClass("fa-caret-up"),t.find(".fa").toggleClass("fa-caret-down")})),i.on("click",".widget_save",(function(e){e.preventDefault();var i=$(e.currentTarget);i.addClass("button-loading"),t(i.closest(".sidebar-item"))})),Apps.callScroll($(".list-page-select-widget"))}}])&&t(i.prototype,o),a&&t(i,a),Object.defineProperty(i,"prototype",{writable:!1}),e}();$(document).ready((function(){(new i).init()}))})();
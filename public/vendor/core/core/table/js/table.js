(()=>{function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(e,a){for(var n=0;n<a.length;n++){var o=a[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,(l=o.key,r=void 0,r=function(e,a){if("object"!==t(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,a||"default");if("object"!==t(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(e)}(l,"string"),"symbol"===t(r)?r:String(r)),o)}var l,r}!function(t,a){"use strict";var n=function(e,a){var n=e.ajax.params();return n.action=a,n._token=t('meta[name="csrf-token"]').attr("content"),n},o=function(e,a){var n=e+"/export",o=new XMLHttpRequest;o.open("POST",n,!0),o.responseType="arraybuffer",o.onload=function(){if(200===this.status){var t="",e=o.getResponseHeader("Content-Disposition");if(e&&-1!==e.indexOf("attachment")){var a=/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(e);null!=a&&a[1]&&(t=a[1].replace(/['"]/g,""))}var n=o.getResponseHeader("Content-Type"),l=new Blob([this.response],{type:n});if(void 0!==window.navigator.msSaveBlob)window.navigator.msSaveBlob(l,t);else{var r=window.URL||window.webkitURL,c=r.createObjectURL(l);if(t){var i=document.createElement("a");void 0===i.download?window.location=c:(i.href=c,i.download=t,document.body.appendChild(i),i.trigger("click"))}else window.location=c;setTimeout((function(){r.revokeObjectURL(c)}),100)}}},o.setRequestHeader("Content-type","application/x-www-form-urlencoded"),o.send(t.param(a))},l=function(e,a){var n=e.ajax.url()||"",o=e.ajax.params();return o.action=a,n.indexOf("?")>-1?n+"&"+t.param(o):n+"?"+t.param(o)};a.ext.buttons.excel={className:"buttons-excel",text:function(t){return'<i class="far fa-file-excel"></i> '+t.i18n("buttons.excel",AppVars.languages.tables.excel?AppVars.languages.tables.excel:"Excel")},action:function(t,e){window.location=l(e,"excel")}},a.ext.buttons.postExcel={className:"buttons-excel",text:function(t){return'<i class="far fa-file-excel"></i> '+t.i18n("buttons.excel",AppVars.languages.tables.excel?AppVars.languages.tables.excel:"Excel")},action:function(t,e){var a=e.ajax.url()||window.location.href,l=n(e,"excel");o(a,l)}},a.ext.buttons.export={extend:"collection",className:"buttons-export",text:function(t){return'<i class="fa fa-download"></i> '+t.i18n("buttons.export",AppVars.languages.tables.export?AppVars.languages.tables.export:"Export")+'&nbsp;<span class="caret"/>'},buttons:["csv","excel"]},a.ext.buttons.csv={className:"buttons-csv",text:function(t){return'<i class="fas fa-file-csv"></i> '+t.i18n("buttons.csv",AppVars.languages.tables.csv?AppVars.languages.tables.csv:"CSV")},action:function(t,e){window.location=l(e,"csv")}},a.ext.buttons.postCsv={className:"buttons-csv",text:function(t){return'<i class="fas fa-file-csv"></i> '+t.i18n("buttons.csv",AppVars.languages.tables.csv?AppVars.languages.tables.csv:"CSV")},action:function(t,e){var a=e.ajax.url()||window.location.href,l=n(e,"csv");o(a,l)}},a.ext.buttons.pdf={className:"buttons-pdf",text:function(t){return'<i class="far fa-file-pdf"></i> '+t.i18n("buttons.pdf","PDF")},action:function(t,e){window.location=l(e,"pdf")}},a.ext.buttons.postPdf={className:"buttons-pdf",text:function(t){return'<i class="far fa-file-pdf"></i> '+t.i18n("buttons.pdf","PDF")},action:function(t,e){var a=e.ajax.url()||window.location.href,l=n(e,"pdf");o(a,l)}},a.ext.buttons.print={className:"buttons-print",text:function(t){return'<i class="fa fa-print"></i> '+t.i18n("buttons.print",AppVars.languages.tables.print?AppVars.languages.tables.print:"Print")},action:function(t,e){window.location=l(e,"print")}},a.ext.buttons.reset={className:"buttons-reset",text:function(t){return'<i class="fa fa-undo"></i> '+t.i18n("buttons.reset",AppVars.languages.tables.reset?AppVars.languages.tables.reset:"Reset")},action:function(){t(".table thead input").val("").keyup(),t(".table thead select").val("").change()}},a.ext.buttons.reload={className:"buttons-reload",text:function(t){return'<i class="fas fa-sync"></i> '+t.i18n("buttons.reload",AppVars.languages.tables.reload?AppVars.languages.tables.reload:"Reload")},action:function(t,e){e.draw(!1)}},a.ext.buttons.create={className:"buttons-create",text:function(t){return'<i class="fa fa-plus"></i> '+t.i18n("buttons.create","Create")},action:function(){window.location=window.location.href.replace(/\/+$/,"")+"/create"}},void 0!==a.ext.buttons.copyHtml5&&t.extend(a.ext.buttons.copyHtml5,{text:function(t){return'<i class="fa fa-copy"></i> '+t.i18n("buttons.copy","Copy")}}),void 0!==a.ext.buttons.colvis&&(t.extend(a.ext.buttons.colvis,{text:function(t){return'<i class="fa fa-eye"></i> '+t.i18n("buttons.colvis","Column visibility")}}),t.extend(a.ext.buttons.columnVisibility,{_columnText:function(t,e){var a=t.column(e.columns).index(),n=t.settings()[0].aoColumns[a].titleAttr||t.settings()[0].aoColumns[a].sTitle;return n||(n=t.column(a).header().innerHTML),n=n.replace(/\n/g," ").replace(/<br\s*\/?>/gi," ").replace(/<select(.*?)<\/select>/g,"").replace(/<!\-\-.*?\-\->/g,"").replace(/<.*?>/g,"").replace(/^\s+|\s+$/g,""),e.columnText?e.columnText(t,a,n):n}}));var r=function(){function a(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,a),this.init(),this.handleActionsRow(),this.handleActionsExport()}var n,o,l;return n=a,(o=[{key:"init",value:function(){t(document).on("change",".table-check-all",(function(e){var a=t(e.currentTarget),n=a.attr("data-set"),o=a.prop("checked");t(n).each((function(e,a){o?t(a).prop("checked",!0).trigger("change"):t(a).prop("checked",!1).trigger("change")}))})),t(document).find(".table-check-all").closest("th").removeAttr("title"),t(document).on("change",".checkboxes",(function(e){var a=t(e.currentTarget),n=a.closest(".table-wrapper").find(".table").prop("id"),o=a.closest(".table-wrapper").find(".table-check-all"),l=[],r=t("#"+n);r.find(".checkboxes:checked").each((function(e,a){l[e]=t(a).val()}));var c=a.closest("tr");a.prop("checked")?c.addClass("selected"):c.removeClass("selected"),l.length!==r.find(".checkboxes").length?(o.prop("checked",!1),l.length>0?o.prop("indeterminate",!0):o.prop("indeterminate",!1)):(o.prop("checked",!0),o.prop("indeterminate",!1))})),t(document).on("click",".btn-show-table-options",(function(e){e.preventDefault(),t(e.currentTarget).closest(".table-wrapper").find(".table-configuration-wrap").slideToggle(500)})),t(document).on("click",".action-item",(function(e){e.preventDefault();var a=t(e.currentTarget).find("span[data-href]"),n=a.data("action"),o=a.data("href");n&&"#"!==o&&(window.location.href=o)}))}},{key:"handleActionsRow",value:function(){var e=this,a=this;t(document).on("click",".deleteDialog",(function(e){e.preventDefault();var a=t(e.currentTarget);t(".delete-crud-entry").data("section",a.data("section")).data("parent-table",a.closest(".table").prop("id")),t(".modal-confirm-delete").modal("show")})),t(".delete-crud-entry").on("click",(function(e){e.preventDefault();var a=t(e.currentTarget);a.addClass("button-loading");var n=a.data("section");$httpClient.make().delete(n).then((function(e){var o=e.data;window.LaravelDataTables[a.data("parent-table")].row(t('a[data-section="'+n+'"]').closest("tr")).remove().draw(),Apps.showSuccess(o.message),a.closest(".modal").modal("hide")})).finally((function(){a.removeClass("button-loading")}))})),t(document).on("click",".delete-many-entry-trigger",(function(e){e.preventDefault();var a=t(e.currentTarget),n=a.closest(".table-wrapper").find(".table").prop("id"),o=[];if(t("#"+n).find(".checkboxes:checked").each((function(e,a){o[e]=t(a).val()})),0===o.length)return Apps.showError(AppVars.languages.tables.please_select_record?AppVars.languages.tables.please_select_record:"Please select at least one record to perform this action!"),!1;t(".delete-many-entry-button").data("href",a.prop("href")).data("parent-table",n).data("class-item",a.data("class-item")),t(".delete-many-modal").modal("show")})),t(".delete-many-entry-button").on("click",(function(e){e.preventDefault();var a=t(e.currentTarget);a.addClass("button-loading");var n=t("#"+a.data("parent-table")),o=[];n.find(".checkboxes:checked").each((function(e,a){o[e]=t(a).val()})),$httpClient.make().delete(a.data("href"),{ids:o,class:a.data("class-item")}).then((function(t){var e=t.data;Apps.showSuccess(e.message),n.find(".table-check-all").prop("checked",!1).prop("indeterminate",!1),window.LaravelDataTables[a.data("parent-table")].draw(),a.closest(".modal").modal("hide")})).finally((function(){a.removeClass("button-loading")}))})),t(document).on("click","[data-trigger-bulk-action]",(function(e){e.preventDefault();var a=t(e.currentTarget),n=a.closest(".table-wrapper").find(".table").prop("id"),o=[];if(t("#".concat(n)).find(".checkboxes:checked").each((function(e,a){return o.push(t(a).val())})),0===o.length)return Apps.showError(AppVars.languages.tables.please_select_record?AppVars.languages.tables.please_select_record:"Please select at least one record to perform this action!"),!1;t(".confirm-trigger-bulk-actions-button").data("href",a.prop("href")).data("method",a.data("method")).data("table-id",n).data("table-target",a.data("table-target")).data("target",a.data("target"));var l=t(".bulk-action-confirm-modal");l.find(".modal-title > strong").text(a.data("confirmation-modal-title")),l.find(".modal-body > div").text(a.data("confirmation-modal-message")),l.find("button.btn-warning").text(a.data("confirmation-modal-cancel-button")),l.find("button.confirm-trigger-bulk-actions-button").text(a.data("confirmation-modal-button")),l.modal("show")})),t(document).on("click",".confirm-trigger-bulk-actions-button",(function(e){e.preventDefault();var a=t(e.currentTarget);a.addClass("button-loading");var n=a.data("table-id"),o=a.data("method").toLowerCase()||"post",l=t("#".concat(n)),r=[];l.find(".checkboxes:checked").each((function(e,a){return r.push(t(a).val())})),$httpClient.make()[o](a.data("href"),{ids:r,bulk_action:1,bulk_action_table:a.data("table-target"),bulk_action_target:a.data("target")}).then((function(t){var e=t.data;Apps.showSuccess(e.message),l.find(".table-check-all").prop("checked",!1).prop("indeterminate",!1),window.LaravelDataTables[n].draw(),a.closest(".modal").modal("hide")})).finally((function(){a.removeClass("button-loading")}))})),t(document).on("click","[data-dt-single-action]",(function(e){e.preventDefault();var a=t(e.currentTarget),o=a.closest(".table-wrapper").find(".table").prop("id");if(a.data("confirmation-modal")){t(".confirm-trigger-single-action-button").data("href",a.prop("href")).data("method",a.data("method")).data("table-id",o);var l=t(".single-action-confirm-modal");l.find(".modal-title > strong").text(a.data("confirmation-modal-title")),l.find(".modal-body > div").text(a.data("confirmation-modal-message")),l.find("button.btn-warning").text(a.data("confirmation-modal-cancel-button")),l.find("button.confirm-trigger-single-action-button").text(a.data("confirmation-modal-button")),l.modal("show")}else n(o,a.prop("href"),a.data("method"))})),t(document).on("click",".confirm-trigger-single-action-button",(function(e){e.preventDefault();var a=t(e.currentTarget);a.addClass("button-loading"),n(a.data("table-id"),a.data("href"),a.data("method"),(function(){a.closest(".modal").modal("hide"),a.removeClass("button-loading")}),(function(){a.removeClass("button-loading")}))}));var n=function(a,n,o,l,r){var c=t("#".concat(a)),i=o.toLowerCase()||"post";$httpClient.make()[i](n).then((function(t){var n=t.data;Apps.showSuccess(n.message),c.find(".table-check-all").prop("checked",!1).prop("indeterminate",!1),window.LaravelDataTables[a].draw(),"function"==typeof l&&l.apply(e,n)})).catch((function(t){"function"==typeof r&&r.apply(e,t)}))};t(document).on("click",".bulk-change-item",(function(e){e.preventDefault();var n=t(e.currentTarget),o=n.closest(".table-wrapper").find(".table").prop("id"),l=[];if(t("#"+o).find(".checkboxes:checked").each((function(e,a){l[e]=t(a).val()})),0===l.length)return Apps.showError(AppVars.languages.tables.please_select_record?AppVars.languages.tables.please_select_record:"Please select at least one record to perform this action!"),!1;a.loadBulkChangeData(n),t(".confirm-bulk-change-button").data("parent-table",o).data("class-item",n.data("class-item")).data("key",n.data("key")).data("url",n.data("save-url")),t(".modal-bulk-change-items").modal("show")})),t(document).on("click",".confirm-bulk-change-button",(function(e){e.preventDefault();var a=t(e.currentTarget),n=a.closest(".modal").find(".input-value").val(),o=a.data("key"),l=t("#"+a.data("parent-table")),r=[];l.find(".checkboxes:checked").each((function(e,a){r[e]=t(a).val()})),a.addClass("button-loading"),$httpClient.make().post(a.data("url"),{ids:r,key:o,value:n,class:a.data("class-item")}).then((function(e){var n=e.data;Apps.showSuccess(n.message),l.find(".table-check-all").prop("checked",!1).prop("indeterminate",!1),t.each(r,(function(t,e){window.LaravelDataTables[a.data("parent-table")].row(l.find('.checkboxes[value="'+e+'"]').closest("tr")).remove().draw()})),a.closest(".modal").modal("hide")})).finally((function(){a.removeClass("button-loading")}))}))}},{key:"loadBulkChangeData",value:function(e){var a=t(".modal-bulk-change-items");$httpClient.make().get(a.find(".confirm-bulk-change-button").data("load-url"),{class:e.data("class-item"),key:e.data("key")}).then((function(e){var n=t.map(e.data.data,(function(t,e){return{id:e,name:t}}));t(".modal-bulk-change-content").html(e.data.html);var o=a.find("input[type=text].input-value");o.length&&(o.typeahead({source:n}),o.data("typeahead").source=n),Apps.initResources()}))}},{key:"handleActionsExport",value:function(){t(document).on("click",".export-data",(function(e){var a=t(e.currentTarget),n=a.closest(".table-wrapper").find(".table").prop("id"),o=[];t("#"+n).find(".checkboxes:checked").each((function(e,a){o[e]=t(a).val()})),e.preventDefault(),$httpClient.make().post(a.prop("href"),{"ids-checked":o}).then((function(t){var e=t.data,a=document.createElement("a");a.href=e.file,a.download=e.name,document.body.appendChild(a),a.trigger("click"),a.remove()}))}))}}])&&e(n.prototype,o),l&&e(n,l),Object.defineProperty(n,"prototype",{writable:!1}),a}();t(document).ready((function(){new r}))}(jQuery,jQuery.fn.dataTable)})();
(()=>{function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e=function(){return r};var n,r={},o=Object.prototype,a=o.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},c="function"==typeof Symbol?Symbol:{},l=c.iterator||"@@iterator",u=c.asyncIterator||"@@asyncIterator",s=c.toStringTag||"@@toStringTag";function f(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(n){f=function(t,e,n){return t[e]=n}}function d(t,e,n,r){var o=e&&e.prototype instanceof b?e:b,a=Object.create(o.prototype),c=new P(r||[]);return i(a,"_invoke",{value:_(t,n,c)}),a}function h(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}r.wrap=d;var p="suspendedStart",m="suspendedYield",v="executing",y="completed",g={};function b(){}function w(){}function $(){}var x={};f(x,l,(function(){return this}));var k=Object.getPrototypeOf,C=k&&k(k(D([])));C&&C!==o&&a.call(C,l)&&(x=C);var E=$.prototype=b.prototype=Object.create(x);function L(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function S(e,n){function r(o,i,c,l){var u=h(e[o],e,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==t(f)&&a.call(f,"__await")?n.resolve(f.__await).then((function(t){r("next",t,c,l)}),(function(t){r("throw",t,c,l)})):n.resolve(f).then((function(t){s.value=t,c(s)}),(function(t){return r("throw",t,c,l)}))}l(u.arg)}var o;i(this,"_invoke",{value:function(t,e){function a(){return new n((function(n,o){r(t,e,n,o)}))}return o=o?o.then(a,a):a()}})}function _(t,e,r){var o=p;return function(a,i){if(o===v)throw new Error("Generator is already running");if(o===y){if("throw"===a)throw i;return{value:n,done:!0}}for(r.method=a,r.arg=i;;){var c=r.delegate;if(c){var l=j(c,r);if(l){if(l===g)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=v;var u=h(t,e,r);if("normal"===u.type){if(o=r.done?y:m,u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=y,r.method="throw",r.arg=u.arg)}}}function j(t,e){var r=e.method,o=t.iterator[r];if(o===n)return e.delegate=null,"throw"===r&&t.iterator.return&&(e.method="return",e.arg=n,j(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var a=h(o,t.iterator,e.arg);if("throw"===a.type)return e.method="throw",e.arg=a.arg,e.delegate=null,g;var i=a.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=n),e.delegate=null,g):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,g)}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function D(e){if(e||""===e){var r=e[l];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function t(){for(;++o<e.length;)if(a.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=n,t.done=!0,t};return i.next=i}}throw new TypeError(t(e)+" is not iterable")}return w.prototype=$,i(E,"constructor",{value:$,configurable:!0}),i($,"constructor",{value:w,configurable:!0}),w.displayName=f($,s,"GeneratorFunction"),r.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,$):(t.__proto__=$,f(t,s,"GeneratorFunction")),t.prototype=Object.create(E),t},r.awrap=function(t){return{__await:t}},L(S.prototype),f(S.prototype,u,(function(){return this})),r.AsyncIterator=S,r.async=function(t,e,n,o,a){void 0===a&&(a=Promise);var i=new S(d(t,e,n,o),a);return r.isGeneratorFunction(e)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},L(E),f(E,s,"Generator"),f(E,l,(function(){return this})),f(E,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},r.values=D,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=n,this.done=!1,this.delegate=null,this.method="next",this.arg=n,this.tryEntries.forEach(O),!t)for(var e in this)"t"===e.charAt(0)&&a.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=n)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(r,o){return c.type="throw",c.arg=t,e.next=r,o&&(e.method="next",e.arg=n),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var l=a.call(i,"catchLoc"),u=a.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&a.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),O(n),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;O(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:D(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=n),g}},r}function n(t,e,n,r,o,a,i){try{var c=t[a](i),l=c.value}catch(t){return void n(t)}c.done?e(l):Promise.resolve(l).then(r,o)}function r(e,n){for(var r=0;r<n.length;r++){var o=n[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,(a=o.key,i=void 0,i=function(e,n){if("object"!==t(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,n||"default");if("object"!==t(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}(a,"string"),"symbol"===t(i)?i:String(i)),o)}var a,i}var o=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var o,a,i,c,l;return o=t,a=[{key:"init",value:function(){var t=this;this.handleMultipleAdminEmails(),$("input[data-key=email-config-status-btn]").on("change",(function(t){var e=$(t.currentTarget),n=e.prop("id"),r=e.data("change-url");$httpClient.make().post(r,{key:n,value:e.prop("checked")?1:0}).then((function(t){var e=t.data;return Apps.showSuccess(e.message)}))})),$(document).on("change",".setting-select-options",(function(t){$(".setting-wrapper").addClass("hidden"),$(".setting-wrapper[data-type="+$(t.currentTarget).val()+"]").removeClass("hidden")})),$(".send-test-email-trigger-button").on("click",(function(t){t.preventDefault();var e=$(t.currentTarget),n=e.text(),r=new FormData(e.closest("form")[0]);e.text(e.data("saving")),$httpClient.make().postForm(route("settings.email.edit"),r).then((function(t){var e=t.data;Apps.showSuccess(e.message),$("#send-test-email-modal").modal("show")})).finally((function(){e.text(n)}))})),$("#send-test-email-btn").on("click",(function(t){t.preventDefault();var e=$(t.currentTarget);e.addClass("button-loading"),$httpClient.make().post(route("setting.email.send.test"),{email:e.closest(".modal-content").find("input[name=email]").val()}).then((function(t){var n=t.data;Apps.showSuccess(n.message),e.closest(".modal").modal("hide")})).finally((function(){e.removeClass("button-loading")}))})),$(".generate-thumbnails-trigger-button").on("click",(function(t){t.preventDefault();var e=$(t.currentTarget),n=e.text();e.text(e.data("saving")),$httpClient.make().postForm(route("settings.media.post"),new FormData(e.closest("form")[0])).then((function(){return $("#generate-thumbnails-modal").modal("show")})).finally((function(){e.text(n)}))})),$("#generate-thumbnails-button").on("click",(function(t){t.preventDefault();var e=$(t.currentTarget);e.addClass("button-loading"),$httpClient.make().post(route("settings.media.generate-thumbnails")).then((function(t){var e=t.data;return Apps.showSuccess(e.message)})).finally((function(){e.removeClass("button-loading"),e.closest(".modal").modal("hide")}))})),"undefined"!=typeof CodeMirror&&Apps.initCodeEditor("mail-template-editor"),$(document).on("click",".btn-trigger-reset-to-default",(function(t){t.preventDefault(),$("#reset-template-to-default-button").data("target",$(t.currentTarget).data("target")),$("#reset-template-to-default-modal").modal("show")})),$(document).on("click",".js-select-mail-variable",(function(t){t.preventDefault();var e=$(t.currentTarget),n=$(".CodeMirror")[0].CodeMirror,r="{{ "+e.data("key")+" }}";if(n.somethingSelected())n.replaceSelection(r);else{var o=n.getCursor(),a={line:o.line,ch:o.ch};n.replaceRange(r,a)}})),$(document).on("click",".js-select-mail-function",(function(t){t.preventDefault();var e=$(t.currentTarget),n=$(".CodeMirror")[0].CodeMirror,r=e.data("sample");if(n.somethingSelected())n.replaceSelection(r);else{var o=n.getCursor(),a={line:o.line,ch:o.ch};n.replaceRange(r,a)}})),$(document).on("click","#reset-template-to-default-button",(function(t){t.preventDefault();var e=$(t.currentTarget);e.addClass("button-loading"),$httpClient.make().post(e.data("target"),{email_subject_key:$("input[name=email_subject_key]").val(),module:$("input[name=module]").val(),template_file:$("input[name=template_file]").val()}).then((function(t){var e=t.data;Apps.showSuccess(e.message),setTimeout((function(){window.location.reload()}),1e3),$("#reset-template-to-default-modal").modal("hide")})).finally((function(){e.removeClass("button-loading")}))})),$(document).on("change",".check-all",(function(t){var e=$(t.currentTarget),n=e.attr("data-set"),r=e.prop("checked");$(n).each((function(t,e){r?$(e).prop("checked",!0):$(e).prop("checked",!1)}))})),$("input.setting-selection-option").each((function(t,e){var n=$($(e).data("target"));$(e).on("change",(function(){"1"==$(e).val()?(n.removeClass("d-none"),Apps.initResources()):n.addClass("d-none")}))})),$(document).on("click",".cronjob #copy-command",(function(){t.copyCommand()}))}},{key:"handleMultipleAdminEmails",value:function(){var t=$("#admin_email_wrapper");if(t.length){var e=t.find("#add"),n=parseInt(t.data("max"),10),r=t.data("emails");0===r.length&&(r=[""]);var o=function(){t.find("input[type=email]").length>=n?e.addClass("disabled"):e.removeClass("disabled")},a=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.before('<div class="d-flex mt-2 more-email align-items-center">\n                <input type="email" class="next-input" placeholder="'.concat(e.data("placeholder"),'" name="admin_email[]" value="').concat(t||"",'" />\n                <a class="btn btn-link text-danger"><i class="fas fa-minus"></i></a>\n            </div>'))};t.on("click",".more-email > a",(function(){$(this).parent(".more-email").remove(),o()})),e.on("click",(function(t){t.preventDefault(),a(),o()})),r.forEach((function(t){a(t)})),o()}}},{key:"copyCommand",value:(c=e().mark((function t(){var n,r,o;return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=$(".cronjob #command"),r=n.val(),!navigator.clipboard||!window.isSecureContext){t.next=8;break}return t.next=5,navigator.clipboard.writeText(r);case 5:Apps.showSuccess(n.data("copied")),t.next=15;break;case 8:(o=document.createElement("textarea")).value=r,o.style.position="absolute",o.style.left="-999999px",document.body.prepend(o),o.select();try{document.execCommand("copy"),Apps.showSuccess(n.data("copied"))}catch(t){console.error(t)}finally{o.remove()}case 15:case"end":return t.stop()}}),t)})),l=function(){var t=this,e=arguments;return new Promise((function(r,o){var a=c.apply(t,e);function i(t){n(a,r,o,i,l,"next",t)}function l(t){n(a,r,o,i,l,"throw",t)}i(void 0)}))},function(){return l.apply(this,arguments)})}],a&&r(o.prototype,a),i&&r(o,i),Object.defineProperty(o,"prototype",{writable:!1}),t}();$(document).ready((function(){(new o).init()}))})();
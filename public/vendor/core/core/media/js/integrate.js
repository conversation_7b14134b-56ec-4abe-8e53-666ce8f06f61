(()=>{var t={3809:(t,e,i)=>{"use strict";i.d(e,{O:()=>a,s:()=>r});var a=$.parseJSON(localStorage.getItem("MediaConfig"))||{},n={app_key:APP_MEDIA_CONFIG.random_hash?APP_MEDIA_CONFIG.random_hash:"21d06709fe1d3abdf0e35ddda89c4b279",request_params:{view_type:"tiles",filter:"everything",view_in:"all_media",sort_by:"created_at-desc",folder_id:0},hide_details_pane:!1,icons:{folder:"fa fa-folder"},actions_list:{basic:[{icon:"fa fa-eye",name:"Preview",action:"preview",order:0,class:"app-action-preview"},{icon:"fa fa-crop",name:"<PERSON>rop",action:"crop",order:1,class:"app-action-crop"}],file:[{icon:"fa fa-link",name:"Copy link",action:"copy_link",order:0,class:"app-action-copy-link"},{icon:"far fa-edit",name:"<PERSON><PERSON>",action:"rename",order:1,class:"app-action-rename"},{icon:"fa fa-copy",name:"Make a copy",action:"make_copy",order:2,class:"app-action-make-copy"},{icon:"fas fa-file-signature",name:"Alt text",action:"alt_text",order:3,class:"app-action-alt-text"}],user:[{icon:"fa fa-star",name:"Favorite",action:"favorite",order:2,class:"app-action-favorite"},{icon:"fa fa-star",name:"Remove favorite",action:"remove_favorite",order:3,class:"app-action-favorite"}],other:[{icon:"fa fa-download",name:"Download",action:"download",order:0,class:"app-action-download"},{icon:"fa fa-trash",name:"Move to trash",action:"trash",order:1,class:"app-action-trash"},{icon:"fa fa-eraser",name:"Delete permanently",action:"delete",order:2,class:"app-action-delete"},{icon:"fa fa-undo",name:"Restore",action:"restore",order:3,class:"app-action-restore"}]}};a.app_key&&a.app_key===n.app_key||(a=n),a.request_params.search="";var r=$.parseJSON(localStorage.getItem("RecentItems"))||[]},3363:(t,e,i)=>{"use strict";i.d(e,{Z:()=>o});var a=i(3809);function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function r(t,e){for(var i=0;i<e.length;i++){var a=e[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,(r=a.key,o=void 0,o=function(t,e){if("object"!==n(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var a=i.call(t,e||"default");if("object"!==n(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(r,"string"),"symbol"===n(o)?o:String(o)),a)}var r,o}var o=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var e,i,n;return e=t,n=[{key:"getUrlParam",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;e||(e=window.location.search);var i=new RegExp("(?:[?&]|&)"+t+"=([^&]+)","i"),a=e.match(i);return a&&a.length>1?a[1]:null}},{key:"asset",value:function(t){if("//"===t.substring(0,2)||"http://"===t.substring(0,7)||"https://"===t.substring(0,8))return t;var e="/"!==APP_MEDIA_URL.base_url.substr(-1,1)?APP_MEDIA_URL.base_url+"/":APP_MEDIA_URL.base_url;return"/"===t.substring(0,1)?e+t.substring(1):e+t}},{key:"showAjaxLoading",value:function(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:$(".app-media-main")).addClass("on-loading").append($("#app_media_loading").html())}},{key:"hideAjaxLoading",value:function(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:$(".app-media-main")).removeClass("on-loading").find(".loading-wrapper").remove()}},{key:"isOnAjaxLoading",value:function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:$(".app-media-items")).hasClass("on-loading")}},{key:"jsonEncode",value:function(t){return void 0===t&&(t=null),JSON.stringify(t)}},{key:"jsonDecode",value:function(t,e){if(!t)return e;if("string"==typeof t){var i;try{i=$.parseJSON(t)}catch(t){i=e}return i}return t}},{key:"getRequestParams",value:function(){return window.appMedia.options&&"modal"===window.appMedia.options.open_in?$.extend(!0,a.O.request_params,window.appMedia.options||{}):a.O.request_params}},{key:"setSelectedFile",value:function(t){void 0!==window.appMedia.options?window.appMedia.options.selected_file_id=t:a.O.request_params.selected_file_id=t}},{key:"getConfigs",value:function(){return a.O}},{key:"storeConfig",value:function(){localStorage.setItem("MediaConfig",t.jsonEncode(a.O))}},{key:"storeRecentItems",value:function(){localStorage.setItem("RecentItems",t.jsonEncode(a.s))}},{key:"addToRecent",value:function(e){e instanceof Array?t.each(e,(function(t){a.s.push(t)})):(a.s.push(e),this.storeRecentItems())}},{key:"getItems",value:function(){var t=[];return $(".js-media-list-title").each((function(e,i){var a=$(i),n=a.data()||{};n.index_key=a.index(),t.push(n)})),t}},{key:"getSelectedItems",value:function(){var t=[];return $(".js-media-list-title input[type=checkbox]:checked").each((function(e,i){var a=$(i).closest(".js-media-list-title"),n=a.data()||{};n.index_key=a.index(),t.push(n)})),t}},{key:"getSelectedFiles",value:function(){var t=[];return $(".js-media-list-title[data-context=file] input[type=checkbox]:checked").each((function(e,i){var a=$(i).closest(".js-media-list-title"),n=a.data()||{};n.index_key=a.index(),t.push(n)})),t}},{key:"getSelectedFolder",value:function(){var t=[];return $(".js-media-list-title[data-context=folder] input[type=checkbox]:checked").each((function(e,i){var a=$(i).closest(".js-media-list-title"),n=a.data()||{};n.index_key=a.index(),t.push(n)})),t}},{key:"isUseInModal",value:function(){return window.appMedia&&window.appMedia.options&&"modal"===window.appMedia.options.open_in}},{key:"resetPagination",value:function(){APP_MEDIA_CONFIG.pagination={paged:1,posts_per_page:40,in_process_get_media:!1,has_more:!0}}},{key:"trans",value:function(t){return _.get(APP_MEDIA_CONFIG.translations,t,t)}},{key:"config",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return _.get(APP_MEDIA_CONFIG,t,e)}},{key:"hasPermission",value:function(e){return t.inArray(t.config("permissions",[]),e)}},{key:"inArray",value:function(t,e){return _.includes(t,e)}},{key:"each",value:function(t,e){return _.each(t,e)}},{key:"forEach",value:function(t,e){return _.forEach(t,e)}},{key:"arrayReject",value:function(t,e){return _.reject(t,e)}},{key:"arrayFilter",value:function(t,e){return _.filter(t,e)}},{key:"arrayFirst",value:function(t){return _.first(t)}},{key:"isArray",value:function(t){return _.isArray(t)}},{key:"isEmpty",value:function(t){return _.isEmpty(t)}},{key:"size",value:function(t){return _.size(t)}}],(i=null)&&r(e.prototype,i),n&&r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}()},5498:(t,e,i)=>{"use strict";i.d(e,{b:()=>l});var a=i(3809),n=i(3363),r=i(6554),o=i(3129),s=i.n(o);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function h(t,e){for(var i=0;i<e.length;i++){var a=e[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,(n=a.key,r=void 0,r=function(t,e){if("object"!==c(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var a=i.call(t,e||"default");if("object"!==c(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n,"string"),"symbol"===c(r)?r:String(r)),a)}var n,r}var l=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var e,i,o;return e=t,o=[{key:"handleDropdown",value:function(){var e=n.Z.size(n.Z.getSelectedItems());t.renderActions(),e>0?$(".app-dropdown-actions").removeClass("disabled"):$(".app-dropdown-actions").addClass("disabled")}},{key:"handlePreview",value:function(){var t=[];n.Z.each(n.Z.getSelectedFiles(),(function(e){e.preview_url&&(t.push({src:e.preview_url,type:e.preview_type}),a.s.push(e.id))})),n.Z.size(t)>0?($.fancybox.open(t),n.Z.storeRecentItems()):this.handleGlobalAction("download")}},{key:"renderCropImage",value:function(){var t,e=$("#app_media_crop_image").html(),i=$("#modal_crop_image .crop-image").empty(),a=n.Z.getSelectedItems()[0],r=$("#modal_crop_image .form-crop"),o=e.replace(/__src__/gi,a.full_url);i.append(o);var c=i.find("img")[0],h={minContainerWidth:550,minContainerHeight:550,dragMode:"move",crop:function(e){t=e.detail,r.find('input[name="image_id"]').val(a.id),r.find('input[name="crop_data"]').val(JSON.stringify(t)),d(t.height),u(t.width)}},l=new(s())(c,h);r.find("#aspectRatio").on("click",(function(){l.destroy(),$(this).is(":checked")?h.aspectRatio=t.width/t.height:h.aspectRatio=null,l=new(s())(c,h)})),r.find("#dataHeight").on("change",(function(){t.height=parseFloat($(this).val()),l.setData(t),d(t.height)})),r.find("#dataWidth").on("change",(function(){t.width=parseFloat($(this).val()),l.setData(t),u(t.width)}));var d=function(t){r.find("#dataHeight").val(parseInt(t))},u=function(t){r.find("#dataWidth").val(parseInt(t))}}},{key:"handleCopyLink",value:function(){var t="";n.Z.each(n.Z.getSelectedFiles(),(function(e){n.Z.isEmpty(t)||(t+="\n"),t+=e.full_url}));var e=$(".js-app-clipboard-temp");e.data("clipboard-text",t),new Clipboard(".js-app-clipboard-temp",{text:function(){return t}}),r.e.showMessage("success",n.Z.trans("clipboard.success"),n.Z.trans("message.success_header")),e.trigger("click")}},{key:"handleGlobalAction",value:function(e,i){var a=[];switch(n.Z.each(n.Z.getSelectedItems(),(function(t){a.push({is_folder:t.is_folder,id:t.id,full_url:t.full_url})})),e){case"rename":$("#modal_rename_items").modal("show").find("form.app-form").data("action",e);break;case"copy_link":t.handleCopyLink();break;case"preview":t.handlePreview();break;case"alt_text":$("#modal_alt_text_items").modal("show").find("form.app-form").data("action",e);break;case"crop":$("#modal_crop_image").modal("show").find("form.app-form").data("action",e);break;case"trash":$("#modal_trash_items").modal("show").find("form.app-form").data("action",e);break;case"delete":$("#modal_delete_items").modal("show").find("form.app-form").data("action",e);break;case"empty_trash":$("#modal_empty_trash").modal("show").find("form.app-form").data("action",e);break;case"download":var o=[];n.Z.each(n.Z.getSelectedItems(),(function(t){n.Z.inArray(n.Z.getConfigs().denied_download,t.mime_type)||o.push({id:t.id,is_folder:t.is_folder})})),o.length?t.handleDownload(o):r.e.showMessage("error",n.Z.trans("download.error"),n.Z.trans("message.error_header"));break;default:t.processAction({selected:a,action:e},i)}}},{key:"processAction",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;n.Z.showAjaxLoading(),$httpClient.make().post(APP_MEDIA_URL.global_actions,t).then((function(t){var i=t.data;n.Z.resetPagination(),r.e.showMessage("success",i.message,n.Z.trans("message.success_header")),e&&e(i)})).finally((function(){return n.Z.hideAjaxLoading()}))}},{key:"renderRenameItems",value:function(){var t=$("#app_media_rename_item").html(),e=$("#modal_rename_items .rename-items").empty();n.Z.each(n.Z.getSelectedItems(),(function(i){var a=t.replace(/__icon__/gi,i.icon||"fa fa-file").replace(/__placeholder__/gi,"Input file name").replace(/__value__/gi,i.name),n=$(a);n.data("id",i.id),n.data("is_folder",i.is_folder),n.data("name",i.name),e.append(n)}))}},{key:"renderAltTextItems",value:function(){var t=$("#app_media_alt_text_item").html(),e=$("#modal_alt_text_items .alt-text-items").empty();n.Z.each(n.Z.getSelectedItems(),(function(i){var a=t.replace(/__icon__/gi,i.icon||"fa fa-file").replace(/__placeholder__/gi,"Input file alt").replace(/__value__/gi,null===i.alt?"":i.alt),n=$(a);n.data("id",i.id),n.data("alt",i.alt),e.append(n)}))}},{key:"renderActions",value:function(){var t=n.Z.getSelectedFolder().length>0,e=$("#app_action_item").html(),i=0,a=$(".app-dropdown-actions .dropdown-menu");a.empty();var r=$.extend({},!0,n.Z.getConfigs().actions_list);t&&(r.basic=n.Z.arrayReject(r.basic,(function(t){return"preview"===t.action})),r.basic=n.Z.arrayReject(r.basic,(function(t){return"crop"===t.action})),r.file=n.Z.arrayReject(r.file,(function(t){return"alt_text"===t.action})),r.file=n.Z.arrayReject(r.file,(function(t){return"copy_link"===t.action})),n.Z.hasPermission("folders.create")||(r.file=n.Z.arrayReject(r.file,(function(t){return"make_copy"===t.action}))),n.Z.hasPermission("folders.edit")||(r.file=n.Z.arrayReject(r.file,(function(t){return n.Z.inArray(["rename"],t.action)})),r.user=n.Z.arrayReject(r.user,(function(t){return n.Z.inArray(["rename"],t.action)}))),n.Z.hasPermission("folders.trash")||(r.other=n.Z.arrayReject(r.other,(function(t){return n.Z.inArray(["trash","restore"],t.action)}))),n.Z.hasPermission("folders.destroy")||(r.other=n.Z.arrayReject(r.other,(function(t){return n.Z.inArray(["delete"],t.action)}))),n.Z.hasPermission("folders.favorite")||(r.other=n.Z.arrayReject(r.other,(function(t){return n.Z.inArray(["favorite","remove_favorite"],t.action)}))));var o=n.Z.getSelectedFiles();n.Z.arrayFilter(o,(function(t){return t.preview_url})).length||(r.basic=n.Z.arrayReject(r.basic,(function(t){return"preview"===t.action}))),n.Z.arrayFilter(o,(function(t){return"image"===t.type})).length||(r.basic=n.Z.arrayReject(r.basic,(function(t){return"crop"===t.action})),r.file=n.Z.arrayReject(r.file,(function(t){return"alt_text"===t.action}))),o.length>0&&(n.Z.hasPermission("files.create")||(r.file=n.Z.arrayReject(r.file,(function(t){return"make_copy"===t.action}))),n.Z.hasPermission("files.edit")||(r.file=n.Z.arrayReject(r.file,(function(t){return n.Z.inArray(["rename"],t.action)}))),n.Z.hasPermission("files.trash")||(r.other=n.Z.arrayReject(r.other,(function(t){return n.Z.inArray(["trash","restore"],t.action)}))),n.Z.hasPermission("files.destroy")||(r.other=n.Z.arrayReject(r.other,(function(t){return n.Z.inArray(["delete"],t.action)}))),n.Z.hasPermission("files.favorite")||(r.other=n.Z.arrayReject(r.other,(function(t){return n.Z.inArray(["favorite","remove_favorite"],t.action)}))),o.length>1&&(r.basic=n.Z.arrayReject(r.basic,(function(t){return"crop"===t.action})))),n.Z.each(r,(function(t,r){n.Z.each(t,(function(t,o){var s=!1;switch(n.Z.getRequestParams().view_in){case"all_media":n.Z.inArray(["remove_favorite","delete","restore"],t.action)&&(s=!0);break;case"recent":n.Z.inArray(["remove_favorite","delete","restore","make_copy"],t.action)&&(s=!0);break;case"favorites":n.Z.inArray(["favorite","delete","restore","make_copy"],t.action)&&(s=!0);break;case"trash":n.Z.inArray(["preview","delete","restore","rename","download"],t.action)||(s=!0)}if(!s){var c=e.replace(/__action__/gi,t.action||"").replace(/__icon__/gi,t.icon||"").replace(/__name__/gi,n.Z.trans("actions_list."+r+"."+t.action)||t.name);!o&&i&&(c='<li role="separator" class="divider"></li>'+c),a.append(c)}})),t.length>0&&i++}))}},{key:"handleDownload",value:function(t){var e=$(".media-download-popup");$httpClient.make().withResponseType("blob").post(APP_MEDIA_URL.download,{selected:t}).then((function(t){var e=(t.headers["content-disposition"]||"").split("filename=")[1].split(";")[0],i=URL.createObjectURL(t.data),a=document.createElement("a");a.href=i,a.download=e,document.body.appendChild(a),a.click(),a.remove(),URL.revokeObjectURL(i)})).finally((function(){e.hide(),clearTimeout(null)}))}}],(i=null)&&h(e.prototype,i),o&&h(e,o),Object.defineProperty(e,"prototype",{writable:!1}),t}()},6261:(t,e,i)=>{"use strict";i.d(e,{L:()=>s});var a=i(5498),n=i(3363);function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function o(t,e){for(var i=0;i<e.length;i++){var a=e[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,(n=a.key,o=void 0,o=function(t,e){if("object"!==r(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var a=i.call(t,e||"default");if("object"!==r(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n,"string"),"symbol"===r(o)?o:String(o)),a)}var n,o}var s=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var e,i,r;return e=t,r=[{key:"initContext",value:function(){jQuery().contextMenu&&($.contextMenu({selector:'.js-context-menu[data-context="file"]',build:function(){return{items:t._fileContextMenu()}}}),$.contextMenu({selector:'.js-context-menu[data-context="folder"]',build:function(){return{items:t._folderContextMenu()}}}))}},{key:"_fileContextMenu",value:function(){var t={preview:{name:"Preview",icon:function(t,e,i,a){return e.html('<i class="fa fa-eye" aria-hidden="true"></i> '+a.name),"context-menu-icon-updated"},callback:function(){a.b.handlePreview()}}};n.Z.each(n.Z.getConfigs().actions_list,(function(e,i){n.Z.each(e,(function(e){t[e.action]={name:e.name,icon:function(t,a,r,o){return a.html('<i class="'+e.icon+'" aria-hidden="true"></i> '+(n.Z.trans("actions_list."+i+"."+e.action)||o.name)),"context-menu-icon-updated"},callback:function(){$('.js-files-action[data-action="'+e.action+'"]').trigger("click")}}}))}));var e=[];switch(n.Z.getRequestParams().view_in){case"all_media":e=["remove_favorite","delete","restore"];break;case"recent":e=["remove_favorite","delete","restore","make_copy"];break;case"favorites":e=["favorite","delete","restore","make_copy"];break;case"trash":t={preview:t.preview,rename:t.rename,download:t.download,delete:t.delete,restore:t.restore}}n.Z.each(e,(function(e){t[e]=void 0})),n.Z.getSelectedFolder().length>0&&(t.preview=void 0,t.crop=void 0,t.copy_link=void 0,n.Z.hasPermission("folders.create")||(t.make_copy=void 0),n.Z.hasPermission("folders.edit")||(t.rename=void 0),n.Z.hasPermission("folders.trash")||(t.trash=void 0,t.restore=void 0),n.Z.hasPermission("folders.destroy")||(t.delete=void 0),n.Z.hasPermission("folders.favorite")||(t.favorite=void 0,t.remove_favorite=void 0));var i=n.Z.getSelectedFiles();return i.length>0&&(n.Z.hasPermission("files.create")||(t.make_copy=void 0),n.Z.hasPermission("files.edit")||(t.rename=void 0),n.Z.hasPermission("files.trash")||(t.trash=void 0,t.restore=void 0),n.Z.hasPermission("files.destroy")||(t.delete=void 0),n.Z.hasPermission("files.favorite")||(t.favorite=void 0,t.remove_favorite=void 0),i.length>1&&(t.crop=void 0)),n.Z.arrayFilter(i,(function(t){return t.preview_url})).length||(t.preview=void 0),n.Z.arrayFilter(i,(function(t){return"image"===t.type})).length||(t.crop=void 0,t.alt_text=void 0),t}},{key:"_folderContextMenu",value:function(){var e=t._fileContextMenu();return e.preview=void 0,e.copy_link=void 0,e}},{key:"destroyContext",value:function(){jQuery().contextMenu&&$.contextMenu("destroy")}}],(i=null)&&o(e.prototype,i),r&&o(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}()},6554:(t,e,i)=>{"use strict";i.d(e,{e:()=>o});var a=i(3363);function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function r(t,e){for(var i=0;i<e.length;i++){var a=e[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,(r=a.key,o=void 0,o=function(t,e){if("object"!==n(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var a=i.call(t,e||"default");if("object"!==n(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(r,"string"),"symbol"===n(o)?o:String(o)),a)}var r,o}var o=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var e,i,n;return e=t,n=[{key:"showMessage",value:function(t,e){toastr.options={closeButton:!0,progressBar:!0,positionClass:"toast-bottom-right",onclick:null,showDuration:1e3,hideDuration:1e3,timeOut:1e4,extendedTimeOut:1e3,showEasing:"swing",hideEasing:"linear",showMethod:"fadeIn",hideMethod:"fadeOut"};var i="";switch(t){case"error":i=a.Z.trans("message.error_header");break;case"success":i=a.Z.trans("message.success_header")}toastr[t](e,i)}},{key:"handleError",value:function(e){void 0===e.responseJSON||a.Z.isArray(e.errors)?void 0!==e.responseJSON?void 0!==e.responseJSON.errors?422===e.status&&t.handleValidationError(e.responseJSON.errors):void 0!==e.responseJSON.message?t.showMessage("error",e.responseJSON.message):$.each(e.responseJSON,(function(e,i){$.each(i,(function(e,i){t.showMessage("error",i)}))})):t.showMessage("error",e.statusText):t.handleValidationError(e.responseJSON.errors)}},{key:"handleValidationError",value:function(e){var i="";$.each(e,(function(t,e){i+=e+"<br />",$('*[name="'+t+'"]').addClass("field-has-error"),$('*[name$="['+t+']"]').addClass("field-has-error")})),t.showMessage("error",i)}}],(i=null)&&r(e.prototype,i),n&&r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}()},3129:function(t){
/*!
 * Cropper.js v1.6.1
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2023-09-17T03:44:19.860Z
 */
t.exports=function(){"use strict";function t(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,a)}return i}function e(e){for(var i=1;i<arguments.length;i++){var a=null!=arguments[i]?arguments[i]:{};i%2?t(Object(a),!0).forEach((function(t){o(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):t(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function a(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function n(t,e){for(var i=0;i<e.length;i++){var a=e[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,f(a.key),a)}}function r(t,e,i){return e&&n(t.prototype,e),i&&n(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}function o(t,e,i){return(e=f(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function s(t){return c(t)||h(t)||l(t)||u()}function c(t){if(Array.isArray(t))return d(t)}function h(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function l(t,e){if(t){if("string"==typeof t)return d(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,a=new Array(e);i<e;i++)a[i]=t[i];return a}function u(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var a=i.call(t,e||"default");if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function f(t){var e=p(t,"string");return"symbol"==typeof e?e:String(e)}var m="undefined"!=typeof window&&void 0!==window.document,v=m?window:{},g=!(!m||!v.document.documentElement)&&"ontouchstart"in v.document.documentElement,y=!!m&&"PointerEvent"in v,w="cropper",b="all",x="crop",_="move",k="zoom",M="e",C="w",D="s",S="n",Z="ne",O="nw",j="se",R="sw",E="".concat(w,"-crop"),P="".concat(w,"-disabled"),A="".concat(w,"-hidden"),T="".concat(w,"-hide"),N="".concat(w,"-invisible"),$="".concat(w,"-modal"),B="".concat(w,"-move"),L="".concat(w,"Action"),I="".concat(w,"Preview"),W="crop",H="move",z="none",Y="crop",X="cropend",U="cropmove",F="cropstart",q="dblclick",V=g?"touchstart":"mousedown",J=g?"touchmove":"mousemove",G=g?"touchend touchcancel":"mouseup",K=y?"pointerdown":V,Q=y?"pointermove":J,tt=y?"pointerup pointercancel":G,et="ready",it="resize",at="wheel",nt="zoom",rt="image/jpeg",ot=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,st=/^data:/,ct=/^data:image\/jpeg;base64,/,ht=/^img|canvas$/i,lt=200,dt=100,ut={viewMode:0,dragMode:W,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:lt,minContainerHeight:dt,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},pt='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>',ft=Number.isNaN||v.isNaN;function mt(t){return"number"==typeof t&&!ft(t)}var vt=function(t){return t>0&&t<1/0};function gt(t){return void 0===t}function yt(t){return"object"===i(t)&&null!==t}var wt=Object.prototype.hasOwnProperty;function bt(t){if(!yt(t))return!1;try{var e=t.constructor,i=e.prototype;return e&&i&&wt.call(i,"isPrototypeOf")}catch(t){return!1}}function xt(t){return"function"==typeof t}var _t=Array.prototype.slice;function kt(t){return Array.from?Array.from(t):_t.call(t)}function Mt(t,e){return t&&xt(e)&&(Array.isArray(t)||mt(t.length)?kt(t).forEach((function(i,a){e.call(t,i,a,t)})):yt(t)&&Object.keys(t).forEach((function(i){e.call(t,t[i],i,t)}))),t}var Ct=Object.assign||function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),a=1;a<e;a++)i[a-1]=arguments[a];return yt(t)&&i.length>0&&i.forEach((function(e){yt(e)&&Object.keys(e).forEach((function(i){t[i]=e[i]}))})),t},Dt=/\.\d*(?:0|9){12}\d*$/;function St(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e11;return Dt.test(t)?Math.round(t*e)/e:t}var Zt=/^width|height|left|top|marginLeft|marginTop$/;function Ot(t,e){var i=t.style;Mt(e,(function(t,e){Zt.test(e)&&mt(t)&&(t="".concat(t,"px")),i[e]=t}))}function jt(t,e){return t.classList?t.classList.contains(e):t.className.indexOf(e)>-1}function Rt(t,e){if(e)if(mt(t.length))Mt(t,(function(t){Rt(t,e)}));else if(t.classList)t.classList.add(e);else{var i=t.className.trim();i?i.indexOf(e)<0&&(t.className="".concat(i," ").concat(e)):t.className=e}}function Et(t,e){e&&(mt(t.length)?Mt(t,(function(t){Et(t,e)})):t.classList?t.classList.remove(e):t.className.indexOf(e)>=0&&(t.className=t.className.replace(e,"")))}function Pt(t,e,i){e&&(mt(t.length)?Mt(t,(function(t){Pt(t,e,i)})):i?Rt(t,e):Et(t,e))}var At=/([a-z\d])([A-Z])/g;function Tt(t){return t.replace(At,"$1-$2").toLowerCase()}function Nt(t,e){return yt(t[e])?t[e]:t.dataset?t.dataset[e]:t.getAttribute("data-".concat(Tt(e)))}function $t(t,e,i){yt(i)?t[e]=i:t.dataset?t.dataset[e]=i:t.setAttribute("data-".concat(Tt(e)),i)}function Bt(t,e){if(yt(t[e]))try{delete t[e]}catch(i){t[e]=void 0}else if(t.dataset)try{delete t.dataset[e]}catch(i){t.dataset[e]=void 0}else t.removeAttribute("data-".concat(Tt(e)))}var Lt=/\s\s*/,It=function(){var t=!1;if(m){var e=!1,i=function(){},a=Object.defineProperty({},"once",{get:function(){return t=!0,e},set:function(t){e=t}});v.addEventListener("test",i,a),v.removeEventListener("test",i,a)}return t}();function Wt(t,e,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=i;e.trim().split(Lt).forEach((function(e){if(!It){var r=t.listeners;r&&r[e]&&r[e][i]&&(n=r[e][i],delete r[e][i],0===Object.keys(r[e]).length&&delete r[e],0===Object.keys(r).length&&delete t.listeners)}t.removeEventListener(e,n,a)}))}function Ht(t,e,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=i;e.trim().split(Lt).forEach((function(e){if(a.once&&!It){var r=t.listeners,o=void 0===r?{}:r;n=function(){delete o[e][i],t.removeEventListener(e,n,a);for(var r=arguments.length,s=new Array(r),c=0;c<r;c++)s[c]=arguments[c];i.apply(t,s)},o[e]||(o[e]={}),o[e][i]&&t.removeEventListener(e,o[e][i],a),o[e][i]=n,t.listeners=o}t.addEventListener(e,n,a)}))}function zt(t,e,i){var a;return xt(Event)&&xt(CustomEvent)?a=new CustomEvent(e,{detail:i,bubbles:!0,cancelable:!0}):(a=document.createEvent("CustomEvent")).initCustomEvent(e,!0,!0,i),t.dispatchEvent(a)}function Yt(t){var e=t.getBoundingClientRect();return{left:e.left+(window.pageXOffset-document.documentElement.clientLeft),top:e.top+(window.pageYOffset-document.documentElement.clientTop)}}var Xt=v.location,Ut=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function Ft(t){var e=t.match(Ut);return null!==e&&(e[1]!==Xt.protocol||e[2]!==Xt.hostname||e[3]!==Xt.port)}function qt(t){var e="timestamp=".concat((new Date).getTime());return t+(-1===t.indexOf("?")?"?":"&")+e}function Vt(t){var e=t.rotate,i=t.scaleX,a=t.scaleY,n=t.translateX,r=t.translateY,o=[];mt(n)&&0!==n&&o.push("translateX(".concat(n,"px)")),mt(r)&&0!==r&&o.push("translateY(".concat(r,"px)")),mt(e)&&0!==e&&o.push("rotate(".concat(e,"deg)")),mt(i)&&1!==i&&o.push("scaleX(".concat(i,")")),mt(a)&&1!==a&&o.push("scaleY(".concat(a,")"));var s=o.length?o.join(" "):"none";return{WebkitTransform:s,msTransform:s,transform:s}}function Jt(t){var i=e({},t),a=0;return Mt(t,(function(t,e){delete i[e],Mt(i,(function(e){var i=Math.abs(t.startX-e.startX),n=Math.abs(t.startY-e.startY),r=Math.abs(t.endX-e.endX),o=Math.abs(t.endY-e.endY),s=Math.sqrt(i*i+n*n),c=(Math.sqrt(r*r+o*o)-s)/s;Math.abs(c)>Math.abs(a)&&(a=c)}))})),a}function Gt(t,i){var a=t.pageX,n=t.pageY,r={endX:a,endY:n};return i?r:e({startX:a,startY:n},r)}function Kt(t){var e=0,i=0,a=0;return Mt(t,(function(t){var n=t.startX,r=t.startY;e+=n,i+=r,a+=1})),{pageX:e/=a,pageY:i/=a}}function Qt(t){var e=t.aspectRatio,i=t.height,a=t.width,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"contain",r=vt(a),o=vt(i);if(r&&o){var s=i*e;"contain"===n&&s>a||"cover"===n&&s<a?i=a/e:a=i*e}else r?i=a/e:o&&(a=i*e);return{width:a,height:i}}function te(t){var e=t.width,i=t.height,a=t.degree;if(90==(a=Math.abs(a)%180))return{width:i,height:e};var n=a%90*Math.PI/180,r=Math.sin(n),o=Math.cos(n),s=e*o+i*r,c=e*r+i*o;return a>90?{width:c,height:s}:{width:s,height:c}}function ee(t,e,i,a){var n=e.aspectRatio,r=e.naturalWidth,o=e.naturalHeight,c=e.rotate,h=void 0===c?0:c,l=e.scaleX,d=void 0===l?1:l,u=e.scaleY,p=void 0===u?1:u,f=i.aspectRatio,m=i.naturalWidth,v=i.naturalHeight,g=a.fillColor,y=void 0===g?"transparent":g,w=a.imageSmoothingEnabled,b=void 0===w||w,x=a.imageSmoothingQuality,_=void 0===x?"low":x,k=a.maxWidth,M=void 0===k?1/0:k,C=a.maxHeight,D=void 0===C?1/0:C,S=a.minWidth,Z=void 0===S?0:S,O=a.minHeight,j=void 0===O?0:O,R=document.createElement("canvas"),E=R.getContext("2d"),P=Qt({aspectRatio:f,width:M,height:D}),A=Qt({aspectRatio:f,width:Z,height:j},"cover"),T=Math.min(P.width,Math.max(A.width,m)),N=Math.min(P.height,Math.max(A.height,v)),$=Qt({aspectRatio:n,width:M,height:D}),B=Qt({aspectRatio:n,width:Z,height:j},"cover"),L=Math.min($.width,Math.max(B.width,r)),I=Math.min($.height,Math.max(B.height,o)),W=[-L/2,-I/2,L,I];return R.width=St(T),R.height=St(N),E.fillStyle=y,E.fillRect(0,0,T,N),E.save(),E.translate(T/2,N/2),E.rotate(h*Math.PI/180),E.scale(d,p),E.imageSmoothingEnabled=b,E.imageSmoothingQuality=_,E.drawImage.apply(E,[t].concat(s(W.map((function(t){return Math.floor(St(t))}))))),E.restore(),R}var ie=String.fromCharCode;function ae(t,e,i){var a="";i+=e;for(var n=e;n<i;n+=1)a+=ie(t.getUint8(n));return a}var ne=/^data:.*,/;function re(t){var e=t.replace(ne,""),i=atob(e),a=new ArrayBuffer(i.length),n=new Uint8Array(a);return Mt(n,(function(t,e){n[e]=i.charCodeAt(e)})),a}function oe(t,e){for(var i=[],a=8192,n=new Uint8Array(t);n.length>0;)i.push(ie.apply(null,kt(n.subarray(0,a)))),n=n.subarray(a);return"data:".concat(e,";base64,").concat(btoa(i.join("")))}function se(t){var e,i=new DataView(t);try{var a,n,r;if(255===i.getUint8(0)&&216===i.getUint8(1))for(var o=i.byteLength,s=2;s+1<o;){if(255===i.getUint8(s)&&225===i.getUint8(s+1)){n=s;break}s+=1}if(n){var c=n+10;if("Exif"===ae(i,n+4,4)){var h=i.getUint16(c);if(((a=18761===h)||19789===h)&&42===i.getUint16(c+2,a)){var l=i.getUint32(c+4,a);l>=8&&(r=c+l)}}}if(r){var d,u,p=i.getUint16(r,a);for(u=0;u<p;u+=1)if(d=r+12*u+2,274===i.getUint16(d,a)){d+=8,e=i.getUint16(d,a),i.setUint16(d,1,a);break}}}catch(t){e=1}return e}function ce(t){var e=0,i=1,a=1;switch(t){case 2:i=-1;break;case 3:e=-180;break;case 4:a=-1;break;case 5:e=90,a=-1;break;case 6:e=90;break;case 7:e=90,i=-1;break;case 8:e=-90}return{rotate:e,scaleX:i,scaleY:a}}var he={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,e=this.options,i=this.container,a=this.cropper,n=Number(e.minContainerWidth),r=Number(e.minContainerHeight);Rt(a,A),Et(t,A);var o={width:Math.max(i.offsetWidth,n>=0?n:lt),height:Math.max(i.offsetHeight,r>=0?r:dt)};this.containerData=o,Ot(a,{width:o.width,height:o.height}),Rt(t,A),Et(a,A)},initCanvas:function(){var t=this.containerData,e=this.imageData,i=this.options.viewMode,a=Math.abs(e.rotate)%180==90,n=a?e.naturalHeight:e.naturalWidth,r=a?e.naturalWidth:e.naturalHeight,o=n/r,s=t.width,c=t.height;t.height*o>t.width?3===i?s=t.height*o:c=t.width/o:3===i?c=t.width/o:s=t.height*o;var h={aspectRatio:o,naturalWidth:n,naturalHeight:r,width:s,height:c};this.canvasData=h,this.limited=1===i||2===i,this.limitCanvas(!0,!0),h.width=Math.min(Math.max(h.width,h.minWidth),h.maxWidth),h.height=Math.min(Math.max(h.height,h.minHeight),h.maxHeight),h.left=(t.width-h.width)/2,h.top=(t.height-h.height)/2,h.oldLeft=h.left,h.oldTop=h.top,this.initialCanvasData=Ct({},h)},limitCanvas:function(t,e){var i=this.options,a=this.containerData,n=this.canvasData,r=this.cropBoxData,o=i.viewMode,s=n.aspectRatio,c=this.cropped&&r;if(t){var h=Number(i.minCanvasWidth)||0,l=Number(i.minCanvasHeight)||0;o>1?(h=Math.max(h,a.width),l=Math.max(l,a.height),3===o&&(l*s>h?h=l*s:l=h/s)):o>0&&(h?h=Math.max(h,c?r.width:0):l?l=Math.max(l,c?r.height:0):c&&(h=r.width,(l=r.height)*s>h?h=l*s:l=h/s));var d=Qt({aspectRatio:s,width:h,height:l});h=d.width,l=d.height,n.minWidth=h,n.minHeight=l,n.maxWidth=1/0,n.maxHeight=1/0}if(e)if(o>(c?0:1)){var u=a.width-n.width,p=a.height-n.height;n.minLeft=Math.min(0,u),n.minTop=Math.min(0,p),n.maxLeft=Math.max(0,u),n.maxTop=Math.max(0,p),c&&this.limited&&(n.minLeft=Math.min(r.left,r.left+(r.width-n.width)),n.minTop=Math.min(r.top,r.top+(r.height-n.height)),n.maxLeft=r.left,n.maxTop=r.top,2===o&&(n.width>=a.width&&(n.minLeft=Math.min(0,u),n.maxLeft=Math.max(0,u)),n.height>=a.height&&(n.minTop=Math.min(0,p),n.maxTop=Math.max(0,p))))}else n.minLeft=-n.width,n.minTop=-n.height,n.maxLeft=a.width,n.maxTop=a.height},renderCanvas:function(t,e){var i=this.canvasData,a=this.imageData;if(e){var n=te({width:a.naturalWidth*Math.abs(a.scaleX||1),height:a.naturalHeight*Math.abs(a.scaleY||1),degree:a.rotate||0}),r=n.width,o=n.height,s=i.width*(r/i.naturalWidth),c=i.height*(o/i.naturalHeight);i.left-=(s-i.width)/2,i.top-=(c-i.height)/2,i.width=s,i.height=c,i.aspectRatio=r/o,i.naturalWidth=r,i.naturalHeight=o,this.limitCanvas(!0,!1)}(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCanvas(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,Ot(this.canvas,Ct({width:i.width,height:i.height},Vt({translateX:i.left,translateY:i.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var e=this.canvasData,i=this.imageData,a=i.naturalWidth*(e.width/e.naturalWidth),n=i.naturalHeight*(e.height/e.naturalHeight);Ct(i,{width:a,height:n,left:(e.width-a)/2,top:(e.height-n)/2}),Ot(this.image,Ct({width:i.width,height:i.height},Vt(Ct({translateX:i.left,translateY:i.top},i)))),t&&this.output()},initCropBox:function(){var t=this.options,e=this.canvasData,i=t.aspectRatio||t.initialAspectRatio,a=Number(t.autoCropArea)||.8,n={width:e.width,height:e.height};i&&(e.height*i>e.width?n.height=n.width/i:n.width=n.height*i),this.cropBoxData=n,this.limitCropBox(!0,!0),n.width=Math.min(Math.max(n.width,n.minWidth),n.maxWidth),n.height=Math.min(Math.max(n.height,n.minHeight),n.maxHeight),n.width=Math.max(n.minWidth,n.width*a),n.height=Math.max(n.minHeight,n.height*a),n.left=e.left+(e.width-n.width)/2,n.top=e.top+(e.height-n.height)/2,n.oldLeft=n.left,n.oldTop=n.top,this.initialCropBoxData=Ct({},n)},limitCropBox:function(t,e){var i=this.options,a=this.containerData,n=this.canvasData,r=this.cropBoxData,o=this.limited,s=i.aspectRatio;if(t){var c=Number(i.minCropBoxWidth)||0,h=Number(i.minCropBoxHeight)||0,l=o?Math.min(a.width,n.width,n.width+n.left,a.width-n.left):a.width,d=o?Math.min(a.height,n.height,n.height+n.top,a.height-n.top):a.height;c=Math.min(c,a.width),h=Math.min(h,a.height),s&&(c&&h?h*s>c?h=c/s:c=h*s:c?h=c/s:h&&(c=h*s),d*s>l?d=l/s:l=d*s),r.minWidth=Math.min(c,l),r.minHeight=Math.min(h,d),r.maxWidth=l,r.maxHeight=d}e&&(o?(r.minLeft=Math.max(0,n.left),r.minTop=Math.max(0,n.top),r.maxLeft=Math.min(a.width,n.left+n.width)-r.width,r.maxTop=Math.min(a.height,n.top+n.height)-r.height):(r.minLeft=0,r.minTop=0,r.maxLeft=a.width-r.width,r.maxTop=a.height-r.height))},renderCropBox:function(){var t=this.options,e=this.containerData,i=this.cropBoxData;(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCropBox(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,t.movable&&t.cropBoxMovable&&$t(this.face,L,i.width>=e.width&&i.height>=e.height?_:b),Ot(this.cropBox,Ct({width:i.width,height:i.height},Vt({translateX:i.left,translateY:i.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),zt(this.element,Y,this.getData())}},le={initPreview:function(){var t=this.element,e=this.crossOrigin,i=this.options.preview,a=e?this.crossOriginUrl:this.url,n=t.alt||"The image to preview",r=document.createElement("img");if(e&&(r.crossOrigin=e),r.src=a,r.alt=n,this.viewBox.appendChild(r),this.viewBoxImage=r,i){var o=i;"string"==typeof i?o=t.ownerDocument.querySelectorAll(i):i.querySelector&&(o=[i]),this.previews=o,Mt(o,(function(t){var i=document.createElement("img");$t(t,I,{width:t.offsetWidth,height:t.offsetHeight,html:t.innerHTML}),e&&(i.crossOrigin=e),i.src=a,i.alt=n,i.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',t.innerHTML="",t.appendChild(i)}))}},resetPreview:function(){Mt(this.previews,(function(t){var e=Nt(t,I);Ot(t,{width:e.width,height:e.height}),t.innerHTML=e.html,Bt(t,I)}))},preview:function(){var t=this.imageData,e=this.canvasData,i=this.cropBoxData,a=i.width,n=i.height,r=t.width,o=t.height,s=i.left-e.left-t.left,c=i.top-e.top-t.top;this.cropped&&!this.disabled&&(Ot(this.viewBoxImage,Ct({width:r,height:o},Vt(Ct({translateX:-s,translateY:-c},t)))),Mt(this.previews,(function(e){var i=Nt(e,I),h=i.width,l=i.height,d=h,u=l,p=1;a&&(u=n*(p=h/a)),n&&u>l&&(d=a*(p=l/n),u=l),Ot(e,{width:d,height:u}),Ot(e.getElementsByTagName("img")[0],Ct({width:r*p,height:o*p},Vt(Ct({translateX:-s*p,translateY:-c*p},t))))})))}},de={bind:function(){var t=this.element,e=this.options,i=this.cropper;xt(e.cropstart)&&Ht(t,F,e.cropstart),xt(e.cropmove)&&Ht(t,U,e.cropmove),xt(e.cropend)&&Ht(t,X,e.cropend),xt(e.crop)&&Ht(t,Y,e.crop),xt(e.zoom)&&Ht(t,nt,e.zoom),Ht(i,K,this.onCropStart=this.cropStart.bind(this)),e.zoomable&&e.zoomOnWheel&&Ht(i,at,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&Ht(i,q,this.onDblclick=this.dblclick.bind(this)),Ht(t.ownerDocument,Q,this.onCropMove=this.cropMove.bind(this)),Ht(t.ownerDocument,tt,this.onCropEnd=this.cropEnd.bind(this)),e.responsive&&Ht(window,it,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,e=this.options,i=this.cropper;xt(e.cropstart)&&Wt(t,F,e.cropstart),xt(e.cropmove)&&Wt(t,U,e.cropmove),xt(e.cropend)&&Wt(t,X,e.cropend),xt(e.crop)&&Wt(t,Y,e.crop),xt(e.zoom)&&Wt(t,nt,e.zoom),Wt(i,K,this.onCropStart),e.zoomable&&e.zoomOnWheel&&Wt(i,at,this.onWheel,{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&Wt(i,q,this.onDblclick),Wt(t.ownerDocument,Q,this.onCropMove),Wt(t.ownerDocument,tt,this.onCropEnd),e.responsive&&Wt(window,it,this.onResize)}},ue={resize:function(){if(!this.disabled){var t,e,i=this.options,a=this.container,n=this.containerData,r=a.offsetWidth/n.width,o=a.offsetHeight/n.height,s=Math.abs(r-1)>Math.abs(o-1)?r:o;1!==s&&(i.restore&&(t=this.getCanvasData(),e=this.getCropBoxData()),this.render(),i.restore&&(this.setCanvasData(Mt(t,(function(e,i){t[i]=e*s}))),this.setCropBoxData(Mt(e,(function(t,i){e[i]=t*s})))))}},dblclick:function(){this.disabled||this.options.dragMode===z||this.setDragMode(jt(this.dragBox,E)?H:W)},wheel:function(t){var e=this,i=Number(this.options.wheelZoomRatio)||.1,a=1;this.disabled||(t.preventDefault(),this.wheeling||(this.wheeling=!0,setTimeout((function(){e.wheeling=!1}),50),t.deltaY?a=t.deltaY>0?1:-1:t.wheelDelta?a=-t.wheelDelta/120:t.detail&&(a=t.detail>0?1:-1),this.zoom(-a*i,t)))},cropStart:function(t){var e=t.buttons,i=t.button;if(!(this.disabled||("mousedown"===t.type||"pointerdown"===t.type&&"mouse"===t.pointerType)&&(mt(e)&&1!==e||mt(i)&&0!==i||t.ctrlKey))){var a,n=this.options,r=this.pointers;t.changedTouches?Mt(t.changedTouches,(function(t){r[t.identifier]=Gt(t)})):r[t.pointerId||0]=Gt(t),a=Object.keys(r).length>1&&n.zoomable&&n.zoomOnTouch?k:Nt(t.target,L),ot.test(a)&&!1!==zt(this.element,F,{originalEvent:t,action:a})&&(t.preventDefault(),this.action=a,this.cropping=!1,a===x&&(this.cropping=!0,Rt(this.dragBox,$)))}},cropMove:function(t){var e=this.action;if(!this.disabled&&e){var i=this.pointers;t.preventDefault(),!1!==zt(this.element,U,{originalEvent:t,action:e})&&(t.changedTouches?Mt(t.changedTouches,(function(t){Ct(i[t.identifier]||{},Gt(t,!0))})):Ct(i[t.pointerId||0]||{},Gt(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var e=this.action,i=this.pointers;t.changedTouches?Mt(t.changedTouches,(function(t){delete i[t.identifier]})):delete i[t.pointerId||0],e&&(t.preventDefault(),Object.keys(i).length||(this.action=""),this.cropping&&(this.cropping=!1,Pt(this.dragBox,$,this.cropped&&this.options.modal)),zt(this.element,X,{originalEvent:t,action:e}))}}},pe={change:function(t){var e,i=this.options,a=this.canvasData,n=this.containerData,r=this.cropBoxData,o=this.pointers,s=this.action,c=i.aspectRatio,h=r.left,l=r.top,d=r.width,u=r.height,p=h+d,f=l+u,m=0,v=0,g=n.width,y=n.height,w=!0;!c&&t.shiftKey&&(c=d&&u?d/u:1),this.limited&&(m=r.minLeft,v=r.minTop,g=m+Math.min(n.width,a.width,a.left+a.width),y=v+Math.min(n.height,a.height,a.top+a.height));var E=o[Object.keys(o)[0]],P={x:E.endX-E.startX,y:E.endY-E.startY},T=function(t){switch(t){case M:p+P.x>g&&(P.x=g-p);break;case C:h+P.x<m&&(P.x=m-h);break;case S:l+P.y<v&&(P.y=v-l);break;case D:f+P.y>y&&(P.y=y-f)}};switch(s){case b:h+=P.x,l+=P.y;break;case M:if(P.x>=0&&(p>=g||c&&(l<=v||f>=y))){w=!1;break}T(M),(d+=P.x)<0&&(s=C,h-=d=-d),c&&(u=d/c,l+=(r.height-u)/2);break;case S:if(P.y<=0&&(l<=v||c&&(h<=m||p>=g))){w=!1;break}T(S),u-=P.y,l+=P.y,u<0&&(s=D,l-=u=-u),c&&(d=u*c,h+=(r.width-d)/2);break;case C:if(P.x<=0&&(h<=m||c&&(l<=v||f>=y))){w=!1;break}T(C),d-=P.x,h+=P.x,d<0&&(s=M,h-=d=-d),c&&(u=d/c,l+=(r.height-u)/2);break;case D:if(P.y>=0&&(f>=y||c&&(h<=m||p>=g))){w=!1;break}T(D),(u+=P.y)<0&&(s=S,l-=u=-u),c&&(d=u*c,h+=(r.width-d)/2);break;case Z:if(c){if(P.y<=0&&(l<=v||p>=g)){w=!1;break}T(S),u-=P.y,l+=P.y,d=u*c}else T(S),T(M),P.x>=0?p<g?d+=P.x:P.y<=0&&l<=v&&(w=!1):d+=P.x,P.y<=0?l>v&&(u-=P.y,l+=P.y):(u-=P.y,l+=P.y);d<0&&u<0?(s=R,l-=u=-u,h-=d=-d):d<0?(s=O,h-=d=-d):u<0&&(s=j,l-=u=-u);break;case O:if(c){if(P.y<=0&&(l<=v||h<=m)){w=!1;break}T(S),u-=P.y,l+=P.y,d=u*c,h+=r.width-d}else T(S),T(C),P.x<=0?h>m?(d-=P.x,h+=P.x):P.y<=0&&l<=v&&(w=!1):(d-=P.x,h+=P.x),P.y<=0?l>v&&(u-=P.y,l+=P.y):(u-=P.y,l+=P.y);d<0&&u<0?(s=j,l-=u=-u,h-=d=-d):d<0?(s=Z,h-=d=-d):u<0&&(s=R,l-=u=-u);break;case R:if(c){if(P.x<=0&&(h<=m||f>=y)){w=!1;break}T(C),d-=P.x,h+=P.x,u=d/c}else T(D),T(C),P.x<=0?h>m?(d-=P.x,h+=P.x):P.y>=0&&f>=y&&(w=!1):(d-=P.x,h+=P.x),P.y>=0?f<y&&(u+=P.y):u+=P.y;d<0&&u<0?(s=Z,l-=u=-u,h-=d=-d):d<0?(s=j,h-=d=-d):u<0&&(s=O,l-=u=-u);break;case j:if(c){if(P.x>=0&&(p>=g||f>=y)){w=!1;break}T(M),u=(d+=P.x)/c}else T(D),T(M),P.x>=0?p<g?d+=P.x:P.y>=0&&f>=y&&(w=!1):d+=P.x,P.y>=0?f<y&&(u+=P.y):u+=P.y;d<0&&u<0?(s=O,l-=u=-u,h-=d=-d):d<0?(s=R,h-=d=-d):u<0&&(s=Z,l-=u=-u);break;case _:this.move(P.x,P.y),w=!1;break;case k:this.zoom(Jt(o),t),w=!1;break;case x:if(!P.x||!P.y){w=!1;break}e=Yt(this.cropper),h=E.startX-e.left,l=E.startY-e.top,d=r.minWidth,u=r.minHeight,P.x>0?s=P.y>0?j:Z:P.x<0&&(h-=d,s=P.y>0?R:O),P.y<0&&(l-=u),this.cropped||(Et(this.cropBox,A),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0))}w&&(r.width=d,r.height=u,r.left=h,r.top=l,this.action=s,this.renderCropBox()),Mt(o,(function(t){t.startX=t.endX,t.startY=t.endY}))}},fe={crop:function(){return!this.ready||this.cropped||this.disabled||(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&Rt(this.dragBox,$),Et(this.cropBox,A),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=Ct({},this.initialImageData),this.canvasData=Ct({},this.initialCanvasData),this.cropBoxData=Ct({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(Ct(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),Et(this.dragBox,$),Rt(this.cropBox,A)),this},replace:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return!this.disabled&&t&&(this.isImg&&(this.element.src=t),e?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,Mt(this.previews,(function(e){e.getElementsByTagName("img")[0].src=t})))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,Et(this.cropper,P)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,Rt(this.cropper,P)),this},destroy:function(){var t=this.element;return t[w]?(t[w]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.canvasData,a=i.left,n=i.top;return this.moveTo(gt(t)?t:a+Number(t),gt(e)?e:n+Number(e))},moveTo:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.canvasData,a=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.movable&&(mt(t)&&(i.left=t,a=!0),mt(e)&&(i.top=e,a=!0),a&&this.renderCanvas(!0)),this},zoom:function(t,e){var i=this.canvasData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(i.width*t/i.naturalWidth,null,e)},zoomTo:function(t,e,i){var a=this.options,n=this.canvasData,r=n.width,o=n.height,s=n.naturalWidth,c=n.naturalHeight;if((t=Number(t))>=0&&this.ready&&!this.disabled&&a.zoomable){var h=s*t,l=c*t;if(!1===zt(this.element,nt,{ratio:t,oldRatio:r/s,originalEvent:i}))return this;if(i){var d=this.pointers,u=Yt(this.cropper),p=d&&Object.keys(d).length?Kt(d):{pageX:i.pageX,pageY:i.pageY};n.left-=(h-r)*((p.pageX-u.left-n.left)/r),n.top-=(l-o)*((p.pageY-u.top-n.top)/o)}else bt(e)&&mt(e.x)&&mt(e.y)?(n.left-=(h-r)*((e.x-n.left)/r),n.top-=(l-o)*((e.y-n.top)/o)):(n.left-=(h-r)/2,n.top-=(l-o)/2);n.width=h,n.height=l,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return mt(t=Number(t))&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var e=this.imageData.scaleY;return this.scale(t,mt(e)?e:1)},scaleY:function(t){var e=this.imageData.scaleX;return this.scale(mt(e)?e:1,t)},scale:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.imageData,a=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.scalable&&(mt(t)&&(i.scaleX=t,a=!0),mt(e)&&(i.scaleY=e,a=!0),a&&this.renderCanvas(!0,!0)),this},getData:function(){var t,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=this.options,a=this.imageData,n=this.canvasData,r=this.cropBoxData;if(this.ready&&this.cropped){t={x:r.left-n.left,y:r.top-n.top,width:r.width,height:r.height};var o=a.width/a.naturalWidth;if(Mt(t,(function(e,i){t[i]=e/o})),e){var s=Math.round(t.y+t.height),c=Math.round(t.x+t.width);t.x=Math.round(t.x),t.y=Math.round(t.y),t.width=c-t.x,t.height=s-t.y}}else t={x:0,y:0,width:0,height:0};return i.rotatable&&(t.rotate=a.rotate||0),i.scalable&&(t.scaleX=a.scaleX||1,t.scaleY=a.scaleY||1),t},setData:function(t){var e=this.options,i=this.imageData,a=this.canvasData,n={};if(this.ready&&!this.disabled&&bt(t)){var r=!1;e.rotatable&&mt(t.rotate)&&t.rotate!==i.rotate&&(i.rotate=t.rotate,r=!0),e.scalable&&(mt(t.scaleX)&&t.scaleX!==i.scaleX&&(i.scaleX=t.scaleX,r=!0),mt(t.scaleY)&&t.scaleY!==i.scaleY&&(i.scaleY=t.scaleY,r=!0)),r&&this.renderCanvas(!0,!0);var o=i.width/i.naturalWidth;mt(t.x)&&(n.left=t.x*o+a.left),mt(t.y)&&(n.top=t.y*o+a.top),mt(t.width)&&(n.width=t.width*o),mt(t.height)&&(n.height=t.height*o),this.setCropBoxData(n)}return this},getContainerData:function(){return this.ready?Ct({},this.containerData):{}},getImageData:function(){return this.sized?Ct({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,e={};return this.ready&&Mt(["left","top","width","height","naturalWidth","naturalHeight"],(function(i){e[i]=t[i]})),e},setCanvasData:function(t){var e=this.canvasData,i=e.aspectRatio;return this.ready&&!this.disabled&&bt(t)&&(mt(t.left)&&(e.left=t.left),mt(t.top)&&(e.top=t.top),mt(t.width)?(e.width=t.width,e.height=t.width/i):mt(t.height)&&(e.height=t.height,e.width=t.height*i),this.renderCanvas(!0)),this},getCropBoxData:function(){var t,e=this.cropBoxData;return this.ready&&this.cropped&&(t={left:e.left,top:e.top,width:e.width,height:e.height}),t||{}},setCropBoxData:function(t){var e,i,a=this.cropBoxData,n=this.options.aspectRatio;return this.ready&&this.cropped&&!this.disabled&&bt(t)&&(mt(t.left)&&(a.left=t.left),mt(t.top)&&(a.top=t.top),mt(t.width)&&t.width!==a.width&&(e=!0,a.width=t.width),mt(t.height)&&t.height!==a.height&&(i=!0,a.height=t.height),n&&(e?a.height=a.width/n:i&&(a.width=a.height*n)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var e=this.canvasData,i=ee(this.image,this.imageData,e,t);if(!this.cropped)return i;var a=this.getData(t.rounded),n=a.x,r=a.y,o=a.width,c=a.height,h=i.width/Math.floor(e.naturalWidth);1!==h&&(n*=h,r*=h,o*=h,c*=h);var l=o/c,d=Qt({aspectRatio:l,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),u=Qt({aspectRatio:l,width:t.minWidth||0,height:t.minHeight||0},"cover"),p=Qt({aspectRatio:l,width:t.width||(1!==h?i.width:o),height:t.height||(1!==h?i.height:c)}),f=p.width,m=p.height;f=Math.min(d.width,Math.max(u.width,f)),m=Math.min(d.height,Math.max(u.height,m));var v=document.createElement("canvas"),g=v.getContext("2d");v.width=St(f),v.height=St(m),g.fillStyle=t.fillColor||"transparent",g.fillRect(0,0,f,m);var y=t.imageSmoothingEnabled,w=void 0===y||y,b=t.imageSmoothingQuality;g.imageSmoothingEnabled=w,b&&(g.imageSmoothingQuality=b);var x,_,k,M,C,D,S=i.width,Z=i.height,O=n,j=r;O<=-o||O>S?(O=0,x=0,k=0,C=0):O<=0?(k=-O,O=0,C=x=Math.min(S,o+O)):O<=S&&(k=0,C=x=Math.min(o,S-O)),x<=0||j<=-c||j>Z?(j=0,_=0,M=0,D=0):j<=0?(M=-j,j=0,D=_=Math.min(Z,c+j)):j<=Z&&(M=0,D=_=Math.min(c,Z-j));var R=[O,j,x,_];if(C>0&&D>0){var E=f/o;R.push(k*E,M*E,C*E,D*E)}return g.drawImage.apply(g,[i].concat(s(R.map((function(t){return Math.floor(St(t))}))))),v},setAspectRatio:function(t){var e=this.options;return this.disabled||gt(t)||(e.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var e=this.options,i=this.dragBox,a=this.face;if(this.ready&&!this.disabled){var n=t===W,r=e.movable&&t===H;t=n||r?t:z,e.dragMode=t,$t(i,L,t),Pt(i,E,n),Pt(i,B,r),e.cropBoxMovable||($t(a,L,t),Pt(a,E,n),Pt(a,B,r))}return this}},me=v.Cropper,ve=function(){function t(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(a(this,t),!e||!ht.test(e.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=e,this.options=Ct({},ut,bt(i)&&i),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return r(t,[{key:"init",value:function(){var t,e=this.element,i=e.tagName.toLowerCase();if(!e[w]){if(e[w]=this,"img"===i){if(this.isImg=!0,t=e.getAttribute("src")||"",this.originalUrl=t,!t)return;t=e.src}else"canvas"===i&&window.HTMLCanvasElement&&(t=e.toDataURL());this.load(t)}}},{key:"load",value:function(t){var e=this;if(t){this.url=t,this.imageData={};var i=this.element,a=this.options;if(a.rotatable||a.scalable||(a.checkOrientation=!1),a.checkOrientation&&window.ArrayBuffer)if(st.test(t))ct.test(t)?this.read(re(t)):this.clone();else{var n=new XMLHttpRequest,r=this.clone.bind(this);this.reloading=!0,this.xhr=n,n.onabort=r,n.onerror=r,n.ontimeout=r,n.onprogress=function(){n.getResponseHeader("content-type")!==rt&&n.abort()},n.onload=function(){e.read(n.response)},n.onloadend=function(){e.reloading=!1,e.xhr=null},a.checkCrossOrigin&&Ft(t)&&i.crossOrigin&&(t=qt(t)),n.open("GET",t,!0),n.responseType="arraybuffer",n.withCredentials="use-credentials"===i.crossOrigin,n.send()}else this.clone()}}},{key:"read",value:function(t){var e=this.options,i=this.imageData,a=se(t),n=0,r=1,o=1;if(a>1){this.url=oe(t,rt);var s=ce(a);n=s.rotate,r=s.scaleX,o=s.scaleY}e.rotatable&&(i.rotate=n),e.scalable&&(i.scaleX=r,i.scaleY=o),this.clone()}},{key:"clone",value:function(){var t=this.element,e=this.url,i=t.crossOrigin,a=e;this.options.checkCrossOrigin&&Ft(e)&&(i||(i="anonymous"),a=qt(e)),this.crossOrigin=i,this.crossOriginUrl=a;var n=document.createElement("img");i&&(n.crossOrigin=i),n.src=a||e,n.alt=t.alt||"The image to crop",this.image=n,n.onload=this.start.bind(this),n.onerror=this.stop.bind(this),Rt(n,T),t.parentNode.insertBefore(n,t.nextSibling)}},{key:"start",value:function(){var t=this,e=this.image;e.onload=null,e.onerror=null,this.sizing=!0;var i=v.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(v.navigator.userAgent),a=function(e,i){Ct(t.imageData,{naturalWidth:e,naturalHeight:i,aspectRatio:e/i}),t.initialImageData=Ct({},t.imageData),t.sizing=!1,t.sized=!0,t.build()};if(!e.naturalWidth||i){var n=document.createElement("img"),r=document.body||document.documentElement;this.sizingImage=n,n.onload=function(){a(n.width,n.height),i||r.removeChild(n)},n.src=e.src,i||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",r.appendChild(n))}else a(e.naturalWidth,e.naturalHeight)}},{key:"stop",value:function(){var t=this.image;t.onload=null,t.onerror=null,t.parentNode.removeChild(t),this.image=null}},{key:"build",value:function(){if(this.sized&&!this.ready){var t=this.element,e=this.options,i=this.image,a=t.parentNode,n=document.createElement("div");n.innerHTML=pt;var r=n.querySelector(".".concat(w,"-container")),o=r.querySelector(".".concat(w,"-canvas")),s=r.querySelector(".".concat(w,"-drag-box")),c=r.querySelector(".".concat(w,"-crop-box")),h=c.querySelector(".".concat(w,"-face"));this.container=a,this.cropper=r,this.canvas=o,this.dragBox=s,this.cropBox=c,this.viewBox=r.querySelector(".".concat(w,"-view-box")),this.face=h,o.appendChild(i),Rt(t,A),a.insertBefore(r,t.nextSibling),Et(i,T),this.initPreview(),this.bind(),e.initialAspectRatio=Math.max(0,e.initialAspectRatio)||NaN,e.aspectRatio=Math.max(0,e.aspectRatio)||NaN,e.viewMode=Math.max(0,Math.min(3,Math.round(e.viewMode)))||0,Rt(c,A),e.guides||Rt(c.getElementsByClassName("".concat(w,"-dashed")),A),e.center||Rt(c.getElementsByClassName("".concat(w,"-center")),A),e.background&&Rt(r,"".concat(w,"-bg")),e.highlight||Rt(h,N),e.cropBoxMovable&&(Rt(h,B),$t(h,L,b)),e.cropBoxResizable||(Rt(c.getElementsByClassName("".concat(w,"-line")),A),Rt(c.getElementsByClassName("".concat(w,"-point")),A)),this.render(),this.ready=!0,this.setDragMode(e.dragMode),e.autoCrop&&this.crop(),this.setData(e.data),xt(e.ready)&&Ht(t,et,e.ready,{once:!0}),zt(t,et)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var t=this.cropper.parentNode;t&&t.removeChild(this.cropper),Et(this.element,A)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],[{key:"noConflict",value:function(){return window.Cropper=me,t}},{key:"setDefaults",value:function(t){Ct(ut,bt(t)&&t)}}]),t}();return Ct(ve.prototype,he,le,de,ue,pe,fe),ve}()}},e={};function i(a){var n=e[a];if(void 0!==n)return n.exports;var r=e[a]={exports:{}};return t[a].call(r.exports,r,r.exports,i),r.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var a in e)i.o(e,a)&&!i.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:e[a]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),(()=>{"use strict";var t=i(3363),e=i(3809),a=i(6261);function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var i=0;i<e.length;i++){var a=e[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,(r=a.key,o=void 0,o=function(t,e){if("object"!==n(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var a=i.call(t,e||"default");if("object"!==n(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(r,"string"),"symbol"===n(o)?o:String(o)),a)}var r,o}function s(t,e,i){return e&&o(t.prototype,e),i&&o(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}var c=function(){function e(){r(this,e)}return s(e,null,[{key:"editorSelectFile",value:function(e){var i=t.Z.getUrlParam("CKEditor")||t.Z.getUrlParam("CKEditorFuncNum");if(window.opener&&i){var a=t.Z.arrayFirst(e);window.opener.CKEDITOR.tools.callFunction(t.Z.getUrlParam("CKEditorFuncNum"),a.full_url),window.opener&&window.close()}}}]),e}(),h=s((function i(n,o){r(this,i),window.appMedia=window.appMedia||{};var s=$("body");o=$.extend(!0,{multiple:!0,type:"*",onSelectFiles:function(t,e){}},o);var c=function(i){i.preventDefault();var n=$(i.currentTarget);$("#app_media_modal").modal("show"),window.appMedia.options=o,window.appMedia.options.open_in="modal",window.appMedia.$el=n,e.O.request_params.filter="everything",t.Z.storeConfig();var r=window.appMedia.$el.data("app-media");void 0!==r&&r.length>0&&(r=r[0],window.appMedia.options=$.extend(!0,window.appMedia.options,r||{}),void 0!==r.selected_file_id?window.appMedia.options.is_popup=!0:void 0!==window.appMedia.options.is_popup&&(window.appMedia.options.is_popup=void 0)),0===$("#app_media_body .app-media-container").length?$("#app_media_body").load(APP_MEDIA_URL.popup,(function(e){e.error&&alert(e.message),$("#app_media_body").removeClass("media-modal-loading").closest(".modal-content").removeClass("bb-loading"),$(document).find(".app-media-container .js-change-action[data-type=refresh]").trigger("click"),"everything"!==t.Z.getRequestParams().filter&&$(".app-media-actions .btn.js-app-media-change-filter-group.js-filter-by-type").hide(),a.L.destroyContext(),a.L.initContext()})):$(document).find(".app-media-container .js-change-action[data-type=refresh]").trigger("click")};"string"==typeof n?s.off("click",n).on("click",n,c):n.off("click").on("click",c)}));window.AppMediaStandAlone=h,$(".js-insert-to-editor").off("click").on("click",(function(e){e.preventDefault();var i=t.Z.getSelectedFiles();t.Z.size(i)>0&&c.editorSelectFile(i)})),$.fn.appMedia=function(i){var a=$(this);e.O.request_params.filter="everything",$(document).find(".js-insert-to-editor").prop("disabled","trash"===e.O.request_params.view_in),t.Z.storeConfig(),new h(a,i)}})()})();
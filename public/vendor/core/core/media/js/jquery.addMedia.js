(()=>{function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}!function(t){"use strict";var a=function(e,a){this.options=a,t(e).appMedia({multiple:!0,onSelectFiles:function(e,n){if(void 0!==e)switch(n.data("editor")){case"summernote":!function(e,a){if(0===a.length)return;for(var n=e.data("target"),r=0;r<a.length;r++)"image"===a[r].type?t(n).summernote("insertImage",a[r].full_url,a[r].basename):t(n).summernote("pasteHTML",'<a href="'+a[r].full_url+'">'+a[r].full_url+"</a>")}(n,e);break;case"wysihtml5":!function(e,t){if(0===t.length)return;for(var a="",n=0;n<t.length;n++)"image"===t[n].type?a+='<img src="'+t[n].full_url+'" alt="'+t[n].name+'" loading="lazy">':a+='<a href="'+t[n].full_url+'">'+t[n].full_url+"</a>";if(e.getValue().length>0){var r=e.getValue();e.composer.commands.exec("insertHTML",a),e.getValue()===r&&e.setValue(e.getValue()+a)}else e.setValue(e.getValue()+a)}(t(a.target).data("wysihtml5").editor,e);break;case"ckeditor":!function(e,a){var n=e.data("target").replace("#",""),r="";t.each(a,(function(e,t){var a=t.full_url;"image"===t.type?r+='<img src="'+a+'" alt="'+t.name+'" loading="lazy"/><br />':r+='<a href="'+a+'">'+t.name+"</a><br />"})),CKEDITOR.instances[n].insertHtml(r)}(n,e);break;case"tinymce":!function(e){var a="";t.each(e,(function(e,t){var n=t.full_url;"image"===t.type?a+='<img src="'+n+'" alt="'+t.name+'" loading="lazy" /><br />':a+='<a href="'+n+'">'+t.name+"</a><br />"})),tinymce.activeEditor.execCommand("mceInsertContent",!1,a)}(e);break;default:var r=new CustomEvent("core-insert-media",{detail:{files:e,element:n}});document.dispatchEvent(r)}}})};function n(n){return this.each((function(){var r=t(this),i=r.data("bs.media"),l=t.extend({},r.data(),"object"===e(n)&&n);i||r.data("bs.media",new a(this,l))}))}a.VERSION="1.1.0",t.fn.addMedia=n,t.fn.addMedia.Constructor=a,t(window).on("load",(function(){t('[data-type="app-media"]').each((function(){var e=t(this);n.call(e,e.data())}))}))}(jQuery)})();
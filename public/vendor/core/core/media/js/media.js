(()=>{var e={3809:(e,t,i)=>{"use strict";i.d(t,{O:()=>r,s:()=>a});var r=$.parseJSON(localStorage.getItem("MediaConfig"))||{},n={app_key:APP_MEDIA_CONFIG.random_hash?APP_MEDIA_CONFIG.random_hash:"21d06709fe1d3abdf0e35ddda89c4b279",request_params:{view_type:"tiles",filter:"everything",view_in:"all_media",sort_by:"created_at-desc",folder_id:0},hide_details_pane:!1,icons:{folder:"fa fa-folder"},actions_list:{basic:[{icon:"fa fa-eye",name:"Preview",action:"preview",order:0,class:"app-action-preview"},{icon:"fa fa-crop",name:"<PERSON><PERSON>",action:"crop",order:1,class:"app-action-crop"}],file:[{icon:"fa fa-link",name:"Copy link",action:"copy_link",order:0,class:"app-action-copy-link"},{icon:"far fa-edit",name:"<PERSON><PERSON>",action:"rename",order:1,class:"app-action-rename"},{icon:"fa fa-copy",name:"Make a copy",action:"make_copy",order:2,class:"app-action-make-copy"},{icon:"fas fa-file-signature",name:"Alt text",action:"alt_text",order:3,class:"app-action-alt-text"}],user:[{icon:"fa fa-star",name:"Favorite",action:"favorite",order:2,class:"app-action-favorite"},{icon:"fa fa-star",name:"Remove favorite",action:"remove_favorite",order:3,class:"app-action-favorite"}],other:[{icon:"fa fa-download",name:"Download",action:"download",order:0,class:"app-action-download"},{icon:"fa fa-trash",name:"Move to trash",action:"trash",order:1,class:"app-action-trash"},{icon:"fa fa-eraser",name:"Delete permanently",action:"delete",order:2,class:"app-action-delete"},{icon:"fa fa-undo",name:"Restore",action:"restore",order:3,class:"app-action-restore"}]}};r.app_key&&r.app_key===n.app_key||(r=n),r.request_params.search="";var a=$.parseJSON(localStorage.getItem("RecentItems"))||[]},3363:(e,t,i)=>{"use strict";i.d(t,{Z:()=>o});var r=i(3809);function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function a(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(a=r.key,o=void 0,o=function(e,t){if("object"!==n(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!==n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(a,"string"),"symbol"===n(o)?o:String(o)),r)}var a,o}var o=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,i,n;return t=e,n=[{key:"getUrlParam",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;t||(t=window.location.search);var i=new RegExp("(?:[?&]|&)"+e+"=([^&]+)","i"),r=t.match(i);return r&&r.length>1?r[1]:null}},{key:"asset",value:function(e){if("//"===e.substring(0,2)||"http://"===e.substring(0,7)||"https://"===e.substring(0,8))return e;var t="/"!==APP_MEDIA_URL.base_url.substr(-1,1)?APP_MEDIA_URL.base_url+"/":APP_MEDIA_URL.base_url;return"/"===e.substring(0,1)?t+e.substring(1):t+e}},{key:"showAjaxLoading",value:function(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:$(".app-media-main")).addClass("on-loading").append($("#app_media_loading").html())}},{key:"hideAjaxLoading",value:function(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:$(".app-media-main")).removeClass("on-loading").find(".loading-wrapper").remove()}},{key:"isOnAjaxLoading",value:function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:$(".app-media-items")).hasClass("on-loading")}},{key:"jsonEncode",value:function(e){return void 0===e&&(e=null),JSON.stringify(e)}},{key:"jsonDecode",value:function(e,t){if(!e)return t;if("string"==typeof e){var i;try{i=$.parseJSON(e)}catch(e){i=t}return i}return e}},{key:"getRequestParams",value:function(){return window.appMedia.options&&"modal"===window.appMedia.options.open_in?$.extend(!0,r.O.request_params,window.appMedia.options||{}):r.O.request_params}},{key:"setSelectedFile",value:function(e){void 0!==window.appMedia.options?window.appMedia.options.selected_file_id=e:r.O.request_params.selected_file_id=e}},{key:"getConfigs",value:function(){return r.O}},{key:"storeConfig",value:function(){localStorage.setItem("MediaConfig",e.jsonEncode(r.O))}},{key:"storeRecentItems",value:function(){localStorage.setItem("RecentItems",e.jsonEncode(r.s))}},{key:"addToRecent",value:function(t){t instanceof Array?e.each(t,(function(e){r.s.push(e)})):(r.s.push(t),this.storeRecentItems())}},{key:"getItems",value:function(){var e=[];return $(".js-media-list-title").each((function(t,i){var r=$(i),n=r.data()||{};n.index_key=r.index(),e.push(n)})),e}},{key:"getSelectedItems",value:function(){var e=[];return $(".js-media-list-title input[type=checkbox]:checked").each((function(t,i){var r=$(i).closest(".js-media-list-title"),n=r.data()||{};n.index_key=r.index(),e.push(n)})),e}},{key:"getSelectedFiles",value:function(){var e=[];return $(".js-media-list-title[data-context=file] input[type=checkbox]:checked").each((function(t,i){var r=$(i).closest(".js-media-list-title"),n=r.data()||{};n.index_key=r.index(),e.push(n)})),e}},{key:"getSelectedFolder",value:function(){var e=[];return $(".js-media-list-title[data-context=folder] input[type=checkbox]:checked").each((function(t,i){var r=$(i).closest(".js-media-list-title"),n=r.data()||{};n.index_key=r.index(),e.push(n)})),e}},{key:"isUseInModal",value:function(){return window.appMedia&&window.appMedia.options&&"modal"===window.appMedia.options.open_in}},{key:"resetPagination",value:function(){APP_MEDIA_CONFIG.pagination={paged:1,posts_per_page:40,in_process_get_media:!1,has_more:!0}}},{key:"trans",value:function(e){return _.get(APP_MEDIA_CONFIG.translations,e,e)}},{key:"config",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return _.get(APP_MEDIA_CONFIG,e,t)}},{key:"hasPermission",value:function(t){return e.inArray(e.config("permissions",[]),t)}},{key:"inArray",value:function(e,t){return _.includes(e,t)}},{key:"each",value:function(e,t){return _.each(e,t)}},{key:"forEach",value:function(e,t){return _.forEach(e,t)}},{key:"arrayReject",value:function(e,t){return _.reject(e,t)}},{key:"arrayFilter",value:function(e,t){return _.filter(e,t)}},{key:"arrayFirst",value:function(e){return _.first(e)}},{key:"isArray",value:function(e){return _.isArray(e)}},{key:"isEmpty",value:function(e){return _.isEmpty(e)}},{key:"size",value:function(e){return _.size(e)}}],(i=null)&&a(t.prototype,i),n&&a(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}()},5498:(e,t,i)=>{"use strict";i.d(t,{b:()=>d});var r=i(3809),n=i(3363),a=i(6554),o=i(3129),s=i.n(o);function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function l(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(n=r.key,a=void 0,a=function(e,t){if("object"!==c(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!==c(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(n,"string"),"symbol"===c(a)?a:String(a)),r)}var n,a}var d=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,i,o;return t=e,o=[{key:"handleDropdown",value:function(){var t=n.Z.size(n.Z.getSelectedItems());e.renderActions(),t>0?$(".app-dropdown-actions").removeClass("disabled"):$(".app-dropdown-actions").addClass("disabled")}},{key:"handlePreview",value:function(){var e=[];n.Z.each(n.Z.getSelectedFiles(),(function(t){t.preview_url&&(e.push({src:t.preview_url,type:t.preview_type}),r.s.push(t.id))})),n.Z.size(e)>0?($.fancybox.open(e),n.Z.storeRecentItems()):this.handleGlobalAction("download")}},{key:"renderCropImage",value:function(){var e,t=$("#app_media_crop_image").html(),i=$("#modal_crop_image .crop-image").empty(),r=n.Z.getSelectedItems()[0],a=$("#modal_crop_image .form-crop"),o=t.replace(/__src__/gi,r.full_url);i.append(o);var c=i.find("img")[0],l={minContainerWidth:550,minContainerHeight:550,dragMode:"move",crop:function(t){e=t.detail,a.find('input[name="image_id"]').val(r.id),a.find('input[name="crop_data"]').val(JSON.stringify(e)),h(e.height),u(e.width)}},d=new(s())(c,l);a.find("#aspectRatio").on("click",(function(){d.destroy(),$(this).is(":checked")?l.aspectRatio=e.width/e.height:l.aspectRatio=null,d=new(s())(c,l)})),a.find("#dataHeight").on("change",(function(){e.height=parseFloat($(this).val()),d.setData(e),h(e.height)})),a.find("#dataWidth").on("change",(function(){e.width=parseFloat($(this).val()),d.setData(e),u(e.width)}));var h=function(e){a.find("#dataHeight").val(parseInt(e))},u=function(e){a.find("#dataWidth").val(parseInt(e))}}},{key:"handleCopyLink",value:function(){var e="";n.Z.each(n.Z.getSelectedFiles(),(function(t){n.Z.isEmpty(e)||(e+="\n"),e+=t.full_url}));var t=$(".js-app-clipboard-temp");t.data("clipboard-text",e),new Clipboard(".js-app-clipboard-temp",{text:function(){return e}}),a.e.showMessage("success",n.Z.trans("clipboard.success"),n.Z.trans("message.success_header")),t.trigger("click")}},{key:"handleGlobalAction",value:function(t,i){var r=[];switch(n.Z.each(n.Z.getSelectedItems(),(function(e){r.push({is_folder:e.is_folder,id:e.id,full_url:e.full_url})})),t){case"rename":$("#modal_rename_items").modal("show").find("form.app-form").data("action",t);break;case"copy_link":e.handleCopyLink();break;case"preview":e.handlePreview();break;case"alt_text":$("#modal_alt_text_items").modal("show").find("form.app-form").data("action",t);break;case"crop":$("#modal_crop_image").modal("show").find("form.app-form").data("action",t);break;case"trash":$("#modal_trash_items").modal("show").find("form.app-form").data("action",t);break;case"delete":$("#modal_delete_items").modal("show").find("form.app-form").data("action",t);break;case"empty_trash":$("#modal_empty_trash").modal("show").find("form.app-form").data("action",t);break;case"download":var o=[];n.Z.each(n.Z.getSelectedItems(),(function(e){n.Z.inArray(n.Z.getConfigs().denied_download,e.mime_type)||o.push({id:e.id,is_folder:e.is_folder})})),o.length?e.handleDownload(o):a.e.showMessage("error",n.Z.trans("download.error"),n.Z.trans("message.error_header"));break;default:e.processAction({selected:r,action:t},i)}}},{key:"processAction",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;n.Z.showAjaxLoading(),$httpClient.make().post(APP_MEDIA_URL.global_actions,e).then((function(e){var i=e.data;n.Z.resetPagination(),a.e.showMessage("success",i.message,n.Z.trans("message.success_header")),t&&t(i)})).finally((function(){return n.Z.hideAjaxLoading()}))}},{key:"renderRenameItems",value:function(){var e=$("#app_media_rename_item").html(),t=$("#modal_rename_items .rename-items").empty();n.Z.each(n.Z.getSelectedItems(),(function(i){var r=e.replace(/__icon__/gi,i.icon||"fa fa-file").replace(/__placeholder__/gi,"Input file name").replace(/__value__/gi,i.name),n=$(r);n.data("id",i.id),n.data("is_folder",i.is_folder),n.data("name",i.name),t.append(n)}))}},{key:"renderAltTextItems",value:function(){var e=$("#app_media_alt_text_item").html(),t=$("#modal_alt_text_items .alt-text-items").empty();n.Z.each(n.Z.getSelectedItems(),(function(i){var r=e.replace(/__icon__/gi,i.icon||"fa fa-file").replace(/__placeholder__/gi,"Input file alt").replace(/__value__/gi,null===i.alt?"":i.alt),n=$(r);n.data("id",i.id),n.data("alt",i.alt),t.append(n)}))}},{key:"renderActions",value:function(){var e=n.Z.getSelectedFolder().length>0,t=$("#app_action_item").html(),i=0,r=$(".app-dropdown-actions .dropdown-menu");r.empty();var a=$.extend({},!0,n.Z.getConfigs().actions_list);e&&(a.basic=n.Z.arrayReject(a.basic,(function(e){return"preview"===e.action})),a.basic=n.Z.arrayReject(a.basic,(function(e){return"crop"===e.action})),a.file=n.Z.arrayReject(a.file,(function(e){return"alt_text"===e.action})),a.file=n.Z.arrayReject(a.file,(function(e){return"copy_link"===e.action})),n.Z.hasPermission("folders.create")||(a.file=n.Z.arrayReject(a.file,(function(e){return"make_copy"===e.action}))),n.Z.hasPermission("folders.edit")||(a.file=n.Z.arrayReject(a.file,(function(e){return n.Z.inArray(["rename"],e.action)})),a.user=n.Z.arrayReject(a.user,(function(e){return n.Z.inArray(["rename"],e.action)}))),n.Z.hasPermission("folders.trash")||(a.other=n.Z.arrayReject(a.other,(function(e){return n.Z.inArray(["trash","restore"],e.action)}))),n.Z.hasPermission("folders.destroy")||(a.other=n.Z.arrayReject(a.other,(function(e){return n.Z.inArray(["delete"],e.action)}))),n.Z.hasPermission("folders.favorite")||(a.other=n.Z.arrayReject(a.other,(function(e){return n.Z.inArray(["favorite","remove_favorite"],e.action)}))));var o=n.Z.getSelectedFiles();n.Z.arrayFilter(o,(function(e){return e.preview_url})).length||(a.basic=n.Z.arrayReject(a.basic,(function(e){return"preview"===e.action}))),n.Z.arrayFilter(o,(function(e){return"image"===e.type})).length||(a.basic=n.Z.arrayReject(a.basic,(function(e){return"crop"===e.action})),a.file=n.Z.arrayReject(a.file,(function(e){return"alt_text"===e.action}))),o.length>0&&(n.Z.hasPermission("files.create")||(a.file=n.Z.arrayReject(a.file,(function(e){return"make_copy"===e.action}))),n.Z.hasPermission("files.edit")||(a.file=n.Z.arrayReject(a.file,(function(e){return n.Z.inArray(["rename"],e.action)}))),n.Z.hasPermission("files.trash")||(a.other=n.Z.arrayReject(a.other,(function(e){return n.Z.inArray(["trash","restore"],e.action)}))),n.Z.hasPermission("files.destroy")||(a.other=n.Z.arrayReject(a.other,(function(e){return n.Z.inArray(["delete"],e.action)}))),n.Z.hasPermission("files.favorite")||(a.other=n.Z.arrayReject(a.other,(function(e){return n.Z.inArray(["favorite","remove_favorite"],e.action)}))),o.length>1&&(a.basic=n.Z.arrayReject(a.basic,(function(e){return"crop"===e.action})))),n.Z.each(a,(function(e,a){n.Z.each(e,(function(e,o){var s=!1;switch(n.Z.getRequestParams().view_in){case"all_media":n.Z.inArray(["remove_favorite","delete","restore"],e.action)&&(s=!0);break;case"recent":n.Z.inArray(["remove_favorite","delete","restore","make_copy"],e.action)&&(s=!0);break;case"favorites":n.Z.inArray(["favorite","delete","restore","make_copy"],e.action)&&(s=!0);break;case"trash":n.Z.inArray(["preview","delete","restore","rename","download"],e.action)||(s=!0)}if(!s){var c=t.replace(/__action__/gi,e.action||"").replace(/__icon__/gi,e.icon||"").replace(/__name__/gi,n.Z.trans("actions_list."+a+"."+e.action)||e.name);!o&&i&&(c='<li role="separator" class="divider"></li>'+c),r.append(c)}})),e.length>0&&i++}))}},{key:"handleDownload",value:function(e){var t=$(".media-download-popup");$httpClient.make().withResponseType("blob").post(APP_MEDIA_URL.download,{selected:e}).then((function(e){var t=(e.headers["content-disposition"]||"").split("filename=")[1].split(";")[0],i=URL.createObjectURL(e.data),r=document.createElement("a");r.href=i,r.download=t,document.body.appendChild(r),r.click(),r.remove(),URL.revokeObjectURL(i)})).finally((function(){t.hide(),clearTimeout(null)}))}}],(i=null)&&l(t.prototype,i),o&&l(t,o),Object.defineProperty(t,"prototype",{writable:!1}),e}()},6261:(e,t,i)=>{"use strict";i.d(t,{L:()=>s});var r=i(5498),n=i(3363);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function o(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(n=r.key,o=void 0,o=function(e,t){if("object"!==a(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!==a(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(n,"string"),"symbol"===a(o)?o:String(o)),r)}var n,o}var s=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,i,a;return t=e,a=[{key:"initContext",value:function(){jQuery().contextMenu&&($.contextMenu({selector:'.js-context-menu[data-context="file"]',build:function(){return{items:e._fileContextMenu()}}}),$.contextMenu({selector:'.js-context-menu[data-context="folder"]',build:function(){return{items:e._folderContextMenu()}}}))}},{key:"_fileContextMenu",value:function(){var e={preview:{name:"Preview",icon:function(e,t,i,r){return t.html('<i class="fa fa-eye" aria-hidden="true"></i> '+r.name),"context-menu-icon-updated"},callback:function(){r.b.handlePreview()}}};n.Z.each(n.Z.getConfigs().actions_list,(function(t,i){n.Z.each(t,(function(t){e[t.action]={name:t.name,icon:function(e,r,a,o){return r.html('<i class="'+t.icon+'" aria-hidden="true"></i> '+(n.Z.trans("actions_list."+i+"."+t.action)||o.name)),"context-menu-icon-updated"},callback:function(){$('.js-files-action[data-action="'+t.action+'"]').trigger("click")}}}))}));var t=[];switch(n.Z.getRequestParams().view_in){case"all_media":t=["remove_favorite","delete","restore"];break;case"recent":t=["remove_favorite","delete","restore","make_copy"];break;case"favorites":t=["favorite","delete","restore","make_copy"];break;case"trash":e={preview:e.preview,rename:e.rename,download:e.download,delete:e.delete,restore:e.restore}}n.Z.each(t,(function(t){e[t]=void 0})),n.Z.getSelectedFolder().length>0&&(e.preview=void 0,e.crop=void 0,e.copy_link=void 0,n.Z.hasPermission("folders.create")||(e.make_copy=void 0),n.Z.hasPermission("folders.edit")||(e.rename=void 0),n.Z.hasPermission("folders.trash")||(e.trash=void 0,e.restore=void 0),n.Z.hasPermission("folders.destroy")||(e.delete=void 0),n.Z.hasPermission("folders.favorite")||(e.favorite=void 0,e.remove_favorite=void 0));var i=n.Z.getSelectedFiles();return i.length>0&&(n.Z.hasPermission("files.create")||(e.make_copy=void 0),n.Z.hasPermission("files.edit")||(e.rename=void 0),n.Z.hasPermission("files.trash")||(e.trash=void 0,e.restore=void 0),n.Z.hasPermission("files.destroy")||(e.delete=void 0),n.Z.hasPermission("files.favorite")||(e.favorite=void 0,e.remove_favorite=void 0),i.length>1&&(e.crop=void 0)),n.Z.arrayFilter(i,(function(e){return e.preview_url})).length||(e.preview=void 0),n.Z.arrayFilter(i,(function(e){return"image"===e.type})).length||(e.crop=void 0,e.alt_text=void 0),e}},{key:"_folderContextMenu",value:function(){var t=e._fileContextMenu();return t.preview=void 0,t.copy_link=void 0,t}},{key:"destroyContext",value:function(){jQuery().contextMenu&&$.contextMenu("destroy")}}],(i=null)&&o(t.prototype,i),a&&o(t,a),Object.defineProperty(t,"prototype",{writable:!1}),e}()},6554:(e,t,i)=>{"use strict";i.d(t,{e:()=>o});var r=i(3363);function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function a(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(a=r.key,o=void 0,o=function(e,t){if("object"!==n(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!==n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(a,"string"),"symbol"===n(o)?o:String(o)),r)}var a,o}var o=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,i,n;return t=e,n=[{key:"showMessage",value:function(e,t){toastr.options={closeButton:!0,progressBar:!0,positionClass:"toast-bottom-right",onclick:null,showDuration:1e3,hideDuration:1e3,timeOut:1e4,extendedTimeOut:1e3,showEasing:"swing",hideEasing:"linear",showMethod:"fadeIn",hideMethod:"fadeOut"};var i="";switch(e){case"error":i=r.Z.trans("message.error_header");break;case"success":i=r.Z.trans("message.success_header")}toastr[e](t,i)}},{key:"handleError",value:function(t){void 0===t.responseJSON||r.Z.isArray(t.errors)?void 0!==t.responseJSON?void 0!==t.responseJSON.errors?422===t.status&&e.handleValidationError(t.responseJSON.errors):void 0!==t.responseJSON.message?e.showMessage("error",t.responseJSON.message):$.each(t.responseJSON,(function(t,i){$.each(i,(function(t,i){e.showMessage("error",i)}))})):e.showMessage("error",t.statusText):e.handleValidationError(t.responseJSON.errors)}},{key:"handleValidationError",value:function(t){var i="";$.each(t,(function(e,t){i+=t+"<br />",$('*[name="'+e+'"]').addClass("field-has-error"),$('*[name$="['+e+']"]').addClass("field-has-error")})),e.showMessage("error",i)}}],(i=null)&&a(t.prototype,i),n&&a(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}()},7400:(e,t,i)=>{"use strict";i.d(t,{G:()=>d});var r=i(3363),n=i(3809),a=i(6261);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(n=r.key,a=void 0,a=function(e,t){if("object"!==o(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!==o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(n,"string"),"symbol"===o(a)?a:String(a)),r)}var n,a}function l(e,t,i){return t&&c(e.prototype,t),i&&c(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}var d=function(){function e(){s(this,e)}return l(e,null,[{key:"editorSelectFile",value:function(e){var t=r.Z.getUrlParam("CKEditor")||r.Z.getUrlParam("CKEditorFuncNum");if(window.opener&&t){var i=r.Z.arrayFirst(e);window.opener.CKEDITOR.tools.callFunction(r.Z.getUrlParam("CKEditorFuncNum"),i.full_url),window.opener&&window.close()}}}]),e}(),h=l((function e(t,i){s(this,e),window.appMedia=window.appMedia||{};var o=$("body");i=$.extend(!0,{multiple:!0,type:"*",onSelectFiles:function(e,t){}},i);var c=function(e){e.preventDefault();var t=$(e.currentTarget);$("#app_media_modal").modal("show"),window.appMedia.options=i,window.appMedia.options.open_in="modal",window.appMedia.$el=t,n.O.request_params.filter="everything",r.Z.storeConfig();var o=window.appMedia.$el.data("app-media");void 0!==o&&o.length>0&&(o=o[0],window.appMedia.options=$.extend(!0,window.appMedia.options,o||{}),void 0!==o.selected_file_id?window.appMedia.options.is_popup=!0:void 0!==window.appMedia.options.is_popup&&(window.appMedia.options.is_popup=void 0)),0===$("#app_media_body .app-media-container").length?$("#app_media_body").load(APP_MEDIA_URL.popup,(function(e){e.error&&alert(e.message),$("#app_media_body").removeClass("media-modal-loading").closest(".modal-content").removeClass("bb-loading"),$(document).find(".app-media-container .js-change-action[data-type=refresh]").trigger("click"),"everything"!==r.Z.getRequestParams().filter&&$(".app-media-actions .btn.js-app-media-change-filter-group.js-filter-by-type").hide(),a.L.destroyContext(),a.L.initContext()})):$(document).find(".app-media-container .js-change-action[data-type=refresh]").trigger("click")};"string"==typeof t?o.off("click",t).on("click",t,c):t.off("click").on("click",c)}));window.AppMediaStandAlone=h,$(".js-insert-to-editor").off("click").on("click",(function(e){e.preventDefault();var t=r.Z.getSelectedFiles();r.Z.size(t)>0&&d.editorSelectFile(t)})),$.fn.appMedia=function(e){var t=$(this);n.O.request_params.filter="everything",$(document).find(".js-insert-to-editor").prop("disabled","trash"===n.O.request_params.view_in),r.Z.storeConfig(),new h(t,e)}},3129:function(e){
/*!
 * Cropper.js v1.6.1
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2023-09-17T03:44:19.860Z
 */
e.exports=function(){"use strict";function e(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}function t(t){for(var i=1;i<arguments.length;i++){var r=null!=arguments[i]?arguments[i]:{};i%2?e(Object(r),!0).forEach((function(e){o(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):e(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,p(r.key),r)}}function a(e,t,i){return t&&n(e.prototype,t),i&&n(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function o(e,t,i){return(t=p(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function s(e){return c(e)||l(e)||d(e)||u()}function c(e){if(Array.isArray(e))return h(e)}function l(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function d(e,t){if(e){if("string"==typeof e)return h(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?h(e,t):void 0}}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,r=new Array(t);i<t;i++)r[i]=e[i];return r}function u(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e,t){if("object"!=typeof e||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function p(e){var t=f(e,"string");return"symbol"==typeof t?t:String(t)}var m="undefined"!=typeof window&&void 0!==window.document,v=m?window:{},g=!(!m||!v.document.documentElement)&&"ontouchstart"in v.document.documentElement,y=!!m&&"PointerEvent"in v,b="cropper",w="all",_="crop",x="move",k="zoom",M="e",C="w",S="s",j="n",$="ne",D="nw",E="se",Z="sw",O="".concat(b,"-crop"),P="".concat(b,"-disabled"),T="".concat(b,"-hidden"),R="".concat(b,"-hide"),I="".concat(b,"-invisible"),L="".concat(b,"-modal"),A="".concat(b,"-move"),N="".concat(b,"Action"),F="".concat(b,"Preview"),B="crop",z="move",W="none",H="crop",U="cropend",G="cropmove",q="cropstart",Y="dblclick",V=g?"touchstart":"mousedown",X=g?"touchmove":"mousemove",J=g?"touchend touchcancel":"mouseup",K=y?"pointerdown":V,Q=y?"pointermove":X,ee=y?"pointerup pointercancel":J,te="ready",ie="resize",re="wheel",ne="zoom",ae="image/jpeg",oe=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,se=/^data:/,ce=/^data:image\/jpeg;base64,/,le=/^img|canvas$/i,de=200,he=100,ue={viewMode:0,dragMode:B,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:de,minContainerHeight:he,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},fe='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>',pe=Number.isNaN||v.isNaN;function me(e){return"number"==typeof e&&!pe(e)}var ve=function(e){return e>0&&e<1/0};function ge(e){return void 0===e}function ye(e){return"object"===i(e)&&null!==e}var be=Object.prototype.hasOwnProperty;function we(e){if(!ye(e))return!1;try{var t=e.constructor,i=t.prototype;return t&&i&&be.call(i,"isPrototypeOf")}catch(e){return!1}}function _e(e){return"function"==typeof e}var xe=Array.prototype.slice;function ke(e){return Array.from?Array.from(e):xe.call(e)}function Me(e,t){return e&&_e(t)&&(Array.isArray(e)||me(e.length)?ke(e).forEach((function(i,r){t.call(e,i,r,e)})):ye(e)&&Object.keys(e).forEach((function(i){t.call(e,e[i],i,e)}))),e}var Ce=Object.assign||function(e){for(var t=arguments.length,i=new Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];return ye(e)&&i.length>0&&i.forEach((function(t){ye(t)&&Object.keys(t).forEach((function(i){e[i]=t[i]}))})),e},Se=/\.\d*(?:0|9){12}\d*$/;function je(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e11;return Se.test(e)?Math.round(e*t)/t:e}var $e=/^width|height|left|top|marginLeft|marginTop$/;function De(e,t){var i=e.style;Me(t,(function(e,t){$e.test(t)&&me(e)&&(e="".concat(e,"px")),i[t]=e}))}function Ee(e,t){return e.classList?e.classList.contains(t):e.className.indexOf(t)>-1}function Ze(e,t){if(t)if(me(e.length))Me(e,(function(e){Ze(e,t)}));else if(e.classList)e.classList.add(t);else{var i=e.className.trim();i?i.indexOf(t)<0&&(e.className="".concat(i," ").concat(t)):e.className=t}}function Oe(e,t){t&&(me(e.length)?Me(e,(function(e){Oe(e,t)})):e.classList?e.classList.remove(t):e.className.indexOf(t)>=0&&(e.className=e.className.replace(t,"")))}function Pe(e,t,i){t&&(me(e.length)?Me(e,(function(e){Pe(e,t,i)})):i?Ze(e,t):Oe(e,t))}var Te=/([a-z\d])([A-Z])/g;function Re(e){return e.replace(Te,"$1-$2").toLowerCase()}function Ie(e,t){return ye(e[t])?e[t]:e.dataset?e.dataset[t]:e.getAttribute("data-".concat(Re(t)))}function Le(e,t,i){ye(i)?e[t]=i:e.dataset?e.dataset[t]=i:e.setAttribute("data-".concat(Re(t)),i)}function Ae(e,t){if(ye(e[t]))try{delete e[t]}catch(i){e[t]=void 0}else if(e.dataset)try{delete e.dataset[t]}catch(i){e.dataset[t]=void 0}else e.removeAttribute("data-".concat(Re(t)))}var Ne=/\s\s*/,Fe=function(){var e=!1;if(m){var t=!1,i=function(){},r=Object.defineProperty({},"once",{get:function(){return e=!0,t},set:function(e){t=e}});v.addEventListener("test",i,r),v.removeEventListener("test",i,r)}return e}();function Be(e,t,i){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=i;t.trim().split(Ne).forEach((function(t){if(!Fe){var a=e.listeners;a&&a[t]&&a[t][i]&&(n=a[t][i],delete a[t][i],0===Object.keys(a[t]).length&&delete a[t],0===Object.keys(a).length&&delete e.listeners)}e.removeEventListener(t,n,r)}))}function ze(e,t,i){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=i;t.trim().split(Ne).forEach((function(t){if(r.once&&!Fe){var a=e.listeners,o=void 0===a?{}:a;n=function(){delete o[t][i],e.removeEventListener(t,n,r);for(var a=arguments.length,s=new Array(a),c=0;c<a;c++)s[c]=arguments[c];i.apply(e,s)},o[t]||(o[t]={}),o[t][i]&&e.removeEventListener(t,o[t][i],r),o[t][i]=n,e.listeners=o}e.addEventListener(t,n,r)}))}function We(e,t,i){var r;return _e(Event)&&_e(CustomEvent)?r=new CustomEvent(t,{detail:i,bubbles:!0,cancelable:!0}):(r=document.createEvent("CustomEvent")).initCustomEvent(t,!0,!0,i),e.dispatchEvent(r)}function He(e){var t=e.getBoundingClientRect();return{left:t.left+(window.pageXOffset-document.documentElement.clientLeft),top:t.top+(window.pageYOffset-document.documentElement.clientTop)}}var Ue=v.location,Ge=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function qe(e){var t=e.match(Ge);return null!==t&&(t[1]!==Ue.protocol||t[2]!==Ue.hostname||t[3]!==Ue.port)}function Ye(e){var t="timestamp=".concat((new Date).getTime());return e+(-1===e.indexOf("?")?"?":"&")+t}function Ve(e){var t=e.rotate,i=e.scaleX,r=e.scaleY,n=e.translateX,a=e.translateY,o=[];me(n)&&0!==n&&o.push("translateX(".concat(n,"px)")),me(a)&&0!==a&&o.push("translateY(".concat(a,"px)")),me(t)&&0!==t&&o.push("rotate(".concat(t,"deg)")),me(i)&&1!==i&&o.push("scaleX(".concat(i,")")),me(r)&&1!==r&&o.push("scaleY(".concat(r,")"));var s=o.length?o.join(" "):"none";return{WebkitTransform:s,msTransform:s,transform:s}}function Xe(e){var i=t({},e),r=0;return Me(e,(function(e,t){delete i[t],Me(i,(function(t){var i=Math.abs(e.startX-t.startX),n=Math.abs(e.startY-t.startY),a=Math.abs(e.endX-t.endX),o=Math.abs(e.endY-t.endY),s=Math.sqrt(i*i+n*n),c=(Math.sqrt(a*a+o*o)-s)/s;Math.abs(c)>Math.abs(r)&&(r=c)}))})),r}function Je(e,i){var r=e.pageX,n=e.pageY,a={endX:r,endY:n};return i?a:t({startX:r,startY:n},a)}function Ke(e){var t=0,i=0,r=0;return Me(e,(function(e){var n=e.startX,a=e.startY;t+=n,i+=a,r+=1})),{pageX:t/=r,pageY:i/=r}}function Qe(e){var t=e.aspectRatio,i=e.height,r=e.width,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"contain",a=ve(r),o=ve(i);if(a&&o){var s=i*t;"contain"===n&&s>r||"cover"===n&&s<r?i=r/t:r=i*t}else a?i=r/t:o&&(r=i*t);return{width:r,height:i}}function et(e){var t=e.width,i=e.height,r=e.degree;if(90==(r=Math.abs(r)%180))return{width:i,height:t};var n=r%90*Math.PI/180,a=Math.sin(n),o=Math.cos(n),s=t*o+i*a,c=t*a+i*o;return r>90?{width:c,height:s}:{width:s,height:c}}function tt(e,t,i,r){var n=t.aspectRatio,a=t.naturalWidth,o=t.naturalHeight,c=t.rotate,l=void 0===c?0:c,d=t.scaleX,h=void 0===d?1:d,u=t.scaleY,f=void 0===u?1:u,p=i.aspectRatio,m=i.naturalWidth,v=i.naturalHeight,g=r.fillColor,y=void 0===g?"transparent":g,b=r.imageSmoothingEnabled,w=void 0===b||b,_=r.imageSmoothingQuality,x=void 0===_?"low":_,k=r.maxWidth,M=void 0===k?1/0:k,C=r.maxHeight,S=void 0===C?1/0:C,j=r.minWidth,$=void 0===j?0:j,D=r.minHeight,E=void 0===D?0:D,Z=document.createElement("canvas"),O=Z.getContext("2d"),P=Qe({aspectRatio:p,width:M,height:S}),T=Qe({aspectRatio:p,width:$,height:E},"cover"),R=Math.min(P.width,Math.max(T.width,m)),I=Math.min(P.height,Math.max(T.height,v)),L=Qe({aspectRatio:n,width:M,height:S}),A=Qe({aspectRatio:n,width:$,height:E},"cover"),N=Math.min(L.width,Math.max(A.width,a)),F=Math.min(L.height,Math.max(A.height,o)),B=[-N/2,-F/2,N,F];return Z.width=je(R),Z.height=je(I),O.fillStyle=y,O.fillRect(0,0,R,I),O.save(),O.translate(R/2,I/2),O.rotate(l*Math.PI/180),O.scale(h,f),O.imageSmoothingEnabled=w,O.imageSmoothingQuality=x,O.drawImage.apply(O,[e].concat(s(B.map((function(e){return Math.floor(je(e))}))))),O.restore(),Z}var it=String.fromCharCode;function rt(e,t,i){var r="";i+=t;for(var n=t;n<i;n+=1)r+=it(e.getUint8(n));return r}var nt=/^data:.*,/;function at(e){var t=e.replace(nt,""),i=atob(t),r=new ArrayBuffer(i.length),n=new Uint8Array(r);return Me(n,(function(e,t){n[t]=i.charCodeAt(t)})),r}function ot(e,t){for(var i=[],r=8192,n=new Uint8Array(e);n.length>0;)i.push(it.apply(null,ke(n.subarray(0,r)))),n=n.subarray(r);return"data:".concat(t,";base64,").concat(btoa(i.join("")))}function st(e){var t,i=new DataView(e);try{var r,n,a;if(255===i.getUint8(0)&&216===i.getUint8(1))for(var o=i.byteLength,s=2;s+1<o;){if(255===i.getUint8(s)&&225===i.getUint8(s+1)){n=s;break}s+=1}if(n){var c=n+10;if("Exif"===rt(i,n+4,4)){var l=i.getUint16(c);if(((r=18761===l)||19789===l)&&42===i.getUint16(c+2,r)){var d=i.getUint32(c+4,r);d>=8&&(a=c+d)}}}if(a){var h,u,f=i.getUint16(a,r);for(u=0;u<f;u+=1)if(h=a+12*u+2,274===i.getUint16(h,r)){h+=8,t=i.getUint16(h,r),i.setUint16(h,1,r);break}}}catch(e){t=1}return t}function ct(e){var t=0,i=1,r=1;switch(e){case 2:i=-1;break;case 3:t=-180;break;case 4:r=-1;break;case 5:t=90,r=-1;break;case 6:t=90;break;case 7:t=90,i=-1;break;case 8:t=-90}return{rotate:t,scaleX:i,scaleY:r}}var lt={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var e=this.element,t=this.options,i=this.container,r=this.cropper,n=Number(t.minContainerWidth),a=Number(t.minContainerHeight);Ze(r,T),Oe(e,T);var o={width:Math.max(i.offsetWidth,n>=0?n:de),height:Math.max(i.offsetHeight,a>=0?a:he)};this.containerData=o,De(r,{width:o.width,height:o.height}),Ze(e,T),Oe(r,T)},initCanvas:function(){var e=this.containerData,t=this.imageData,i=this.options.viewMode,r=Math.abs(t.rotate)%180==90,n=r?t.naturalHeight:t.naturalWidth,a=r?t.naturalWidth:t.naturalHeight,o=n/a,s=e.width,c=e.height;e.height*o>e.width?3===i?s=e.height*o:c=e.width/o:3===i?c=e.width/o:s=e.height*o;var l={aspectRatio:o,naturalWidth:n,naturalHeight:a,width:s,height:c};this.canvasData=l,this.limited=1===i||2===i,this.limitCanvas(!0,!0),l.width=Math.min(Math.max(l.width,l.minWidth),l.maxWidth),l.height=Math.min(Math.max(l.height,l.minHeight),l.maxHeight),l.left=(e.width-l.width)/2,l.top=(e.height-l.height)/2,l.oldLeft=l.left,l.oldTop=l.top,this.initialCanvasData=Ce({},l)},limitCanvas:function(e,t){var i=this.options,r=this.containerData,n=this.canvasData,a=this.cropBoxData,o=i.viewMode,s=n.aspectRatio,c=this.cropped&&a;if(e){var l=Number(i.minCanvasWidth)||0,d=Number(i.minCanvasHeight)||0;o>1?(l=Math.max(l,r.width),d=Math.max(d,r.height),3===o&&(d*s>l?l=d*s:d=l/s)):o>0&&(l?l=Math.max(l,c?a.width:0):d?d=Math.max(d,c?a.height:0):c&&(l=a.width,(d=a.height)*s>l?l=d*s:d=l/s));var h=Qe({aspectRatio:s,width:l,height:d});l=h.width,d=h.height,n.minWidth=l,n.minHeight=d,n.maxWidth=1/0,n.maxHeight=1/0}if(t)if(o>(c?0:1)){var u=r.width-n.width,f=r.height-n.height;n.minLeft=Math.min(0,u),n.minTop=Math.min(0,f),n.maxLeft=Math.max(0,u),n.maxTop=Math.max(0,f),c&&this.limited&&(n.minLeft=Math.min(a.left,a.left+(a.width-n.width)),n.minTop=Math.min(a.top,a.top+(a.height-n.height)),n.maxLeft=a.left,n.maxTop=a.top,2===o&&(n.width>=r.width&&(n.minLeft=Math.min(0,u),n.maxLeft=Math.max(0,u)),n.height>=r.height&&(n.minTop=Math.min(0,f),n.maxTop=Math.max(0,f))))}else n.minLeft=-n.width,n.minTop=-n.height,n.maxLeft=r.width,n.maxTop=r.height},renderCanvas:function(e,t){var i=this.canvasData,r=this.imageData;if(t){var n=et({width:r.naturalWidth*Math.abs(r.scaleX||1),height:r.naturalHeight*Math.abs(r.scaleY||1),degree:r.rotate||0}),a=n.width,o=n.height,s=i.width*(a/i.naturalWidth),c=i.height*(o/i.naturalHeight);i.left-=(s-i.width)/2,i.top-=(c-i.height)/2,i.width=s,i.height=c,i.aspectRatio=a/o,i.naturalWidth=a,i.naturalHeight=o,this.limitCanvas(!0,!1)}(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCanvas(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,De(this.canvas,Ce({width:i.width,height:i.height},Ve({translateX:i.left,translateY:i.top}))),this.renderImage(e),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(e){var t=this.canvasData,i=this.imageData,r=i.naturalWidth*(t.width/t.naturalWidth),n=i.naturalHeight*(t.height/t.naturalHeight);Ce(i,{width:r,height:n,left:(t.width-r)/2,top:(t.height-n)/2}),De(this.image,Ce({width:i.width,height:i.height},Ve(Ce({translateX:i.left,translateY:i.top},i)))),e&&this.output()},initCropBox:function(){var e=this.options,t=this.canvasData,i=e.aspectRatio||e.initialAspectRatio,r=Number(e.autoCropArea)||.8,n={width:t.width,height:t.height};i&&(t.height*i>t.width?n.height=n.width/i:n.width=n.height*i),this.cropBoxData=n,this.limitCropBox(!0,!0),n.width=Math.min(Math.max(n.width,n.minWidth),n.maxWidth),n.height=Math.min(Math.max(n.height,n.minHeight),n.maxHeight),n.width=Math.max(n.minWidth,n.width*r),n.height=Math.max(n.minHeight,n.height*r),n.left=t.left+(t.width-n.width)/2,n.top=t.top+(t.height-n.height)/2,n.oldLeft=n.left,n.oldTop=n.top,this.initialCropBoxData=Ce({},n)},limitCropBox:function(e,t){var i=this.options,r=this.containerData,n=this.canvasData,a=this.cropBoxData,o=this.limited,s=i.aspectRatio;if(e){var c=Number(i.minCropBoxWidth)||0,l=Number(i.minCropBoxHeight)||0,d=o?Math.min(r.width,n.width,n.width+n.left,r.width-n.left):r.width,h=o?Math.min(r.height,n.height,n.height+n.top,r.height-n.top):r.height;c=Math.min(c,r.width),l=Math.min(l,r.height),s&&(c&&l?l*s>c?l=c/s:c=l*s:c?l=c/s:l&&(c=l*s),h*s>d?h=d/s:d=h*s),a.minWidth=Math.min(c,d),a.minHeight=Math.min(l,h),a.maxWidth=d,a.maxHeight=h}t&&(o?(a.minLeft=Math.max(0,n.left),a.minTop=Math.max(0,n.top),a.maxLeft=Math.min(r.width,n.left+n.width)-a.width,a.maxTop=Math.min(r.height,n.top+n.height)-a.height):(a.minLeft=0,a.minTop=0,a.maxLeft=r.width-a.width,a.maxTop=r.height-a.height))},renderCropBox:function(){var e=this.options,t=this.containerData,i=this.cropBoxData;(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCropBox(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,e.movable&&e.cropBoxMovable&&Le(this.face,N,i.width>=t.width&&i.height>=t.height?x:w),De(this.cropBox,Ce({width:i.width,height:i.height},Ve({translateX:i.left,translateY:i.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),We(this.element,H,this.getData())}},dt={initPreview:function(){var e=this.element,t=this.crossOrigin,i=this.options.preview,r=t?this.crossOriginUrl:this.url,n=e.alt||"The image to preview",a=document.createElement("img");if(t&&(a.crossOrigin=t),a.src=r,a.alt=n,this.viewBox.appendChild(a),this.viewBoxImage=a,i){var o=i;"string"==typeof i?o=e.ownerDocument.querySelectorAll(i):i.querySelector&&(o=[i]),this.previews=o,Me(o,(function(e){var i=document.createElement("img");Le(e,F,{width:e.offsetWidth,height:e.offsetHeight,html:e.innerHTML}),t&&(i.crossOrigin=t),i.src=r,i.alt=n,i.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',e.innerHTML="",e.appendChild(i)}))}},resetPreview:function(){Me(this.previews,(function(e){var t=Ie(e,F);De(e,{width:t.width,height:t.height}),e.innerHTML=t.html,Ae(e,F)}))},preview:function(){var e=this.imageData,t=this.canvasData,i=this.cropBoxData,r=i.width,n=i.height,a=e.width,o=e.height,s=i.left-t.left-e.left,c=i.top-t.top-e.top;this.cropped&&!this.disabled&&(De(this.viewBoxImage,Ce({width:a,height:o},Ve(Ce({translateX:-s,translateY:-c},e)))),Me(this.previews,(function(t){var i=Ie(t,F),l=i.width,d=i.height,h=l,u=d,f=1;r&&(u=n*(f=l/r)),n&&u>d&&(h=r*(f=d/n),u=d),De(t,{width:h,height:u}),De(t.getElementsByTagName("img")[0],Ce({width:a*f,height:o*f},Ve(Ce({translateX:-s*f,translateY:-c*f},e))))})))}},ht={bind:function(){var e=this.element,t=this.options,i=this.cropper;_e(t.cropstart)&&ze(e,q,t.cropstart),_e(t.cropmove)&&ze(e,G,t.cropmove),_e(t.cropend)&&ze(e,U,t.cropend),_e(t.crop)&&ze(e,H,t.crop),_e(t.zoom)&&ze(e,ne,t.zoom),ze(i,K,this.onCropStart=this.cropStart.bind(this)),t.zoomable&&t.zoomOnWheel&&ze(i,re,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),t.toggleDragModeOnDblclick&&ze(i,Y,this.onDblclick=this.dblclick.bind(this)),ze(e.ownerDocument,Q,this.onCropMove=this.cropMove.bind(this)),ze(e.ownerDocument,ee,this.onCropEnd=this.cropEnd.bind(this)),t.responsive&&ze(window,ie,this.onResize=this.resize.bind(this))},unbind:function(){var e=this.element,t=this.options,i=this.cropper;_e(t.cropstart)&&Be(e,q,t.cropstart),_e(t.cropmove)&&Be(e,G,t.cropmove),_e(t.cropend)&&Be(e,U,t.cropend),_e(t.crop)&&Be(e,H,t.crop),_e(t.zoom)&&Be(e,ne,t.zoom),Be(i,K,this.onCropStart),t.zoomable&&t.zoomOnWheel&&Be(i,re,this.onWheel,{passive:!1,capture:!0}),t.toggleDragModeOnDblclick&&Be(i,Y,this.onDblclick),Be(e.ownerDocument,Q,this.onCropMove),Be(e.ownerDocument,ee,this.onCropEnd),t.responsive&&Be(window,ie,this.onResize)}},ut={resize:function(){if(!this.disabled){var e,t,i=this.options,r=this.container,n=this.containerData,a=r.offsetWidth/n.width,o=r.offsetHeight/n.height,s=Math.abs(a-1)>Math.abs(o-1)?a:o;1!==s&&(i.restore&&(e=this.getCanvasData(),t=this.getCropBoxData()),this.render(),i.restore&&(this.setCanvasData(Me(e,(function(t,i){e[i]=t*s}))),this.setCropBoxData(Me(t,(function(e,i){t[i]=e*s})))))}},dblclick:function(){this.disabled||this.options.dragMode===W||this.setDragMode(Ee(this.dragBox,O)?z:B)},wheel:function(e){var t=this,i=Number(this.options.wheelZoomRatio)||.1,r=1;this.disabled||(e.preventDefault(),this.wheeling||(this.wheeling=!0,setTimeout((function(){t.wheeling=!1}),50),e.deltaY?r=e.deltaY>0?1:-1:e.wheelDelta?r=-e.wheelDelta/120:e.detail&&(r=e.detail>0?1:-1),this.zoom(-r*i,e)))},cropStart:function(e){var t=e.buttons,i=e.button;if(!(this.disabled||("mousedown"===e.type||"pointerdown"===e.type&&"mouse"===e.pointerType)&&(me(t)&&1!==t||me(i)&&0!==i||e.ctrlKey))){var r,n=this.options,a=this.pointers;e.changedTouches?Me(e.changedTouches,(function(e){a[e.identifier]=Je(e)})):a[e.pointerId||0]=Je(e),r=Object.keys(a).length>1&&n.zoomable&&n.zoomOnTouch?k:Ie(e.target,N),oe.test(r)&&!1!==We(this.element,q,{originalEvent:e,action:r})&&(e.preventDefault(),this.action=r,this.cropping=!1,r===_&&(this.cropping=!0,Ze(this.dragBox,L)))}},cropMove:function(e){var t=this.action;if(!this.disabled&&t){var i=this.pointers;e.preventDefault(),!1!==We(this.element,G,{originalEvent:e,action:t})&&(e.changedTouches?Me(e.changedTouches,(function(e){Ce(i[e.identifier]||{},Je(e,!0))})):Ce(i[e.pointerId||0]||{},Je(e,!0)),this.change(e))}},cropEnd:function(e){if(!this.disabled){var t=this.action,i=this.pointers;e.changedTouches?Me(e.changedTouches,(function(e){delete i[e.identifier]})):delete i[e.pointerId||0],t&&(e.preventDefault(),Object.keys(i).length||(this.action=""),this.cropping&&(this.cropping=!1,Pe(this.dragBox,L,this.cropped&&this.options.modal)),We(this.element,U,{originalEvent:e,action:t}))}}},ft={change:function(e){var t,i=this.options,r=this.canvasData,n=this.containerData,a=this.cropBoxData,o=this.pointers,s=this.action,c=i.aspectRatio,l=a.left,d=a.top,h=a.width,u=a.height,f=l+h,p=d+u,m=0,v=0,g=n.width,y=n.height,b=!0;!c&&e.shiftKey&&(c=h&&u?h/u:1),this.limited&&(m=a.minLeft,v=a.minTop,g=m+Math.min(n.width,r.width,r.left+r.width),y=v+Math.min(n.height,r.height,r.top+r.height));var O=o[Object.keys(o)[0]],P={x:O.endX-O.startX,y:O.endY-O.startY},R=function(e){switch(e){case M:f+P.x>g&&(P.x=g-f);break;case C:l+P.x<m&&(P.x=m-l);break;case j:d+P.y<v&&(P.y=v-d);break;case S:p+P.y>y&&(P.y=y-p)}};switch(s){case w:l+=P.x,d+=P.y;break;case M:if(P.x>=0&&(f>=g||c&&(d<=v||p>=y))){b=!1;break}R(M),(h+=P.x)<0&&(s=C,l-=h=-h),c&&(u=h/c,d+=(a.height-u)/2);break;case j:if(P.y<=0&&(d<=v||c&&(l<=m||f>=g))){b=!1;break}R(j),u-=P.y,d+=P.y,u<0&&(s=S,d-=u=-u),c&&(h=u*c,l+=(a.width-h)/2);break;case C:if(P.x<=0&&(l<=m||c&&(d<=v||p>=y))){b=!1;break}R(C),h-=P.x,l+=P.x,h<0&&(s=M,l-=h=-h),c&&(u=h/c,d+=(a.height-u)/2);break;case S:if(P.y>=0&&(p>=y||c&&(l<=m||f>=g))){b=!1;break}R(S),(u+=P.y)<0&&(s=j,d-=u=-u),c&&(h=u*c,l+=(a.width-h)/2);break;case $:if(c){if(P.y<=0&&(d<=v||f>=g)){b=!1;break}R(j),u-=P.y,d+=P.y,h=u*c}else R(j),R(M),P.x>=0?f<g?h+=P.x:P.y<=0&&d<=v&&(b=!1):h+=P.x,P.y<=0?d>v&&(u-=P.y,d+=P.y):(u-=P.y,d+=P.y);h<0&&u<0?(s=Z,d-=u=-u,l-=h=-h):h<0?(s=D,l-=h=-h):u<0&&(s=E,d-=u=-u);break;case D:if(c){if(P.y<=0&&(d<=v||l<=m)){b=!1;break}R(j),u-=P.y,d+=P.y,h=u*c,l+=a.width-h}else R(j),R(C),P.x<=0?l>m?(h-=P.x,l+=P.x):P.y<=0&&d<=v&&(b=!1):(h-=P.x,l+=P.x),P.y<=0?d>v&&(u-=P.y,d+=P.y):(u-=P.y,d+=P.y);h<0&&u<0?(s=E,d-=u=-u,l-=h=-h):h<0?(s=$,l-=h=-h):u<0&&(s=Z,d-=u=-u);break;case Z:if(c){if(P.x<=0&&(l<=m||p>=y)){b=!1;break}R(C),h-=P.x,l+=P.x,u=h/c}else R(S),R(C),P.x<=0?l>m?(h-=P.x,l+=P.x):P.y>=0&&p>=y&&(b=!1):(h-=P.x,l+=P.x),P.y>=0?p<y&&(u+=P.y):u+=P.y;h<0&&u<0?(s=$,d-=u=-u,l-=h=-h):h<0?(s=E,l-=h=-h):u<0&&(s=D,d-=u=-u);break;case E:if(c){if(P.x>=0&&(f>=g||p>=y)){b=!1;break}R(M),u=(h+=P.x)/c}else R(S),R(M),P.x>=0?f<g?h+=P.x:P.y>=0&&p>=y&&(b=!1):h+=P.x,P.y>=0?p<y&&(u+=P.y):u+=P.y;h<0&&u<0?(s=D,d-=u=-u,l-=h=-h):h<0?(s=Z,l-=h=-h):u<0&&(s=$,d-=u=-u);break;case x:this.move(P.x,P.y),b=!1;break;case k:this.zoom(Xe(o),e),b=!1;break;case _:if(!P.x||!P.y){b=!1;break}t=He(this.cropper),l=O.startX-t.left,d=O.startY-t.top,h=a.minWidth,u=a.minHeight,P.x>0?s=P.y>0?E:$:P.x<0&&(l-=h,s=P.y>0?Z:D),P.y<0&&(d-=u),this.cropped||(Oe(this.cropBox,T),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0))}b&&(a.width=h,a.height=u,a.left=l,a.top=d,this.action=s,this.renderCropBox()),Me(o,(function(e){e.startX=e.endX,e.startY=e.endY}))}},pt={crop:function(){return!this.ready||this.cropped||this.disabled||(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&Ze(this.dragBox,L),Oe(this.cropBox,T),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=Ce({},this.initialImageData),this.canvasData=Ce({},this.initialCanvasData),this.cropBoxData=Ce({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(Ce(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),Oe(this.dragBox,L),Ze(this.cropBox,T)),this},replace:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return!this.disabled&&e&&(this.isImg&&(this.element.src=e),t?(this.url=e,this.image.src=e,this.ready&&(this.viewBoxImage.src=e,Me(this.previews,(function(t){t.getElementsByTagName("img")[0].src=e})))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(e))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,Oe(this.cropper,P)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,Ze(this.cropper,P)),this},destroy:function(){var e=this.element;return e[b]?(e[b]=void 0,this.isImg&&this.replaced&&(e.src=this.originalUrl),this.uncreate(),this):this},move:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,i=this.canvasData,r=i.left,n=i.top;return this.moveTo(ge(e)?e:r+Number(e),ge(t)?t:n+Number(t))},moveTo:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,i=this.canvasData,r=!1;return e=Number(e),t=Number(t),this.ready&&!this.disabled&&this.options.movable&&(me(e)&&(i.left=e,r=!0),me(t)&&(i.top=t,r=!0),r&&this.renderCanvas(!0)),this},zoom:function(e,t){var i=this.canvasData;return e=(e=Number(e))<0?1/(1-e):1+e,this.zoomTo(i.width*e/i.naturalWidth,null,t)},zoomTo:function(e,t,i){var r=this.options,n=this.canvasData,a=n.width,o=n.height,s=n.naturalWidth,c=n.naturalHeight;if((e=Number(e))>=0&&this.ready&&!this.disabled&&r.zoomable){var l=s*e,d=c*e;if(!1===We(this.element,ne,{ratio:e,oldRatio:a/s,originalEvent:i}))return this;if(i){var h=this.pointers,u=He(this.cropper),f=h&&Object.keys(h).length?Ke(h):{pageX:i.pageX,pageY:i.pageY};n.left-=(l-a)*((f.pageX-u.left-n.left)/a),n.top-=(d-o)*((f.pageY-u.top-n.top)/o)}else we(t)&&me(t.x)&&me(t.y)?(n.left-=(l-a)*((t.x-n.left)/a),n.top-=(d-o)*((t.y-n.top)/o)):(n.left-=(l-a)/2,n.top-=(d-o)/2);n.width=l,n.height=d,this.renderCanvas(!0)}return this},rotate:function(e){return this.rotateTo((this.imageData.rotate||0)+Number(e))},rotateTo:function(e){return me(e=Number(e))&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=e%360,this.renderCanvas(!0,!0)),this},scaleX:function(e){var t=this.imageData.scaleY;return this.scale(e,me(t)?t:1)},scaleY:function(e){var t=this.imageData.scaleX;return this.scale(me(t)?t:1,e)},scale:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,i=this.imageData,r=!1;return e=Number(e),t=Number(t),this.ready&&!this.disabled&&this.options.scalable&&(me(e)&&(i.scaleX=e,r=!0),me(t)&&(i.scaleY=t,r=!0),r&&this.renderCanvas(!0,!0)),this},getData:function(){var e,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=this.options,r=this.imageData,n=this.canvasData,a=this.cropBoxData;if(this.ready&&this.cropped){e={x:a.left-n.left,y:a.top-n.top,width:a.width,height:a.height};var o=r.width/r.naturalWidth;if(Me(e,(function(t,i){e[i]=t/o})),t){var s=Math.round(e.y+e.height),c=Math.round(e.x+e.width);e.x=Math.round(e.x),e.y=Math.round(e.y),e.width=c-e.x,e.height=s-e.y}}else e={x:0,y:0,width:0,height:0};return i.rotatable&&(e.rotate=r.rotate||0),i.scalable&&(e.scaleX=r.scaleX||1,e.scaleY=r.scaleY||1),e},setData:function(e){var t=this.options,i=this.imageData,r=this.canvasData,n={};if(this.ready&&!this.disabled&&we(e)){var a=!1;t.rotatable&&me(e.rotate)&&e.rotate!==i.rotate&&(i.rotate=e.rotate,a=!0),t.scalable&&(me(e.scaleX)&&e.scaleX!==i.scaleX&&(i.scaleX=e.scaleX,a=!0),me(e.scaleY)&&e.scaleY!==i.scaleY&&(i.scaleY=e.scaleY,a=!0)),a&&this.renderCanvas(!0,!0);var o=i.width/i.naturalWidth;me(e.x)&&(n.left=e.x*o+r.left),me(e.y)&&(n.top=e.y*o+r.top),me(e.width)&&(n.width=e.width*o),me(e.height)&&(n.height=e.height*o),this.setCropBoxData(n)}return this},getContainerData:function(){return this.ready?Ce({},this.containerData):{}},getImageData:function(){return this.sized?Ce({},this.imageData):{}},getCanvasData:function(){var e=this.canvasData,t={};return this.ready&&Me(["left","top","width","height","naturalWidth","naturalHeight"],(function(i){t[i]=e[i]})),t},setCanvasData:function(e){var t=this.canvasData,i=t.aspectRatio;return this.ready&&!this.disabled&&we(e)&&(me(e.left)&&(t.left=e.left),me(e.top)&&(t.top=e.top),me(e.width)?(t.width=e.width,t.height=e.width/i):me(e.height)&&(t.height=e.height,t.width=e.height*i),this.renderCanvas(!0)),this},getCropBoxData:function(){var e,t=this.cropBoxData;return this.ready&&this.cropped&&(e={left:t.left,top:t.top,width:t.width,height:t.height}),e||{}},setCropBoxData:function(e){var t,i,r=this.cropBoxData,n=this.options.aspectRatio;return this.ready&&this.cropped&&!this.disabled&&we(e)&&(me(e.left)&&(r.left=e.left),me(e.top)&&(r.top=e.top),me(e.width)&&e.width!==r.width&&(t=!0,r.width=e.width),me(e.height)&&e.height!==r.height&&(i=!0,r.height=e.height),n&&(t?r.height=r.width/n:i&&(r.width=r.height*n)),this.renderCropBox()),this},getCroppedCanvas:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var t=this.canvasData,i=tt(this.image,this.imageData,t,e);if(!this.cropped)return i;var r=this.getData(e.rounded),n=r.x,a=r.y,o=r.width,c=r.height,l=i.width/Math.floor(t.naturalWidth);1!==l&&(n*=l,a*=l,o*=l,c*=l);var d=o/c,h=Qe({aspectRatio:d,width:e.maxWidth||1/0,height:e.maxHeight||1/0}),u=Qe({aspectRatio:d,width:e.minWidth||0,height:e.minHeight||0},"cover"),f=Qe({aspectRatio:d,width:e.width||(1!==l?i.width:o),height:e.height||(1!==l?i.height:c)}),p=f.width,m=f.height;p=Math.min(h.width,Math.max(u.width,p)),m=Math.min(h.height,Math.max(u.height,m));var v=document.createElement("canvas"),g=v.getContext("2d");v.width=je(p),v.height=je(m),g.fillStyle=e.fillColor||"transparent",g.fillRect(0,0,p,m);var y=e.imageSmoothingEnabled,b=void 0===y||y,w=e.imageSmoothingQuality;g.imageSmoothingEnabled=b,w&&(g.imageSmoothingQuality=w);var _,x,k,M,C,S,j=i.width,$=i.height,D=n,E=a;D<=-o||D>j?(D=0,_=0,k=0,C=0):D<=0?(k=-D,D=0,C=_=Math.min(j,o+D)):D<=j&&(k=0,C=_=Math.min(o,j-D)),_<=0||E<=-c||E>$?(E=0,x=0,M=0,S=0):E<=0?(M=-E,E=0,S=x=Math.min($,c+E)):E<=$&&(M=0,S=x=Math.min(c,$-E));var Z=[D,E,_,x];if(C>0&&S>0){var O=p/o;Z.push(k*O,M*O,C*O,S*O)}return g.drawImage.apply(g,[i].concat(s(Z.map((function(e){return Math.floor(je(e))}))))),v},setAspectRatio:function(e){var t=this.options;return this.disabled||ge(e)||(t.aspectRatio=Math.max(0,e)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(e){var t=this.options,i=this.dragBox,r=this.face;if(this.ready&&!this.disabled){var n=e===B,a=t.movable&&e===z;e=n||a?e:W,t.dragMode=e,Le(i,N,e),Pe(i,O,n),Pe(i,A,a),t.cropBoxMovable||(Le(r,N,e),Pe(r,O,n),Pe(r,A,a))}return this}},mt=v.Cropper,vt=function(){function e(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(r(this,e),!t||!le.test(t.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=t,this.options=Ce({},ue,we(i)&&i),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return a(e,[{key:"init",value:function(){var e,t=this.element,i=t.tagName.toLowerCase();if(!t[b]){if(t[b]=this,"img"===i){if(this.isImg=!0,e=t.getAttribute("src")||"",this.originalUrl=e,!e)return;e=t.src}else"canvas"===i&&window.HTMLCanvasElement&&(e=t.toDataURL());this.load(e)}}},{key:"load",value:function(e){var t=this;if(e){this.url=e,this.imageData={};var i=this.element,r=this.options;if(r.rotatable||r.scalable||(r.checkOrientation=!1),r.checkOrientation&&window.ArrayBuffer)if(se.test(e))ce.test(e)?this.read(at(e)):this.clone();else{var n=new XMLHttpRequest,a=this.clone.bind(this);this.reloading=!0,this.xhr=n,n.onabort=a,n.onerror=a,n.ontimeout=a,n.onprogress=function(){n.getResponseHeader("content-type")!==ae&&n.abort()},n.onload=function(){t.read(n.response)},n.onloadend=function(){t.reloading=!1,t.xhr=null},r.checkCrossOrigin&&qe(e)&&i.crossOrigin&&(e=Ye(e)),n.open("GET",e,!0),n.responseType="arraybuffer",n.withCredentials="use-credentials"===i.crossOrigin,n.send()}else this.clone()}}},{key:"read",value:function(e){var t=this.options,i=this.imageData,r=st(e),n=0,a=1,o=1;if(r>1){this.url=ot(e,ae);var s=ct(r);n=s.rotate,a=s.scaleX,o=s.scaleY}t.rotatable&&(i.rotate=n),t.scalable&&(i.scaleX=a,i.scaleY=o),this.clone()}},{key:"clone",value:function(){var e=this.element,t=this.url,i=e.crossOrigin,r=t;this.options.checkCrossOrigin&&qe(t)&&(i||(i="anonymous"),r=Ye(t)),this.crossOrigin=i,this.crossOriginUrl=r;var n=document.createElement("img");i&&(n.crossOrigin=i),n.src=r||t,n.alt=e.alt||"The image to crop",this.image=n,n.onload=this.start.bind(this),n.onerror=this.stop.bind(this),Ze(n,R),e.parentNode.insertBefore(n,e.nextSibling)}},{key:"start",value:function(){var e=this,t=this.image;t.onload=null,t.onerror=null,this.sizing=!0;var i=v.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(v.navigator.userAgent),r=function(t,i){Ce(e.imageData,{naturalWidth:t,naturalHeight:i,aspectRatio:t/i}),e.initialImageData=Ce({},e.imageData),e.sizing=!1,e.sized=!0,e.build()};if(!t.naturalWidth||i){var n=document.createElement("img"),a=document.body||document.documentElement;this.sizingImage=n,n.onload=function(){r(n.width,n.height),i||a.removeChild(n)},n.src=t.src,i||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",a.appendChild(n))}else r(t.naturalWidth,t.naturalHeight)}},{key:"stop",value:function(){var e=this.image;e.onload=null,e.onerror=null,e.parentNode.removeChild(e),this.image=null}},{key:"build",value:function(){if(this.sized&&!this.ready){var e=this.element,t=this.options,i=this.image,r=e.parentNode,n=document.createElement("div");n.innerHTML=fe;var a=n.querySelector(".".concat(b,"-container")),o=a.querySelector(".".concat(b,"-canvas")),s=a.querySelector(".".concat(b,"-drag-box")),c=a.querySelector(".".concat(b,"-crop-box")),l=c.querySelector(".".concat(b,"-face"));this.container=r,this.cropper=a,this.canvas=o,this.dragBox=s,this.cropBox=c,this.viewBox=a.querySelector(".".concat(b,"-view-box")),this.face=l,o.appendChild(i),Ze(e,T),r.insertBefore(a,e.nextSibling),Oe(i,R),this.initPreview(),this.bind(),t.initialAspectRatio=Math.max(0,t.initialAspectRatio)||NaN,t.aspectRatio=Math.max(0,t.aspectRatio)||NaN,t.viewMode=Math.max(0,Math.min(3,Math.round(t.viewMode)))||0,Ze(c,T),t.guides||Ze(c.getElementsByClassName("".concat(b,"-dashed")),T),t.center||Ze(c.getElementsByClassName("".concat(b,"-center")),T),t.background&&Ze(a,"".concat(b,"-bg")),t.highlight||Ze(l,I),t.cropBoxMovable&&(Ze(l,A),Le(l,N,w)),t.cropBoxResizable||(Ze(c.getElementsByClassName("".concat(b,"-line")),T),Ze(c.getElementsByClassName("".concat(b,"-point")),T)),this.render(),this.ready=!0,this.setDragMode(t.dragMode),t.autoCrop&&this.crop(),this.setData(t.data),_e(t.ready)&&ze(e,te,t.ready,{once:!0}),We(e,te)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var e=this.cropper.parentNode;e&&e.removeChild(this.cropper),Oe(this.element,T)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],[{key:"noConflict",value:function(){return window.Cropper=mt,e}},{key:"setDefaults",value:function(e){Ce(ue,we(e)&&e)}}]),e}();return Ce(vt.prototype,lt,dt,ht,ut,ft,pt),vt}()}},t={};function i(r){var n=t[r];if(void 0!==n)return n.exports;var a=t[r]={exports:{}};return e[r].call(a.exports,a,a.exports,i),a.exports}i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=i(3809),t=i(3363),r=i(6554),n=i(5498),a=i(6261);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function s(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(n=r.key,a=void 0,a=function(e,t){if("object"!==o(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!==o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(n,"string"),"symbol"===o(a)?a:String(a)),r)}var n,a}var c=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.group={},this.group.list=$("#app_media_items_list").html(),this.group.tiles=$("#app_media_items_tiles").html(),this.item={},this.item.list=$("#app_media_items_list_element").html(),this.item.tiles=$("#app_media_items_tiles_element").html(),this.$groupContainer=$(".app-media-items")}var i,r,a;return i=e,r=[{key:"renderData",value:function(e){var i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=this,o=t.Z.getConfigs(),s=a.group[t.Z.getRequestParams().view_type],c=t.Z.getRequestParams().view_in;t.Z.inArray(["all_media","public","trash","favorites","recent"],c)||(c="all_media"),s=s.replace(/__noItemIcon__/gi,t.Z.trans("no_item."+c+".icon")||"").replace(/__noItemTitle__/gi,t.Z.trans("no_item."+c+".title")||"").replace(/__noItemMessage__/gi,t.Z.trans("no_item."+c+".message")||"");var l=$(s),d=l.find("ul");r&&this.$groupContainer.find(".app-media-grid ul").length>0&&(d=this.$groupContainer.find(".app-media-grid ul")),t.Z.size(e.folders)>0||t.Z.size(e.files)>0||r?$(".app-media-items").addClass("has-items"):$(".app-media-items").removeClass("has-items"),t.Z.forEach(e.folders,(function(e){var i=a.item[t.Z.getRequestParams().view_type];i=i.replace(/__type__/gi,"folder").replace(/__id__/gi,e.id).replace(/__name__/gi,e.name||"").replace(/__size__/gi,"").replace(/__date__/gi,e.created_at||"").replace(/__thumb__/gi,'<i class="fa fa-folder"></i>');var r=$(i);t.Z.forEach(e,(function(e,t){r.data(t,e)})),r.data("is_folder",!0),r.data("icon",o.icons.folder),d.append(r)})),t.Z.forEach(e.files,(function(e){var i=a.item[t.Z.getRequestParams().view_type];i=i.replace(/__type__/gi,"file").replace(/__id__/gi,e.id).replace(/__name__/gi,e.name||"").replace(/__size__/gi,e.size||"").replace(/__date__/gi,e.created_at||""),i="list"===t.Z.getRequestParams().view_type?i.replace(/__thumb__/gi,'<i class="'+e.icon+'"></i>'):i.replace(/__thumb__/gi,"image"===e.type?'<img src="'+(e.thumb?e.thumb:e.full_url)+'" alt="'+e.name+'">':'<i class="'+e.icon+'"></i>');var r=$(i);r.data("is_folder",!1),t.Z.forEach(e,(function(e,t){r.data(t,e)})),d.append(r)})),!1!==i&&a.$groupContainer.empty(),r&&this.$groupContainer.find(".app-media-grid ul").length>0||a.$groupContainer.append(l),a.$groupContainer.find(".loading-wrapper").remove(),n.b.handleDropdown(),$(".js-media-list-title[data-id="+e.selected_file_id+"]").trigger("click")}}],r&&s(i.prototype,r),a&&s(i,a),Object.defineProperty(i,"prototype",{writable:!1}),e}();function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function d(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(n=r.key,a=void 0,a=function(e,t){if("object"!==l(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!==l(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(n,"string"),"symbol"===l(a)?a:String(a)),r)}var n,a}var h=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.$detailsWrapper=$(".app-media-main .app-media-details"),this.descriptionItemTemplate='<div class="app-media-name"><p>__title__</p>__url__</div>',this.onlyFields=["name","alt","full_url","size","mime_type","created_at","updated_at","nothing_selected"]}var i,r,n;return i=e,(r=[{key:"renderData",value:function(e){var i=this,r="image"===e.type?'<img src="'+e.full_url+'" alt="'+e.name+'">':'<i class="'+e.icon+'"></i>',n="",a=!1;t.Z.forEach(e,(function(e,r){t.Z.inArray(i.onlyFields,r)&&e&&(t.Z.inArray(["size","mime_type"],r)||(n+=i.descriptionItemTemplate.replace(/__title__/gi,t.Z.trans(r)).replace(/__url__/gi,e?"full_url"===r?'<div class="input-group"><input id="file_details_url" type="text" value="'+e+'" class="form-control"><span class="input-group-text"><button class="btn btn-default js-btn-copy-to-clipboard" type="button" data-clipboard-target="#file_details_url" title="Copied"><img class="clippy" src="'+t.Z.asset("/vendor/core/core/media/images/clippy.svg")+'" width="13" alt="Copy to clipboard"></button></span></div>':'<span title="'+e+'">'+e+"</span>":""),"full_url"===r&&(a=!0)))})),i.$detailsWrapper.find(".app-media-thumbnail").html(r),i.$detailsWrapper.find(".app-media-description").html(n),a&&(new Clipboard(".js-btn-copy-to-clipboard"),$(".js-btn-copy-to-clipboard").tooltip().on("mouseenter",(function(e){$(e.currentTarget).tooltip("hide")})).on("mouseleave",(function(e){$(e.currentTarget).tooltip("hide")})))}}])&&d(i.prototype,r),n&&d(i,n),Object.defineProperty(i,"prototype",{writable:!1}),e}();function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function f(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(n=r.key,a=void 0,a=function(e,t){if("object"!==u(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!==u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(n,"string"),"symbol"===u(a)?a:String(a)),r)}var n,a}var p=function(){function i(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),this.MediaList=new c,this.MediaDetails=new h,this.breadcrumbTemplate=$("#app_media_breadcrumb_item").html()}var r,o,s;return r=i,o=[{key:"getMedia",value:function(){var r=arguments.length>0&&void 0!==arguments[0]&&arguments[0],a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(void 0!==APP_MEDIA_CONFIG.pagination){if(APP_MEDIA_CONFIG.pagination.in_process_get_media)return;APP_MEDIA_CONFIG.pagination.in_process_get_media=!0}var s=this;s.getFileDetails({icon:"far fa-image",nothing_selected:""});var c=t.Z.getRequestParams();"recent"===c.view_in&&(c.recent_items=e.s),c.is_popup=!0===a||void 0,c.onSelectFiles=void 0,void 0!==c.search&&""!=c.search&&void 0!==c.selected_file_id&&(c.selected_file_id=void 0),c.load_more_file=o,void 0!==APP_MEDIA_CONFIG.pagination&&(c.paged=APP_MEDIA_CONFIG.pagination.paged,c.posts_per_page=APP_MEDIA_CONFIG.pagination.posts_per_page),t.Z.showAjaxLoading(),$httpClient.make().get(APP_MEDIA_URL.get_media,c).then((function(e){var t=e.data;s.MediaList.renderData(t.data,r,o),s.renderBreadcrumbs(t.data.breadcrumbs),i.refreshFilter(),n.b.renderActions(),void 0!==APP_MEDIA_CONFIG.pagination&&(void 0!==APP_MEDIA_CONFIG.pagination.paged&&(APP_MEDIA_CONFIG.pagination.paged+=1),void 0!==APP_MEDIA_CONFIG.pagination.in_process_get_media&&(APP_MEDIA_CONFIG.pagination.in_process_get_media=!1),void 0!==APP_MEDIA_CONFIG.pagination.posts_per_page&&t.data.files.length+t.data.folders.length<APP_MEDIA_CONFIG.pagination.posts_per_page&&void 0!==APP_MEDIA_CONFIG.pagination.has_more&&(APP_MEDIA_CONFIG.pagination.has_more=!1))})).finally((function(){return t.Z.hideAjaxLoading()}))}},{key:"getFileDetails",value:function(e){this.MediaDetails.renderData(e)}},{key:"renderBreadcrumbs",value:function(e){var i=this,r=$(".app-media-breadcrumb .breadcrumb");r.find("li").remove(),t.Z.each(e,(function(e){var t=i.breadcrumbTemplate;t=t.replace(/__name__/gi,e.name||"").replace(/__icon__/gi,e.icon?'<i class="'+e.icon+'"></i>':"").replace(/__folderId__/gi,e.id||0),r.append($(t))})),$(".app-media-container").attr("data-breadcrumb-count",t.Z.size(e))}}],s=[{key:"refreshFilter",value:function(){var e=$(".app-media-container"),i=t.Z.getRequestParams().view_in;"all_media"===i||t.Z.getRequestParams().folder_id?($('.app-media-actions .btn:not([data-type="refresh"]):not(label)').removeClass("disabled"),e.attr("data-allow-upload","true")):($('.app-media-actions .btn:not([data-type="refresh"]):not(label)').addClass("disabled"),e.attr("data-allow-upload","false")),$(".app-media-actions .btn.js-app-media-change-filter-group").removeClass("disabled");var r=$('.app-media-actions .btn[data-action="empty_trash"]');"trash"===i?(r.removeClass("hidden").removeClass("disabled"),t.Z.size(t.Z.getItems())||r.addClass("hidden").addClass("disabled")):r.addClass("hidden"),a.L.destroyContext(),a.L.initContext(),e.attr("data-view-in",i)}}],o&&f(r.prototype,o),s&&f(r,s),Object.defineProperty(r,"prototype",{writable:!1}),i}();function m(e){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(e)}function v(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(n=r.key,a=void 0,a=function(e,t){if("object"!==m(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!==m(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(n,"string"),"symbol"===m(a)?a:String(a)),r)}var n,a}var g=function(){function i(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),this.MediaService=new p,$(document).on("shown.bs.modal","#modal_add_folder",(function(e){$(e.currentTarget).find(".form-add-folder input[type=text]").focus()}))}var n,a,o;return n=i,o=[{key:"closeModal",value:function(){$(document).find("#modal_add_folder").modal("hide")}}],(a=[{key:"create",value:function(e){var n=this;t.Z.showAjaxLoading(),$httpClient.make().post(APP_MEDIA_URL.create_folder,{parent_id:t.Z.getRequestParams().folder_id,name:e}).then((function(e){var a=e.data;r.e.showMessage("success",a.message,t.Z.trans("message.success_header")),t.Z.resetPagination(),n.MediaService.getMedia(!0),i.closeModal()})).finally((function(){return t.Z.hideAjaxLoading()}))}},{key:"changeFolder",value:function(i){e.O.request_params.folder_id=i,t.Z.storeConfig(),this.MediaService.getMedia(!0)}}])&&v(n.prototype,a),o&&v(n,o),Object.defineProperty(n,"prototype",{writable:!1}),i}();function y(e){return y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},y(e)}function b(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}function w(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?b(Object(i),!0).forEach((function(t){_(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):b(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function _(e,t,i){return(t=k(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function x(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,k(r.key),r)}}function k(e){var t=function(e,t){if("object"!==y(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!==y(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===y(t)?t:String(t)}var M=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.$body=$("body"),this.dropZone=null,this.uploadUrl=APP_MEDIA_URL.upload_file,this.uploadProgressBox=$(".app-upload-progress"),this.uploadProgressContainer=$(".app-upload-progress .app-upload-progress-table"),this.uploadProgressTemplate=$("#app_media_upload_progress_item").html(),this.totalQueued=1,this.MediaService=new p,this.totalError=0}var i,r,n;return i=e,n=[{key:"formatFileSize",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1]?1e3:1024;if(Math.abs(e)<t)return e+" B";var i=["KB","MB","GB","TB","PB","EB","ZB","YB"],r=-1;do{e/=t,++r}while(Math.abs(e)>=t&&r<i.length-1);return e.toFixed(1)+" "+i[r]}}],(r=[{key:"init",value:function(){t.Z.hasPermission("files.create")&&$(".app-media-items").length>0&&this.setupDropZone(),this.handleEvents()}},{key:"setupDropZone",value:function(){var e=this,i=this.getDropZoneConfig();e.filesUpload=0,e.dropZone&&e.dropZone.destroy(),e.dropZone=new Dropzone(document.querySelector(".app-media-items"),w(w({},i),{},{thumbnailWidth:!1,thumbnailHeight:!1,parallelUploads:1,autoQueue:!0,clickable:".js-dropzone-upload",previewsContainer:!1,sending:function(e,i,r){r.append("_token",$('meta[name="csrf-token"]').attr("content")),r.append("folder_id",t.Z.getRequestParams().folder_id),r.append("view_in",t.Z.getRequestParams().view_in),r.append("path",e.fullPath)},chunksUploaded:function(t,i){e.uploadProgressContainer.find(".progress-percent").html("100%"),i()},accept:function(t,i){e.filesUpload++,e.totalError=0,i()},uploadprogress:function(t,i,r){var n=r/t.size*100;t.upload.chunked&&n>99&&(n-=1);var a=(n>100?"100":parseInt(n))+"%";e.uploadProgressContainer.find("li").eq(t.index-1).find(".progress-percent").html(a)}})),e.dropZone.on("addedfile",(function(t){t.index=e.totalQueued,e.totalQueued++})),e.dropZone.on("sending",(function(t){e.initProgress(t.name,t.size)})),e.dropZone.on("complete",(function(t){t.accepted&&e.changeProgressStatus(t),e.filesUpload=0})),e.dropZone.on("queuecomplete",(function(){t.Z.resetPagination(),e.MediaService.getMedia(!0),0===e.totalError&&setTimeout((function(){$(".app-upload-progress .close-pane").trigger("click")}),5e3)}))}},{key:"handleEvents",value:function(){var e=this;e.$body.off("click",".app-upload-progress .close-pane").on("click",".app-upload-progress .close-pane",(function(t){t.preventDefault(),$(".app-upload-progress").addClass("hide-the-pane"),e.totalError=0,setTimeout((function(){$(".app-upload-progress li").remove(),e.totalQueued=1}),300)}))}},{key:"initProgress",value:function(t,i){var r=this.uploadProgressTemplate.replace(/__fileName__/gi,t).replace(/__fileSize__/gi,e.formatFileSize(i)).replace(/__status__/gi,"warning").replace(/__message__/gi,"Uploading");this.checkUploadTotalProgress()&&this.uploadProgressContainer.find("li").length>=1||(this.uploadProgressContainer.append(r),this.uploadProgressBox.removeClass("hide-the-pane"),this.uploadProgressBox.find(".panel-body").animate({scrollTop:this.uploadProgressContainer.height()},150))}},{key:"changeProgressStatus",value:function(e){var i=this,r=i.uploadProgressContainer.find("li:nth-child("+e.index+")");this.checkUploadTotalProgress()&&(r=i.uploadProgressContainer.find("li:first"));var n=r.find(".label");n.removeClass("label-success label-danger label-warning");var a=t.Z.jsonDecode(e.xhr.responseText||"",{});if(i.totalError=i.totalError+(!0===a.error||"error"===e.status?1:0),n.addClass(!0===a.error||"error"===e.status?"label-danger":"label-success"),n.html(!0===a.error||"error"===e.status?"Error":"Uploaded"),"error"===e.status)if(422===e.xhr.status){var o="";$.each(a.errors,(function(e,t){o+='<span class="text-danger">'+t+"</span><br>"})),r.find(".file-error").html(o)}else 500===e.xhr.status&&r.find(".file-error").html('<span class="text-danger">'+e.xhr.statusText+"</span>");else a.error?r.find(".file-error").html('<span class="text-danger">'+a.message+"</span>"):(t.Z.addToRecent(a.data.id),t.Z.setSelectedFile(a.data.id))}},{key:"getDropZoneConfig",value:function(){return{url:this.uploadUrl,uploadMultiple:!APP_MEDIA_CONFIG.chunk.enabled,chunking:APP_MEDIA_CONFIG.chunk.enabled,forceChunking:!0,parallelChunkUploads:!1,chunkSize:APP_MEDIA_CONFIG.chunk.chunk_size,retryChunks:!0,retryChunksLimit:3,timeout:0,maxFilesize:APP_MEDIA_CONFIG.chunk.max_file_size,maxFiles:null}}},{key:"checkUploadTotalProgress",value:function(){return 1===this.filesUpload}}])&&x(i.prototype,r),n&&x(i,n),Object.defineProperty(i,"prototype",{writable:!1}),e}();function C(e){return C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},C(e)}function S(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */S=function(){return t};var e,t={},i=Object.prototype,r=i.hasOwnProperty,n=Object.defineProperty||function(e,t,i){e[t]=i.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function l(e,t,i){return Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,i){return e[t]=i}}function d(e,t,i,r){var a=t&&t.prototype instanceof g?t:g,o=Object.create(a.prototype),s=new O(r||[]);return n(o,"_invoke",{value:$(e,i,s)}),o}function h(e,t,i){try{return{type:"normal",arg:e.call(t,i)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var u="suspendedStart",f="suspendedYield",p="executing",m="completed",v={};function g(){}function y(){}function b(){}var w={};l(w,o,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(P([])));x&&x!==i&&r.call(x,o)&&(w=x);var k=b.prototype=g.prototype=Object.create(w);function M(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function j(e,t){function i(n,a,o,s){var c=h(e[n],e,a);if("throw"!==c.type){var l=c.arg,d=l.value;return d&&"object"==C(d)&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){i("next",e,o,s)}),(function(e){i("throw",e,o,s)})):t.resolve(d).then((function(e){l.value=e,o(l)}),(function(e){return i("throw",e,o,s)}))}s(c.arg)}var a;n(this,"_invoke",{value:function(e,r){function n(){return new t((function(t,n){i(e,r,t,n)}))}return a=a?a.then(n,n):n()}})}function $(t,i,r){var n=u;return function(a,o){if(n===p)throw new Error("Generator is already running");if(n===m){if("throw"===a)throw o;return{value:e,done:!0}}for(r.method=a,r.arg=o;;){var s=r.delegate;if(s){var c=D(s,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===u)throw n=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=p;var l=h(t,i,r);if("normal"===l.type){if(n=r.done?m:f,l.arg===v)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n=m,r.method="throw",r.arg=l.arg)}}}function D(t,i){var r=i.method,n=t.iterator[r];if(n===e)return i.delegate=null,"throw"===r&&t.iterator.return&&(i.method="return",i.arg=e,D(t,i),"throw"===i.method)||"return"!==r&&(i.method="throw",i.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var a=h(n,t.iterator,i.arg);if("throw"===a.type)return i.method="throw",i.arg=a.arg,i.delegate=null,v;var o=a.arg;return o?o.done?(i[t.resultName]=o.value,i.next=t.nextLoc,"return"!==i.method&&(i.method="next",i.arg=e),i.delegate=null,v):o:(i.method="throw",i.arg=new TypeError("iterator result is not an object"),i.delegate=null,v)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function Z(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function P(t){if(t||""===t){var i=t[o];if(i)return i.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,a=function i(){for(;++n<t.length;)if(r.call(t,n))return i.value=t[n],i.done=!1,i;return i.value=e,i.done=!0,i};return a.next=a}}throw new TypeError(C(t)+" is not iterable")}return y.prototype=b,n(k,"constructor",{value:b,configurable:!0}),n(b,"constructor",{value:y,configurable:!0}),y.displayName=l(b,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,l(e,c,"GeneratorFunction")),e.prototype=Object.create(k),e},t.awrap=function(e){return{__await:e}},M(j.prototype),l(j.prototype,s,(function(){return this})),t.AsyncIterator=j,t.async=function(e,i,r,n,a){void 0===a&&(a=Promise);var o=new j(d(e,i,r,n),a);return t.isGeneratorFunction(i)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},M(k),l(k,c,"Generator"),l(k,o,(function(){return this})),l(k,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),i=[];for(var r in t)i.push(r);return i.reverse(),function e(){for(;i.length;){var r=i.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=P,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(Z),!t)for(var i in this)"t"===i.charAt(0)&&r.call(this,i)&&!isNaN(+i.slice(1))&&(this[i]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var i=this;function n(r,n){return s.type="throw",s.arg=t,i.next=r,n&&(i.method="next",i.arg=e),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var c=r.call(o,"catchLoc"),l=r.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var i=this.tryEntries.length-1;i>=0;--i){var n=this.tryEntries[i];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.finallyLoc===e)return this.complete(i.completion,i.afterLoc),Z(i),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.tryLoc===e){var r=i.completion;if("throw"===r.type){var n=r.arg;Z(i)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(t,i,r){return this.delegate={iterator:P(t),resultName:i,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function j(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=function(e,t){if(!e)return;if("string"==typeof e)return D(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return D(e,t)}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==i.return||i.return()}finally{if(s)throw a}}}}function D(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,r=new Array(t);i<t;i++)r[i]=e[i];return r}function E(e,t,i,r,n,a,o){try{var s=e[a](o),c=s.value}catch(e){return void i(e)}s.done?t(c):Promise.resolve(c).then(r,n)}function Z(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(n=r.key,a=void 0,a=function(e,t){if("object"!==C(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!==C(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(n,"string"),"symbol"===C(a)?a:String(a)),r)}var n,a}var O=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.MediaService=new p,$(document).on("shown.bs.modal","#modal_download_url",(function(e){$(e.currentTarget).find(".form-download-url input[type=text]").focus()}))}var i,n,a,o,s;return i=e,n=[{key:"download",value:(o=S().mark((function i(n,a){var o,s,c,l,d,h;return S().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:o=this,n=$.trim(n).split(/\r?\n/),s=0,c=!1,l=j(n),i.prev=5,h=S().mark((function e(){var i,r,o;return S().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:i=d.value,r="";try{r=new URL(i).pathname.split("/").pop()}catch(e){r=i}return o=a("".concat(s," / ").concat(n.length),r,i),e.next=6,new Promise((function(e,r){$httpClient.make().post(APP_MEDIA_URL.download_url,{folderId:t.Z.getRequestParams().folder_id,url:i}).then((function(t){var i,r=t.data;o(!0,r.message||(null===(i=r.data)||void 0===i?void 0:i.message)),e()})).catch((function(e){return r(e)}))}));case 6:s+=1;case 7:case"end":return e.stop()}}),e)})),l.s();case 8:if((d=l.n()).done){i.next=12;break}return i.delegateYield(h(),"t0",10);case 10:i.next=8;break;case 12:i.next=17;break;case 14:i.prev=14,i.t1=i.catch(5),l.e(i.t1);case 17:return i.prev=17,l.f(),i.finish(17);case 20:t.Z.resetPagination(),o.MediaService.getMedia(!0),c||(e.closeModal(),r.e.showMessage("success",t.Z.trans("message.success_header")));case 23:case"end":return i.stop()}}),i,this,[[5,14,17,20]])})),s=function(){var e=this,t=arguments;return new Promise((function(i,r){var n=o.apply(e,t);function a(e){E(n,i,r,a,s,"next",e)}function s(e){E(n,i,r,a,s,"throw",e)}a(void 0)}))},function(e,t){return s.apply(this,arguments)})}],a=[{key:"closeModal",value:function(){$(document).find("#modal_download_url").modal("hide")}}],n&&Z(i.prototype,n),a&&Z(i,a),Object.defineProperty(i,"prototype",{writable:!1}),e}(),P=i(7400);function T(e){return T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}function R(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */R=function(){return t};var e,t={},i=Object.prototype,r=i.hasOwnProperty,n=Object.defineProperty||function(e,t,i){e[t]=i.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function l(e,t,i){return Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,i){return e[t]=i}}function d(e,t,i,r){var a=t&&t.prototype instanceof g?t:g,o=Object.create(a.prototype),s=new E(r||[]);return n(o,"_invoke",{value:S(e,i,s)}),o}function h(e,t,i){try{return{type:"normal",arg:e.call(t,i)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var u="suspendedStart",f="suspendedYield",p="executing",m="completed",v={};function g(){}function y(){}function b(){}var w={};l(w,o,(function(){return this}));var _=Object.getPrototypeOf,x=_&&_(_(Z([])));x&&x!==i&&r.call(x,o)&&(w=x);var k=b.prototype=g.prototype=Object.create(w);function M(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function i(n,a,o,s){var c=h(e[n],e,a);if("throw"!==c.type){var l=c.arg,d=l.value;return d&&"object"==T(d)&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){i("next",e,o,s)}),(function(e){i("throw",e,o,s)})):t.resolve(d).then((function(e){l.value=e,o(l)}),(function(e){return i("throw",e,o,s)}))}s(c.arg)}var a;n(this,"_invoke",{value:function(e,r){function n(){return new t((function(t,n){i(e,r,t,n)}))}return a=a?a.then(n,n):n()}})}function S(t,i,r){var n=u;return function(a,o){if(n===p)throw new Error("Generator is already running");if(n===m){if("throw"===a)throw o;return{value:e,done:!0}}for(r.method=a,r.arg=o;;){var s=r.delegate;if(s){var c=j(s,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===u)throw n=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=p;var l=h(t,i,r);if("normal"===l.type){if(n=r.done?m:f,l.arg===v)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n=m,r.method="throw",r.arg=l.arg)}}}function j(t,i){var r=i.method,n=t.iterator[r];if(n===e)return i.delegate=null,"throw"===r&&t.iterator.return&&(i.method="return",i.arg=e,j(t,i),"throw"===i.method)||"return"!==r&&(i.method="throw",i.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var a=h(n,t.iterator,i.arg);if("throw"===a.type)return i.method="throw",i.arg=a.arg,i.delegate=null,v;var o=a.arg;return o?o.done?(i[t.resultName]=o.value,i.next=t.nextLoc,"return"!==i.method&&(i.method="next",i.arg=e),i.delegate=null,v):o:(i.method="throw",i.arg=new TypeError("iterator result is not an object"),i.delegate=null,v)}function $(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function D(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach($,this),this.reset(!0)}function Z(t){if(t||""===t){var i=t[o];if(i)return i.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,a=function i(){for(;++n<t.length;)if(r.call(t,n))return i.value=t[n],i.done=!1,i;return i.value=e,i.done=!0,i};return a.next=a}}throw new TypeError(T(t)+" is not iterable")}return y.prototype=b,n(k,"constructor",{value:b,configurable:!0}),n(b,"constructor",{value:y,configurable:!0}),y.displayName=l(b,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,l(e,c,"GeneratorFunction")),e.prototype=Object.create(k),e},t.awrap=function(e){return{__await:e}},M(C.prototype),l(C.prototype,s,(function(){return this})),t.AsyncIterator=C,t.async=function(e,i,r,n,a){void 0===a&&(a=Promise);var o=new C(d(e,i,r,n),a);return t.isGeneratorFunction(i)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},M(k),l(k,c,"Generator"),l(k,o,(function(){return this})),l(k,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),i=[];for(var r in t)i.push(r);return i.reverse(),function e(){for(;i.length;){var r=i.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=Z,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(D),!t)for(var i in this)"t"===i.charAt(0)&&r.call(this,i)&&!isNaN(+i.slice(1))&&(this[i]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var i=this;function n(r,n){return s.type="throw",s.arg=t,i.next=r,n&&(i.method="next",i.arg=e),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var c=r.call(o,"catchLoc"),l=r.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var i=this.tryEntries.length-1;i>=0;--i){var n=this.tryEntries[i];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.finallyLoc===e)return this.complete(i.completion,i.afterLoc),D(i),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.tryLoc===e){var r=i.completion;if("throw"===r.type){var n=r.arg;D(i)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(t,i,r){return this.delegate={iterator:Z(t),resultName:i,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function I(e,t,i,r,n,a,o){try{var s=e[a](o),c=s.value}catch(e){return void i(e)}s.done?t(c):Promise.resolve(c).then(r,n)}function L(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(n=r.key,a=void 0,a=function(e,t){if("object"!==T(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!==T(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(n,"string"),"symbol"===T(a)?a:String(a)),r)}var n,a}var A=function(){function i(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),this.MediaService=new p,this.UploadService=new M,this.FolderService=new g,this.DownloadService=new O,this.$body=$("body")}var r,a,o;return r=i,a=[{key:"init",value:function(){t.Z.resetPagination(),this.setupLayout(),this.handleMediaList(),this.changeViewType(),this.changeFilter(),this.search(),this.handleActions(),this.UploadService.init(),this.handleModals(),this.scrollGetMore()}},{key:"setupLayout",value:function(){var i=$('.js-app-media-change-filter[data-type="filter"][data-value="'+t.Z.getRequestParams().filter+'"]');i.closest("li").addClass("active").closest(".dropdown").find(".js-app-media-filter-current").html("("+i.html()+")");var r=$('.js-app-media-change-filter[data-type="view_in"][data-value="'+t.Z.getRequestParams().view_in+'"]');r.closest("li").addClass("active").closest(".dropdown").find(".js-app-media-filter-current").html("("+r.html()+")"),t.Z.isUseInModal()&&$(".app-media-footer").removeClass("hidden"),$('.js-app-media-change-filter[data-type="sort_by"][data-value="'+t.Z.getRequestParams().sort_by+'"]').closest("li").addClass("active");var n=$("#media_details_collapse");n.prop("checked",e.O.hide_details_pane||!1),setTimeout((function(){$(".app-media-details").removeClass("hidden")}),300),n.on("change",(function(i){i.preventDefault(),e.O.hide_details_pane=$(i.currentTarget).is(":checked"),t.Z.storeConfig()})),$(document).on("click",".js-download-action",(function(e){e.preventDefault(),$("#modal_download_url").modal("show")})),$(document).on("click",".js-create-folder-action",(function(e){e.preventDefault(),$("#modal_add_folder").modal("show")}))}},{key:"handleMediaList",value:function(){var e=this,i=!1,r=!1,a=!1;$(document).on("keyup keydown",(function(e){i=e.ctrlKey,r=e.metaKey,a=e.shiftKey})),e.$body.off("click",".js-media-list-title").on("click",".js-media-list-title",(function(o){o.preventDefault();var s=$(o.currentTarget);if(a){var c=t.Z.arrayFirst(t.Z.getSelectedItems());if(c){var l=c.index_key,d=s.index();$(".app-media-items li").each((function(e,t){e>l&&e<=d&&$(t).find("input[type=checkbox]").prop("checked",!0)}))}}else i||r||s.closest(".app-media-items").find("input[type=checkbox]").prop("checked",!1);s.find("input[type=checkbox]").prop("checked",!0),n.b.handleDropdown(),e.MediaService.getFileDetails(s.data())})).on("dblclick",".js-media-list-title",(function(i){i.preventDefault();var r=$(i.currentTarget).data();if(!0===r.is_folder)t.Z.resetPagination(),e.FolderService.changeFolder(r.id);else if(t.Z.isUseInModal()){if("trash"!==t.Z.getConfigs().request_params.view_in){var a=t.Z.getSelectedFiles();t.Z.size(a)>0&&P.G.editorSelectFile(a)}}else n.b.handlePreview()})).on("dblclick",".js-up-one-level",(function(e){e.preventDefault();var t=$(".app-media-breadcrumb .breadcrumb li").length;$(".app-media-breadcrumb .breadcrumb li:nth-child("+(t-1)+") a").trigger("click")})).on("contextmenu",".js-context-menu",(function(e){$(e.currentTarget).find("input[type=checkbox]").is(":checked")||$(e.currentTarget).trigger("click")})).on("click contextmenu",".app-media-items",(function(i){t.Z.size(i.target.closest(".js-context-menu"))||($('.app-media-items input[type="checkbox"]').prop("checked",!1),$(".app-dropdown-actions").addClass("disabled"),e.MediaService.getFileDetails({icon:"far fa-image",nothing_selected:""}))}))}},{key:"changeViewType",value:function(){var i=this;i.$body.off("click",".js-app-media-change-view-type .btn").on("click",".js-app-media-change-view-type .btn",(function(r){r.preventDefault();var n=$(r.currentTarget);n.hasClass("active")||(n.closest(".js-app-media-change-view-type").find(".btn").removeClass("active"),n.addClass("active"),e.O.request_params.view_type=n.data("type"),"trash"===n.data("type")?$(document).find(".js-insert-to-editor").prop("disabled",!0):$(document).find(".js-insert-to-editor").prop("disabled",!1),t.Z.storeConfig(),void 0!==APP_MEDIA_CONFIG.pagination&&void 0!==APP_MEDIA_CONFIG.pagination.paged&&(APP_MEDIA_CONFIG.pagination.paged=1),i.MediaService.getMedia(!0,!1))})),$('.js-app-media-change-view-type .btn[data-type="'+t.Z.getRequestParams().view_type+'"]').trigger("click"),this.bindIntegrateModalEvents()}},{key:"changeFilter",value:function(){var i=this;i.$body.off("click",".js-app-media-change-filter").on("click",".js-app-media-change-filter",(function(r){if(r.preventDefault(),!t.Z.isOnAjaxLoading()){var n=$(r.currentTarget),a=n.closest("ul"),o=n.data();e.O.request_params[o.type]=o.value,window.appMedia.options&&"view_in"===o.type&&(window.appMedia.options.view_in=o.value),"view_in"===o.type&&(e.O.request_params.folder_id=0,"trash"===o.value?$(document).find(".js-insert-to-editor").prop("disabled",!0):$(document).find(".js-insert-to-editor").prop("disabled",!1)),n.closest(".dropdown").find(".js-app-media-filter-current").html("("+n.html()+")"),t.Z.storeConfig(),p.refreshFilter(),t.Z.resetPagination(),i.MediaService.getMedia(!0),a.find("> li").removeClass("active"),n.closest("li").addClass("active")}}))}},{key:"search",value:function(){var i=this;$('.input-search-wrapper input[type="text"]').val(t.Z.getRequestParams().search||""),i.$body.off("submit",".input-search-wrapper").on("submit",".input-search-wrapper",(function(r){r.preventDefault(),e.O.request_params.search=$(r.currentTarget).find('input[type="text"]').val(),t.Z.storeConfig(),t.Z.resetPagination(),i.MediaService.getMedia(!0)}))}},{key:"handleActions",value:function(){var e=this;e.$body.off("click",'.app-media-actions .js-change-action[data-type="refresh"]').on("click",'.app-media-actions .js-change-action[data-type="refresh"]',(function(i){i.preventDefault(),t.Z.resetPagination();var r=void 0!==window.appMedia.$el?window.appMedia.$el.data("app-media"):void 0;void 0!==r&&r.length>0&&void 0!==r[0].selected_file_id?e.MediaService.getMedia(!0,!0):e.MediaService.getMedia(!0,!1)})).off("click",".app-media-items li.no-items").on("click",".app-media-items li.no-items",(function(e){e.preventDefault(),$(".app-media-header .app-media-top-header .app-media-actions .js-dropzone-upload").trigger("click")})).off("submit",".form-add-folder").on("submit",".form-add-folder",(function(t){t.preventDefault();var i=$(t.currentTarget).find("input[type=text]"),r=i.val();return e.FolderService.create(r),i.val(""),!1})).off("click",".js-change-folder").on("click",".js-change-folder",(function(i){i.preventDefault();var r=$(i.currentTarget).data("folder");t.Z.resetPagination(),e.FolderService.changeFolder(r)})).off("click",".js-files-action").on("click",".js-files-action",(function(i){i.preventDefault(),n.b.handleGlobalAction($(i.currentTarget).data("action"),(function(){t.Z.resetPagination(),e.MediaService.getMedia(!0)}))})).off("submit",".form-download-url").on("submit",".form-download-url",function(){var t,i=(t=R().mark((function t(i){var r,n,a,o,s,c,l,d;return R().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=$("#modal_download_url"),n=r.find("#download-form-wrapper"),a=r.find("#modal-notice").empty(),i.preventDefault(),o=r.find(".modal-title"),s=r.find('textarea[name="urls"]').prop("disabled",!0),c=r.find('[type="submit"]').addClass("button-loading").prop("disabled",!0),l=s.val(),d=[],n.slideUp(),t.next=10,e.DownloadService.download(l,(function(e,t,i){var r=$('\n                        <div class="p-2 text-primary">\n                            <i class="fa fa-info-circle"></i>\n                            <span>'.concat(t,"</span>\n                        </div>\n                    "));return a.append(r).scrollTop(a[0].scrollHeight),o.html('<i class="fas fa-cloud-download-alt"></i> '.concat(o.data("downloading")," (").concat(e,")")),function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";e||d.push(i),r.find("span").text("".concat(t,": ").concat(n)),r.attr("class","p-2 text-".concat(e?"success":"danger")).find("i").attr("class",e?"fas fa-check-circle":"fas fa-times-circle")}}));case 10:return n.slideDown(),s.val(d.join("\n")).prop("disabled",!1),o.html('<i class="fas fa-cloud-download-alt"></i> '.concat(o.data("text"))),c.removeClass("button-loading").prop("disabled",!1),t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t)})),function(){var e=this,i=arguments;return new Promise((function(r,n){var a=t.apply(e,i);function o(e){I(a,r,n,o,s,"next",e)}function s(e){I(a,r,n,o,s,"throw",e)}o(void 0)}))});return function(e){return i.apply(this,arguments)}}())}},{key:"handleModals",value:function(){var i=this;i.$body.on("show.bs.modal","#modal_rename_items",(function(){n.b.renderRenameItems()})),i.$body.on("show.bs.modal","#modal_alt_text_items",(function(){n.b.renderAltTextItems()})),i.$body.on("show.bs.modal","#modal_crop_image",(function(){n.b.renderCropImage()})),i.$body.on("hidden.bs.modal","#modal_download_url",(function(){var e=$("#modal_download_url");e.find("textarea").val(""),e.find("#modal-notice").empty()})),i.$body.off("submit","#modal_crop_image .form-crop").on("submit","#modal_crop_image .form-crop",(function(e){e.preventDefault();var t=$(e.currentTarget),r=t.find('input[name="image_id"]').val(),a=t.find('input[name="crop_data"]').val();n.b.processAction({action:t.data("action"),imageId:r,cropData:a},(function(e){e.error||(t.closest(".modal").modal("hide"),i.MediaService.getMedia(!0))}))})),i.$body.off("submit","#modal_rename_items .form-rename").on("submit","#modal_rename_items .form-rename",(function(e){e.preventDefault();var r=[],a=$(e.currentTarget);$("#modal_rename_items .form-control").each((function(e,t){var i=$(t),n=i.closest(".form-group").data();n.name=i.val(),r.push(n)})),n.b.processAction({action:a.data("action"),selected:r},(function(e){e.error?$("#modal_rename_items .form-group").each((function(i,r){var n=$(r);t.Z.inArray(e.data,n.data("id"))?n.addClass("has-error"):n.removeClass("has-error")})):(a.closest(".modal").modal("hide"),i.MediaService.getMedia(!0))}))})),i.$body.off("submit","#modal_alt_text_items .form-alt-text").on("submit","#modal_alt_text_items .form-alt-text",(function(e){e.preventDefault();var r=[],a=$(e.currentTarget);$("#modal_alt_text_items .form-control").each((function(e,t){var i=$(t),n=i.closest(".form-group").data();n.alt=i.val(),r.push(n)})),n.b.processAction({action:a.data("action"),selected:r},(function(e){e.error?$("#modal_alt_text_items .form-group").each((function(i,r){var n=$(r);t.Z.inArray(e.data,n.data("id"))?n.addClass("has-error"):n.removeClass("has-error")})):(a.closest(".modal").modal("hide"),i.MediaService.getMedia(!0))}))})),i.$body.off("submit",".form-delete-items").on("submit",".form-delete-items",(function(e){e.preventDefault();var r=[],a=$(e.currentTarget);t.Z.each(t.Z.getSelectedItems(),(function(e){r.push({id:e.id,is_folder:e.is_folder})})),n.b.processAction({action:a.data("action"),selected:r},(function(e){a.closest(".modal").modal("hide"),e.error||i.MediaService.getMedia(!0)}))})),i.$body.off("submit","#modal_empty_trash .app-form").on("submit","#modal_empty_trash .app-form",(function(e){e.preventDefault();var t=$(e.currentTarget);n.b.processAction({action:t.data("action")},(function(){t.closest(".modal").modal("hide"),i.MediaService.getMedia(!0)}))})),"trash"===e.O.request_params.view_in?$(document).find(".js-insert-to-editor").prop("disabled",!0):$(document).find(".js-insert-to-editor").prop("disabled",!1),this.bindIntegrateModalEvents()}},{key:"checkFileTypeSelect",value:function(e){if(void 0!==window.appMedia.$el){var i=t.Z.arrayFirst(e),r=window.appMedia.$el.data("app-media");if(void 0!==r&&void 0!==r[0]&&void 0!==r[0].file_type&&"undefined"!==i&&"undefined"!==i.type){if(!r[0].file_type.match(i.type))return!1;if(void 0!==r[0].ext_allowed&&$.isArray(r[0].ext_allowed)&&-1===$.inArray(i.mime_type,r[0].ext_allowed))return!1}}return!0}},{key:"bindIntegrateModalEvents",value:function(){var e=$("#app_media_modal"),i=this;e.off("click",".js-insert-to-editor").on("click",".js-insert-to-editor",(function(r){r.preventDefault();var n=t.Z.getSelectedFiles();t.Z.size(n)>0&&(window.appMedia.options.onSelectFiles(n,window.appMedia.$el),i.checkFileTypeSelect(n)&&e.find(".btn-close").trigger("click"))})),e.off("dblclick",".js-media-list-title").on("dblclick",".js-media-list-title",(function(r){if(r.preventDefault(),"trash"!==t.Z.getConfigs().request_params.view_in){var a=t.Z.getSelectedFiles();t.Z.size(a)>0&&(window.appMedia.options.onSelectFiles(a,window.appMedia.$el),i.checkFileTypeSelect(a)&&e.find(".btn-close").trigger("click"))}else n.b.handlePreview()}))}},{key:"scrollGetMore",value:function(){var e=this;$(".app-media-main .app-media-items").bind("DOMMouseScroll mousewheel",(function(t){(t.originalEvent.detail>0||t.originalEvent.wheelDelta<0)&&($(t.currentTarget).closest(".media-modal").length>0?$(t.currentTarget).scrollTop()+$(t.currentTarget).innerHeight()/2>=$(t.currentTarget)[0].scrollHeight-450:$(t.currentTarget).scrollTop()+$(t.currentTarget).innerHeight()>=$(t.currentTarget)[0].scrollHeight-150)&&void 0!==APP_MEDIA_CONFIG.pagination&&APP_MEDIA_CONFIG.pagination.has_more&&e.MediaService.getMedia(!1,!1,!0)}))}}],a&&L(r.prototype,a),o&&L(r,o),Object.defineProperty(r,"prototype",{writable:!1}),i}();$(document).ready((function(){window.appMedia=window.appMedia||{},(new A).init()}))})()})();
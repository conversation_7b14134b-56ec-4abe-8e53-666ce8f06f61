<!DOCTYPE html>
<html>
<head>
    <title>Test Form</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test Form</h1>
    
    <form id="test-form" action="/register/send" method="POST">
        <input type="hidden" name="_token" value="{{ csrf_token() }}">
        <div>
            <label>Name:</label>
            <input type="text" name="name" required>
        </div>
        <div>
            <label>Email:</label>
            <input type="email" name="email" required>
        </div>
        <div>
            <label>Phone:</label>
            <input type="tel" name="phone" required>
        </div>
        <input type="hidden" name="car_id" value="1">
        <input type="hidden" name="showroom_id" value="1">
        <input type="hidden" name="type_response" value="1">
        <button type="submit">Submit</button>
    </form>

    <script>
        $('#test-form').on('submit', function(e) {
            e.preventDefault();
            
            var formData = $(this).serialize();
            console.log('Form data:', formData);
            
            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                success: function(response) {
                    console.log('Success:', response);
                    alert('Success: ' + response.message);
                },
                error: function(xhr) {
                    console.log('Error:', xhr);
                    alert('Error: ' + xhr.status + ' - ' + xhr.responseText);
                }
            });
        });
    </script>
</body>
</html>

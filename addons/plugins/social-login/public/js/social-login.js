(()=>{function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(o){return typeof o}:function(o){return o&&"function"==typeof Symbol&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},o(e)}function e(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(i=r.key,a=void 0,a=function(e,n){if("object"!==o(e)||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!==o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}(i,"string"),"symbol"===o(a)?a:String(a)),r)}var i,a}var n=function(){function o(){!function(o,e){if(!(o instanceof e))throw new TypeError("Cannot call a class as a function")}(this,o)}var n,t,r;return n=o,(t=[{key:"init",value:function(){$("#social_login_enable").on("change",(function(o){$(o.currentTarget).prop("checked")?$(".wrapper-list-social-login-options").show():$(".wrapper-list-social-login-options").hide()})),$(".enable-social-login-option").on("change",(function(o){var e=$(o.currentTarget);e.prop("checked")?(e.closest(".wrapper-content").find(".enable-social-login-option-wrapper").show(),e.closest(".form-group").removeClass("mb-0")):(e.closest(".wrapper-content").find(".enable-social-login-option-wrapper").hide(),e.closest(".form-group").addClass("mb-0"))}))}}])&&e(n.prototype,t),r&&e(n,r),Object.defineProperty(n,"prototype",{writable:!1}),o}();$(document).ready((function(){(new n).init()}))})();
<?php

namespace Addons\Gallery\Facades;

use Addons\Gallery\GallerySupport;
use Illuminate\Support\Facades\Facade;

/**
 * @method static \Addons\Gallery\GallerySupport registerModule(array|string $model)
 * @method static array getSupportedModules()
 * @method static \Addons\Gallery\GallerySupport removeModule(array|string $model)
 * @method static void saveGallery(\Illuminate\Http\Request $request, \Illuminate\Database\Eloquent\Model|null $data)
 * @method static bool deleteGallery(\Illuminate\Database\Eloquent\Model|null $data)
 * @method static \Addons\Gallery\GallerySupport registerAssets()
 * @method static string|null getGalleriesPageUrl()
 *
 * @see \Addons\Gallery\GallerySupport
 */
class Gallery extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return GallerySupport::class;
    }
}

<section class="section page-intro pt-100 pb-100 bg-cover">
    <div
        class="bg-overlay"
        style="opacity: 0.7"
    ></div>
    <div class="container">
        <h3 class="page-intro__title">{{ __('Galleries') }}</h3>
        {!! Theme::breadcrumb()->render() !!}
    </div>
</section>
<section class="section pt-50 pb-100">
    <div class="container">
        <div class="page-content">
            <article class="post post--single">
                <div class="post__content">
                    @if (isset($galleries) && !$galleries->isEmpty())
                        <div class="gallery-wrap">
                            @foreach ($galleries as $gallery)
                                <div class="gallery-item">
                                    <div class="img-wrap">
                                        <a href="{{ $gallery->url }}"><img
                                                src="{{ AppMedia::getImageUrl($gallery->image, 'medium') }}"
                                                alt="{{ $gallery->name }}"
                                            ></a>
                                    </div>
                                    <div class="gallery-detail">
                                        <div class="gallery-title"><a
                                                href="{{ $gallery->url }}">{{ $gallery->name }}</a></div>
                                        <div class="gallery-author">{{ __('By') }} {{ $gallery->user->name }}</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            </article>
        </div>
    </div>
</section>

<?php

namespace Addons\BookingCar\Models;

use Addons\Base\Casts\SafeContent;
use Addons\Base\Enums\BaseStatusEnum;
use Addons\Base\Models\BaseModel;
use Addons\Car\Models\Car;
use Addons\Showroom\Models\Showroom;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @method static \Addons\Base\Models\BaseQueryBuilder<static> query()
 */
class BookingCar extends BaseModel
{
    protected $table = 'booking_cars';

    protected $fillable = [
        'name',
        'status',
        'phone',
        'car_id',
        'showroom_id',
        'address',
        'date',
        'price_car',
        'price_license_plate',
        'price_entry',
        'total_price',
        'price_registry',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
    ];

    public function car(): BelongsTo
    {
        return $this->belongsTo(Car::class, 'car_id');
    }
    public function showRoom(): BelongsTo
    {
        return $this->belongsTo(Showroom::class, 'showroom_id');
    }
}

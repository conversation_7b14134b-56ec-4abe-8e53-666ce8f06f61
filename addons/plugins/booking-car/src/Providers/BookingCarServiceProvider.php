<?php

namespace Addons\BookingCar\Providers;

use Addons\BookingCar\Models\BookingCar;
use Addons\Base\Facades\DashboardMenu;
use Addons\Base\Traits\LoadAndPublishDataTrait;
use Addons\Base\Supports\ServiceProvider;
use Illuminate\Routing\Events\RouteMatched;

class BookingCarServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function boot(): void
    {
        $this
            ->setNamespace('plugins/booking-car')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions'])
            ->loadMigrations()
            ->loadAndPublishTranslations()
            ->loadAndPublishViews()
            ->loadRoutes();

        if (defined('LANGUAGE_ADVANCED_MODULE_SCREEN_NAME')) {
            \Addons\LanguageAdvanced\Supports\LanguageAdvancedManager::registerModule(BookingCar::class, [
                'name',
            ]);
        }

        $this->app['events']->listen(RouteMatched::class, function () {
            DashboardMenu::registerItem([
                'id' => 'cms-plugins-booking-car',
                'priority' => 5,
                'parent_id' => null,
                'name' => 'plugins/booking-car::booking-car.name',
                'icon' => 'fa-solid fa-car',
                'url' => route('booking-car.index'),
                'permissions' => ['booking-car.index'],
            ]);
        });
    }
}

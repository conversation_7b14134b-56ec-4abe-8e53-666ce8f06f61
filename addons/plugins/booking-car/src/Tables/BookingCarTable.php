<?php

namespace Addons\BookingCar\Tables;

use Addons\Base\Facades\BaseHelper;
use Addons\Base\Facades\Html;
use Addons\Base\Enums\BaseStatusEnum;
use Addons\BookingCar\Models\BookingCar;
use Addons\Car\Models\Car;
use Addons\Showroom\Models\Showroom;
use Addons\Table\Abstracts\TableAbstract;
use Addons\Table\Actions\DeleteAction;
use Addons\Table\Actions\EditAction;
use Addons\Table\BulkActions\DeleteBulkAction;
use Addons\Table\Columns\CreatedAtColumn;
use Addons\Table\Columns\IdColumn;
use Addons\Table\Columns\NameColumn;
use Addons\Table\Columns\StatusColumn;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;

class BookingCarTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(BookingCar::class)
            ->addActions([
                EditAction::make()
                    ->route('booking-car.edit'),
                // DeleteAction::make()
                //     ->route('booking-car.destroy'),
            ]);
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('name', function (BookingCar $item) {
                if (!$this->hasPermission('booking-car.edit')) {
                    return BaseHelper::clean($item->name);
                }
                return Html::link(route('booking-car.edit', $item->getKey()), BaseHelper::clean($item->name));
            })
            ->editColumn('status', function ($item) {
                return $item->status->toHtml();
            })
            ->editColumn('car_id', function ($item) {
                return $item->car ?  BaseHelper::clean($item->car->name) : 'N/a';
            })
            ->editColumn('showroom_id', function ($item) {
                return $item->showRoom ?  BaseHelper::clean($item->showRoom->name) : 'N/a';
            })
            ->editColumn('phone', function ($item) {
                return $item->phone ?  BaseHelper::clean($item->phone) : 'N/a';
            })
            ->editColumn('price_car', function ($item) {
                return number_format($item->price_car, 0, ',', '.') . ' đ';
            })
            ->editColumn('price_license_plate', function ($item) {
                return number_format($item->price_license_plate, 0, ',', '.') . ' đ';
            })
            ->editColumn('price_entry', function ($item) {
                return number_format($item->price_entry, 0, ',', '.') . ' đ';
            })
            ->editColumn('total_price', function ($item) {
                return number_format($item->total_price, 0, ',', '.') . ' đ';
            })
            ->editColumn('total_price', function ($item) {
                return number_format($item->total_price, 0, ',', '.') . ' đ';
            })
            ->editColumn('price_registry', function ($item) {
                return number_format($item->price_registry, 0, ',', '.') . ' đ';
            })
            ->editColumn('created_at', function ($item) {
                return BaseHelper::formatDate($item->created_at);
            });

        return $this->toJson($data);
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        $query = $this
            ->getModel()
            ->query()
            ->select([
                'id',
                'created_at',
                'name',
                'status',
                'phone',
                'car_id',
                'showroom_id',
                'address',
                'date',
                'price_car',
                'price_license_plate',
                'price_entry',
                'total_price',
                'price_registry',
            ]);

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        return [
            'id' => [
                'title' => trans('core/base::tables.id'),
                'width' => '20px',
            ],
            'name' => [
                'title' => trans('TÊN'),
                'class' => '200px',
            ],
            'phone' => [
                'title' => trans('SỐ ĐIỆN THOẠI'),
                'width' => '100px',
            ],
            'car_id' => [
                'title' => trans('LOẠI XE'),
                'width' => '100px',
            ],
            'showroom_id' => [
                'title' => trans('SHOW ROOM'),
                'width' => '100px',
            ],
            'total_price' => [
                'title' => trans('TỔNG GIÁ'),
                'width' => '75px',
            ],
            'price_license_plate' => [
                'title' => trans('PHÍ TRƯỚC BẠ'),
                'width' => '75px',
            ],
            'price_entry' => [
                'title' => trans('PHÍ BIỂN SỐ '),
                'width' => '75px',
            ],
            'price_registry' => [
                'title' => trans('PHÍ ĐĂNG KIỂM'),
                'width' => '75px',
            ],
            'created_at' => [
                'title' => trans('NGÀY TẠO'),
                'width' => '100px',
            ],
            'status' => [
                'title' => trans('TRẠNG THÁI'),
                'width' => '50px',
            ],
        ];
    }

    // public function buttons(): array
    // {
    //     return $this->addCreateButton(route('booking-car.create'), 'booking-car.create');
    // }

    // public function bulkActions(): array
    // {
    //     return [
    //         DeleteBulkAction::make()->permission('booking-car.destroy'),
    //     ];
    // }

    public function getBulkChanges(): array
    {


        $showroom =  Showroom::pluck('name', 'id')->toArray();
        // dd($showroom);
        $car = Car::pluck('name', 'id')->toArray();
        return [
            'name' => [
                'title' => trans('Tên'),
                'type' => 'text',
                'validate' => 'required|max:120',
            ],
            'status' => [
                'title' => trans('Trạng thái'),
                'type' => 'select',
                'choices' => BaseStatusEnum::labels(),
                'validate' => 'required|in:' . implode(',', BaseStatusEnum::values()),
            ],
            'car_id' => [
                'title' => trans('Loại xe'),
                'type'     => 'select',
                'choices'  => $car,
                'width' => '300px',
            ],
            'showroom_id' => [
                'title' => trans('Showroom'),
                'type'     => 'select',
                'choices'  => $showroom,
                'width' => '300px',
            ],
            'price_car' => [
                'title' => trans('Giá xe'),
                'type' => 'text',
                'validate' => 'required|max:120',
            ],
            'total_price' => [
                'title' => trans('Tổng tiền'),
                'type' => 'text',
                'validate' => 'required|max:120',
            ],
            'price_license_plate' => [
                'title' => trans('Phí trước bạ'),
                'type' => 'text',
                'validate' => 'required|max:120',
            ],
            'price_entry' => [
                'title' => trans('Phí biển số'),
                'type' => 'text',
                'validate' => 'required|max:120',
            ],
            'price_registry' => [
                'title' => trans('Phí đăng kiểm'),
                'type' => 'text',
                'validate' => 'required|max:120',
            ],
            'created_at' => [
                'title' => trans('Ngày tạo'),
                'type' => 'date',
            ],

        ];
    }

    public function getFilters(): array
    {
        return $this->getBulkChanges();
    }

    public function getDefaultButtons(): array
    {
        return [
            'export',
            'reload',
        ];
    }
}

<?php

namespace Addons\BookingCar\Http\Controllers;

use Addons\BookingCar\Http\Requests\BookingCarRequest;
use Addons\BookingCar\Models\BookingCar;
use Addons\Base\Facades\PageTitle;
use Addons\Base\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use Exception;
use Addons\BookingCar\Tables\BookingCarTable;
use Addons\Base\Events\CreatedContentEvent;
use Addons\Base\Events\DeletedContentEvent;
use Addons\Base\Events\UpdatedContentEvent;
use Addons\Base\Http\Responses\BaseHttpResponse;
use Addons\BookingCar\Forms\BookingCarForm;
use Addons\Base\Forms\FormBuilder;
use Addons\Car\Models\Car;
use Illuminate\Support\Facades\DB;

class BookingCarController extends BaseController
{
    public function index(BookingCarTable $table)
    {
        PageTitle::setTitle(trans('plugins/booking-car::booking-car.name'));

        return $table->renderTable();
    }

    public function create(FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('plugins/booking-car::booking-car.create'));

        return $formBuilder->create(BookingCarForm::class)->renderForm();
    }

    public function store(BookingCarRequest $request, BaseHttpResponse $response)
    {
        $bookingCar = BookingCar::query()->create($request->input());

        event(new CreatedContentEvent(BOOKING_CAR_MODULE_SCREEN_NAME, $request, $bookingCar));

        return $response
            ->setPreviousUrl(route('booking-car.index'))
            ->setNextUrl(route('booking-car.edit', $bookingCar->getKey()))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function edit(BookingCar $bookingCar, FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('core/base::forms.edit_item', ['name' => $bookingCar->name]));

        return $formBuilder->create(BookingCarForm::class, ['model' => $bookingCar])->renderForm();
    }

    public function update(BookingCar $bookingCar, BookingCarRequest $request, BaseHttpResponse $response)
    {
        $bookingCar->fill($request->input());

        $bookingCar->save();

        event(new UpdatedContentEvent(BOOKING_CAR_MODULE_SCREEN_NAME, $request, $bookingCar));

        return $response
            ->setPreviousUrl(route('booking-car.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(BookingCar $bookingCar, Request $request, BaseHttpResponse $response)
    {
        try {
            $bookingCar->delete();

            event(new DeletedContentEvent(BOOKING_CAR_MODULE_SCREEN_NAME, $request, $bookingCar));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    public function sendBookingCar(BookingCarRequest $request, BaseHttpResponse $response)
    {
        $newDate = date("Y/m/d", strtotime($request->date));
        try {
            DB::beginTransaction();
            $data = [
                'name' => $request->name,
                'phone' => $request->phone,
                'car_id' => $request->car_id,
                // 'date' =>  $newDate,
                'showroom_id' => $request->showroom_id,
                // 'address' => $request->address,
                'price_car' => $request->price_car,
                'price_license_plate' => $request->price_license_plate,
                'price_entry' => $request->price_entry,
                'price_registry' => $request->price_inspection,
                'total_price' => $request->total_price,

            ];
            BookingCar::create($data);
            DB::commit();
            return $response->setMessage(trans('Đặt xe thành công'));
        } catch (Exception $exception) {
            DB::rollback();
            return $response
                ->setError()
                ->setMessage(trans("Đặt xe thất bại vui lòng điền đầy đủ các trường"));
        }
    }
    public function changePriceCar(Request $request, BaseHttpResponse $response)
    {
        // dd($request->all());
        if (blank($request->matp_id)) {
            $id = $request->car_id;
            if (!blank($id)) {
                $car  = Car::where('id', $id)->first();
                $price = $car->price;
            } else {
                $price = 0;
            }
            return $response->setData([
                'price' =>  $price,
            ]);
        }
        $data = get_registration_fee_by_id($request->matp_id);


        $id = $request->car_id;
        if (!blank($id)) {
            $car  = Car::where('id', $id)->first();
            $price = $car->price;
        } else {
            $price = 0;
        }

        return response()->json(
            [
                'price' =>  $price,
                'phi_truoc_ba' => intval($data->phi_truoc_ba ?? 0),
                'phi_dang_ky_bien_so' => intval($data->phi_dang_ky_bien_so ?? 0),
                'phi_dang_kiem_xe' => intval($data->phi_dang_kiem_xe ?? 0),
                'type' => 'success',
            ]
        );
    }

    public function Carprice(Request $request, BaseHttpResponse $response)
    {
        $data = get_registration_fee_by_id($request->matp_id);
        return response()->json(
            [
                'phi_truoc_ba' => intval($data->phi_truoc_ba ?? 0),
                'phi_dang_ky_bien_so' => intval($data->phi_dang_ky_bien_so ?? 0),
                'phi_dang_kiem_xe' => intval($data->phi_dang_kiem_xe ?? 0),
                'type' => 'success',
            ]
        );
    }
}

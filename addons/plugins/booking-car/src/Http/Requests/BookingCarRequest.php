<?php

namespace Addons\BookingCar\Http\Requests;

use Addons\Support\Http\Requests\Request;

class BookingCarRequest extends Request
{

    public function rules(): array
    {
        $rules = [
            'name'              => 'required|',
            'phone'             => 'required|digits:10',
            'car_id'            => 'required|',
            'showroom_id'       => 'required',
            // 'address'           => 'required|',
            // 'date'              => 'required|',
        ];
        return $rules;
    }
    public function messages()
    {
        return [
            'name.*'                =>  'Họ và tên không được bỏ trống',
            'car_id.*'              =>  'Loại xe không được bỏ trống',
            // 'address.*'             =>  'Địa chỉ không được bỏ trống',
            'phone.required'               =>  'Số điện thoại không được bỏ trống',
            'phone.digits'               =>  'Số điện thoại phải là 10 số',
            // 'date.*'                =>  'Loại phản hồi không được bỏ trống',
            'showroom_id.*'         =>  '<PERSON>ại phản hồi không được bỏ trống',
        ];
    }
}

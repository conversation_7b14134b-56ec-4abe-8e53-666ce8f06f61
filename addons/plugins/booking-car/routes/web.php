<?php

use Addons\Base\Facades\BaseHelper;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Addons\BookingCar\Http\Controllers', 'middleware' => ['web', 'core']], function () {

    Route::group(['prefix' => BaseHelper::getAdminPrefix(), 'middleware' => 'auth'], function () {

        Route::group(['prefix' => 'booking-cars', 'as' => 'booking-car.'], function () {
            Route::resource('', 'BookingCarController')->parameters(['' => 'booking-car']);
        });
    });
});


Route::group(['namespace' => 'Addons\BookingCar\Http\Controllers', 'middleware' => ['web', 'core']], function () {
    Route::group(apply_filters(BASE_FILTER_GROUP_PUBLIC_ROUTE, []), function () {
        Route::post('booking-car/send', [
            'as' => 'booking-car/send',
            'uses' => 'BookingCarController@sendBookingCar',
        ]);
    });
});

Route::group(['namespace' => 'Addons\BookingCar\Http\Controllers', 'middleware' => ['web', 'core']], function () {
    Route::group(apply_filters(BASE_FILTER_GROUP_PUBLIC_ROUTE, []), function () {
        Route::get('change-price-car', 'BookingCarController@changePriceCar')->name('change-price-car');
        Route::get('public.ajax.carprice', 'BookingCarController@Carprice')->name('public.ajax.carprice');
    });
});

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration
{
    public function up(): void
    {
        Schema::create('booking_cars', function (Blueprint $table) {
            $table->id();
            $table->string('name', 255);
            $table->integer('car_id')->nullable();
            $table->integer('showroom_id')->nullable();
            $table->string('phone')->nullable();
            $table->string('address')->nullable();
            $table->date('date')->nullable();
            $table->string('price_car')->nullable();
            $table->string('price_license_plate')->nullable();
            $table->string('price_entry')->nullable();
            $table->string('price_registry')->nullable();
            $table->string('total_price')->nullable();
            $table->string('status', 60)->default('published');
            $table->timestamps();
        });

        Schema::create('booking_cars_translations', function (Blueprint $table) {
            $table->string('lang_code');
            $table->foreignId('booking_cars_id');
            $table->string('name', 255)->nullable();

            $table->primary(['lang_code', 'booking_cars_id'], 'booking_cars_translations_primary');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('booking_cars');
        Schema::dropIfExists('booking_cars_translations');
    }
};

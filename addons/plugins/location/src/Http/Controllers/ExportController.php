<?php

namespace Addons\Location\Http\Controllers;

use Assets;
use BaseHelper;
use Addons\Base\Http\Controllers\BaseController;
use Addons\Location\Exports\CsvLocationExport;
use Addons\Location\Repositories\Interfaces\CityInterface;
use Addons\Location\Repositories\Interfaces\CountryInterface;
use Addons\Location\Repositories\Interfaces\StateInterface;
use Maatwebsite\Excel\Excel;

class ExportController extends BaseController
{
    public function index(
        CountryInterface $countryRepository,
        StateInterface $stateRepository,
        CityInterface $cityRepository
    ) {
        page_title()->setTitle(trans('plugins/location::location.export_location'));

        Assets::addScriptsDirectly(['vendor/core/plugins/location/js/export.js']);

        $countryCount = $countryRepository->count();
        $stateCount = $stateRepository->count();
        $cityCount = $cityRepository->count();

        return view('plugins/location::export.index', compact('countryCount', 'stateCount', 'cityCount'));
    }

    public function export()
    {
        BaseHelper::maximumExecutionTimeAndMemoryLimit();

        return (new CsvLocationExport())->download('exported_location.csv', Excel::CSV, ['Content-Type' => 'text/csv']);
    }
}

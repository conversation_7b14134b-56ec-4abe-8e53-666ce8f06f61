<?php

namespace Addons\Location\Http\Controllers;

use BaseHelper;
use Addons\Base\Events\BeforeEditContentEvent;
use Addons\Location\Http\Requests\CountryRequest;
use Addons\Location\Http\Resources\CountryResource;
use Addons\Location\Models\Country;
use Addons\Location\Repositories\Interfaces\CountryInterface;
use Addons\Base\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use Exception;
use Addons\Location\Tables\CountryTable;
use Addons\Base\Events\CreatedContentEvent;
use Addons\Base\Events\DeletedContentEvent;
use Addons\Base\Events\UpdatedContentEvent;
use Addons\Base\Http\Responses\BaseHttpResponse;
use Addons\Location\Forms\CountryForm;
use Addons\Base\Forms\FormBuilder;

class CountryController extends BaseController
{
    public function __construct(protected CountryInterface $countryRepository)
    {
    }

    public function index(CountryTable $table)
    {
        page_title()->setTitle(trans('plugins/location::country.name'));

        return $table->renderTable();
    }

    public function create(FormBuilder $formBuilder)
    {
        page_title()->setTitle(trans('plugins/location::country.create'));

        return $formBuilder->create(CountryForm::class)->renderForm();
    }

    public function store(CountryRequest $request, BaseHttpResponse $response)
    {
        $country = $this->countryRepository->createOrUpdate($request->input());

        event(new CreatedContentEvent(COUNTRY_MODULE_SCREEN_NAME, $request, $country));

        return $response
            ->setPreviousUrl(route('country.index'))
            ->setNextUrl(route('country.edit', $country->id))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function edit(int|string $id, FormBuilder $formBuilder, Request $request)
    {
        $country = $this->countryRepository->findOrFail($id);

        event(new BeforeEditContentEvent($request, $country));

        page_title()->setTitle(trans('plugins/location::country.edit') . ' "' . $country->name . '"');

        return $formBuilder->create(CountryForm::class, ['model' => $country])->renderForm();
    }

    public function update(int|string $id, CountryRequest $request, BaseHttpResponse $response)
    {
        $country = $this->countryRepository->findOrFail($id);

        $country->fill($request->input());

        $this->countryRepository->createOrUpdate($country);

        event(new UpdatedContentEvent(COUNTRY_MODULE_SCREEN_NAME, $request, $country));

        return $response
            ->setPreviousUrl(route('country.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(int|string $id, Request $request, BaseHttpResponse $response)
    {
        try {
            $country = $this->countryRepository->findOrFail($id);

            $this->countryRepository->delete($country);

            event(new DeletedContentEvent(COUNTRY_MODULE_SCREEN_NAME, $request, $country));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    public function deletes(Request $request, BaseHttpResponse $response)
    {
        $ids = $request->input('ids');
        if (empty($ids)) {
            return $response
                ->setError()
                ->setMessage(trans('core/base::notices.no_select'));
        }

        foreach ($ids as $id) {
            $country = $this->countryRepository->findOrFail($id);
            $this->countryRepository->delete($country);
            event(new DeletedContentEvent(COUNTRY_MODULE_SCREEN_NAME, $request, $country));
        }

        return $response->setMessage(trans('core/base::notices.delete_success_message'));
    }

    public function getList(Request $request, BaseHttpResponse $response)
    {
        $keyword = BaseHelper::stringify($request->input('q'));

        if (! $keyword) {
            return $response->setData([]);
        }

        $data = $this->countryRepository->advancedGet([
            'condition' => [
                ['countries.name', 'LIKE', '%' . $keyword . '%'],
            ],
            'select' => ['countries.id', 'countries.name'],
            'take' => 10,
            'order_by' => ['order' => 'ASC', 'name' => 'ASC'],
        ]);

        $data->prepend(new Country(['id' => 0, 'name' => trans('plugins/location::city.select_country')]));

        return $response->setData(CountryResource::collection($data));
    }
}

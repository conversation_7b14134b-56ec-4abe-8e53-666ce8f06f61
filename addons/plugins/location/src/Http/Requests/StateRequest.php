<?php

namespace Addons\Location\Http\Requests;

use Addons\Base\Enums\BaseStatusEnum;
use Addons\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class StateRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => 'required',
            'country_id' => 'required',
            'order' => 'required|integer|min:0|max:127',
            'abbreviation' => 'max:3',
            'status' => Rule::in(BaseStatusEnum::values()),
        ];
    }
}

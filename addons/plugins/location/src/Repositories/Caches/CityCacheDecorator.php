<?php

namespace Addons\Location\Repositories\Caches;

use Addons\Support\Repositories\Caches\CacheAbstractDecorator;
use Addons\Location\Repositories\Interfaces\CityInterface;
use Illuminate\Database\Eloquent\Collection;

class CityCacheDecorator extends CacheAbstractDecorator implements CityInterface
{
    public function filters(?string $keyword, ?int $limit = 10, array $with = [], array $select = ['cities.*']): Collection
    {
        return $this->getDataIfExistCache(__FUNCTION__, func_get_args());
    }
}

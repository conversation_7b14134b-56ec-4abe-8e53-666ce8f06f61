<?php

namespace Addons\Testimonial\Models;

use Addons\Base\Enums\BaseStatusEnum;
use Addons\Base\Models\BaseModel;
use Addons\Base\Traits\EnumCastable;

class Testimonial extends BaseModel
{
    use EnumCastable;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'testimonials';

    /**
     * @var array
     */
    protected $fillable = [
        'name',
        'company',
        'content',
        'image',
        'status',
    ];

    /**
     * @var array
     */
    protected $casts = [
        'status' => BaseStatusEnum::class,
    ];
}

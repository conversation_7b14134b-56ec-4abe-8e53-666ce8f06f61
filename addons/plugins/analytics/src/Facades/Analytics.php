<?php

namespace Addons\Analytics\Facades;

use Addons\Analytics\Abstracts\AnalyticsAbstract;
use Illuminate\Support\Facades\Facade;

/**
 * @method static \Illuminate\Support\Collection|\Google\Service\Analytics\GaData|array|null performQuery(\Addons\Analytics\Period $period, string $metrics, array $others = [])
 * @method static \Illuminate\Support\Collection fetchMostVisitedPages(\Addons\Analytics\Period $period, int $maxResults = 20)
 * @method static \Illuminate\Support\Collection fetchTopReferrers(\Addons\Analytics\Period $period, int $maxResults = 20)
 * @method static \Illuminate\Support\Collection fetchUserTypes(\Addons\Analytics\Period $period)
 * @method static \Illuminate\Support\Collection fetchTopBrowsers(\Addons\Analytics\Period $period, int $maxResults = 10)
 * @method static \Google_Service_Analytics getAnalyticsService()
 * @method static string getPropertyId()
 * @method static static setPropertyId(string $propertyId)
 * @method static void macro(string $name, object|callable $macro)
 * @method static void mixin(object $mixin, bool $replace = true)
 * @method static bool hasMacro(string $name)
 * @method static void flushMacros()
 *
 * @see \Addons\Analytics\Analytics
 */
class Analytics extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return AnalyticsAbstract::class;
    }
}

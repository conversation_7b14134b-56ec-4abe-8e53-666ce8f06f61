<?php

return [
    'menu' => 'IP Blocker',
    'ip_blocker_title' => 'Blacklisted IPs',
    'ip_addresses' => 'IP Addresses',
    'save_settings' => 'Save settings',
    'update_settings_success' => 'Update settings success',
    'please_enter_ip_address' => 'Please enter IP addresses',
    'last_visited' => 'Last visited',
    'count_requests' => 'Count Requests',
    'ip_address' => 'IP Address',
    'history' => 'History',
    'ip_blocker_description' => 'List of blocked IP addresses',
    'history_description' => 'Access history of blocked IP addresses',
    'message' => 'Your IP has been blocked.',
    'please_enter_wildcard_ip_address' => 'Please enter Wildcard IP Address (Example: 192.168.*)',
    'delete_all' => 'Delete all records',
    'delete_success' => 'Delete success',
    'api_secret_key_label' => 'API secret key',
    'api_secret_key_placeholder' => 'Please enter API secret key',
    'activation_success' => 'Activation success',
    'activation_failed' => 'Activation failed',
    'available_countries' => 'Allowed IP from countries',
    'all_countries' => 'Allowed all countries',
    'activate' => 'Activate',
    'api_secret_key_helper' => 'Go to :link and get the API key to detach the IP',
    'update_settings_ip_address' => 'IP address',
    'update_settings_wildcard_ip' => 'Wildcard IP address',
];

# Overview
This is a plugin for Laravel CMS so you have to purchase Laravel CMS first to use this plugin. 
Purchase it here: [https://laravel-cms.demo.fsofts.com](https://fsofts.com)

Impersonate is a plugin that allows you to authenticate as your users.

# Installation
- Download and rename folder `impersonate-master` to `impersonate`.
- Copy folder `impersonate` into `/addons/plugins`.
- Go to Admin -> Plugins then activate plugin Impersonate.

# Usage
- Go to Admin -> Platform Administration -> Users and use "Login as this user"

# Screenshots

![Screenshot](https://raw.githubusercontent.com/fsofts/impersonate/master/public/images/screenshot-1.png)

![Screenshot](https://raw.githubusercontent.com/fsofts/impersonate/master/public/images/screenshot-2.png)

# Credits
- https://github.com/404labfr/laravel-impersonate
- [<PERSON><PERSON>](https://github.com/laravel/framework) community

# Contact us
- Website: [https://laravel-cms.demo.fsofts.com](https://laravel-cms.demo.fsofts.com)
- Email: [<EMAIL>](mailto:<EMAIL>)

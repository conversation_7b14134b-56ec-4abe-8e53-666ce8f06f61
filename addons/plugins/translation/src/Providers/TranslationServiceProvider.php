<?php

namespace Addons\Translation\Providers;

use Addons\Base\Facades\DashboardMenu;
use Addons\Base\Supports\ServiceProvider;
use Addons\Base\Traits\LoadAndPublishDataTrait;
use Addons\Translation\Console\CleanCommand;
use Addons\Translation\Console\DownloadLocaleCommand;
use Addons\Translation\Console\ExportCommand;
use Addons\Translation\Console\ImportCommand;
use Addons\Translation\Console\RemoveLocaleCommand;
use Addons\Translation\Console\RemoveUnusedTranslationsCommand;
use Addons\Translation\Console\ResetCommand;
use Addons\Translation\Console\UpdateThemeTranslationCommand;
use Addons\Translation\Manager;
use Addons\Translation\Models\Translation;
use Addons\Translation\Repositories\Eloquent\TranslationRepository;
use Addons\Translation\Repositories\Interfaces\TranslationInterface;
use Illuminate\Routing\Events\RouteMatched;

class TranslationServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->bind(TranslationInterface::class, function () {
            return new TranslationRepository(new Translation());
        });

        $this->app->bind('translation-manager', Manager::class);

        $this->commands([
            ImportCommand::class,
        ]);

        if ($this->app->runningInConsole()) {
            $this->commands([
                ResetCommand::class,
                ExportCommand::class,
                CleanCommand::class,
                UpdateThemeTranslationCommand::class,
                RemoveUnusedTranslationsCommand::class,
                DownloadLocaleCommand::class,
                RemoveLocaleCommand::class,
            ]);
        }
    }

    public function boot(): void
    {
        $this->setNamespace('plugins/translation')
            ->loadAndPublishConfigurations(['general', 'permissions'])
            ->loadMigrations()
            ->loadRoutes()
            ->loadAndPublishViews()
            ->loadAndPublishTranslations()
            ->publishAssets();

        $this->app['events']->listen(RouteMatched::class, function () {
            DashboardMenu::make()
                ->registerItem([
                    'id' => 'cms-plugin-translation',
                    'priority' => 997,
                    'parent_id' => null,
                    'name' => 'plugins/translation::translation.translations',
                    'icon' => 'fas fa-language',
                    'url' => route('translations.index'),
                    'permissions' => ['translations.index'],
                ])
                ->registerItem([
                    'id' => 'cms-plugin-translation-locale',
                    'priority' => 1,
                    'parent_id' => 'cms-plugin-translation',
                    'name' => 'plugins/translation::translation.locales',
                    'icon' => null,
                    'url' => route('translations.locales'),
                    'permissions' => ['translations.index'],
                ])
                ->registerItem([
                    'id' => 'cms-plugin-translation-theme-translations',
                    'priority' => 2,
                    'parent_id' => 'cms-plugin-translation',
                    'name' => 'plugins/translation::translation.theme-translations',
                    'icon' => null,
                    'url' => route('translations.theme-translations'),
                    'permissions' => ['translations.index'],
                ])
                ->registerItem([
                    'id' => 'cms-plugin-translation-admin-translations',
                    'priority' => 3,
                    'parent_id' => 'cms-plugin-translation',
                    'name' => 'plugins/translation::translation.admin-translations',
                    'icon' => null,
                    'url' => route('translations.index'),
                    'permissions' => ['translations.index'],
                ]);
        });
    }
}

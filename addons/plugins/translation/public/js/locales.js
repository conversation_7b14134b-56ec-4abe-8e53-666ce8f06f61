$(document).ready((function(){var t=this,a=$(".table-language");a.on("click",".delete-locale-button",(function(t){t.preventDefault(),$(".delete-crud-entry").data("url",$(t.currentTarget).data("url")),$(".modal-confirm-delete").modal("show")})),$(document).on("click",".delete-crud-entry",(function(e){e.preventDefault(),$(".modal-confirm-delete").modal("hide");var o=$(e.currentTarget).data("url");$(t).prop("disabled",!0).addClass("button-loading"),$httpClient.make().delete(o).then((function(t){var e=t.data;e.data&&(a.find("i[data-locale="+e.data+"]").unwrap(),$(".tooltip").remove()),a.find('a[data-url="'.concat(o,'"]')).closest("tr").remove(),Apps.showSuccess(e.message)})).finally((function(){$(t).prop("disabled",!1).removeClass("button-loading")}))})),$(document).on("click",".add-locale-form button[type=submit]",(function(t){var e=this;t.preventDefault(),t.stopPropagation(),$(this).prop("disabled",!0).addClass("button-loading");var o=new FormData($(this).closest("form")[0]);$httpClient.make().postForm($(this).closest("form").prop("action"),o).then((function(t){var e=t.data;Apps.showSuccess(e.message),a.load(window.location.href+" .table-language > *")})).finally((function(){$(e).prop("disabled",!1).removeClass("button-loading")}))}));var e=$("#available-remote-locales");if(e.length){var o=function(){$httpClient.make().get(e.data("url")).then((function(t){var o=t.data;a.load(window.location.href+" .table-language > *"),e.html(o.data)}))};o(),$(document).on("click",".btn-import-remote-locale",(function(t){t.preventDefault(),$(".button-confirm-import-locale").data("url",$(this).data("url")),$(".modal-confirm-import-locale").modal("show")})),$(".button-confirm-import-locale").on("click",(function(t){t.preventDefault();var a=$(t.currentTarget);a.addClass("button-loading");var e=a.data("url");$httpClient.make().post(e).then((function(t){var a=t.data;Apps.showSuccess(a.message),o()})).finally((function(){a.closest(".modal").modal("hide"),a.removeClass("button-loading")}))}))}}));
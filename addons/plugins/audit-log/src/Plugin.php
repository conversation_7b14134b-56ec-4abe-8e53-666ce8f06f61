<?php

namespace Addons\AuditLog;

use <PERSON>dons\Dashboard\Models\DashboardWidget;
use Addons\PluginManagement\Abstracts\PluginOperationAbstract;
use Addons\Widget\Models\Widget;
use Illuminate\Support\Facades\Schema;

class Plugin extends PluginOperationAbstract
{
    public static function remove(): void
    {
        Schema::dropIfExists('audit_histories');

        Widget::query()
            ->where('widget_id', 'widget_audit_logs')
            ->each(fn (DashboardWidget $dashboardWidget) => $dashboardWidget->delete());
    }
}

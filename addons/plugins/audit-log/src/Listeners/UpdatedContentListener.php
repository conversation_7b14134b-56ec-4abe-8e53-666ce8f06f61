<?php

namespace Addons\AuditLog\Listeners;

use Addons\AuditLog\Events\AuditHandlerEvent;
use Addons\AuditLog\Facades\AuditLog;
use Addons\Base\Events\UpdatedContentEvent;
use Exception;

class UpdatedContentListener
{
    public function handle(UpdatedContentEvent $event): void
    {
        try {
            if ($event->data->getKey()) {
                event(new AuditHandlerEvent(
                    $event->screen,
                    'updated',
                    $event->data->getKey(),
                    AuditLog::getReferenceName($event->screen, $event->data),
                    'primary'
                ));
            }
        } catch (Exception $exception) {
            info($exception->getMessage());
        }
    }
}

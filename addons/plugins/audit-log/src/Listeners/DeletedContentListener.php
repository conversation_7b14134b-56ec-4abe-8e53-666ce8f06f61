<?php

namespace Addons\AuditLog\Listeners;

use Addons\AuditLog\Events\AuditHandlerEvent;
use Addons\AuditLog\Facades\AuditLog;
use Addons\Base\Events\DeletedContentEvent;
use Exception;

class DeletedContentListener
{
    public function handle(DeletedContentEvent $event): void
    {
        try {
            if ($event->data->getKey()) {
                event(new AuditHandlerEvent(
                    $event->screen,
                    'deleted',
                    $event->data->getKey(),
                    AuditLog::getReferenceName($event->screen, $event->data),
                    'danger'
                ));
            }
        } catch (Exception $exception) {
            info($exception->getMessage());
        }
    }
}

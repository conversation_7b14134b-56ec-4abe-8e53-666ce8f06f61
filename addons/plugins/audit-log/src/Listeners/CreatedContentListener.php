<?php

namespace Addons\AuditLog\Listeners;

use Addons\AuditLog\Events\AuditHandlerEvent;
use Addons\AuditLog\Facades\AuditLog;
use Addons\Base\Events\CreatedContentEvent;
use Exception;

class CreatedContentListener
{
    public function handle(CreatedContentEvent $event): void
    {
        try {
            if ($event->data->getKey()) {
                event(new AuditHandlerEvent(
                    $event->screen,
                    'created',
                    $event->data->getKey(),
                    AuditLog::getReferenceName($event->screen, $event->data),
                    'info'
                ));
            }
        } catch (Exception $exception) {
            info($exception->getMessage());
        }
    }
}

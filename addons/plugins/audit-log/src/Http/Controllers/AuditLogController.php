<?php

namespace Addons\AuditLog\Http\Controllers;

use Addons\AuditLog\Models\AuditHistory;
use Addons\AuditLog\Tables\AuditLogTable;
use Addons\Base\Events\DeletedContentEvent;
use Addons\Base\Facades\PageTitle;
use Addons\Base\Http\Controllers\BaseController;
use Addons\Base\Http\Responses\BaseHttpResponse;
use Exception;
use Illuminate\Http\Request;

class AuditLogController extends BaseController
{
    public function getWidgetActivities(BaseHttpResponse $response, Request $request)
    {
        $limit = $request->integer('paginate', 10);
        $limit = $limit > 0 ? $limit : 10;

        $histories = AuditHistory::query()
            ->with(['user'])
            ->orderByDesc('created_at')
            ->paginate($limit);

        return $response
            ->setData(view('plugins/audit-log::widgets.activities', compact('histories', 'limit'))->render());
    }

    public function index(AuditLogTable $dataTable)
    {
        PageTitle::setTitle(trans('plugins/audit-log::history.name'));

        return $dataTable->renderTable();
    }

    public function destroy(AuditHistory $auditLog, Request $request, BaseHttpResponse $response)
    {
        try {
            $auditLog->delete();

            event(new DeletedContentEvent(AUDIT_LOG_MODULE_SCREEN_NAME, $request, $auditLog));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $ex) {
            return $response
                ->setError()
                ->setMessage($ex->getMessage());
        }
    }

    public function deleteAll(BaseHttpResponse $response)
    {
        AuditHistory::query()->truncate();

        return $response->setMessage(trans('core/base::notices.delete_success_message'));
    }
}

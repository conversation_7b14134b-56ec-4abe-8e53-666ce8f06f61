<?php

namespace Addons\AuditLog\Providers;

use Addons\AuditLog\Events\AuditHandlerEvent;
use Addons\AuditLog\Listeners\AuditHandlerListener;
use Addons\AuditLog\Listeners\CreatedContentListener;
use Addons\AuditLog\Listeners\DeletedContentListener;
use Addons\AuditLog\Listeners\LoginListener;
use Addons\AuditLog\Listeners\UpdatedContentListener;
use Addons\Base\Events\CreatedContentEvent;
use Addons\Base\Events\DeletedContentEvent;
use Addons\Base\Events\UpdatedContentEvent;
use Illuminate\Auth\Events\Login;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        AuditHandlerEvent::class => [
            AuditHandlerListener::class,
        ],
        Login::class => [
            LoginListener::class,
        ],
        UpdatedContentEvent::class => [
            UpdatedContentListener::class,
        ],
        CreatedContentEvent::class => [
            CreatedContentListener::class,
        ],
        DeletedContentEvent::class => [
            DeletedContentListener::class,
        ],
    ];
}

<?php

namespace Addons\AuditLog\Providers;

use Addons\AuditLog\Commands\ActivityLogClearCommand;
use Addons\AuditLog\Commands\CleanOldLogsCommand;
use Addons\Base\Supports\ServiceProvider;

class CommandServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                ActivityLogClearCommand::class,
                CleanOldLogsCommand::class,
            ]);
        }
    }
}

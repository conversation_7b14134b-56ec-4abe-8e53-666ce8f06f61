<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration
{
    public function up(): void
    {
        Schema::create('test_drives', function (Blueprint $table) {
            $table->id();
            $table->string('name', 255);
            $table->integer('car_id')->nullable();
            $table->string('phone')->nullable();
            $table->string('email')->nullable();
            $table->string('type_response')->nullable();
            $table->integer('showroom_id')->nullable();
            $table->string('status', 60)->default('published');
            $table->timestamps();
        });

        Schema::create('test_drives_translations', function (Blueprint $table) {
            $table->string('lang_code');
            $table->foreignId('test_drives_id');
            $table->string('name', 255)->nullable();

            $table->primary(['lang_code', 'test_drives_id'], 'test_drives_translations_primary');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('test_drives');
        Schema::dropIfExists('test_drives_translations');
    }
};

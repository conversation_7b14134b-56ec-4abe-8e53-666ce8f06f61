<?php

use Addons\Base\Facades\BaseHelper;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Addons\TestDrive\Http\Controllers', 'middleware' => ['web', 'core']], function () {

    Route::group(['prefix' => BaseHelper::getAdminPrefix(), 'middleware' => 'auth'], function () {

        Route::group(['prefix' => 'test-drives', 'as' => 'test-drive.'], function () {
            Route::resource('', 'TestDriveController')->parameters(['' => 'test-drive']);
        });
    });
});



Route::group(['namespace' => 'Addons\TestDrive\Http\Controllers', 'middleware' => ['web', 'core']], function () {
    Route::group(apply_filters(BASE_FILTER_GROUP_PUBLIC_ROUTE, []), function () {
        Route::post('test-drives/send', [
            'as' => 'test-drives/send',
            'uses' => 'PublicController@sendTestDrive',
        ]);

        Route::post('register/send', [
            'as' => 'register/send',
            'uses' => 'PublicController@sendRegister',
        ]);
    });
});


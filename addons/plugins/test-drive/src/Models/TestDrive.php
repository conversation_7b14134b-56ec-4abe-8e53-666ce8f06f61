<?php

namespace Addons\TestDrive\Models;

use Addons\Base\Casts\SafeContent;
use Addons\Base\Enums\BaseStatusEnum;
use Addons\Base\Models\BaseModel;
use Addons\Car\Models\Car;
use Addons\Showroom\Models\Province;
use Addons\Showroom\Models\Showroom;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @method static \Addons\Base\Models\BaseQueryBuilder<static> query()
 */
class TestDrive extends BaseModel
{
    protected $table = 'test_drives';

    protected $fillable = [
        'name',
//        'car_id',
        'phone',
//        'showroom_id',
        'email',
//        'type_response',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
    ];

    public function car(): BelongsTo
    {
        return $this->belongsTo(Car::class, 'car_id');
    }
    public function showRoom(): BelongsTo
    {
        return $this->belongsTo(Showroom::class, 'showroom_id');
    }
}

<?php

namespace Addons\TestDrive\Repositories\Eloquent;

use Addons\Support\Repositories\Eloquent\RepositoriesAbstract;
use Addons\TestDrive\Repositories\Interfaces\TestDriveInterface;
use Illuminate\Database\Eloquent\Collection;

class TestDriveRepository extends RepositoriesAbstract implements TestDriveInterface
{
    // public function getDataSiteMap(): Collection
    // {
    //     $data = $this->model
    //         ->with('slugable')
    //         ->wherePublished()
    //         ->select(['id', 'name', 'updated_at'])
    //         ->orderByDesc('created_at');

    //     return $this->applyBeforeExecuteQuery($data)->get();
    // }


}

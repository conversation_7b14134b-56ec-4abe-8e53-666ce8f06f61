<?php

namespace Addons\TestDrive\Repositories\Interfaces;

use Addons\Base\Enums\BaseStatusEnum;
use Addons\Base\Models\BaseModel;
use Addons\Blog\Models\Category;
use Addons\Support\Repositories\Interfaces\RepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface TestDriveInterface extends RepositoryInterface
{
    // public function getUnread(array $select = ['*']): Collection;
}

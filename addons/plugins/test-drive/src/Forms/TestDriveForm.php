<?php

namespace Addons\TestDrive\Forms;

use Addons\Base\Forms\FormAbstract;
use Addons\Base\Enums\BaseStatusEnum;
use Addons\TestDrive\Http\Requests\TestDriveRequest;
use Addons\TestDrive\Models\TestDrive;

class TestDriveForm extends FormAbstract
{
    public function buildForm(): void
    {
        $this
            ->setupModel(new TestDrive)
            ->setValidatorClass(TestDriveRequest::class)
            ->withCustomFields()
            ->addMetaBoxes([
                'information' => [
                    'title' => trans('Chi tiết người đăng ký thử xe'),
                    'content' => view('plugins/test-drive::test_drive', ['data' => $this->getModel()])->render(),
                    'attributes' => [
                        'style' => 'margin-top: 0',
                    ],
                ],
            ])
            ->add('status', 'customSelect', [
                'label' => trans('core/base::tables.status'),
                'label_attr' => ['class' => 'control-label required'],
                'attr' => [
                    'class' => 'form-control select-full',
                ],
                'choices' => BaseStatusEnum::labels(),
            ])
            ->setBreakFieldPoint('status');
    }
}

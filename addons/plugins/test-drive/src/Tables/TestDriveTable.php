<?php

namespace Addons\TestDrive\Tables;

use Addons\Base\Facades\BaseHelper;
use Addons\Base\Facades\Html;
use Addons\Base\Enums\BaseStatusEnum;
use Addons\Car\Models\Car;
use Addons\Showroom\Models\Showroom;
use Addons\TestDrive\Models\TestDrive;
use Addons\Table\Abstracts\TableAbstract;
use Addons\Table\Actions\DeleteAction;
use Addons\Table\Actions\EditAction;
use Addons\Table\BulkActions\DeleteBulkAction;
use Addons\Table\Columns\CreatedAtColumn;
use Addons\Table\Columns\IdColumn;
use Addons\Table\Columns\NameColumn;
use Addons\Table\Columns\StatusColumn;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;

class TestDriveTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(TestDrive::class)
            ->addActions([
                EditAction::make()
                    ->route('test-drive.edit'),
                // DeleteAction::make()
                //     ->route('test-drive.destroy'),
            ]);
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('name', function (TestDrive $item) {
                if (!$this->hasPermission('test-drive.edit')) {
                    return BaseHelper::clean($item->name);
                }
                return Html::link(route('test-drive.edit', $item->getKey()), BaseHelper::clean($item->name));
            })->editColumn('car_id', function ($item) {
                return $item->car ?  BaseHelper::clean($item->car->name) : 'N/a';
            })
            ->editColumn('showroom_id', function ($item) {
                return $item->showRoom ?  BaseHelper::clean($item->showRoom->name) : 'N/a';
            })
            ->editColumn('phone', function ($item) {
                return $item->phone ?  BaseHelper::clean($item->phone) : 'N/a';
            })
            ->editColumn('type_response', function ($item) {
                if ($item->type_response == 1) {
                    return Html::tag('div', 'PHẢN HỒI BẰNG EMAIL', [
                        'class' => ''
                    ]);
                } elseif ($item->type_response == 2) {
                    return Html::tag('div', 'GỬI BẰNG SMS', [
                        'class' => ''
                    ]);
                } else {
                    return     Html::tag('div', 'GỬI BẰNG SỐ ĐIỆN THOẠI', [
                        'class' => ''
                    ]);
                }
            })
            ->editColumn('status', function ($item) {
                return $item->status->toHtml();
            })
            ->editColumn('created_at', function ($item) {
                return BaseHelper::formatDate($item->created_at);
            });

        return $this->toJson($data);
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        $query = $this
            ->getModel()
            ->query()
            ->select([
                'id',
                'name',
                'car_id',
                'phone',
                'showroom_id',
                'email',
                'type_response',
                'name',
                'created_at',
                'status',
            ]);

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        return [
            'id' => [
                'title' => trans('core/base::tables.id'),
                'width' => '20px',
            ],
            'name' => [
                'title' => trans('TÊN'),
                'class' => 'text-start',
            ],
            'phone' => [
                'title' => trans('SỐ ĐIỆN THOẠI'),
                'width' => '100px',
            ],
            'car_id' => [
                'title' => trans('LOẠI XE'),
                'width' => '100px',
            ],
            'showroom_id' => [
                'title' => trans('SHOW ROOM'),
                'width' => '200px',
            ],
            'type_response' => [
                'title' => trans('LOẠI PHẢN HỒI'),
                'width' => '200px',
            ],
            'status' => [
                'title' => trans('TRẠNG THÁI'),
                'width' => '100px',
            ],
            'created_at' => [
                'title' => trans('NGÀY TẠO'),
                'width' => '100px',
            ],
        ];
    }

    public function bulkActions(): array
    {
        return [
            DeleteBulkAction::make()->permission('test-drive.destroy'),
        ];
    }

    public function getBulkChanges(): array
    {

        $showroom =  Showroom::pluck('name', 'id')->toArray();
        // dd($showroom);
        $car = Car::pluck('name', 'id')->toArray();
        return [
            'name' => [
                'title' => trans('Tên'),
                'type' => 'text',
                'validate' => 'required|max:120',
            ],
            'phone' => [
                'title' => trans('Số điện thoại'),
                'type' => 'text',
                'validate' => 'required|max:120',
            ],
            'car_id' => [
                'title' => trans('Loại xe'),
                'type'     => 'select',
                'choices'  => $car,
                'width' => '300px',
            ],
            'showroom_id' => [
                'title' => trans('Showroom'),
                'type'     => 'select',
                'choices'  => $showroom,
                'width' => '300px',
            ],
            'created_at' => [
                'title' => trans('Ngày tạo'),
                'type' => 'date',
            ],
            'status' => [
                'title' => trans('Trạng thái'),
                'type' => 'select',
                'choices' => BaseStatusEnum::labels(),
                'validate' => 'required|in:' . implode(',', BaseStatusEnum::values()),
            ],
        ];
    }

    public function getFilters(): array
    {
        return $this->getBulkChanges();
    }
    public function getDefaultButtons(): array
    {
        return [
            'export',
            'reload',
        ];
    }
}

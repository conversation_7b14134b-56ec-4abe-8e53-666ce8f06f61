<?php

namespace Addons\TestDrive\Http\Requests;

use Addons\Support\Http\Requests\Request;

class RegisterRequest extends Request
{
    public function rules(): array
    {
        $rules = [
            'name'              => 'required|string|max:255',
            'phone'             => 'required|string|max:20',
            'email'             => 'required|email|max:255',
        ];

        return $rules;
    }

    public function messages()
    {
        return [
            'name.required'     => 'Họ và tên không được bỏ trống',
            'name.string'       => 'Họ và tên phải là chuỗi ký tự',
            'name.max'          => 'Họ và tên không được quá 255 ký tự',
            'phone.required'    => 'Số điện thoại không được bỏ trống',
            'phone.string'      => 'Số điện thoại phải là chuỗi ký tự',
            'phone.max'         => 'Số điện thoại không được quá 20 ký tự',
            'email.required'    => 'Email không được bỏ trống',
            'email.email'       => 'Email không đúng định dạng',
            'email.max'         => 'Email không được quá 255 ký tự',
        ];
    }
}

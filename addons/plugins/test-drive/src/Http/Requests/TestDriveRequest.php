<?php

namespace Addons\TestDrive\Http\Requests;

use Addons\Base\Facades\BaseHelper;
use Addons\Captcha\Facades\Captcha;
use Addons\Support\Http\Requests\Request;

class TestDriveRequest extends Request
{
    public function rules(): array
    {
        // dd($this);
        $rules = [
            'name'              => 'required|',
            'phone'             => 'required|digits:10',
//            'car_id'            => 'required|',
//            // 'address'            => 'required|',
//            'showroom_id'       => 'required',
//            'type_response'     => 'required|',

             // car_id: 1
            // showroom_id: 44
            // name: <PERSON><PERSON><PERSON>ê<PERSON>
            // phone: 0999999999
            // type_response: 1
        ];

        return $rules;
    }
    public function messages()
    {
        return [
            'name.*'                =>  'Họ và tên không được bỏ trống',
            'car_id.*'              =>  'Loại xe không được bỏ trống',
            // 'address.*'             =>  'Địa chỉ không được bỏ trống',
            'phone.*'               =>  '<PERSON><PERSON> điện thoại không được bỏ trống',
            'showroom_id.*'               =>  'ShowRoom không được bỏ trống',
            'type_response.*'       =>  'Loại phản hồi không được bỏ trống',
        ];
    }
}

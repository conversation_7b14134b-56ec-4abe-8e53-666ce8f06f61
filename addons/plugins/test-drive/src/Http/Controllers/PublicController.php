<?php

namespace Addons\TestDrive\Http\Controllers;


use Addons\Base\Http\Controllers\BaseController;
use Addons\Base\Http\Responses\BaseHttpResponse;
use Addons\Support\Http\Requests\Request;
use Addons\TestDrive\Http\Requests\TestDriveRequest;
use Addons\TestDrive\Models\TestDrive;
use Addons\TestDrive\Repositories\Interfaces\TestDriveInterface;
use Exception;
use Illuminate\Support\Facades\DB;

class PublicController extends BaseController
{

    public function sendTestDrive(TestDriveRequest $request, BaseHttpResponse $response)
    {
        // Debug: var_dump all request data
        var_dump('=== sendTestDrive called ===');
        var_dump($request->all());
        var_dump('=== end debug ===');

        try {
            DB::beginTransaction();
            var_dump($request->all());die;
            $data = [
                'name' => $request->name,
                'phone' => $request->phone,
                'car_id' => $request->car_id ?? 1,
                'showroom_id' => $request->showroom_id ?? 1,
                'type_response' => $request->type_response ?? 1,
                'email' => $request->email,
            ];

            var_dump('Data to create:', $data);

            TestDrive::create($data);
            DB::commit();
            return $response->setMessage(trans('Đăng ký lái thử thành công'));
        } catch (Exception $exception) {
            DB::rollback();
            var_dump('Exception:', $exception->getMessage());
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }
}

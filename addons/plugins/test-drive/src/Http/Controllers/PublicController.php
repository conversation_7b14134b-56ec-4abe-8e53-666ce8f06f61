<?php

namespace Addons\TestDrive\Http\Controllers;


use Addons\Base\Http\Controllers\BaseController;
use Addons\Base\Http\Responses\BaseHttpResponse;
use Addons\Support\Http\Requests\Request;
use Addons\TestDrive\Http\Requests\TestDriveRequest;
use Addons\TestDrive\Models\TestDrive;
use Addons\TestDrive\Repositories\Interfaces\TestDriveInterface;
use Exception;
use Illuminate\Support\Facades\DB;

class PublicController extends BaseController
{

    public function sendTestDrive(TestDriveRequest $request, BaseHttpResponse $response)
    {
        try {
            DB::beginTransaction();
            $data = [
                'name' => $request->name,
                'phone' => $request->phone,
                'car_id' => $request->car_id,
                'showroom_id' => $request->showroom_id,
                'type_response' => $request->type_response,
            ];
            TestDrive::create($data);
            DB::commit();
            return $response->setMessage(trans('Đăng ký lái thử thành công'));
        } catch (Exception $exception) {
            DB::rollback();
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }
}

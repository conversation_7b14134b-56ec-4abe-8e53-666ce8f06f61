<?php

namespace Addons\TestDrive\Http\Controllers;

use Addons\TestDrive\Http\Requests\TestDriveRequest;
use Addons\TestDrive\Models\TestDrive;
use Addons\Base\Facades\PageTitle;
use Addons\Base\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use Exception;
use Addons\TestDrive\Tables\TestDriveTable;
use Addons\Base\Events\CreatedContentEvent;
use Addons\Base\Events\DeletedContentEvent;
use Addons\Base\Events\UpdatedContentEvent;
use Addons\Base\Http\Responses\BaseHttpResponse;
use Addons\TestDrive\Forms\TestDriveForm;
use Addons\Base\Forms\FormBuilder;

class TestDriveController extends BaseController
{
    public function index(TestDriveTable $table)
    {
        PageTitle::setTitle(trans('plugins/test-drive::test-drive.name'));

        return $table->renderTable();
    }

    public function create(FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('plugins/test-drive::test-drive.create'));

        return $formBuilder->create(TestDriveForm::class)->renderForm();
    }

    public function store(TestDriveRequest $request, BaseHttpResponse $response)
    {
        $testDrive = TestDrive::query()->create($request->input());

        event(new CreatedContentEvent(TEST_DRIVE_MODULE_SCREEN_NAME, $request, $testDrive));

        return $response
            ->setPreviousUrl(route('test-drive.index'))
            ->setNextUrl(route('test-drive.edit', $testDrive->getKey()))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function edit(TestDrive $testDrive, FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('core/base::forms.edit_item', ['name' => $testDrive->name]));

        return $formBuilder->create(TestDriveForm::class, ['model' => $testDrive])->renderForm();
    }

    public function update(TestDrive $testDrive, TestDriveRequest $request, BaseHttpResponse $response)
    {
        $testDrive->fill($request->input());

        $testDrive->save();

        event(new UpdatedContentEvent(TEST_DRIVE_MODULE_SCREEN_NAME, $request, $testDrive));

        return $response
            ->setPreviousUrl(route('test-drive.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(TestDrive $testDrive, Request $request, BaseHttpResponse $response)
    {
        try {
            $testDrive->delete();

            event(new DeletedContentEvent(TEST_DRIVE_MODULE_SCREEN_NAME, $request, $testDrive));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }
}

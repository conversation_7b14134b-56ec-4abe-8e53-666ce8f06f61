<?php

use Addons\Base\Facades\BaseHelper;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Addons\Showroom\Http\Controllers', 'middleware' => ['web', 'core']], function () {

    Route::group(['prefix' => BaseHelper::getAdminPrefix(), 'middleware' => 'auth'], function () {

        Route::group(['prefix' => 'showrooms', 'as' => 'showroom.'], function () {
            Route::resource('', 'ShowroomController')->parameters(['' => 'showroom']);
        });
    });
});

// change-matp

Route::group(['namespace' => 'Addons\Showroom\Http\Controllers', 'middleware' => ['web', 'core']], function () {
    Route::group(apply_filters(BASE_FILTER_GROUP_PUBLIC_ROUTE, []), function () {
        Route::get('change-matp', 'ShowroomController@changeMaTp')->name('change-matp');
        Route::get('change-google-map', 'ShowroomController@ChangeGoogleMap')->name('change-google-map');
    });
});

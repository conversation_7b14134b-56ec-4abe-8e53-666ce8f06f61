<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration
{
    public function up(): void
    {
        Schema::create('showrooms', function (Blueprint $table) {
            $table->id();
            $table->string('name', 255);

            $table->string('status', 60)->default('published');
            $table->tinyInteger('order')->default(0);
            $table->timestamps();
        });

        Schema::create('showrooms_translations', function (Blueprint $table) {
            $table->string('lang_code');
            $table->foreignId('showrooms_id');
            $table->string('name', 255)->nullable();

            $table->primary(['lang_code', 'showrooms_id'], 'showrooms_translations_primary');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('showrooms');
        Schema::dropIfExists('showrooms_translations');
    }
};

<?php

namespace Addons\Showroom\Repositories\Eloquent;

use Addons\Showroom\Models\Province;
use Addons\Showroom\Repositories\Interfaces\ShowroomInterface;
use Addons\Support\Repositories\Eloquent\RepositoriesAbstract;

class ShowroomRepository extends RepositoriesAbstract implements ShowroomInterface
{
    /**
     * {@inheritDoc}
     */
    public function getAllShowroom($active = true)
    {
        $data = $this->model
            ->orderBy('order', 'desc')
            ->orderBy('created_at', 'desc');

        if ($active) {
            $data = $data->wherePublished();
        }

        return $this->applyBeforeExecuteQuery($data)->get();
    }
    public function getAllCitis($active = true)
    {
        $data = app(Province::class)
            ->getModel()
            ->orderBy('order', 'desc')
            ->orderBy('name', 'desc');


        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getShowRoomByCityId($id = 0, $active = true)
    {
        $data = $this->model
            ->where('matp_id', $id)
            ->orderBy('order', 'desc')
            ->orderBy('created_at', 'desc')
            ->with('province');


        if ($active) {
            $data = $data->wherePublished();
        }
        return $this->applyBeforeExecuteQuery($data)->get();
    }
    public function getShowRoomById($id = 0, $active = true)
    {
        $data = $this->model
            ->where('id', $id)
            ->with('province');

        if ($active) {
            $data = $data->wherePublished();
        }
        return $this->applyBeforeExecuteQuery($data)->get();
    }
}

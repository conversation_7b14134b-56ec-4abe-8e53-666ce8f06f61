<?php

namespace Addons\Showroom\Repositories\Interfaces;

use Addons\Support\Repositories\Interfaces\RepositoryInterface;

interface ShowroomInterface extends RepositoryInterface
{
    public function getAllShowroom($active = true);
    public function getAllCitis($active = true);
    public function getShowRoomByCityId($id = 0, $active = true);
    public function getShowRoomById($id = 0, $active = true);
}

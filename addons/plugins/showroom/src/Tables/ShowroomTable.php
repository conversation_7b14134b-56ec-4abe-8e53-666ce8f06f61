<?php

namespace Addons\Showroom\Tables;

use Addons\Base\Facades\BaseHelper;
use Addons\Base\Facades\Html;
use Addons\Base\Enums\BaseStatusEnum;
use Addons\Showroom\Models\Province;
use Addons\Showroom\Models\Showroom;
use Addons\Table\Abstracts\TableAbstract;
use Addons\Table\Actions\DeleteAction;
use Addons\Table\Actions\EditAction;
use Addons\Table\BulkActions\DeleteBulkAction;
use Addons\Table\Columns\CreatedAtColumn;
use Addons\Table\Columns\IdColumn;
use Addons\Table\Columns\NameColumn;
use Addons\Table\Columns\StatusColumn;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;

class ShowroomTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(Showroom::class)
            ->addActions([
                EditAction::make()
                    ->route('showroom.edit'),
                DeleteAction::make()
                    ->route('showroom.destroy'),
            ]);
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('name', function (Showroom $item) {
                if (!$this->hasPermission('showroom.edit')) {
                    return BaseHelper::clean($item->name);
                }
                return Html::link(route('showroom.edit', $item->getKey()), BaseHelper::clean($item->name));
            })
            ->editColumn('status', function ($item) {
                return $item->status->toHtml();
            })
            ->editColumn('created_at', function ($item) {
                return BaseHelper::formatDate($item->created_at);
            })
            ->editColumn('hotline', function ($item) {
                return $item->hotline ?  BaseHelper::clean($item->hotline) : 'N/a';
            })
            ->editColumn('matp_id', function ($item) {
                return $item->province ?  BaseHelper::clean($item->province->name) : 'N/a';
            });

        return $this->toJson($data);
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        $query = $this
            ->getModel()
            ->query()
            ->select([
                'id',
                'name',
                // 'sup_hotline',
                'hotline',
                // 'link_website',
                'address',
                // 'region',
                'matp_id',
                'created_at',
                'status',
            ]);

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        return [
            'id' => [
                'title' => trans('core/base::tables.id'),
                'width' => '20px',
            ],
            'name' => [
                'title' => trans('TÊN'),
                'class' => 'text-start',
            ],
            'address' => [
                'title' => trans('ĐỊA CHỈ'),
                'class' => 'text-start',
            ],
            'matp_id' => [
                'title' => trans('THÀNH PHỐ'),
                'class' => 'text-start',
            ],
            'created_at' => [
                'title' => trans('NGÀY TẠO'),
                'width' => '100px',
            ],
            'status' => [
                'title' => trans('TRẠNG THÁI'),
                'width' => '100px',
            ],
        ];
    }

    public function buttons(): array
    {
        return $this->addCreateButton(route('showroom.create'), 'showroom.create');
    }

    public function bulkActions(): array
    {
        return [
            DeleteBulkAction::make()->permission('showroom.destroy'),
        ];
    }

    public function getBulkChanges(): array
    {
        $showroom =  Showroom::pluck('name', 'id')->toArray();
        $city =  Province::pluck('name', 'matp')->toArray();
        return [
            'name' => [
                'title' => trans('core/base::tables.name'),
                'type' => 'text',
                'validate' => 'required|max:120',
            ],
            'showroom_id' => [
                'title' => trans('Showroom'),
                'type'     => 'select',
                'choices'  => $showroom,
                'width' => '300px',
            ],
            'matp_id' => [
                'title' => trans('Thành phố'),
                'type'     => 'select',
                'choices'  => $city,
                'width' => '300px',
            ],
            'status' => [
                'title' => trans('Trạng thái'),
                'type' => 'select',
                'choices' => BaseStatusEnum::labels(),
                'validate' => 'required|in:' . implode(',', BaseStatusEnum::values()),
            ],
            'created_at' => [
                'title' => trans('Ngày tạo'),
                'type' => 'date',
            ],
        ];
    }

    public function getFilters(): array
    {
        return $this->getBulkChanges();
    }

    public function getDefaultButtons(): array
    {
        return [
            'export',
            'reload',
        ];
    }
}

<?php

namespace Addons\Showroom\Providers;

use <PERSON><PERSON>s\Showroom\Models\Showroom;
use Addons\Base\Facades\DashboardMenu;
use Addons\Base\Traits\LoadAndPublishDataTrait;
use Addons\Base\Supports\ServiceProvider;
use Addons\Showroom\Repositories\Eloquent\ShowroomRepository as EloquentShowroomRepository;
use Addons\Showroom\Repositories\Interfaces\ShowroomInterface;
use Illuminate\Routing\Events\RouteMatched;

class ShowroomServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->bind(ShowroomInterface::class, function () {
            return new EloquentShowroomRepository(new Showroom());
        });
    }

    public function boot(): void
    {
        $this
            ->setNamespace('plugins/showroom')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions'])
            ->loadMigrations()
            ->loadAndPublishTranslations()
            ->loadAndPublishViews()
            ->loadRoutes();

        if (defined('LANGUAGE_ADVANCED_MODULE_SCREEN_NAME')) {
            \Addons\LanguageAdvanced\Supports\LanguageAdvancedManager::registerModule(Showroom::class, [
                'name',
            ]);
        }

        $this->app['events']->listen(RouteMatched::class, function () {
            DashboardMenu::registerItem([
                'id' => 'cms-plugins-showroom',
                'priority' => 5,
                'parent_id' => null,
                'name' => 'plugins/showroom::showroom.name',
                'icon' => 'fa-solid fa-landmark',
                'url' => route('showroom.index'),
                'permissions' => ['showroom.index'],
            ]);
        });
    }
}

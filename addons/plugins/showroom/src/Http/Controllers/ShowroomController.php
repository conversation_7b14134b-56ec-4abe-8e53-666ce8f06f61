<?php

namespace Addons\Showroom\Http\Controllers;

use Addons\Showroom\Http\Requests\ShowroomRequest;
use Addons\Showroom\Models\Showroom;
use Addons\Base\Facades\PageTitle;
use Addons\Base\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use Exception;
use Addons\Showroom\Tables\ShowroomTable;
use Addons\Base\Events\CreatedContentEvent;
use Addons\Base\Events\DeletedContentEvent;
use Addons\Base\Events\UpdatedContentEvent;
use Addons\Base\Http\Responses\BaseHttpResponse;
use Addons\Showroom\Forms\ShowroomForm;
use Addons\Base\Forms\FormBuilder;
use Addons\Theme\Facades\Theme;

class ShowroomController extends BaseController
{
    public function index(ShowroomTable $table)
    {
        PageTitle::setTitle(trans('plugins/showroom::showroom.name'));

        return $table->renderTable();
    }

    public function create(FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('plugins/showroom::showroom.create'));

        return $formBuilder->create(ShowroomForm::class)->renderForm();
    }

    public function store(ShowroomRequest $request, BaseHttpResponse $response)
    {
        $showroom = Showroom::query()->create($request->input());

        event(new CreatedContentEvent(SHOWROOM_MODULE_SCREEN_NAME, $request, $showroom));

        return $response
            ->setPreviousUrl(route('showroom.index'))
            ->setNextUrl(route('showroom.edit', $showroom->getKey()))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function edit(Showroom $showroom, FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('core/base::forms.edit_item', ['name' => $showroom->name]));

        return $formBuilder->create(ShowroomForm::class, ['model' => $showroom])->renderForm();
    }

    public function update(Showroom $showroom, ShowroomRequest $request, BaseHttpResponse $response)
    {
        $showroom->fill($request->input());

        $showroom->save();

        event(new UpdatedContentEvent(SHOWROOM_MODULE_SCREEN_NAME, $request, $showroom));

        return $response
            ->setPreviousUrl(route('showroom.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(Showroom $showroom, Request $request, BaseHttpResponse $response)
    {
        try {
            $showroom->delete();

            event(new DeletedContentEvent(SHOWROOM_MODULE_SCREEN_NAME, $request, $showroom));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    public function changeMaTp(Request $request, BaseHttpResponse $response)
    {

        $id = $request->matp_id;
        $price_car  = $request->price_car;


        $showRoom = get_showroom_by_city_id($id);

        if (blank($showRoom)) {
            $template = Theme::partial('templates.showroom.change-show-room', ['showRoom' => $showRoom]);
            return $response->setData([
                'template' =>  $template,
                'type' =>  false,
            ]);
        }
        $template = Theme::partial('templates.showroom.change-show-room', ['showRoom' => $showRoom]);


        return $response->setData([
            'template' =>  $template,
            'type' => true,
        ]);
    }
    public function ChangeGoogleMap(Request $request, BaseHttpResponse $response)
    {
        $id = $request->show_room_id;
        $showRoom = get_showroom_by_id($id);

        $templateGoogleMap = Theme::partial('templates.showroom.change-google-map', ['showRoom' => $showRoom]);

        return $response->setData([
            'templateGoogleMap' =>  $templateGoogleMap,
        ]);
    }
}

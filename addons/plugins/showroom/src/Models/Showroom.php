<?php

namespace Addons\Showroom\Models;

use Addons\Base\Casts\SafeContent;
use Addons\Base\Enums\BaseStatusEnum;
use Addons\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @method static \Addons\Base\Models\BaseQueryBuilder<static> query()
 */
class Showroom extends BaseModel
{
    protected $table = 'showrooms';

    protected $fillable = [
        'name',
        'hotline',
        'address',
        'status',
        'matp_id',
        'order',
        'google_map_link',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
    ];

    public function province(): BelongsTo
    {
        return $this->belongsTo(Province::class, 'matp_id', 'matp');
    }
}

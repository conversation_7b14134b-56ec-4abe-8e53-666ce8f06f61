<?php

namespace Addons\CustomField\Http\Controllers;

use Addons\Base\Facades\Assets;
use Addons\Base\Facades\PageTitle;
use Addons\Base\Forms\FormBuilder;
use Addons\Base\Http\Controllers\BaseController;
use Addons\Base\Http\Responses\BaseHttpResponse;
use Addons\CustomField\Actions\CreateCustomFieldAction;
use Addons\CustomField\Actions\DeleteCustomFieldAction;
use Addons\CustomField\Actions\ExportCustomFieldsAction;
use Addons\CustomField\Actions\ImportCustomFieldsAction;
use Addons\CustomField\Actions\UpdateCustomFieldAction;
use Addons\CustomField\Facades\CustomField;
use Addons\CustomField\Forms\CustomFieldForm;
use Addons\CustomField\Http\Requests\CreateFieldGroupRequest;
use Addons\CustomField\Http\Requests\UpdateFieldGroupRequest;
use Addons\CustomField\Models\FieldGroup;
use Addons\CustomField\Tables\CustomFieldTable;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class CustomFieldController extends BaseController
{
    public function index(CustomFieldTable $dataTable)
    {
        PageTitle::setTitle(trans('plugins/custom-field::base.page_title'));

        Assets::addScriptsDirectly('vendor/core/plugins/custom-field/js/import-field-group.js')
            ->addScripts(['blockui']);

        return $dataTable->renderTable();
    }

    public function create(FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('plugins/custom-field::base.form.create_field_group'));

        Assets::addStylesDirectly([
            'vendor/core/plugins/custom-field/css/custom-field.css',
            'vendor/core/plugins/custom-field/css/edit-field-group.css',
        ])
            ->addScriptsDirectly('vendor/core/plugins/custom-field/js/edit-field-group.js')
            ->addScripts(['jquery-ui']);

        return $formBuilder->create(CustomFieldForm::class)->renderForm();
    }

    public function store(CreateFieldGroupRequest $request, CreateCustomFieldAction $action, BaseHttpResponse $response)
    {
        $result = $action->run($request->input());

        $hasError = false;
        $message = trans('core/base::notices.create_success_message');
        if ($result['error']) {
            $hasError = true;
            $message = Arr::first($result['messages']);
        }

        return $response
            ->setError($hasError)
            ->setPreviousUrl(route('custom-fields.index'))
            ->setNextUrl(route('custom-fields.edit', $result['data']['id']))
            ->setMessage($message);
    }

    public function edit(int|string $id, FormBuilder $formBuilder)
    {
        Assets::addStylesDirectly([
            'vendor/core/plugins/custom-field/css/custom-field.css',
            'vendor/core/plugins/custom-field/css/edit-field-group.css',
        ])
            ->addScriptsDirectly('vendor/core/plugins/custom-field/js/edit-field-group.js')
            ->addScripts(['jquery-ui']);

        $fieldGroup = FieldGroup::query()->findOrFail($id);

        PageTitle::setTitle(trans('plugins/custom-field::base.form.edit_field_group') . ' "' . $fieldGroup->title . '"');

        $fieldGroup->rules_template = CustomField::renderRules();

        return $formBuilder->create(CustomFieldForm::class, ['model' => $fieldGroup])->renderForm();
    }

    public function update(
        int|string $id,
        UpdateFieldGroupRequest $request,
        UpdateCustomFieldAction $action,
        BaseHttpResponse $response
    ) {
        $fieldGroup = FieldGroup::query()->findOrFail($id);

        $result = $action->run($fieldGroup, $request->input());

        $message = trans('core/base::notices.update_success_message');
        if ($result['error']) {
            $response->setError();
            $message = Arr::first($result['messages']);
        }

        return $response
            ->setPreviousUrl(route('custom-fields.index'))
            ->setMessage($message);
    }

    public function destroy(int|string $id, BaseHttpResponse $response, DeleteCustomFieldAction $action)
    {
        try {
            $fieldGroup = FieldGroup::query()->findOrFail($id);

            $action->run($fieldGroup);

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    public function getExport(ExportCustomFieldsAction $action, $id = null)
    {
        $ids = [];

        if (! $id) {
            foreach (FieldGroup::query()->get() as $item) {
                $ids[] = $item->id;
            }
        } else {
            $ids[] = $id;
        }

        $json = $action->run($ids)['data'];

        return response()->json($json, 200, [
            'Content-type' => 'application/json',
            'Content-Disposition' => 'attachment; filename="export-field-group.json"',
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    }

    public function postImport(ImportCustomFieldsAction $action, Request $request)
    {
        $json = (array)$request->input('json_data', []);

        return $action->run($json);
    }
}

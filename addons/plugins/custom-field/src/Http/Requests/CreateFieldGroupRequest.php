<?php

namespace Addons\CustomField\Http\Requests;

use Addons\Base\Enums\BaseStatusEnum;
use Addons\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class CreateFieldGroupRequest extends Request
{
    public function rules(): array
    {
        return [
            'order' => 'integer|min:0|required',
            'rules' => 'json|required',
            'title' => 'required|string|max:255',
            'status' => ['required', 'string', Rule::in(BaseStatusEnum::values())],
        ];
    }
}

<?php

namespace Addons\CustomField\Listeners;

use Addons\Base\Events\UpdatedContentEvent;
use Addons\CustomField\Facades\CustomField;
use Exception;

class UpdatedContentListener
{
    public function handle(UpdatedContentEvent $event): void
    {
        try {
            CustomField::saveCustomFields($event->request, $event->data);
        } catch (Exception $exception) {
            info($exception->getMessage());
        }
    }
}

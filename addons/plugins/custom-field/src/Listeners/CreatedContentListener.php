<?php

namespace Addons\CustomField\Listeners;

use Addons\Base\Events\CreatedContentEvent;
use Addons\CustomField\Facades\CustomField;
use Exception;

class CreatedContentListener
{
    public function handle(CreatedContentEvent $event): void
    {
        try {
            CustomField::saveCustomFields($event->request, $event->data);
        } catch (Exception $exception) {
            info($exception->getMessage());
        }
    }
}

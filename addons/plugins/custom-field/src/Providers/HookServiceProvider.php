<?php

namespace Addons\CustomField\Providers;

use Addons\ACL\Models\Role;
use Addons\Base\Facades\Assets;
use Addons\Base\Supports\ServiceProvider;
use Addons\Blog\Models\Post;
use Addons\CustomField\Facades\CustomField;
use Addons\Page\Models\Page;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class HookServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        add_action(BASE_ACTION_META_BOXES, [$this, 'handle'], 125, 2);
    }

    public function handle(string $priority, ?Model $object = null): void
    {
        $reference = get_class($object);
        if (CustomField::isSupportedModule($reference) && $priority == 'advanced') {
            add_custom_fields_rules_to_check([
                $reference => $object->id ?? null,
                'model_name' => $reference,
            ]);

            /**
             * Every model will have these rules by default
             */
            if (Auth::check()) {
                add_custom_fields_rules_to_check([
                    'logged_in_user' => Auth::id(),
                    'logged_in_user_has_role' => Role::query()->pluck('id')->all(),
                ]);
            }

            if (defined('PAGE_MODULE_SCREEN_NAME') && $reference == Page::class) {
                add_custom_fields_rules_to_check([
                    'page_template' => $object->template ?? '',
                ]);
            }

            if (defined('POST_MODULE_SCREEN_NAME')) {
                if ($object instanceof Post) {
                    $relatedCategoryIds = $object->categories()->allRelatedIds()->toArray();
                    add_custom_fields_rules_to_check([
                        $reference . '_post_with_related_category' => $relatedCategoryIds,
                        $reference . '_post_format' => $object->format_type,
                    ]);
                }
            }

            echo $this->render($reference, $object->id ?? null);
        }
    }

    protected function render(string $reference, int|string|null $id): string|null
    {
        $customFieldBoxes = get_custom_field_boxes($reference, $id);

        if (! $customFieldBoxes) {
            return null;
        }

        Assets::addStylesDirectly([
            'vendor/core/plugins/custom-field/css/custom-field.css',
        ])
            ->addScriptsDirectly([
                'vendor/core/plugins/custom-field/js/use-custom-fields.js',
            ])
            ->addScripts(['jquery-ui']);

        CustomField::renderAssets();

        return CustomField::renderCustomFieldBoxes($customFieldBoxes);
    }
}

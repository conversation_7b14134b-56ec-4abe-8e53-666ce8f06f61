.widget-title {
    cursor: initial !important;
}

/*Custom fields*/
.line-group-container {
    width: 100%;
    overflow: auto;

    .line-group {
        width: 700px;

        &:before {
            content: attr(data-text);
            display: block;
            width: 100%;
            text-transform: capitalize;
            margin-bottom: 10px;
            font-weight: 700;
            margin-top: 10px;
        }

        &:first-child:before {
            display: none;
        }

        .rule-line {
            position: relative;
            margin-bottom: 10px;

            &:hover .remove-rule-line {
                opacity: 1;
            }
        }

        .remove-rule-line {
            opacity: 0;
            background-color: #cccccc;
            border-radius: 50% !important;
            display: block;
            height: 30px;
            position: absolute;
            right: 0;
            top: 0;
            width: 30px;
            transition: all 0.15s ease-in-out 0s;
            transform: rotate(45deg);

            span {
                width: 20px;
                height: 1px;
                background-color: #000000;
                display: block;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);

                &:before {
                    content: '';
                    display: block;
                    width: 1px;
                    height: 20px;
                    background-color: #000000;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
            }

            &:hover {
                opacity: 1;
            }
        }

        .rule-a,
        .rule-type,
        .rules-b-group {
            width: 220px;
            margin-left: 5px !important;
        }

        .rule-a {
            margin-left: 0 !important;
        }

        .rule-type {
            width: 130px;
        }

        .location-add-rule-and {
            margin-left: 5px;
            height: 38px;
            line-height: 20px;
        }
    }

    > .line-group:first-child > .rule-line:first-child .remove-rule-line {
        display: none !important;
    }
}

.custom-fields-rules .rules-b-group .form-control {
    margin-left: 0;
}

.add-new-field {
    &:before {
        content: '#';
        display: inline-block;
        width: 51px;
        text-align: center;
        line-height: 39px;
        position: absolute;
        border-left: 1px solid #aaaaaa;
        border-top: 1px solid #aaaaaa;
        border-bottom: 1px solid #aaaaaa;
    }
}

.item-details {
    .add-new-field {
        &:before {
            display: none;
        }
    }
}

/*Sortable list*/
.field-table-header {
    margin: 0;
    padding-left: 50px;

    .list-group-item {
        border: 1px solid #aaaaaa;
        border-radius: 0 !important;
        padding: 10px 15px 9px;
        margin-bottom: 0;

        &:not(:first-child) {
            border-left: 0 none;
        }
    }
}

.field-group-items {
    &.edit-field-group-items {
        margin-top: -1px;
    }

    .item-details {
        display: none;

        .line {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            width: 100%;

            .clearfix {
                display: none;
            }

            .col-xs-3,
            .col-xs-9 {
                float: none;
            }

            .col-xs-9 h5 {
                display: none;
            }

            .col-xs-9 .col-xs-9 h5 {
                display: block;
            }
        }

        h5 {
            font-weight: 700;
        }
    }

    .field-column {
        clear: both;
        padding: 10px;
    }

    li.active {
        > .item-details {
            display: block;
        }

        > .field-column {
            border-bottom: 1px solid #aaaaaa;
            padding: 10px;
        }

        > .item-details {
            > .line {
                margin-bottom: 0;
                border-bottom: 1px solid #aaaaaa;
                padding: 10px;
            }

            > .options > .line {
                border-bottom: 1px solid #aaaaaa;
                margin-bottom: 0;
                padding: 10px;
            }
        }
    }

    > li > .field-column > .show-item-details {
        background-color: #cccccc;
        border-left: 1px solid #aaaaaa;
        color: #000000;
        height: 40px;
        line-height: 40px;
        position: absolute;
        right: 0;
        text-align: center;
        top: 0;
        width: 39px;

        &.active {
            height: 40px;
            line-height: 40px;
        }
    }
}

.pt10 {
    padding-top: 10px;
}

.p10 {
    padding: 10px;
}

body[dir='rtl'] {
    .line-group-container .line-group .remove-rule-line {
        right: auto;
        left: 0;
    }

    .line-group-container .line-group .rule-a,
    .line-group-container .line-group .rule-type,
    .line-group-container .line-group .rules-b-group {
        margin-left: 0 !important;
        margin-right: 5px !important;
    }

    .add-new-field:before {
        border-left: none;
        border-right: 1px solid #aaaaaa;
    }

    .field-table-header .list-group-item:not(:first-child) {
        border-left: 1px solid #aaaaaa;
        border-right: none;
    }

    .field-table-header {
        padding-left: 0;
        padding-right: 50px;
    }

    .field-group-items > li:after {
        left: auto;
        right: 0;
        border-right: none;
        border-left: 1px solid #aaa;
    }

    .field-group-items li {
        padding: 0 50px 0 0;
    }

    .field-group-items > li:before {
        left: auto;
        right: 10px;
    }

    .field-group-items {
        padding-right: 0;
    }

    .field-group-items > li > .field-column > .show-item-details {
        right: auto;
        left: 0;
        border-left: none;
        border-right: 1px solid #aaaaaa;
    }

    .line-group-container .line-group .location-add-rule-and {
        margin-left: 0;
        margin-right: 5px;
    }
}

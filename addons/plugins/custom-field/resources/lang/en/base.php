<?php

return [
    'admin_menu' => [
        'title' => 'Custom Fields',
    ],

    'page_title' => 'Custom Fields',

    'all_field_groups' => 'All field groups',

    'form' => [
        'create_field_group' => 'Create field group',
        'edit_field_group' => 'Edit field group',
        'field_items_information' => 'Field items information',

        'repeater_fields' => 'Repeater',
        'add_field' => 'Add field',
        'remove_field' => 'Remove field',
        'close_field' => 'Close field',
        'new_field' => 'New field',

        'field_label' => 'Label',
        'field_label_helper' => 'This is the title of field item. It will be shown on edit pages.',
        'field_name' => 'Field name',
        'field_name_helper' => 'The alias of field item. Accepted numbers, characters and underscore.',
        'field_type' => 'Field type',
        'field_type_helper' => 'Please select the type of this field.',
        'field_instructions' => 'Field instructions',
        'field_instructions_helper' => 'The instructions guide for user easier know what they need to input.',

        'default_value' => 'Default value',
        'default_value_helper' => 'The default value of field when leave it blank',
        'placeholder' => 'Placeholder',
        'placeholder_helper' => 'Placeholder text',
        'rows' => 'Rows',
        'rows_helper' => 'Rows of this textarea',
        'choices' => 'Choices',
        'choices_helper' => 'Enter each choice on a new line.<br>For more control, you may specify both a value and label like this:<br>red: Red<br>blue: Blue',
        'button_label' => 'Button for repeater',

        'groups' => [
            'basic' => 'Basic',
            'content' => 'Content',
            'choice' => 'Choices',
            'other' => 'Other',
        ],

        'types' => [
            'text' => 'Text field',
            'textarea' => 'Textarea',
            'number' => 'Number',
            'email' => 'Email',
            'password' => 'Password',
            'wysiwyg' => 'WYSIWYG editor',
            'image' => 'Image',
            'file' => 'File',
            'select' => 'Select',
            'checkbox' => 'Checkbox',
            'radio' => 'Radio',
            'repeater' => 'Repeater',
        ],

        'rules' => [
            'rules' => 'Display rules',
            'rules_helper' => 'Show this field group if',
            'add_rule_group' => 'Add rule group',
            'is_equal_to' => 'Equal to',
            'is_not_equal_to' => 'Not equal to',
            'and' => 'And',
            'or' => 'Or',
        ],
    ],

    'import' => 'Import',
    'export' => 'Export',
    'publish' => 'Publish',
    'remove_this_line' => 'Remove this line',
    'collapse_this_line' => 'Collapse this line',
    'error_occurred' => 'Error occurred',
    'request_completed' => 'Request completed',
    'item_not_existed' => 'Item is not exists',
];

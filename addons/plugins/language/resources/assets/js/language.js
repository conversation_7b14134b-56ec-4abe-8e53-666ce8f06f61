class LanguageManagement {
    static formatState(state) {
        if (!state.id || state.element.value.toLowerCase().includes('...')) {
            return state.text
        }

        return $(
            '<span><img src="' +
                $('#language_flag_path').val() +
                state.element.value.toLowerCase() +
                '.svg" class="img-flag" width="16" alt="Language flag"/> ' +
                state.text +
                '</span>'
        )
    }

    bindEventToElement() {
        if (jQuery().select2) {
            $('.select-search-language').select2({
                width: '100%',
                templateResult: LanguageManagement.formatState,
                templateSelection: LanguageManagement.formatState,
            })
        }

        let languageTable = $('.table-language')

        $(document).on('change', '#language_id', (event) => {
            let language = $(event.currentTarget).find('option:selected').data('language')
            if (typeof language != 'undefined' && language.length > 0) {
                $('#lang_name').val(language[2])
                $('#lang_locale').val(language[0])
                $('#lang_code').val(language[1])
                $('#flag_list').val(language[4]).trigger('change')
                $('.lang_is_' + language[3]).prop('checked', true)
                $('#btn-language-submit-edit').prop('id', 'btn-language-submit').text('Add new language')
            }
        })

        $(document).on('click', '#btn-language-submit', (event) => {
            event.preventDefault()
            let name = $('#lang_name').val()
            let locale = $('#lang_locale').val()
            let code = $('#lang_code').val()
            let flag = $('#flag_list').val()
            let order = $('#lang_order').val()
            let isRTL = $('.lang_is_rtl').prop('checked') ? 1 : 0
            LanguageManagement.createOrUpdateLanguage(0, name, locale, code, flag, order, isRTL, 0)
        })

        $(document).on('click', '#btn-language-submit-edit', (event) => {
            event.preventDefault()
            let id = $('#lang_id').val()
            let name = $('#lang_name').val()
            let locale = $('#lang_locale').val()
            let code = $('#lang_code').val()
            let flag = $('#flag_list').val()
            let order = $('#lang_order').val()
            let isRTL = $('.lang_is_rtl').prop('checked') ? 1 : 0
            LanguageManagement.createOrUpdateLanguage(id, name, locale, code, flag, order, isRTL, 1)
        })

        languageTable.on('click', '.deleteDialog', (event) => {
            event.preventDefault()

            $('.delete-crud-entry').data('section', $(event.currentTarget).data('section'))
            $('.modal-confirm-delete').modal('show')
        })

        $('.delete-crud-entry').on('click', (event) => {
            event.preventDefault()
            $('.modal-confirm-delete').modal('hide')

            let deleteURL = $(event.currentTarget).data('section')
            $(this).prop('disabled', true).addClass('button-loading')

            $httpClient
                .make()
                .delete(deleteURL)
                .then(({ data }) => {
                    if (data.data) {
                        languageTable.find('i[data-id=' + data.data + ']').unwrap()
                        $('.tooltip').remove()
                    }
                    languageTable
                        .find('a[data-section="' + deleteURL + '"]')
                        .closest('tr')
                        .remove()
                    Apps.showSuccess(data.message)
                })
                .finally(() => {
                    $(this).prop('disabled', false).removeClass('button-loading')
                })
        })

        languageTable.on('click', '.set-language-default', (event) => {
            event.preventDefault()
            let _self = $(event.currentTarget)

            $httpClient
                .make()
                .get(_self.data('section'))
                .then(({ data }) => {
                    let star = languageTable.find('td > i')
                    star.replaceWith(
                        `<a data-section="${route('languages.set.default')}?lang_id=${star.data(
                            'id'
                        )}" class="set-language-default" data-bs-toggle="tooltip" data-bs-original-title="Choose ${star.data(
                            'name'
                        )} as default language">${star.closest('td').html()}</a>`
                    )
                    _self.find('i').unwrap()

                    $('.tooltip').remove()

                    Apps.showSuccess(data.message)
                })
        })

        languageTable.on('click', '.edit-language-button', (event) => {
            event.preventDefault()
            let _self = $(event.currentTarget)

            $httpClient
                .make()
                .get(route('languages.get') + '?lang_id=' + _self.data('id'))
                .then(({ data }) => {
                    let language = data.data

                    $('#lang_id').val(language.lang_id)
                    $('#lang_name').val(language.lang_name)
                    $('#lang_locale').val(language.lang_locale)
                    $('#lang_code').val(language.lang_code)
                    $('#flag_list').val(language.lang_flag).trigger('change')
                    $('.lang_is_rtl').prop('checked', language.lang_is_rtl)
                    $('.lang_is_ltr').prop('checked', !language.lang_is_rtl)
                    $('#lang_order').val(language.lang_order)

                    $('#btn-language-submit').prop('id', 'btn-language-submit-edit').text('Update')
                })
        })

        $(document).on('click', '.button-save-language-settings', (event) => {
            event.preventDefault()
            let _self = $(event.currentTarget)
            _self.addClass('button-loading')

            const $form = _self.closest('form')
            const formData = new FormData($form[0])

            $httpClient
                .make()
                .postForm($form.prop('action'), formData)
                .then(({ data }) => {
                    Apps.showSuccess(data.message)
                    $form.removeClass('dirty')
                })
                .finally(() => {
                    _self.removeClass('button-loading')
                })
        })
    }

    static createOrUpdateLanguage(id, name, locale, code, flag, order, isRTL, edit) {
        let url = route('languages.store')

        if (edit) {
            url = route('languages.edit') + '?lang_code=' + code
        }

        $('#btn-language-submit').addClass('button-loading')

        $httpClient
            .make()
            .post(url, {
                lang_id: id.toString(),
                lang_name: name,
                lang_locale: locale,
                lang_code: code,
                lang_flag: flag,
                lang_order: order,
                lang_is_rtl: isRTL,
            })
            .then(({ data }) => {
                if (edit) {
                    $('.table-language')
                        .find('tr[data-id=' + id + ']')
                        .replaceWith(data.data)
                } else {
                    $('.table-language').append(data.data)
                }
                Apps.showSuccess(data.message)
            })
            .finally(() => {
                $('#language_id').val('').trigger('change')
                $('#lang_name').val('')
                $('#lang_locale').val('')
                $('#lang_code').val('')
                $('#flag_list').val('').trigger('change')
                $('.lang_is_ltr').prop('checked', true)

                $('#btn-language-submit-edit').prop('id', 'btn-language-submit').text('Add new language')
                $('#btn-language-submit').removeClass('button-loading')
            })
    }
}

$(document).ready(() => {
    new LanguageManagement().bindEventToElement()
})

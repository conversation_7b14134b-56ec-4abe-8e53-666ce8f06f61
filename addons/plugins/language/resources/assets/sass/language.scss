.table-language {
    background: #f1f1f1;

    thead,
    tbody,
    tr {
        width: 100% !important;
    }

    th {
        background-color: lighten(#32c5d2, 1%);
        color: #ffffff !important;
    }

    th,
    td {
        text-align: center;
    }

    .text-start {
        text-align: left;
    }

    tr {
        .set-language-default {
            display: none;
        }

        &:hover {
            .set-language-default {
                display: block;
            }
        }
    }
}

#btn-language-submit,
#btn-language-submit-edit {
    margin: 10px 0;
}

.select-language-table {
    tr {
        border-bottom: 10px solid transparent;
    }
}

.page-content {
    .dataTables_wrapper {
        .language-column {
            a {
                display: inline-block;
                margin: 0 7px;
            }
        }

        .language-header {
            img {
                margin-right: 7px;
            }
        }
    }

    #added-widget {
        .admin-list-language-chooser {
            display: inline-block;
            margin: 28px 0 10px 0;
        }
    }
}

#confirm-change-language-button {
    background: #4d97c1;
    border-color: #2d8ec5;
    color: #fff;

    &.button-loading:before {
        border-color: #ffffff;
        border-bottom-color: transparent;
    }
}

<?php

namespace Addons\Language\Providers;

use Addons\Base\Supports\ServiceProvider;
use Addons\Language\Commands\RouteCacheCommand;
use Addons\Language\Commands\RouteClearCommand;
use Addons\Language\Commands\RouteTranslationsListCommand;
use Addons\Language\Commands\SyncOldDataCommand;
use Illuminate\Foundation\Console\RouteCacheCommand as BaseRouteCacheCommand;
use Illuminate\Foundation\Console\RouteClearCommand as BaseRouteClearCommand;

class CommandServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->commands([
            SyncOldDataCommand::class,
            RouteTranslationsListCommand::class,
        ]);

        $this->app->extend(BaseRouteCacheCommand::class, function () {
            return new RouteCacheCommand($this->app['files']);
        });

        $this->app->extend(BaseRouteClearCommand::class, function () {
            return new RouteClearCommand($this->app['files']);
        });
    }
}

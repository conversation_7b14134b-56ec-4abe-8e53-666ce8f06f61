<?php

namespace Addons\Car\Forms;

use Addons\Base\Forms\FormAbstract;
use Addons\Base\Enums\BaseStatusEnum;
use Addons\Car\Http\Requests\CarRequest;
use Addons\Car\Models\Car;

class CarForm extends FormAbstract
{
    public function buildForm(): void
    {
        $this
            ->setupModel(new Car)
            ->setValidatorClass(CarRequest::class)
            ->withCustomFields()
            ->add('name', 'text', [
                'label' => trans('core/base::forms.name'),
                'label_attr' => ['class' => 'control-label required'],
                'attr' => [
                    'placeholder' => trans('core/base::forms.name_placeholder'),
                    'data-counter' => 120,
                ],
            ])
            ->add('price', 'text', [
                'label' => trans('Giá'),
                'label_attr' => ['class' => 'control-label required'],
                'attr' => [
                    'min' => 0,
                    'placeholder' => trans('Giá'),
                    'class' => 'form-control input-mask-number',
                ],

            ])
            ->add('status', 'customSelect', [
                'label' => trans('core/base::tables.status'),
                'label_attr' => ['class' => 'control-label required'],
                'attr' => [
                    'class' => 'form-control select-full',
                ],
                'choices' => BaseStatusEnum::labels(),
            ])
            ->setBreakFieldPoint('status');
    }
}

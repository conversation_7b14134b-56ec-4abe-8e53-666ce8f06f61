{!! Theme::footer() !!}
<script type="text/javascript" src="{{ asset('vendor/core/core/js-validation/js/js-validation.js') }}"></script>
{!! JsValidator::formRequest(Addons\Contact\Http\Requests\ContactRequest::class, '#contact-form') !!}
{!! JsValidator::formRequest(Addons\TestDrive\Http\Requests\TestDriveRequest::class, '#test-drive-form') !!}
{!! JsValidator::formRequest(Addons\TestDrive\Http\Requests\RegisterRequest::class, '#register-form') !!}
{!! JsValidator::formRequest(Addons\BookingCar\Http\Requests\BookingCarRequest::class, '#estimate-cost-form') !!}


{{-- @if (session('success_msg') || session('error_msg'))
    <script>
        console.log(1);
        $('#successModal').modal('show');
        setTimeout(() => {
            $('#successModal').modal({
                show: 'false'
            });
        }, 5s);
    </script>
@endif --}}
@if (session('success_msg') || session('error_msg'))
    <script>
        $('.camon').show();
        $('body').addClass('active-popup');
    </script>
@endif
<script>
    var carPriceUrl = '{{ route('public.ajax.carprice') }}';
</script>

<script>
    $(document).ready(function() {
        var list360Car = $('.car-360').threeSixty({
            dragDirection: 'horizontal',
            useKeys: true,
            draggable: true,
        });
        var intervalThreesixtyFrame = setInterval(() => {
            const element = $('.car-360 .threesixty-frame')
            if (element && element.length) {
                clearInterval(intervalThreesixtyFrame)
                $('.color-1 .car-360').setFrame('6');
            }
        }, 100);
        $('.color-slides ul li').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $('.color-item').hide();
            $('.color-' + $(this).data('index')).show();
            $('.color-' + $(this).data('index') + ' .car-360').setFrame('6');
            $(this).addClass('active').siblings().removeClass('active')
            $('.color-slides .color-title ').html($(this).data('color'))
        })

    })
</script>
</body>

</html>

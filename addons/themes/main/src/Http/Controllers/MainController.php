<?php

namespace Theme\Main\Http\Controllers;

use Theme;
use MetaBox;
use RvMedia;
use Response;
use Exception;
use SeoHelper;
use BaseHelper;
use SlugHelper;
use SiteMapManager;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use Addons\Page\Models\Page;
use Addons\Page\Services\PageService;
use Addons\Theme\Events\RenderingSingleEvent;
use Addons\Theme\Events\RenderingHomePageEvent;
use Addons\Base\Http\Responses\BaseHttpResponse;
use Addons\Theme\Http\Controllers\PublicController;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use AppMedia;

class MainController extends PublicController
{
    /**
     * {@inheritDoc}
     */
    public function getIndex()
    {
        if (defined('PAGE_MODULE_SCREEN_NAME')) {
            $homepageId = BaseHelper::getHomepageId();
            if ($homepageId) {
                $slug = SlugHelper::getSlug(null, SlugHelper::getPrefix(Page::class), Page::class, $homepageId);
                if ($slug) {
                    $result = (new PageService)->handleFrontRoutes($slug);
                    // dd($data);
                    Theme::layout('default');

            $view = Arr::get($result, 'data.page')->template ?? Arr::get($result, 'view', '');
            return Theme::scope($view, $result['data'], Arr::get($result, 'default_view'))->render();

                    // return Theme::scope('index', $data['data'])->render();
                }
            }
        }

        SeoHelper::setTitle(theme_option('seo_title', 'Car Service'))
            ->setDescription(theme_option('seo_description', 'Car Service'))
            ->openGraph()
            ->setTitle(@theme_option('seo_title'))
            ->setSiteName(@theme_option('site_title'))
            ->setUrl(route('public.index'))
            ->setImage(AppMedia::getImageUrl(theme_option('seo_og_image'), 'og'))
            ->addProperty('image:width', '1200')
            ->addProperty('image:height', '630');


        Theme::breadcrumb()->add(__('Home'), route('public.index'));

        event(RenderingHomePageEvent::class);
    }

    /**
     * {@inheritDoc}
     */
    public function getView($key = null, $prefix = '')
    {
        SeoHelper::setTitle(theme_option('seo_title', 'Car Service'))
            ->setDescription(theme_option('seo_description', 'Car Service'))
            ->openGraph()
            ->setTitle(@theme_option('seo_title'))
            ->setSiteName(@theme_option('site_title'))
            ->setUrl(route('public.index'))
            ->setImage(AppMedia::getImageUrl(theme_option('seo_og_image'), 'og'))
            ->addProperty('image:width', '1200')
            ->addProperty('image:height', '630');
        if (empty($key)) {
            return $this->getIndex();
        }
        $slug = SlugHelper::getSlug($key, '');

        if (!$slug) {
            abort(404);
        }

        if (defined('PAGE_MODULE_SCREEN_NAME')) {
            if ($slug->reference_type == Page::class && BaseHelper::isHomepage($slug->reference_id)) {
                return redirect()->to('/');
            }
        }

        $result = apply_filters(BASE_FILTER_PUBLIC_SINGLE_DATA, $slug);

        if (isset($result['slug']) && $result['slug'] !== $key) {
            return redirect()->route('public.single', $result['slug']);
        }

        event(new RenderingSingleEvent($slug));
        Theme::layout('default');
        if (!empty($result) && is_array($result)) {
            $view = Arr::get($result, 'data.page')->template ?? Arr::get($result, 'view', '');
            return Theme::scope($view, $result['data'], Arr::get($result, 'default_view'))->render();
        }
        abort(404);
        Theme::breadcrumb()->add(__('Trang chủ'), url("public.index"));
    }

    /**
     * {@inheritDoc}
     */
    public function getSiteMap()
    {
        return parent::getSiteMap();
    }
    public function submitContact(Request $request)
    {
        try {
            DB::beginTransaction();
            $contact = new Contact();
            $input = $request->only(

                'name',
                'phone',
                'email',

            );

            $contact = $contact->create($input);
            DB::commit();
            $this->createLeadCustomer($request);
            return redirect()->to(route('public.thankyou'))->withSuccess('Đăng ký nhận thông tin thành công!');
        } catch (Throwable $th) {
            Log::channel('slack')->error($th->getMessage(), []);
            DB::rollBack();
            return response()->json('Đăng ký nhận thông tin thất bại, vui lòng thử lại sau')->setStatusCode(500);
        }
    }

}

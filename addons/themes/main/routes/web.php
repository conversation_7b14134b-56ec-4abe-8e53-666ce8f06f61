<?php

use Illuminate\Support\Facades\Route;
use Theme\Main\Http\Controllers\MainController;

// Custom routes
// You can delete this route group if you don't need to add your custom routes.
Route::group(['controller' => MainController::class, 'middleware' => ['web', 'core']], function () {
    Route::group(apply_filters(BASE_FILTER_GROUP_PUBLIC_ROUTE, []), function () {


        // Add your custom route here
        // Ex: Route::get('hello', 'getHello');

    });
});

Theme::routes();

Route::group(['namespace' => 'Theme\Main\Http\Controllers', 'middleware' => ['web', 'core']], function () {
    Route::group(apply_filters(BASE_FILTER_GROUP_PUBLIC_ROUTE, []), function () {

        Route::get('/', 'MainController@getIndex')
            ->name('public.index');
        Route::post('submit-contact', 'MainController@submitContact')->name('public.submitContact');

        Route::get('sitemap.xml', 'MainController@getSiteMap')
            ->name('public.sitemap');

        Route::get('{slug?}' . config('core.base.general.public_single_ending_url'), 'MainController@getView')
            ->name('public.single');
    });
});

<?php

use Illuminate\Support\Facades\DB;

if (!function_exists('get_registration_fee_by_id')) {
    /**
     * @return \Illuminate\Support\Collection
     * @throws Exception
     */
    function get_registration_fee_by_id($id)
    {
        $data = DB::table('registration_fees')->select('*')
            ->where('city_id', $id)->first();

        $registrationFee = new \Illuminate\Support\Fluent($data);

        if (in_array($registrationFee->city_id, [42])) {
            $registrationFee->phi_truoc_ba = 5.5;
        } elseif (in_array($registrationFee->city_id, [01, 10, 04, 20, 14, 22, 31, 92])) {
            $registrationFee->phi_truoc_ba = 6;
        } else {
            $registrationFee->phi_truoc_ba = 5;
        }

        return $registrationFee;
    }
}

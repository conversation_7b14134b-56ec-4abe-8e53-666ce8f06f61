<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Wed Jun 23 16:35:50 2021
 By Aleksey,,,
Copyright (c) 2019 by Peugeot. All rights reserved.
</metadata>
<defs>
<font id="VN-PeugeotNew-BoldItalic" horiz-adv-x="760" >
  <font-face 
    font-family="VN-Peugeot New"
    font-weight="700"
    font-style="italic"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 0 0 0 0 0 0 0 0"
    ascent="802"
    descent="-198"
    x-height="572"
    cap-height="802"
    bbox="-340 -246 1660 1203"
    underline-thickness="138"
    underline-position="-267"
    slope="-12"
    unicode-range="U+0020-FEFF"
  />
<missing-glyph horiz-adv-x="584" 
d="M109 802h584l-212 -1000h-584zM302 195l33 85q54 0 91.5 10t62.5 28t39 43.5t21 56.5q5 23 5 43q0 58 -47.5 96t-190.5 38q-45 0 -96 -5.5t-91 -13.5l-26 -124q48 9 95.5 14.5t89.5 5.5q40 0 63.5 -2t35 -7.5t13 -12t1.5 -10.5q0 -7 -2 -15q-3 -12 -7 -21t-13.5 -14
t-25.5 -7.5t-42 -2.5h-109l-15 -70l-5 -115h120zM278 0l33 153h-154l-33 -153h154z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="850" 
d="M49 0l91 430h-79l23 108l86 34l2 9q12 58 32 102t53 74t80.5 45.5t113.5 15.5q32 0 55 -2.5t50 -7.5l-31 -144q-15 2 -30 3t-31 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h198l2 9q12 58 32 102t52.5 74t80 45.5t114.5 15.5q36 0 62 -2.5t57 -7.5l-30 -144
q-19 2 -37.5 3t-38.5 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h159l-30 -142h-159l-91 -430h-187l91 430h-198l-91 -430h-187z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="1134" 
d="M45 0l91 430h-79l23 108l86 34l2 9q12 58 32 102t52.5 74t80 45.5t114.5 15.5q32 0 55 -2.5t50 -7.5l-31 -144q-15 2 -30.5 3t-32.5 1q-24 0 -41 -3.5t-29 -12t-19 -23.5t-12 -38l-4 -19h196l2 9q12 58 32 102t52.5 74t79.5 45.5t114 15.5q23 0 42 -2.5t35 -6.5l-30 -144
q-18 3 -36 3q-24 0 -41 -3.5t-29 -12t-19 -23.5t-12 -38l-4 -19h386l-122 -572h-187l92 430h-199l-91 -430h-187l92 430h-196l-91 -430h-187zM981 797h188l-30 -145h-189z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="1153" 
d="M45 0l91 430h-79l23 108l86 34l2 9q12 58 32 102t52.5 74t80 45.5t114.5 15.5q32 0 55 -2.5t50 -7.5l-31 -144q-15 2 -30 3t-31 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h198l2 9q12 58 32 102t52.5 74t80 45.5t114.5 15.5q29 0 50 -2.5t45 -7.5l-30 -144
q-23 4 -52 4q-25 0 -42.5 -3.5t-29.5 -12t-19 -23.5t-12 -38l-4 -19h135l-31 -142h-134l-91 -430h-188l92 430h-198l-91 -430h-187zM1001 802h187l-170 -802h-187z" />
    <glyph glyph-name="f_b" unicode="fb" horiz-adv-x="1200" 
d="M45 0l91 430h-79l23 108l86 34l2 9q12 58 32 102t52.5 74t80 45.5t114.5 15.5q29 0 50 -2.5t45 -7.5l-30 -144q-23 4 -52 4q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h136l-31 -142h-135l-91 -430h-187zM617 802h187l-52 -244q43 15 82 22.5t78 7.5
q70 0 123 -19t84.5 -58.5t37 -80t5.5 -62.5q0 -47 -12 -104q-17 -81 -47 -134.5t-71.5 -86t-94 -46t-115.5 -13.5q-57 0 -108 13.5t-106 37.5l-33 -35h-128zM852 437q-32 0 -65 -7.5t-66 -19.5l-52 -247q28 -13 58 -20t62 -7q40 0 69 7.5t49.5 24.5t34 45.5t22.5 70.5
q7 34 7 60q0 7 -1.5 25t-16 35.5t-40 25t-61.5 7.5z" />
    <glyph glyph-name="f_h" unicode="fh" horiz-adv-x="1212" 
d="M45 0l91 430h-79l23 108l86 34l2 9q12 58 32 102t52.5 74t80 45.5t114.5 15.5q29 0 50 -2.5t45 -7.5l-30 -144q-23 4 -52 4q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h136l-31 -142h-135l-91 -430h-187zM617 802h187l-53 -251q45 17 89 27t93 10
q135 0 180.5 -56t45.5 -125q0 -31 -7 -67l-73 -340h-186l71 338q4 17 4 31q0 25 -16 42t-70 17q-41 0 -82.5 -11.5t-83.5 -29.5l-82 -387h-187z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="751" 
d="M45 0l91 430h-78l22 108l86 34l2 9q12 58 32 102t52.5 74t80 45.5t113.5 15.5q23 0 42.5 -2.5t34.5 -6.5l-30 -144q-18 3 -35 3q-24 0 -41 -3.5t-29 -12t-19 -23.5t-12 -38l-4 -19h384l-121 -572h-187l91 430h-197l-91 -430h-187zM596 797h190l-31 -145h-189z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="750" 
d="M45 0l91 430h-79l23 108l86 34l2 9q12 58 32 102t52.5 74t80 45.5t113.5 15.5q23 0 42.5 -2.5t34.5 -6.5l-30 -144q-18 3 -35 3q-24 0 -41 -3.5t-29 -12t-19 -23.5t-12 -38l-4 -19h383l-120 -566q-12 -58 -30 -99.5t-46.5 -68.5t-70 -39.5t-100.5 -12.5q-33 0 -58.5 1.5
t-49.5 7.5l31 144q14 -2 29.5 -2.5t27.5 -0.5q35 0 53 14.5t26 55.5l91 424h-196l-91 -430h-187zM596 797h190l-31 -145h-189z" />
    <glyph glyph-name="f_k" unicode="fk" horiz-adv-x="1164" 
d="M45 0l91 430h-79l23 108l86 34l2 9q12 58 32 102t52.5 74t80 45.5t114.5 15.5q29 0 50 -2.5t45 -7.5l-29 -144q-13 2 -26 3t-27 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h136l-31 -142h-135l-91 -430h-187zM617 802h187l-100 -471l272 241h230l-309 -268
l191 -304h-232l-161 288l-61 -288h-187z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="768" 
d="M45 0l91 430h-79l23 108l86 34l2 9q12 58 32 102t52.5 74t80 45.5t114.5 15.5q29 0 50 -2.5t45 -7.5l-30 -144q-23 4 -52 4q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h136l-31 -142h-135l-91 -430h-187zM617 802h187l-170 -802h-187z" />
    <glyph glyph-name="f_f_b" unicode="ffb" horiz-adv-x="1584" 
d="M45 0l91 430h-79l23 108l86 34l2 9q12 58 32 102t52.5 74t80 45.5t114.5 15.5q32 0 55 -2.5t50 -7.5l-29 -144q-16 2 -31.5 3t-31.5 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h198l2 9q12 58 32 102t52.5 74t80 45.5t114.5 15.5q29 0 50 -2.5t45 -7.5
l-29 -144q-14 2 -26.5 3t-26.5 1q-25 0 -42.5 -3.5t-29.5 -12t-19 -23.5t-12 -38l-4 -19h135l-31 -142h-134l-91 -430h-188l92 430h-198l-91 -430h-187zM1001 802h187l-52 -245q84 31 160 31q70 0 123 -19t84.5 -58.5t37 -80t5.5 -62.5q0 -47 -12 -104q-17 -81 -47 -134.5
t-71.5 -86t-94 -46t-115.5 -13.5q-57 0 -108 13.5t-106 38.5l-33 -36h-128zM1236 437q-32 0 -65 -7.5t-66 -19.5l-52 -247q28 -13 58 -20t63 -7q39 0 67.5 7.5t49.5 24.5t34.5 45.5t22.5 70.5q7 34 7 60q0 7 -1.5 25t-16 35.5t-40 25t-61.5 7.5z" />
    <glyph glyph-name="f_f_h" unicode="ffh" horiz-adv-x="1597" 
d="M45 0l91 430h-78l22 108l86 34l2 9q12 58 32 102t52.5 74t80 45.5t114.5 15.5q32 0 55 -2.5t50 -7.5l-31 -144q-15 2 -30 3t-31 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h198l2 9q12 58 32 102t52.5 74t80 45.5t114.5 15.5q29 0 50 -2.5t45 -7.5l-30 -144
q-23 4 -52 4q-25 0 -42.5 -3.5t-29.5 -12t-19 -23.5t-12 -38l-4 -19h135l-30 -142h-135l-91 -430h-188l92 430h-198l-91 -430h-187zM1001 802h187l-53 -251q45 17 89 27t93 10q135 0 180.5 -55.5t45.5 -124.5q0 -31 -8 -68l-71 -340h-187l72 338q3 17 3 30q0 25 -15.5 42.5
t-70.5 17.5q-41 0 -82.5 -11.5t-83.5 -29.5l-82 -387h-187z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="1134" 
d="M45 0l91 430h-79l23 108l86 34l2 9q12 58 32 102t52.5 74t80 45.5t114.5 15.5q32 0 55 -2.5t50 -7.5l-31 -144q-15 2 -30 3t-31 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h196l2 9q12 58 32 102t52.5 74t79.5 45.5t114 15.5q23 0 42 -2.5t35 -6.5l-30 -144
q-16 3 -35 3q-24 0 -41.5 -3.5t-29 -12t-19 -23.5t-12.5 -38l-4 -19h386l-120 -566q-12 -58 -30 -99.5t-46.5 -68.5t-70 -39.5t-100.5 -12.5q-34 0 -59 1.5t-49 7.5l31 145q14 -2 29 -3t28 -1q35 0 53 15t26 55l91 424h-199l-91 -430h-187l92 430h-196l-91 -430h-187z
M981 797h189l-31 -145h-189z" />
    <glyph glyph-name="f_f_k" unicode="ffk" horiz-adv-x="1549" 
d="M45 0l91 430h-78l22 108l86 34l2 9q12 58 32 102t52.5 74t80 45.5t114.5 15.5q32 0 55 -2.5t50 -7.5l-29 -137q-16 2 -31.5 3t-31.5 1q-25 0 -42 -4t-29 -13.5t-19.5 -26t-12.5 -40.5l-4 -19h198l2 9q12 58 32 102t52.5 74t80 45.5t114.5 15.5q29 0 50 -2.5t45 -7.5
l-29 -137q-14 2 -26.5 3t-26.5 1q-25 0 -42.5 -4t-29 -13.5t-19 -26t-12.5 -40.5l-4 -19h135l-30 -142h-135l-91 -430h-188l92 430h-198l-91 -430h-187zM1001 802h187l-96 -454l259 224h238l-312 -269l194 -303h-217l-172 301l-64 -301h-187z" />
    <glyph glyph-name=".notdef" horiz-adv-x="584" 
d="M109 802h584l-212 -1000h-584zM302 195l33 85q54 0 91.5 10t62.5 28t39 43.5t21 56.5q5 23 5 43q0 58 -47.5 96t-190.5 38q-45 0 -96 -5.5t-91 -13.5l-26 -124q48 9 95.5 14.5t89.5 5.5q40 0 63.5 -2t35 -7.5t13 -12t1.5 -10.5q0 -7 -2 -15q-3 -12 -7 -21t-13.5 -14
t-25.5 -7.5t-42 -2.5h-109l-15 -70l-5 -115h120zM278 0l33 153h-154l-33 -153h154z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni000D" horiz-adv-x="321" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="285" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="380" 
d="M157 579l47 223h190l-48 -223l-87 -311h-146zM71 183h193l-39 -183h-192z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="613" 
d="M200 802h201l-93 -336h-158zM452 802h201l-93 -336h-158z" />
    <glyph glyph-name="numbersign" unicode="#" 
d="M81 190h-92l73 129h90l90 164h-91l73 129h89l105 190h146l-105 -190h124l104 190h147l-105 -190h92l-73 -129h-91l-90 -164h92l-73 -129h-91l-105 -190h-144l104 190h-123l-105 -190h-146zM421 319l91 164h-124l-90 -164h123z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="716" 
d="M60 229q64 -17 136 -30.5t144 -13.5q41 0 67 3t41.5 9.5t23 18t10.5 29.5q3 14 3 25q0 5 -1.5 14.5t-14 19t-38.5 15.5t-69 11q-75 9 -127.5 24t-82.5 42.5t-35.5 58t-5.5 50.5q0 33 8 74q9 43 28 76.5t51 57.5t79.5 38t111.5 18l20 93h152l-20 -95q48 -4 92 -12t87 -20
l-30 -157q-69 17 -129 26.5t-125 9.5q-38 0 -64 -2t-42.5 -8t-25 -16t-11.5 -27q-3 -14 -3 -24q0 -5 1.5 -13t15 -16t39.5 -12.5t69 -10.5q79 -10 132 -26.5t82.5 -45.5t33.5 -57.5t4 -43.5q0 -35 -10 -81t-27.5 -81.5t-49 -60.5t-79 -39.5t-116.5 -17.5l-20 -92h-152l20 95
q-120 11 -205 41z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="907" 
d="M267 467q-102 0 -137.5 35.5t-35.5 91.5q0 29 7 65q9 44 26 74t43 49t62.5 27.5t85.5 8.5q51 0 88 -10t58 -32.5t24.5 -47t3.5 -39.5q0 -28 -7 -64q-9 -43 -26 -73.5t-43 -49t-62.5 -27t-86.5 -8.5zM178 0h-182l784 802h184zM279 568q34 0 48.5 16t22.5 58q6 23 6 39
q0 12 -6 23.5t-43 11.5q-34 0 -48.5 -16t-22.5 -58q-6 -23 -6 -39q0 -12 6 -23.5t43 -11.5zM640 -15q-102 0 -137.5 35.5t-35.5 91.5q0 28 7 64q18 87 68.5 123.5t148.5 36.5q51 0 87.5 -10t58 -33t25 -47.5t3.5 -38.5q0 -28 -7 -64q-9 -43 -26 -73.5t-43 -49t-62.5 -27
t-86.5 -8.5zM652 86q34 0 48.5 16t22.5 57q5 23 5 39q0 13 -5.5 24.5t-43.5 11.5q-34 0 -48 -16t-23 -59q-5 -22 -5 -37q0 -13 6 -24.5t43 -11.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="802" 
d="M791 402q-16 -43 -39 -89q-20 -40 -49 -88t-67 -96l65 -65l-116 -58l-356 328q-5 -2 -9 -4l-8 -4q-8 -7 -15.5 -14.5t-12.5 -18.5q-12 -19 -18 -51q-2 -14 -2.5 -22.5t-0.5 -12.5q0 -7 1 -14q6 -25 27.5 -34.5t43.5 -13.5q17 -2 37 -3.5t44 -1.5q21 0 42 0.5t41 4.5
q1 0 1.5 0.5t2.5 0.5l115 -111q-16 -6 -33 -11.5t-37 -9.5q-3 -1 -5.5 -1.5t-5.5 -0.5q-64 -12 -153 -12q-23 0 -52 0.5t-59 4t-59.5 11t-54.5 20.5t-43.5 32.5t-26.5 47.5q-7 21 -7 47q0 27 7 58q13 69 49.5 118t107.5 66l-45 40l-11 12q-23 32 -23 83q0 26 6 57
q4 18 9.5 34.5t11.5 31.5q4 8 7 15t8 13q21 36 56.5 57.5t77 33t85 15.5t81.5 5q31 -1 72 -4t79 -14t67.5 -33t38.5 -60q2 -6 2.5 -13t0.5 -15q1 -10 1 -19q0 -23 -5 -47q-10 -48 -28 -82t-44 -58l1 -2l-13 -8q-9 -7 -24.5 -17t-36 -20.5t-46.5 -19.5t-56 -15l-79 68
q57 14 88.5 36.5t47.5 43.5q19 24 24 51q3 14 3 25t-3 20q-4 19 -23 31q-21 11 -45 14.5t-48 5.5q-26 -2 -51.5 -5.5t-48.5 -14.5q-24 -13 -35 -31q-7 -9 -11 -20t-7 -25l-1 -7v-7t-1 -9q0 -3 1.5 -10t4.5 -15.5t8.5 -18t15.5 -16.5l63 -53l77 -67l136 -138q12 14 26 33
t27 41t24.5 46.5t18.5 48.5h161z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="380" 
d="M200 802h201l-93 -336h-158z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="411" 
d="M129 -146q-23 46 -41.5 99t-27 109.5t-8.5 112.5q0 15 1.5 59.5t19.5 131.5q29 135 92.5 242t147.5 194h183q-40 -42 -77.5 -95.5t-69.5 -114.5t-57.5 -128t-40.5 -136q-17 -76 -19 -120.5t-2 -64.5q0 -46 6 -97t22 -101.5t37 -90.5h-166z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="411" 
d="M302 802q23 -46 41.5 -99t26.5 -109t8 -112q0 -16 -1.5 -60.5t-19.5 -131.5q-29 -136 -92 -242t-148 -194h-183q40 42 77.5 95.5t69.5 114.5t57.5 128t40.5 136q17 76 19 120.5t2 64.5q0 46 -6 97t-22 101.5t-37 90.5h167z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="515" 
d="M310 496l-121 -167l-94 81l139 132l-149 38l53 123l141 -78l21 177h137l-67 -185l158 86l32 -132l-172 -34l93 -138l-114 -69z" />
    <glyph glyph-name="plus" unicode="+" 
d="M126 361h193l45 211h154l-45 -211h195l-32 -150h-195l-45 -211h-154l45 211h-193z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="380" 
d="M114 186h205l-201 -337h-171z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="380" 
d="M17 209l32 155h318l-33 -155h-317z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="380" 
d="M73 183h192l-38 -183h-192z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="474" 
d="M387 802h188l-469 -948h-186z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="716" 
d="M452 818q89 0 149 -25t91.5 -79t33 -101t1.5 -56q0 -79 -22 -186q-21 -96 -50 -168.5t-74 -121t-110 -73t-158 -24.5q-89 0 -149 25t-91.5 79t-33 101t-1.5 56q0 79 22 186q20 96 49 168.5t74 121t110.5 73t158.5 24.5zM331 144q40 0 68 12t48.5 41.5t36.5 79t32 124.5
t19.5 108.5t3.5 48.5q0 28 -7.5 52t-31.5 36t-66 12q-40 0 -68 -12t-48.5 -41.5t-36.5 -79t-32 -124.5t-19.5 -108.5t-3.5 -48.5q0 -28 7.5 -52t31.5 -36t66 -12z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="604" 
d="M422 802h188l-170 -802h-191l126 593l-226 -104l-54 161z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="716" 
d="M448 818q166 0 228.5 -49.5t62.5 -127.5q0 -30 -8 -65q-10 -41 -23 -69.5t-36.5 -51.5t-60 -45t-92.5 -51l-231 -119q-31 -17 -43 -30.5t-17 -37.5l-3 -15h411l-33 -157h-604l42 194q7 33 18.5 58.5t30.5 46.5t46.5 39.5t65.5 37.5l241 121q29 15 46.5 24.5t27.5 18
t14.5 17t7.5 21.5q3 12 3 22q0 26 -26 40t-107 14q-30 0 -63 -2t-66 -6t-64.5 -8.5t-58.5 -9.5l35 163q27 6 55 11t59 8.5t65.5 5.5t76.5 2z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="716" 
d="M32 171q60 -14 116 -21.5t125 -7.5q57 0 93 4t57.5 14t31.5 27t16 42q5 20 5 36q0 29 -20 46.5t-87 17.5h-217l32 157h216q59 0 89 15t40 61q4 18 4 32q0 8 -2.5 21t-18.5 24.5t-46 16.5t-77 5q-39 0 -71 -1.5t-61 -4.5t-57.5 -8t-59.5 -11l33 155q46 11 111.5 19
t144.5 8q92 0 155.5 -15t100 -46t43.5 -64.5t7 -55.5q0 -32 -8 -70q-17 -77 -58 -114.5t-104 -53.5q61 -26 81 -67.5t20 -81.5q0 -29 -8 -63q-12 -56 -37 -95t-70 -63t-113.5 -34.5t-167.5 -10.5q-92 0 -159 10t-112 21z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="716" 
d="M703 456l-97 -456h-188l33 153h-449l31 147l372 502h216l-367 -494h230l31 148h188z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="716" 
d="M33 177q121 -29 260 -29q47 0 79 4t52.5 14t31.5 28t17 46q4 17 4 32q0 34 -24.5 53t-101.5 19h-258l97 458h549l-33 -158h-358l-30 -142h73q172 0 227.5 -57t55.5 -140q0 -38 -10 -85q-15 -76 -46 -122.5t-79.5 -72t-115.5 -33.5t-155 -8q-42 0 -81.5 3t-74.5 7.5
t-64 10t-49 10.5z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="716" 
d="M334 -16q-98 0 -162 25t-97 78.5t-34.5 99.5t-1.5 55q0 77 22 181q23 108 55.5 183t81 122t116.5 68.5t162 21.5q81 0 143.5 -9.5t104.5 -23.5l-34 -160q-48 12 -100.5 20.5t-112.5 8.5q-46 0 -79 -7t-57.5 -25.5t-41 -50t-29.5 -80.5q32 11 69 18t80 7q150 0 203 -60.5
t53 -142.5q0 -39 -10 -87q-14 -66 -40 -112.5t-66.5 -75t-96 -41.5t-128.5 -13zM369 358q-37 0 -69 -6t-63 -17q-10 -56 -10.5 -76t-0.5 -23q0 -34 11 -56t37 -31.5t68 -9.5q60 0 90 24t43 85q5 22 5 39q0 8 -2.5 22.5t-15.5 26.5t-36.5 17t-56.5 5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="716" 
d="M148 802h640l-31 -147l-493 -655h-239l509 644h-420z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="716" 
d="M319 -16q-82 0 -145.5 12.5t-103.5 41.5t-51 64.5t-11 64.5t8 65q16 75 59 119t110 63q-51 22 -68 57.5t-17 71.5q0 32 9 72q11 53 36 91.5t66 63.5t99.5 36.5t137.5 11.5t140 -12t100 -39.5t50 -60.5t11 -61q0 -27 -7 -58q-17 -79 -57.5 -123.5t-102.5 -66.5
q29 -10 50 -26t33 -40.5t13 -44t1 -24.5q0 -30 -8 -69q-12 -55 -38 -94.5t-69 -65t-103.5 -37.5t-141.5 -12zM402 477q35 0 59.5 5t41 15.5t26.5 28.5t15 43q4 17 4 31q0 5 -1.5 16t-13.5 22.5t-35.5 16.5t-61.5 5q-36 0 -61 -5t-41.5 -16.5t-26 -28.5t-15.5 -41
q-3 -15 -3 -28q0 -29 19.5 -46.5t93.5 -17.5zM330 141q74 0 109 22t46 75q4 17 4 31q0 32 -23 50.5t-99 18.5q-70 0 -106.5 -22t-48.5 -78q-4 -17 -4 -30q0 -30 22 -48.5t100 -18.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="716" 
d="M424 818q98 0 162 -25t97 -78t34.5 -99t1.5 -56q0 -77 -22 -181q-23 -108 -55.5 -183t-81.5 -122t-116.5 -68.5t-161.5 -21.5q-81 0 -144 9.5t-104 23.5l34 160q48 -12 100 -20.5t112 -8.5q46 0 79.5 7t57.5 25.5t41 50t29 80.5q-32 -11 -69 -18t-80 -7q-149 0 -202 60.5
t-53 142.5q0 39 10 87q14 66 39.5 112.5t66 75t96.5 41.5t129 13zM283 554q-5 -23 -5 -40q0 -34 22 -52t89 -18q37 0 69 6t63 17q11 56 11.5 75.5t0.5 21.5q0 35 -11.5 57.5t-37.5 32t-68 9.5q-60 0 -90.5 -24t-42.5 -85z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="380" 
d="M165 571h192l-38 -183h-192zM82 183h192l-38 -183h-192z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="362" 
d="M156 571h192l-38 -183h-192zM95 186h205l-201 -337h-171z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M130 385l581 169l-35 -164l-390 -110l343 -112l-32 -149l-511 163z" />
    <glyph glyph-name="equal" unicode="=" 
d="M155 496h542l-32 -150h-542zM98 228h542l-33 -151h-542z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M633 188l-580 -169l34 164l390 111l-343 111l32 149l511 -163z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="669" 
d="M176 402l20 94h144q51 0 84 4t53 13.5t29.5 25t13.5 37.5q3 15 3 27q0 9 -2.5 21t-20.5 22.5t-53 15t-91 4.5q-60 0 -126 -7.5t-130 -21.5l31 154q27 6 59 11t65.5 8.5t67.5 5.5t65 2q188 0 255.5 -52t67.5 -133q0 -26 -6 -56q-11 -57 -35 -96.5t-64 -64.5t-97 -36
t-134 -11h-17l-40 -101h-147zM131 183h192l-39 -183h-191z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="928" 
d="M373 -128q-95 0 -166.5 24.5t-113.5 79.5t-49 111.5t-7 85.5q0 70 19 160q20 94 51.5 163.5t85.5 116t134.5 69.5t198.5 23q221 0 296.5 -84t75.5 -202q0 -59 -15 -130l-37 -176q-17 -79 -55.5 -112.5t-113.5 -33.5q-88 0 -133.5 32.5t-45.5 91.5v8q-46 -20 -111 -20
q-34 0 -63.5 8t-49 25.5t-25.5 38.5t-6 37q0 18 5 39q11 56 47 84t112 28h148l2 9q2 7 2 13q0 3 -1 8.5t-11.5 10.5t-30.5 7.5t-55 2.5q-38 0 -75.5 -3.5t-79.5 -9.5l23 106q21 4 38.5 6.5t36 4.5t39.5 3t48 1q66 0 108 -10.5t64 -31t25 -41t3 -32.5q0 -23 -6 -50l-46 -218
q-2 -9 -2 -16q0 -12 6.5 -21.5t32.5 -9.5q21 0 31 9.5t14 27.5l39 184q15 75 15 128q0 10 -2 44t-32 69t-87.5 49t-146.5 14q-79 0 -134.5 -14.5t-93 -47.5t-61 -85.5t-40.5 -128.5q-17 -82 -17 -140q0 -12 2.5 -50t31.5 -75.5t82.5 -52t131.5 -14.5q17 0 36 1t36 3
l-25 -111q-33 -6 -83 -6zM418 175q58 0 101 23l13 52h-99q-25 0 -36 -8t-16 -28q-2 -8 -2 -14q0 -10 7 -17.5t32 -7.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="905" 
d="M406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31l-159 -293h225z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="859" 
d="M195 802h380q84 0 142.5 -13t92.5 -43t40.5 -63t6.5 -54q0 -35 -10 -78q-28 -119 -153 -154q29 -10 52 -26.5t37 -41t16 -45t2 -30.5q0 -24 -6 -54q-13 -59 -38.5 -98t-66.5 -61.5t-98 -31.5t-132 -9h-435zM356 636l-35 -163h211q52 0 79 16.5t37 65.5q4 18 4 31
q0 25 -16.5 37.5t-70.5 12.5h-209zM292 333l-36 -167h206q33 0 57.5 3.5t41.5 13t27.5 25.5t15.5 41q4 17 4 30q0 25 -17.5 39.5t-66.5 14.5h-232z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="851" 
d="M530 818q91 0 156.5 -19.5t108 -55.5t63 -87t21.5 -114h-205q-8 53 -45.5 79t-117.5 26q-58 0 -97.5 -13.5t-67.5 -43.5t-46 -76.5t-32 -112.5t-14.5 -91t-0.5 -27q0 -44 16.5 -72.5t54.5 -42t101 -13.5q38 0 66.5 6t50.5 19t39 33t32 47h206q-25 -70 -59.5 -121.5
t-83 -86t-114.5 -51.5t-156 -17q-114 0 -189 25.5t-114.5 79.5t-42.5 103.5t-3 65.5q0 73 20 168q21 98 55 171t87 122.5t129 74t182 24.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="893" 
d="M25 1l170 801h309q120 0 200 -25t123.5 -76.5t49.5 -104t6 -79.5q0 -62 -15 -140q-22 -96 -56 -167t-86.5 -117.5t-126.5 -69t-176 -22.5h-398zM424 170q62 0 104 13t70.5 41t46 72t30.5 105q13 62 13 105.5t-19 72t-59 41.5t-106 13h-149l-99 -463h168z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="777" 
d="M195 802h637l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441l-35 -167h-637z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="745" 
d="M195 802h626l-35 -169h-431l-35 -166h393l-36 -169h-393l-64 -298h-195z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="912" 
d="M550 818q96 0 165 -20t114 -56t66.5 -87t22.5 -113h-204q-10 51 -51.5 78t-130.5 27q-63 0 -106.5 -14t-74 -44t-50 -76.5t-33.5 -111.5q-14 -66 -14 -112.5t19 -76.5t60.5 -43.5t110.5 -13.5q54 0 93.5 7.5t66.5 24t43.5 42.5t25.5 63h-241l34 164h437l-38 -178
q-15 -72 -46.5 -126.5t-84.5 -92t-129 -56.5t-180 -19q-114 0 -192 25.5t-121 79.5t-48.5 107t-5.5 76q0 70 19 157q21 96 56 168.5t91 121.5t135.5 74t190.5 25z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="947" 
d="M195 802h196l-67 -313h387l66 313h196l-171 -802h-195l68 320h-387l-68 -320h-195z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="365" 
d="M195 802h196l-171 -802h-195z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="471" 
d="M86 -14q-37 0 -62.5 1.5t-57.5 9.5l33 161q20 -2 41 -3t41 -1q27 0 45 4t30.5 13.5t20 25t12.5 37.5l120 568h195l-121 -572q-15 -71 -36 -118t-54 -75t-83 -39.5t-124 -11.5z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="863" 
d="M195 802h196l-68 -321l368 321h255l-447 -386l283 -416h-226l-253 389l-83 -389h-195z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="657" 
d="M195 802h196l-136 -638h346l-34 -164h-542z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1094" 
d="M195 802h273l78 -444h10l267 444h297l-171 -802h-195l119 557l-261 -421h-190l-82 425l-120 -561h-195z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="954" 
d="M195 802h226l247 -545l116 545h195l-170 -802h-232l-244 534l-113 -534h-195z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="924" 
d="M417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q115 0 191.5 -27t118 -82t46 -106.5t4.5 -71.5q0 -71 -20 -161q-21 -99 -56.5 -171.5t-92.5 -120t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5
t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="851" 
d="M195 802h378q94 0 158.5 -17t101 -55t42 -78.5t5.5 -62.5q0 -47 -12 -107q-16 -70 -41.5 -121t-66 -84t-98.5 -48.5t-139 -15.5h-258l-45 -213h-195zM356 639l-56 -263h222q33 0 57.5 5.5t43 19.5t31.5 39.5t21 65.5q7 31 7 54q0 6 -1.5 21.5t-15.5 30.5t-39.5 21
t-62.5 6h-207z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="924" 
d="M417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q115 0 191.5 -27t118 -82t46 -106.5t4.5 -71.5q0 -71 -20 -161q-21 -100 -57 -172t-93 -120l41 -74l-137 -77l-45 79q-79 -22 -189 -22zM439 155
q46 0 80 7l-66 116l138 78l60 -108q18 29 31 66.5t24 86.5q14 66 14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="881" 
d="M195 802h378q90 0 154 -17t101.5 -55t45 -80t7.5 -69q0 -43 -11 -97q-24 -103 -70 -164t-130 -85l89 -235h-206l-72 217h-215l-46 -217h-195zM356 639l-55 -259h221q33 0 58 5t43.5 19.5t31.5 39.5t21 64q6 30 6 52q0 6 -1.5 22t-16.5 30.5t-42 20.5t-67 6h-199z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="825" 
d="M58 199q78 -17 163 -32t169 -15q44 0 73 3.5t48 11.5t29 21.5t14 32.5q3 15 3 26q0 6 -1.5 16.5t-16.5 22t-45 20t-80 16.5q-84 12 -143.5 30t-94.5 49.5t-41.5 65.5t-6.5 56q0 37 10 83q10 47 33 86t66.5 67t112.5 43.5t171 15.5q82 0 157.5 -8t156.5 -28l-36 -170
q-85 19 -155.5 28.5t-145.5 9.5q-83 0 -120.5 -13t-45.5 -50q-3 -14 -3 -24q0 -8 2.5 -18t18.5 -19t46 -16t78 -15q85 -13 145.5 -30.5t95.5 -49t42 -66t7 -56.5q0 -38 -11 -87q-11 -51 -35.5 -92.5t-68.5 -70t-112.5 -44t-168.5 -15.5q-90 0 -182 12t-164 33z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="799" 
d="M376 633h-271l35 169h739l-36 -169h-271l-135 -633h-195z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="923" 
d="M421 -16q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64q0 58 16 133l94 446h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h196l-96 -449q-22 -104 -52 -175t-79.5 -114t-124.5 -61.5t-187 -18.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="905" 
d="M127 802h199l78 -637h31l346 637h218l-451 -802h-313z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1337" 
d="M134 802h202l3 -638h15l253 638h330l-19 -638h16l274 638h214l-354 -802h-328l11 637h-15l-260 -637h-328z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="903" 
d="M-35 0l382 414l-202 388h228l121 -257l226 257h256l-373 -405l206 -397h-220l-135 268l-233 -268h-256z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="856" 
d="M335 313l-225 489h209l141 -313l275 313h229l-434 -489l-66 -313h-195z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="787" 
d="M13 163l515 470h-398l36 169h676l-35 -164l-512 -469h430l-36 -169h-711z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="411" 
d="M174 802h316l-31 -146h-140l-140 -656h141l-31 -146h-317z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="474" 
d="M122 802h174l77 -948h-175z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="411" 
d="M256 -146h-315l31 146h139l139 656h-139l31 146h315z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M391 802h197l91 -510h-150l-63 352l-213 -352h-168z" />
    <glyph glyph-name="underscore" unicode="_" 
d="M-102 -198l29 138h760l-29 -138h-760z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="570" 
d="M225 802h200l104 -170h-165z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="768" 
d="M289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25
t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="768" 
d="M185 802h187l-52 -244q43 15 81.5 22.5t78.5 7.5q70 0 123 -19t84.5 -58.5t37 -80t5.5 -62.5q0 -47 -12 -104q-17 -81 -47 -134.5t-71.5 -86t-94 -46t-115.5 -13.5q-57 0 -108 13.5t-106 38.5l-33 -36h-128zM420 437q-32 0 -65 -7.5t-66 -19.5l-52 -247q28 -13 58 -20
t62 -7q40 0 69 7t49.5 24.5t34 46t22.5 70.5q7 34 7 60q0 7 -1.5 25t-15.5 35.5t-40 25t-62 7.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="650" 
d="M416 433q-43 0 -73 -7t-50.5 -23.5t-33 -44.5t-21.5 -70q-8 -38 -8 -64q0 -4 1 -20.5t15 -33.5t41 -24t67 -7q27 0 51.5 1t50.5 3.5t54.5 6t62.5 8.5l-30 -147q-55 -14 -107 -20.5t-106 -6.5q-86 0 -147.5 16.5t-96.5 55t-41 80t-6 64.5q0 49 13 111q17 76 47 129t75 86
t106 47.5t140 14.5q62 0 118 -7t113 -20l-30 -148q-31 5 -57 9t-50.5 6.5t-48 3.5t-49.5 1z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="768" 
d="M289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q48 0 92.5 -10t90.5 -28l54 252h187l-170 -802h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25
t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-29 12 -58.5 19.5t-62.5 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="699" 
d="M403 588q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139q-65 -15 -123.5 -21t-122.5 -6q-87 0 -148.5 16.5t-96.5 55t-41 80t-6 64.5q0 49 13 111
q17 77 47 129.5t73.5 85.5t101 47.5t130.5 14.5zM395 449q-29 0 -51.5 -5t-39 -16.5t-29 -30.5t-22.5 -47h239q1 10 1 18q0 40 -21 60.5t-77 20.5z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="466" 
d="M140 430h-79l23 108l86 34l2 9q12 58 32.5 102t53 74t79.5 45.5t114 15.5q36 0 62 -2.5t57 -7.5l-29 -144q-19 2 -38 3t-39 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h160l-30 -142h-160l-91 -430h-187z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="759" 
d="M286 8q-69 0 -121 18t-82.5 56.5t-36 77.5t-5.5 60q0 45 12 100q16 77 45.5 128.5t70.5 82.5t93 44t114 13q56 0 106.5 -13.5t104.5 -36.5l31 34h129l-113 -529q-16 -75 -44 -124t-71.5 -78t-103 -41t-137.5 -12q-34 0 -65.5 1t-64.5 3t-68.5 6.5t-77.5 11.5l30 142
q44 -7 79 -11t64 -6.5t53.5 -3t46.5 -0.5q47 0 77.5 6.5t49.5 19.5t28.5 32t14.5 44l1 5q-42 -15 -81 -22.5t-79 -7.5zM238 299q-6 -30 -6 -53q0 -39 22 -63t92 -24q32 0 65.5 7.5t66.5 19.5l48 225q-28 12 -56.5 19t-60.5 7q-39 0 -67.5 -7t-48.5 -23t-33.5 -42.5
t-21.5 -65.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="780" 
d="M185 802h187l-53 -251q45 17 89.5 27t93.5 10q135 0 180 -55.5t45 -124.5q0 -32 -7 -68l-72 -340h-187l71 338q4 18 4 32q0 24 -15.5 41t-70.5 17q-40 0 -81.5 -9.5t-83.5 -26.5l-83 -392h-187z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="338" 
d="M183 797h190l-31 -145h-190zM137 572h187l-121 -572h-187z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="337" 
d="M183 797h190l-31 -145h-190zM-121 -61q14 -2 29 -2.5t28 -0.5q35 0 53.5 15t26.5 55l120 566h187l-120 -566q-12 -58 -29.5 -99.5t-46 -68.5t-70 -39.5t-101.5 -12.5q-33 0 -58 1.5t-49 7.5z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="732" 
d="M185 802h187l-96 -454l259 224h236l-312 -269l194 -303h-215l-172 301l-64 -301h-187z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="337" 
d="M185 802h187l-170 -802h-187z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1173" 
d="M283 535q50 24 97 38.5t101 14.5q72 0 118.5 -18t71.5 -51q59 31 112.5 50t116.5 19q70 0 117 -17t72.5 -49.5t28.5 -62.5t3 -44q0 -35 -8 -76l-72 -339h-187l72 338q4 19 4 33q0 23 -12.5 40t-60.5 17q-39 0 -78.5 -12t-79.5 -31q-1 -11 -3 -22l-4 -22l-71 -341h-188
l72 338q4 19 4 34q0 22 -12 39t-60 17q-38 0 -76.5 -11.5t-76.5 -29.5l-82 -387h-187l122 572h128z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="780" 
d="M282 535q54 25 107 39t113 14q135 0 180.5 -54.5t45.5 -124.5q0 -32 -8 -69l-72 -340h-187l71 338q4 17 4 31q0 25 -16 42t-70 17q-42 0 -83 -11.5t-83 -29.5l-82 -387h-187l121 572h128z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="738" 
d="M324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q84 0 144.5 -17t96 -56t41.5 -80t6 -64q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48t-143.5 -15zM341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5
q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="768" 
d="M136 572h128l15 -30q55 23 103.5 34.5t97.5 11.5q70 0 123 -19t84.5 -58.5t37 -80t5.5 -62.5q0 -47 -12 -104q-17 -81 -47 -134.5t-71.5 -86t-94 -46t-115.5 -13.5q-49 0 -93 10t-90 29l-47 -221h-187zM420 437q-32 0 -65.5 -7.5t-66.5 -19.5l-52 -247q28 -13 58.5 -20.5
t62.5 -7.5q40 0 69 7.5t49.5 24.5t34 46t22.5 71q7 34 7 60q0 7 -1.5 25t-15.5 35.5t-40 25t-62 7.5z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="768" 
d="M290 -16q-70 0 -123 19t-84.5 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-163 -770h-187l45 212q-42 -15 -81 -22.5t-78 -7.5zM237 288q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25
t61.5 -7.5q32 0 65.5 7t65.5 20l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-49.5 -24.5t-34.5 -46t-23 -71z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="465" 
d="M136 572h128l17 -35q53 26 103.5 37t113.5 12l-33 -164q-48 -2 -92 -9t-88 -20l-83 -393h-187z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="679" 
d="M34 155q64 -14 136 -24t138 -10q35 0 57.5 1.5t36 5.5t20 10.5t8.5 16.5q2 8 2 14t-2.5 12.5t-14.5 13t-34.5 11t-58.5 8.5q-73 8 -123 20t-78 36t-33 50.5t-5 43.5q0 28 7 64q8 37 25 66.5t50.5 50.5t88 32t137.5 11q135 0 274 -27l-29 -138q-69 14 -129.5 21t-117.5 7
q-35 0 -57.5 -2t-36 -6t-20 -10.5t-8.5 -16.5q-2 -8 -2 -14q0 -5 2 -11t13.5 -11.5t34 -9t60.5 -8.5q73 -8 123 -21t78.5 -37t33.5 -50.5t5 -43.5q0 -28 -7 -63q-8 -38 -25.5 -68t-51 -51t-88.5 -32.5t-138 -11.5q-37 0 -77 2t-79.5 6t-76.5 10.5t-67 14.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="487" 
d="M283 -8q-72 0 -117 14t-67.5 42t-24.5 53.5t-2 36.5q0 34 9 77l46 215h-78l23 108l86 34l31 145h187l-31 -145h160l-30 -142h-160l-47 -219q-4 -16 -4 -28q0 -18 11.5 -29.5t60.5 -11.5q20 0 39 1t39 3l-30 -145q-21 -4 -46.5 -6.5t-54.5 -2.5z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="780" 
d="M280 -16q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h187l-121 -572h-129l-18 37q-54 -25 -107 -39t-112 -14z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="716" 
d="M71 572h188l55 -419h11l234 419h208l-326 -572h-287z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1085" 
d="M84 572h189l-11 -420h7l188 420h277l7 -420l8 1l168 419h207l-249 -572h-285l-13 419h-11l-192 -419h-284z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="712" 
d="M-54 0l308 296l-172 276h202l92 -156l151 156h229l-298 -287l182 -285h-209l-96 165l-160 -165h-229z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="715" 
d="M71 572h189l53 -407h20l226 406l208 1l-339 -594q-31 -56 -59.5 -92.5t-63 -59t-78.5 -31.5t-107 -9q-33 0 -65.5 2.5t-65.5 7.5l31 146q20 -3 41.5 -4.5t40.5 -1.5q33 0 55 2.5t36 8t23.5 15t16.5 23.5l16 28h-95z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="664" 
d="M12 129l357 301h-281l31 142h549l-28 -129l-361 -301h310l-30 -142h-575z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="411" 
d="M289 -146h-61q-54 0 -95 10t-66.5 35t-30.5 53.5t-5 46.5q0 33 9 75l28 133q2 11 2 19q0 11 -5 20.5t-34 9.5h-29l31 145h31q27 0 38.5 10.5t16.5 35.5l38 180q11 51 27.5 85t42 54t62.5 28t88 8h113l-31 -146h-68q-29 0 -42 -9t-20 -40l-29 -138q-12 -59 -36 -96
t-70 -55q35 -19 43 -44t8 -45t-5 -42l-28 -129q-4 -17 -4 -28q0 -14 7.5 -21.5t40.5 -7.5h64z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="365" 
d="M207 802h173l-202 -948h-173z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="411" 
d="M273 29q-11 -51 -27.5 -85t-42 -54t-62 -28t-87.5 -8h-113l30 146h67q30 0 43 9.5t20 40.5l29 137q13 59 36.5 96t68.5 56q-33 19 -41.5 44t-8.5 46q0 19 5 40l28 129q4 19 4 30.5t-5.5 17.5t-15.5 8t-27 2h-63l31 146h61q54 0 95 -10t66 -35t30 -53.5t5 -46.5
q0 -33 -9 -75l-28 -133q-2 -10 -2 -19q0 -10 5 -19.5t34 -9.5h29l-31 -145h-30q-28 0 -39.5 -10.5t-16.5 -35.5z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M47 247q10 51 27.5 85.5t42 55.5t56.5 29.5t70 8.5q50 0 90 -18t74.5 -39.5t64 -39.5t56.5 -18q29 0 41.5 22t19.5 65l4 20h139l-14 -77q-17 -92 -67.5 -131.5t-126.5 -39.5q-49 0 -89.5 18t-75 40t-63.5 40t-56 18q-30 0 -42.5 -22t-19.5 -65l-4 -21h-139z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="285" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="380" 
d="M300 389h-193l39 183h192zM214 -7l-47 -223h-190l48 223l87 311h146z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="716" 
d="M603 802l-22 -102q36 -3 72 -8.5t72 -13.5l-33 -159q-36 6 -66.5 10t-58 7t-54.5 4t-56 1q-45 0 -73.5 -7t-47 -23t-29.5 -41.5t-19 -63.5q-9 -41 -9 -68.5t13 -44t39.5 -23.5t71.5 -7q30 0 57.5 1t57 3t61.5 5t71 8l-33 -153q-42 -11 -81.5 -16.5t-81.5 -7.5l-22 -103
h-151l22 104q-65 6 -111.5 27.5t-73 60.5t-30 75.5t-3.5 53.5q0 48 12 108q15 74 42 125t67.5 83t96 48t127.5 19l21 98h152z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="716" 
d="M71 156q38 0 58 17t30 62l20 95h-129l32 147h128l21 100q13 62 34 107t57 75t90.5 44.5t133.5 14.5q70 0 124 -8.5t90 -18.5l-33 -157q-40 11 -79.5 16.5t-95.5 5.5q-65 0 -96.5 -20t-41.5 -67l-20 -92h275l-31 -147h-275l-16 -76q-14 -62 -54 -98h352l-33 -156h-631
l32 156h58z" />
    <glyph glyph-name="currency" unicode="&#xa4;" 
d="M67 143l123 105q-7 22 -7 51q0 30 8 67q13 64 39 106l-79 106l97 76l81 -108q20 6 42 8.5t48 2.5q61 0 104 -15l131 112l77 -86l-128 -110q4 -19 4 -42q0 -30 -7 -67q-13 -67 -40 -110l79 -104l-100 -77l-81 108q-36 -10 -88 -10q-59 0 -98 13l-129 -111zM374 262
q50 0 73 21t34 73q5 24 5 42q0 22 -11 38t-60 16q-48 0 -71 -21.5t-35 -74.5q-5 -23 -5 -40q0 -21 11 -37.5t59 -16.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="716" 
d="M252 234h-198l28 132h198l15 74h-198l28 132h112l-112 230h192l95 -213l192 213h208l-212 -230h114l-28 -132h-199l-16 -74h199l-28 -132h-199l-50 -234h-191z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="365" 
d="M128 432l79 370h173l-79 -370h-173zM84 224h173l-79 -370h-173z" />
    <glyph glyph-name="section" unicode="&#xa7;" 
d="M35 -3q63 -21 130.5 -34t135.5 -13t96 12.5t35 46.5q3 13 3 24q0 5 -1.5 13.5t-12.5 17.5t-32.5 16t-56.5 14l-86 18q-49 9 -86 24.5t-59.5 40t-28 49t-5.5 41.5q0 23 6 50q8 41 35 75t72 57q-23 30 -27 58t-4 43q0 33 9 73q10 46 29 82.5t53.5 61.5t87.5 38t129 13
q74 0 147 -10t130 -27l-34 -162q-55 17 -115.5 26t-121.5 9q-33 0 -55.5 -2.5t-37 -9.5t-22.5 -18t-11 -28q-3 -13 -3 -22q0 -5 1.5 -13.5t12 -17.5t32 -15.5t56.5 -13.5l87 -18q49 -9 86 -24.5t59.5 -40t28 -49t5.5 -41.5q0 -23 -6 -50q-8 -41 -35.5 -75t-72.5 -57
q31 -39 31 -102q0 -34 -9 -74q-10 -47 -29 -83.5t-54 -61.5t-88.5 -38.5t-131.5 -13.5q-81 0 -162.5 12.5t-143.5 34.5zM450 230q35 -7 63 -18q20 20 24 45q4 17 4 31q0 23 -14 38.5t-56 23.5l-152 28q-18 4 -34 8t-30 10q-19 -19 -24 -45q-4 -17 -4 -30q0 -23 14.5 -38.5
t56.5 -23.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="570" 
d="M216 791h141l-31 -146h-141zM429 791h141l-30 -146h-141z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="924" 
d="M75 424q21 102 54 176t87.5 122.5t135 72t196.5 23.5q114 0 192.5 -25.5t122 -79.5t49.5 -107.5t6 -79.5q0 -65 -17 -147q-21 -102 -55.5 -176.5t-90 -123t-135.5 -72t-193 -23.5q-117 0 -196 25.5t-121.5 79.5t-47.5 106t-5 75q0 68 18 154zM793 394q15 72 15 127
q0 12 -2.5 48.5t-32.5 75.5t-87.5 56.5t-146.5 17.5q-85 0 -145 -17.5t-101 -55t-67 -96.5t-43 -142q-16 -73 -16 -128q0 -11 2.5 -47.5t32.5 -75.5t87.5 -56.5t146.5 -17.5q85 0 145 17.5t101.5 55t67.5 96.5t43 142zM442 149q-128 0 -165 52t-37 120q0 43 11 98
q13 61 35 105t55.5 72.5t80 42.5t109.5 14q99 0 152.5 -41t53.5 -121v-6h-134q-6 28 -25.5 40.5t-58.5 12.5q-32 0 -54.5 -7.5t-38 -24t-25.5 -42.5t-18 -63q-7 -36 -7 -62q0 -27 11 -51t67 -24q23 0 39 3t27.5 9.5t20 16.5t16.5 24h144q-17 -43 -38.5 -75t-51.5 -52.5
t-71 -30.5t-98 -10z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="452" 
d="M244 467q-41 0 -72 11.5t-50 34.5t-22.5 46.5t-3.5 37.5q0 26 7 59q10 48 28 79.5t42 49.5t54.5 25.5t66.5 7.5q34 0 63 -7.5t61 -22.5l21 21h92l-70 -333h-92l-10 17q-33 -14 -60 -20t-55 -6zM237 642q-3 -16 -3 -29q0 -18 10 -29.5t44 -11.5q29 0 60 11l26 118
q-28 11 -55 11q-38 0 -55.5 -14t-26.5 -56z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="663" 
d="M215 519h191l-192 -233l92 -231h-189l-93 232zM501 519h191l-192 -233l92 -231h-189l-93 232z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M155 496h542l-32 -150h-1l-57 -269h-152l57 269h-389z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="463" 
d="M95 621q11 51 27.5 88t44 61.5t67.5 36t97 11.5q117 0 151 -42.5t34 -100.5q0 -34 -9 -76q-11 -51 -28.5 -88.5t-45 -61.5t-67.5 -36t-96 -12q-119 0 -152 42.5t-33 98.5q0 35 10 79zM453 606q8 38 8 66q0 5 -1 22.5t-16 37t-44 28.5t-74 9q-42 0 -72 -9t-51 -27.5
t-34 -48t-21 -71.5q-8 -35 -8 -62q0 -41 23 -71t112 -30q42 0 72 9t51 28t34 48.5t21 70.5zM225 731h111q52 0 70.5 -18t18.5 -46q0 -14 -4 -32q-8 -31 -22 -49.5t-40 -26.5l23 -70h-67l-18 65h-44l-14 -65h-65zM279 676l-14 -67h50q17 0 26 6t14 26q2 9 2 16q0 9 -5.5 14
t-21.5 5h-51z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="570" 
d="M190 780h401l-26 -124h-401z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="433" 
d="M267 467q-102 0 -137.5 35.5t-35.5 91.5q0 29 7 65q9 44 26 74t43 49t62.5 27.5t85.5 8.5q51 0 88 -10t58 -32.5t24.5 -47t3.5 -39.5q0 -28 -7 -64q-9 -43 -26 -73.5t-43 -49t-62.5 -27t-86.5 -8.5zM279 568q34 0 48.5 16t22.5 58q6 23 6 39q0 12 -6 23.5t-43 11.5
q-34 0 -48.5 -16t-22.5 -58q-6 -23 -6 -39q0 -12 6 -23.5t43 -11.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M146 457h195l25 115h150l-24 -115h196l-32 -150h-196l-24 -115h-151l25 115h-196zM81 151h542l-32 -151h-542z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="416" 
d="M349 1009q98 0 137.5 -29.5t39.5 -76.5q0 -18 -5 -40q-6 -24 -14 -41t-22.5 -31t-37 -27.5t-56.5 -30.5l-123 -61q-11 -5 -15 -10.5t-5 -12.5h222l-23 -109h-361l25 123q8 36 29.5 59t66.5 44l129 62q29 14 37 20t10 15q1 5 1 9q0 12 -13 17t-51 5q-33 0 -74.5 -4.5
t-73.5 -10.5l24 114q33 8 68 12t85 4z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="416" 
d="M109 662q36 -8 67.5 -13t71.5 -5q29 0 47 1.5t28.5 5t15 10.5t7.5 17q2 8 2 14q0 14 -9 21t-36 7h-133l22 108h134q24 0 35.5 6t15.5 26q1 6 1 11q0 13 -12 19.5t-59 6.5q-24 0 -42.5 -1t-35 -3t-33 -4.5t-36.5 -6.5l23 111q26 6 66 11t88 5q114 0 151.5 -29.5
t37.5 -75.5q0 -18 -5 -39q-10 -45 -33 -66t-60 -31q56 -27 56 -87q0 -15 -4 -33q-7 -33 -22 -55.5t-42.5 -36t-68.5 -19.5t-101 -6q-55 0 -95 6t-65 13z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="570" 
d="M360 802h213l-211 -170h-179z" />
    <glyph glyph-name="uni00B5" unicode="&#xb5;" 
d="M156 572h187l-72 -339q-3 -15 -3 -28q0 -27 14.5 -45t63.5 -18q54 0 107 22l87 408h187l-122 -572h-127l-12 24q-37 -21 -70.5 -30.5t-71.5 -9.5q-63 0 -99 31l-45 -213h-188z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" 
d="M265 234q-56 3 -93 21.5t-55.5 56t-19 67.5t-0.5 31q0 57 18 136q14 66 38.5 114.5t61.5 80t88.5 46.5t120.5 15h347l-201 -948h-136l174 820h-112l-174 -820h-137z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="380" 
d="M82 377h192l-38 -183h-192z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="570" 
d="M180 3h86l-55 -45q56 -1 81 -18t25 -48q0 -12 -3 -27q-4 -21 -14 -36t-28 -24.5t-44.5 -14t-65.5 -4.5q-29 0 -52 2t-46 7l14 65q27 -5 45.5 -6t39.5 -1q36 0 48 4.5t15 18.5q1 3 1 6q0 9 -8 13.5t-45 4.5q-17 0 -33 -1.5t-36 -3.5l13 57z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="349" 
d="M329 1000h137l-98 -460h-139l66 310l-116 -54l-38 115z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="435" 
d="M267 467q-102 0 -137.5 35.5t-35.5 91.5q0 29 7 65q9 44 26 74t43 49t62.5 27.5t85.5 8.5q51 0 88 -10t58 -32.5t24.5 -47t3.5 -39.5q0 -28 -7 -64q-9 -43 -26 -73.5t-43 -49t-62.5 -27t-86.5 -8.5zM279 568q34 0 48.5 16t22.5 58q6 23 6 39q0 12 -6 23.5t-43 11.5
q-34 0 -48.5 -16t-22.5 -58q-6 -23 -6 -39q0 -12 6 -23.5t43 -11.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="663" 
d="M164 55h-190l191 232l-92 232h189l94 -232zM450 55h-190l191 232l-92 232h189l94 -232z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1028" 
d="M287 802h137l-98 -460h-139l66 310l-116 -54l-38 115zM805 802h185l-787 -802h-182zM1008 262l-55 -262h-136l17 79h-248l22 106l204 275h155l-205 -274h94l16 76h136z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1028" 
d="M287 802h137l-98 -460h-139l66 310l-116 -54l-38 115zM805 802h185l-787 -802h-182zM847 469q98 0 137.5 -29.5t39.5 -76.5q0 -18 -5 -40q-6 -24 -14 -41t-22.5 -31t-37 -27.5t-56.5 -30.5l-123 -61q-11 -5 -15 -10.5t-5 -12.5h222l-23 -109h-361l25 123q8 36 29.5 59
t66.5 44l129 62q29 14 37 20t10 15q1 5 1 9q0 12 -13 17t-51 5q-33 0 -74.5 -4.5t-73.5 -10.5l24 114q33 8 68 12t85 4z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1095" 
d="M67 464q36 -8 67.5 -13t71.5 -5q29 0 47 1.5t28.5 5t15 10.5t7.5 17q2 8 2 14q0 14 -9 21t-36 7h-133l22 108h134q24 0 35.5 6t15.5 26q1 6 1 11q0 13 -12 19.5t-59 6.5q-24 0 -42.5 -1t-35 -3t-33 -4.5t-36.5 -6.5l23 111q26 6 66 11t88 5q114 0 151.5 -29.5t37.5 -75.5
q0 -18 -5 -39q-10 -45 -33 -66t-60 -31q56 -27 56 -87q0 -15 -4 -33q-7 -33 -22 -55.5t-42.5 -36t-68.5 -19.5t-101 -6q-55 0 -95 6t-65 13zM872 802h185l-787 -802h-182zM1075 262l-55 -262h-136l17 79h-248l22 106l204 275h155l-205 -274h94l16 76h136z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="669" 
d="M538 389h-192l39 183h191zM493 170l-20 -94h-144q-51 0 -84 -4t-53 -13.5t-29 -25t-14 -37.5q-3 -14 -3 -26q0 -9 3 -21.5t21 -23t52.5 -15t90.5 -4.5q60 0 126 7.5t130 21.5l-31 -154q-27 -6 -59 -11t-65.5 -8.5t-67.5 -5.5t-65 -2q-187 0 -255 52t-68 133q0 26 6 56
q11 57 35 96.5t64 64.5t97 36t134 11h17l40 101h147z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="905" 
d="M389 987h199l83 -140h-174zM406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31l-159 -293h225z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="905" 
d="M609 987h213l-167 -140h-189zM406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31l-159 -293h225z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="905" 
d="M488 987h230l96 -140h-158l-60 72l-92 -72h-172zM406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31l-159 -293h225z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="905" 
d="M359 867q16 66 49 93t87 27q31 0 57 -6t47.5 -13.5t39.5 -14t33 -6.5t22.5 7t9.5 18l2 10h116l-4 -22q-14 -66 -48.5 -93t-88.5 -27q-31 0 -57 6.5t-47.5 14t-39.5 14t-33 6.5t-22 -7t-10 -20l-2 -10h-116zM406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217z
M588 349l-35 293h-31l-159 -293h225z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="905" 
d="M409 978h149l-26 -122h-149zM641 978h150l-26 -122h-149zM406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31l-159 -293h225z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="905" 
d="M406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31l-159 -293h225z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1313" 
d="M598 181h-287l-136 -181h-241l617 802h817l-35 -167h-443l-31 -147h404l-35 -167h-405l-34 -161h443l-35 -160h-637zM634 350l61 285h-41l-215 -285h195z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="851" 
d="M530 818q91 0 156.5 -19.5t108 -55.5t63 -87t21.5 -114h-205q-8 53 -45.5 79t-117.5 26q-58 0 -97.5 -13.5t-67.5 -43.5t-46 -76.5t-32 -112.5t-14.5 -91t-0.5 -27q0 -44 16.5 -72.5t54.5 -42t101 -13.5q38 0 66.5 6t50.5 19t39 33t32 47h206q-23 -64 -53.5 -113t-73 -83
t-99 -53.5t-131.5 -24.5l-152 -184h-164l160 188q-84 11 -138 42t-81.5 84.5t-28.5 95t-1 48.5q0 73 20 166q21 98 55 171t87 122.5t129 74t182 24.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="777" 
d="M343 987h199l83 -140h-174zM195 802h637l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441l-35 -167h-637z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="777" 
d="M563 987h213l-167 -140h-189zM195 802h637l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441l-35 -167h-637z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="777" 
d="M442 987h230l96 -140h-158l-60 72l-92 -72h-172zM195 802h637l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441l-35 -167h-637z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="777" 
d="M363 978h149l-26 -122h-149zM595 978h150l-26 -122h-149zM195 802h637l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441l-35 -167h-637z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="365" 
d="M120 987h199l83 -140h-174zM195 802h196l-171 -802h-195z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="365" 
d="M340 987h213l-167 -140h-189zM195 802h196l-171 -802h-195z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="365" 
d="M219 987h230l96 -140h-158l-60 72l-92 -72h-172zM195 802h196l-171 -802h-195z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="365" 
d="M140 978h149l-26 -122h-149zM372 978h150l-26 -122h-149zM195 802h196l-171 -802h-195z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="893" 
d="M40 474h85l70 328h309q120 0 200 -25t123.5 -76.5t49.5 -104t6 -79.5q0 -62 -15 -140q-22 -96 -56 -167t-86.5 -117.5t-126.5 -69t-176 -22.5h-398l71 334h-85zM424 170q62 0 104 13t70.5 41t46 72t30.5 105q13 62 13 105.5t-19 72t-59 41.5t-106 13h-149l-34 -159h158
l-30 -139h-158l-35 -165h168z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="954" 
d="M383 867q16 66 49 93t87 27q31 0 57 -6t47.5 -13.5t39.5 -14t33 -6.5t22.5 7t9.5 18l2 10h116l-4 -22q-14 -66 -48.5 -93t-88.5 -27q-31 0 -57 6.5t-47.5 14t-39.5 14t-33 6.5t-22 -7t-10 -20l-2 -10h-116zM195 802h226l247 -545l116 545h195l-170 -802h-232l-244 534
l-113 -534h-195z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="924" 
d="M399 987h199l83 -140h-174zM417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q115 0 191.5 -27t118 -82t46 -106.5t4.5 -71.5q0 -71 -20 -161q-21 -99 -56.5 -171.5t-92.5 -120t-138 -71t-193 -23.5z
M439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="924" 
d="M619 987h213l-167 -140h-189zM417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q115 0 191.5 -27t118 -82t46 -106.5t4.5 -71.5q0 -71 -20 -161q-21 -99 -56.5 -171.5t-92.5 -120t-138 -71t-193 -23.5z
M439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="924" 
d="M498 987h230l96 -140h-158l-60 72l-92 -72h-172zM417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q115 0 191.5 -27t118 -82t46 -106.5t4.5 -71.5q0 -71 -20 -161q-21 -99 -56.5 -171.5t-92.5 -120
t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="924" 
d="M369 867q16 66 49 93t87 27q31 0 57 -6t47.5 -13.5t39.5 -14t33 -6.5t22.5 7t9.5 18l2 10h116l-4 -22q-14 -66 -48.5 -93t-88.5 -27q-31 0 -57 6.5t-47.5 14t-39.5 14t-33 6.5t-22 -7t-10 -20l-2 -10h-116zM417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5
q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q115 0 191.5 -27t118 -82t46 -106.5t4.5 -71.5q0 -71 -20 -161q-21 -99 -56.5 -171.5t-92.5 -120t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5
t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="924" 
d="M419 978h149l-26 -122h-149zM651 978h150l-26 -122h-149zM417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q115 0 191.5 -27t118 -82t46 -106.5t4.5 -71.5q0 -71 -20 -161q-21 -99 -56.5 -171.5
t-92.5 -120t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M85 130l193 166l-121 163l121 98l118 -160l187 161l92 -115l-192 -165l121 -164l-120 -98l-119 161l-186 -161z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="924" 
d="M4 91l73 59q-20 52 -20 120q0 1 0.5 37t19.5 125q21 99 56.5 171.5t92.5 120t138 71t193 23.5q169 0 256 -59l69 59l88 -107l-73 -59q20 -52 20 -120q0 -1 -0.5 -37t-19.5 -125q-21 -99 -56.5 -171.5t-92.5 -120t-138 -71t-193 -23.5q-167 0 -255 58l-69 -58zM268 401
q-6 -30 -9.5 -56t-4.5 -49l403 331q-41 20 -123 20q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5zM439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5q6 30 9.5 56t4.5 49l-402 -332q42 -19 121 -19z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="923" 
d="M399 987h199l83 -140h-174zM421 -16q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64q0 58 16 133l94 446h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h196l-96 -449q-22 -104 -52 -175t-79.5 -114t-124.5 -61.5
t-187 -18.5z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="923" 
d="M619 987h213l-167 -140h-189zM421 -16q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64q0 58 16 133l94 446h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h196l-96 -449q-22 -104 -52 -175t-79.5 -114
t-124.5 -61.5t-187 -18.5z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="923" 
d="M498 987h230l96 -140h-158l-60 72l-92 -72h-172zM421 -16q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64q0 58 16 133l94 446h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h196l-96 -449q-22 -104 -52 -175
t-79.5 -114t-124.5 -61.5t-187 -18.5z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="923" 
d="M419 978h149l-26 -122h-149zM651 978h150l-26 -122h-149zM421 -16q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64q0 58 16 133l94 446h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h196l-96 -449
q-22 -104 -52 -175t-79.5 -114t-124.5 -61.5t-187 -18.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="856" 
d="M586 987h213l-167 -140h-189zM335 313l-225 489h209l141 -313l275 313h229l-434 -489l-66 -313h-195z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="851" 
d="M391 802l-22 -105h182q94 0 158.5 -17t101 -55t42.5 -78.5t6 -64.5q0 -46 -12 -105q-16 -69 -41.5 -119.5t-66 -82.5t-99 -47.5t-139.5 -15.5h-257l-24 -112h-195l170 802h196zM334 534l-55 -259h221q33 0 57.5 5t43 19t31.5 38.5t21 63.5q7 31 7 54q0 6 -1.5 21.5
t-15.5 30.5t-39.5 21t-62.5 6h-207z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="733" 
d="M383 -5q-35 0 -57.5 1t-50.5 6l30 141q15 -2 30.5 -3.5t26.5 -1.5q29 0 50 4.5t36.5 15t25.5 29t16 46.5q5 23 5 42q0 32 -19 52t-79 20h-49l27 123h53q49 0 76 21t37 73q5 22 5 40q0 32 -20 51t-77 19q-58 0 -85.5 -21.5t-38.5 -75.5l-123 -577h-187l122 576
q12 58 34 103t59.5 76t95.5 47t142 16q153 0 210.5 -53.5t57.5 -128.5q0 -30 -7 -64q-30 -137 -148 -167q28 -9 49.5 -25t33.5 -41.5t13.5 -47t1.5 -28.5q0 -30 -8 -68q-22 -103 -90.5 -151.5t-197.5 -48.5z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="768" 
d="M235 802h200l104 -170h-165zM289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288q-7 -34 -7 -60
q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="768" 
d="M546 800h213l-211 -170h-179zM289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288q-7 -34 -7 -60
q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="768" 
d="M395 802h198l88 -170h-159l-38 86l-77 -86h-172zM289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288
q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="768" 
d="M246 663q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116zM289 -16q-70 0 -122.5 19
t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5
l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="768" 
d="M315 791h141l-31 -146h-141zM528 791h141l-30 -146h-141zM289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5z
M237 288q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="768" 
d="M460 617q-81 0 -111 23t-30 62q0 16 4 35q7 32 19 55t32 37t51 20.5t75 6.5q80 0 110.5 -23.5t30.5 -62.5q0 -16 -4 -34q-7 -33 -19 -55.5t-32.5 -36.5t-51 -20.5t-74.5 -6.5zM468 688q37 0 53 9.5t22 38.5q3 11 3 20q0 13 -9 21t-45 8q-37 0 -53 -9.5t-22 -38.5
q-2 -11 -2 -19q0 -14 8.5 -22t44.5 -8zM289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288
q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1147" 
d="M253 -16q-60 0 -106 12t-75 38.5t-36 57t-7 52.5q0 29 7 64q8 36 25.5 63.5t47.5 46.5t74 28.5t106 9.5h191l3 14q3 12 3 22q0 5 -1.5 14t-15 17.5t-42 13t-77.5 4.5q-54 0 -110 -5t-117 -13l30 146q33 5 58 9t49.5 6t52.5 3t67 1q93 0 148 -18t81 -51q40 37 93.5 53
t123.5 16q73 0 126.5 -17.5t85 -55t37 -76.5t5.5 -62q0 -43 -10 -96l-10 -55h-418v-14q0 -20 5 -36t20 -26.5t40 -14.5t61 -4q31 0 59 0.5t57.5 3t61.5 6t72 9.5l-27 -139q-64 -16 -123 -21.5t-123 -5.5q-85 0 -144.5 15.5t-95.5 50.5q-50 -32 -110.5 -49t-141.5 -17z
M810 458q-30 0 -52.5 -5.5t-39 -18t-28.5 -33.5t-22 -52h240q1 11 1 20q0 43 -21 66t-78 23zM285 112q51 0 92 10.5t79 31.5q-2 17 -2.5 29t-0.5 16q0 16 2 34h-159q-38 0 -56 -12t-24 -40q-3 -12 -3 -22q0 -21 14.5 -34t57.5 -13z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="650" 
d="M416 433q-43 0 -73 -7t-50.5 -23.5t-33 -44.5t-21.5 -70q-8 -38 -8 -64q0 -4 1 -20.5t15 -33.5t41 -24t67 -7q27 0 51.5 1t50.5 3.5t54.5 6t62.5 8.5l-30 -147q-76 -19 -152 -25l-153 -184h-164l160 188q-60 8 -102 30t-65 60.5t-25.5 72.5t-2.5 47q0 49 13 111
q17 76 47 129t75 86t106 47.5t140 14.5q62 0 118 -7t113 -20l-30 -148q-31 5 -57 9t-50.5 6.5t-48 3.5t-49.5 1z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="699" 
d="M211 802h200l104 -170h-165zM403 588q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139q-65 -15 -123.5 -21t-122.5 -6q-87 0 -148.5 16.5t-96.5 55t-41 80
t-6 64.5q0 49 13 111q17 77 47 129.5t73.5 85.5t101 47.5t130.5 14.5zM395 449q-29 0 -51.5 -5t-39 -16.5t-29 -30.5t-22.5 -47h239q1 10 1 18q0 40 -21 60.5t-77 20.5z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="699" 
d="M523 800h213l-211 -170h-179zM403 588q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139q-65 -15 -123.5 -21t-122.5 -6q-87 0 -148.5 16.5t-96.5 55t-41 80
t-6 64.5q0 49 13 111q17 77 47 129.5t73.5 85.5t101 47.5t130.5 14.5zM395 449q-29 0 -51.5 -5t-39 -16.5t-29 -30.5t-22.5 -47h239q1 10 1 18q0 40 -21 60.5t-77 20.5z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="699" 
d="M371 802h198l88 -170h-159l-38 86l-77 -86h-172zM403 588q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139q-65 -15 -123.5 -21t-122.5 -6q-87 0 -148.5 16.5
t-96.5 55t-41 80t-6 64.5q0 49 13 111q17 77 47 129.5t73.5 85.5t101 47.5t130.5 14.5zM395 449q-29 0 -51.5 -5t-39 -16.5t-29 -30.5t-22.5 -47h239q1 10 1 18q0 40 -21 60.5t-77 20.5z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="699" 
d="M291 791h141l-31 -146h-141zM504 791h141l-30 -146h-141zM403 588q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139q-65 -15 -123.5 -21t-122.5 -6
q-87 0 -148.5 16.5t-96.5 55t-41 80t-6 64.5q0 49 13 111q17 77 47 129.5t73.5 85.5t101 47.5t130.5 14.5zM395 449q-29 0 -51.5 -5t-39 -16.5t-29 -30.5t-22.5 -47h239q1 10 1 18q0 40 -21 60.5t-77 20.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="338" 
d="M20 802h200l104 -170h-165zM137 572h187l-121 -572h-187z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="338" 
d="M331 800h213l-211 -170h-179zM137 572h187l-121 -572h-187z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="338" 
d="M180 802h198l88 -170h-159l-38 86l-77 -86h-172zM137 572h187l-121 -572h-187z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="338" 
d="M100 791h141l-31 -146h-141zM313 791h141l-30 -146h-141zM137 572h187l-121 -572h-187z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="737" 
d="M196 697l147 30q-39 38 -84 75h223q12 -9 23 -18.5t22 -19.5l185 38l26 -105l-124 -26q63 -85 76.5 -154t13.5 -115q0 -56 -13 -117q-18 -82 -47 -139.5t-72.5 -93.5t-102.5 -52t-137 -16q-83 0 -145 16.5t-98.5 55.5t-43 81.5t-6.5 66.5q0 51 14 117q26 127 107 188
t230 61q43 0 85 -9q-20 39 -46 72l-205 -42zM503 301q11 54 11 98v7q-25 9 -51 14.5t-55 5.5q-39 0 -67.5 -7.5t-49 -24.5t-34 -45.5t-22.5 -70.5q-8 -36 -8 -63q0 -3 0.5 -18.5t12.5 -32.5t37 -25t65 -8q36 0 62 9.5t45 30.5t32 53t22 77z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="780" 
d="M252 663q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116zM282 535q54 25 107 39t113 14
q135 0 180.5 -54.5t45.5 -124.5q0 -32 -8 -69l-72 -340h-187l71 338q4 17 4 31q0 25 -16 42t-70 17q-42 0 -83 -11.5t-83 -29.5l-82 -387h-187l121 572h128z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="738" 
d="M220 802h200l104 -170h-165zM324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q84 0 144.5 -17t96 -56t41.5 -80t6 -64q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48t-143.5 -15zM341 131
q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="738" 
d="M532 800h213l-211 -170h-179zM324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q84 0 144.5 -17t96 -56t41.5 -80t6 -64q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48t-143.5 -15zM341 131
q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="738" 
d="M380 802h198l88 -170h-159l-38 86l-77 -86h-172zM324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q84 0 144.5 -17t96 -56t41.5 -80t6 -64q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48t-143.5 -15z
M341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="738" 
d="M231 663q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116zM324 -16q-83 0 -143 17.5
t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q84 0 144.5 -17t96 -56t41.5 -80t6 -64q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48t-143.5 -15zM341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5t-12 48t-37 26.5
t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="738" 
d="M300 791h141l-31 -146h-141zM513 791h141l-30 -146h-141zM324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q84 0 144.5 -17t96 -56t41.5 -80t6 -64q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48
t-143.5 -15zM341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M365 562h148l-30 -142h-148zM126 361h542l-32 -150h-542zM277 152h149l-30 -142h-148z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="738" 
d="M-5 82l54 42q-11 34 -11 78q0 48 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q63 0 112.5 -9.5t85.5 -30.5l54 40l79 -98l-54 -42q11 -33 11 -77q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48t-143.5 -15q-62 0 -111 9.5t-84 30.5l-54 -40zM237 286
q-4 -19 -6 -37l239 181q-25 11 -72 11q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5zM341 131q49 0 78.5 15t47 40.5t26.5 60.5t16 75l-238 -180q23 -11 70 -11z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="780" 
d="M241 802h200l104 -170h-165zM280 -16q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h187l-121 -572h-129l-18 37q-54 -25 -107 -39t-112 -14z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="780" 
d="M553 800h213l-211 -170h-179zM280 -16q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h187l-121 -572h-129l-18 37q-54 -25 -107 -39t-112 -14z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="780" 
d="M401 802h198l88 -170h-159l-38 86l-77 -86h-172zM280 -16q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h187l-121 -572h-129l-18 37q-54 -25 -107 -39t-112 -14z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="780" 
d="M321 791h141l-31 -146h-141zM534 791h141l-30 -146h-141zM280 -16q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h187l-121 -572h-129l-18 37q-54 -25 -107 -39t-112 -14z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="715" 
d="M520 800h213l-211 -170h-179zM71 572h189l53 -407h20l226 406l208 1l-339 -594q-31 -56 -59.5 -92.5t-63 -59t-78.5 -31.5t-107 -9q-33 0 -65.5 2.5t-65.5 7.5l31 146q20 -3 41.5 -4.5t40.5 -1.5q33 0 55 2.5t36 8t23.5 15t16.5 23.5l16 28h-95z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="768" 
d="M185 802h187l-52 -244q42 15 81 22.5t79 7.5q70 0 123 -19t84.5 -58.5t37 -80t5.5 -62.5q0 -47 -12 -104q-17 -81 -47 -134.5t-71.5 -86t-94 -46t-115.5 -13.5q-49 0 -93 10t-90 29l-47 -221h-187zM420 435q-32 0 -65.5 -7t-65.5 -20l-52 -244q28 -12 58 -19.5t62 -7.5
q40 0 69 7t49.5 24.5t34 45.5t22.5 70q7 34 7 60q0 7 -1.5 24.5t-16 35t-40 24.5t-61.5 7z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="715" 
d="M288 791h141l-31 -146h-141zM501 791h141l-30 -146h-141zM71 572h189l53 -407h20l226 406l208 1l-339 -594q-31 -56 -59.5 -92.5t-63 -59t-78.5 -31.5t-107 -9q-33 0 -65.5 2.5t-65.5 7.5l31 146q20 -3 41.5 -4.5t40.5 -1.5q33 0 55 2.5t36 8t23.5 15t16.5 23.5l16 28
h-95z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="905" 
d="M395 974h407l-24 -116h-407zM406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31l-159 -293h225z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="768" 
d="M289 780h401l-26 -124h-401zM289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288q-7 -34 -7 -60
q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="905" 
d="M580 842q-66 0 -110 8t-69 25.5t-30 37.5t-5 34q0 18 5 40h140q-1 -5 -1 -10q0 -15 14 -23.5t69 -8.5q28 0 46.5 2t30 7t17.5 13t9 20h140q-8 -39 -24 -66.5t-45.5 -45t-74.5 -25.5t-112 -8zM406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31
l-159 -293h225z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="768" 
d="M463 632q-112 0 -151 32.5t-39 84.5q0 22 6 48h140q-3 -13 -3 -23q0 -16 9 -27.5t54 -11.5t63 14.5t25 47.5h141q-9 -44 -24 -75.5t-42 -51.5t-70 -29t-109 -9zM289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5
q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z
" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="905" 
d="M406 802h313l109 -802h-58q-32 -14 -51.5 -23t-30 -16.5t-15 -14.5t-6.5 -16q-1 -5 -1 -10q0 -11 8.5 -17t40.5 -6q19 0 37.5 2.5t38.5 4.5l-22 -106q-26 -5 -52 -8.5t-57 -3.5q-80 0 -108.5 22.5t-28.5 59.5q0 13 3 28q8 31 30 56.5t72 52.5l-20 176h-336l-98 -181h-217
zM588 349l-35 293h-31l-159 -293h225z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="768" 
d="M289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-24q-32 -14 -51.5 -23t-30 -16.5t-15 -14.5t-6.5 -16q-1 -5 -1 -10q0 -11 8.5 -17t40.5 -6q19 0 37.5 2.5
t38.5 4.5l-22 -106q-26 -5 -52 -8.5t-57 -3.5q-80 0 -108.5 22.5t-28.5 59.5q0 13 3 28q5 18 13 33.5t23 30t38 29t56 30.5l-5 11q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248
q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="851" 
d="M598 987h213l-167 -140h-189zM530 818q91 0 156.5 -19.5t108 -55.5t63 -87t21.5 -114h-205q-8 53 -45.5 79t-117.5 26q-58 0 -97.5 -13.5t-67.5 -43.5t-46 -76.5t-32 -112.5t-14.5 -91t-0.5 -27q0 -44 16.5 -72.5t54.5 -42t101 -13.5q38 0 66.5 6t50.5 19t39 33t32 47
h206q-25 -70 -59.5 -121.5t-83 -86t-114.5 -51.5t-156 -17q-114 0 -189 25.5t-114.5 79.5t-42.5 103.5t-3 65.5q0 73 20 168q21 98 55 171t87 122.5t129 74t182 24.5z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="650" 
d="M505 800h213l-211 -170h-179zM416 433q-43 0 -73 -7t-50.5 -23.5t-33 -44.5t-21.5 -70q-8 -38 -8 -64q0 -4 1 -20.5t15 -33.5t41 -24t67 -7q27 0 51.5 1t50.5 3.5t54.5 6t62.5 8.5l-30 -147q-55 -14 -107 -20.5t-106 -6.5q-86 0 -147.5 16.5t-96.5 55t-41 80t-6 64.5
q0 49 13 111q17 76 47 129t75 86t106 47.5t140 14.5q62 0 118 -7t113 -20l-30 -148q-31 5 -57 9t-50.5 6.5t-48 3.5t-49.5 1z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="851" 
d="M477 987h230l96 -140h-158l-60 72l-92 -72h-172zM530 818q91 0 156.5 -19.5t108 -55.5t63 -87t21.5 -114h-205q-8 53 -45.5 79t-117.5 26q-58 0 -97.5 -13.5t-67.5 -43.5t-46 -76.5t-32 -112.5t-14.5 -91t-0.5 -27q0 -44 16.5 -72.5t54.5 -42t101 -13.5q38 0 66.5 6
t50.5 19t39 33t32 47h206q-25 -70 -59.5 -121.5t-83 -86t-114.5 -51.5t-156 -17q-114 0 -189 25.5t-114.5 79.5t-42.5 103.5t-3 65.5q0 73 20 168q21 98 55 171t87 122.5t129 74t182 24.5z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="650" 
d="M354 802h198l88 -170h-159l-38 86l-77 -86h-172zM416 433q-43 0 -73 -7t-50.5 -23.5t-33 -44.5t-21.5 -70q-8 -38 -8 -64q0 -4 1 -20.5t15 -33.5t41 -24t67 -7q27 0 51.5 1t50.5 3.5t54.5 6t62.5 8.5l-30 -147q-55 -14 -107 -20.5t-106 -6.5q-86 0 -147.5 16.5t-96.5 55
t-41 80t-6 64.5q0 49 13 111q17 76 47 129t75 86t106 47.5t140 14.5q62 0 118 -7t113 -20l-30 -148q-31 5 -57 9t-50.5 6.5t-48 3.5t-49.5 1z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="851" 
d="M494 978h189l-25 -122h-190zM530 818q91 0 156.5 -19.5t108 -55.5t63 -87t21.5 -114h-205q-8 53 -45.5 79t-117.5 26q-58 0 -97.5 -13.5t-67.5 -43.5t-46 -76.5t-32 -112.5t-14.5 -91t-0.5 -27q0 -44 16.5 -72.5t54.5 -42t101 -13.5q38 0 66.5 6t50.5 19t39 33t32 47h206
q-25 -70 -59.5 -121.5t-83 -86t-114.5 -51.5t-156 -17q-114 0 -189 25.5t-114.5 79.5t-42.5 103.5t-3 65.5q0 73 20 168q21 98 55 171t87 122.5t129 74t182 24.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="650" 
d="M356 791h190l-31 -146h-190zM416 433q-43 0 -73 -7t-50.5 -23.5t-33 -44.5t-21.5 -70q-8 -38 -8 -64q0 -4 1 -20.5t15 -33.5t41 -24t67 -7q27 0 51.5 1t50.5 3.5t54.5 6t62.5 8.5l-30 -147q-55 -14 -107 -20.5t-106 -6.5q-86 0 -147.5 16.5t-96.5 55t-41 80t-6 64.5
q0 49 13 111q17 76 47 129t75 86t106 47.5t140 14.5q62 0 118 -7t113 -20l-30 -148q-31 5 -57 9t-50.5 6.5t-48 3.5t-49.5 1z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="851" 
d="M351 987h158l61 -72l91 72h172l-156 -140h-230zM530 818q91 0 156.5 -19.5t108 -55.5t63 -87t21.5 -114h-205q-8 53 -45.5 79t-117.5 26q-58 0 -97.5 -13.5t-67.5 -43.5t-46 -76.5t-32 -112.5t-14.5 -91t-0.5 -27q0 -44 16.5 -72.5t54.5 -42t101 -13.5q38 0 66.5 6
t50.5 19t39 33t32 47h206q-25 -70 -59.5 -121.5t-83 -86t-114.5 -51.5t-156 -17q-114 0 -189 25.5t-114.5 79.5t-42.5 103.5t-3 65.5q0 73 20 168q21 98 55 171t87 122.5t129 74t182 24.5z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="650" 
d="M230 802h158l40 -85l76 85h172l-160 -170h-198zM416 433q-43 0 -73 -7t-50.5 -23.5t-33 -44.5t-21.5 -70q-8 -38 -8 -64q0 -4 1 -20.5t15 -33.5t41 -24t67 -7q27 0 51.5 1t50.5 3.5t54.5 6t62.5 8.5l-30 -147q-55 -14 -107 -20.5t-106 -6.5q-86 0 -147.5 16.5t-96.5 55
t-41 80t-6 64.5q0 49 13 111q17 76 47 129t75 86t106 47.5t140 14.5q62 0 118 -7t113 -20l-30 -148q-31 5 -57 9t-50.5 6.5t-48 3.5t-49.5 1z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="893" 
d="M357 987h158l61 -72l91 72h172l-156 -140h-230zM25 1l170 801h309q120 0 200 -25t123.5 -76.5t49.5 -104t6 -79.5q0 -62 -15 -140q-22 -96 -56 -167t-86.5 -117.5t-126.5 -69t-176 -22.5h-398zM424 170q62 0 104 13t70.5 41t46 72t30.5 105q13 62 13 105.5t-19 72
t-59 41.5t-106 13h-149l-99 -463h168z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="803" 
d="M289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q48 0 92.5 -10t90.5 -28l54 252h187l-170 -802h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM891 802h165l-170 -252h-93zM237 288q-7 -34 -7 -60
q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-29 12 -58.5 19.5t-62.5 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="893" 
d="M40 474h85l70 328h309q120 0 200 -25t123.5 -76.5t49.5 -104t6 -79.5q0 -62 -15 -140q-22 -96 -56 -167t-86.5 -117.5t-126.5 -69t-176 -22.5h-398l71 334h-85zM424 170q62 0 104 13t70.5 41t46 72t30.5 105q13 62 13 105.5t-19 72t-59 41.5t-106 13h-149l-34 -159h158
l-30 -139h-158l-35 -165h168z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="768" 
d="M289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q48 0 92.5 -10t90.5 -28l16 73h-172l27 132h173l10 47h187l-10 -47h75l-28 -132h-75l-132 -623h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288
q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-29 12 -58.5 19.5t-62.5 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="777" 
d="M349 974h407l-24 -116h-407zM195 802h637l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441l-35 -167h-637z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="699" 
d="M265 780h401l-26 -124h-401zM403 588q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139q-65 -15 -123.5 -21t-122.5 -6q-87 0 -148.5 16.5t-96.5 55t-41 80
t-6 64.5q0 49 13 111q17 77 47 129.5t73.5 85.5t101 47.5t130.5 14.5zM395 449q-29 0 -51.5 -5t-39 -16.5t-29 -30.5t-22.5 -47h239q1 10 1 18q0 40 -21 60.5t-77 20.5z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="777" 
d="M534 842q-66 0 -110 8t-69 25.5t-30 37.5t-5 34q0 18 5 40h140q-1 -5 -1 -10q0 -15 14 -23.5t69 -8.5q28 0 46.5 2t30 7t17.5 13t9 20h140q-8 -39 -24 -66.5t-45.5 -45t-74.5 -25.5t-112 -8zM195 802h637l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441
l-35 -167h-637z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="699" 
d="M439 632q-112 0 -151 32.5t-39 84.5q0 22 6 48h140q-3 -13 -3 -23q0 -16 9 -27.5t54 -11.5t63 14.5t25 47.5h141q-9 -44 -24 -75.5t-42 -51.5t-70 -29t-109 -9zM403 588q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5
t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139q-65 -15 -123.5 -21t-122.5 -6q-87 0 -148.5 16.5t-96.5 55t-41 80t-6 64.5q0 49 13 111q17 77 47 129.5t73.5 85.5t101 47.5t130.5 14.5zM395 449q-29 0 -51.5 -5t-39 -16.5t-29 -30.5t-22.5 -47h239q1 10 1 18
q0 40 -21 60.5t-77 20.5z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="777" 
d="M459 978h189l-25 -122h-190zM195 802h637l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441l-35 -167h-637z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="699" 
d="M373 791h190l-31 -146h-190zM403 588q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139q-65 -15 -123.5 -21t-122.5 -6q-87 0 -148.5 16.5t-96.5 55t-41 80
t-6 64.5q0 49 13 111q17 77 47 129.5t73.5 85.5t101 47.5t130.5 14.5zM395 449q-29 0 -51.5 -5t-39 -16.5t-29 -30.5t-22.5 -47h239q1 10 1 18q0 40 -21 60.5t-77 20.5z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="777" 
d="M195 802h637l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441l-35 -167h-43q-32 -14 -51.5 -23t-30 -16.5t-15 -14.5t-6.5 -16q-1 -5 -1 -10q0 -11 8.5 -17t40.5 -6q19 0 37.5 2.5t38.5 4.5l-22 -106q-26 -5 -52 -8.5t-57 -3.5q-80 0 -108.5 22.5t-28.5 59.5
q0 13 3 28q8 30 27.5 54t65.5 50h-443z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="699" 
d="M428 -70q-1 -5 -1 -10q0 -11 8.5 -17t40.5 -6q19 0 37.5 2.5t38.5 4.5l-22 -106q-26 -5 -52 -8.5t-57 -3.5q-80 0 -108.5 22.5t-28.5 59.5q0 13 3 28q7 26 21.5 46.5t45.5 41.5h-24q-87 0 -148.5 16.5t-96.5 55t-41 80t-6 64.5q0 49 13 111q17 77 47 129.5t73.5 85.5
t101 47.5t130.5 14.5q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139l-39 -8q-34 -14 -54.5 -24t-32 -18t-16 -15t-6.5 -16zM395 449q-29 0 -51.5 -5t-39 -16.5
t-29 -30.5t-22.5 -47h239q1 10 1 18q0 40 -21 60.5t-77 20.5z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="777" 
d="M316 987h158l61 -72l91 72h172l-156 -140h-230zM195 802h637l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441l-35 -167h-637z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="699" 
d="M247 802h158l40 -85l76 85h172l-160 -170h-198zM403 588q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139q-65 -15 -123.5 -21t-122.5 -6q-87 0 -148.5 16.5
t-96.5 55t-41 80t-6 64.5q0 49 13 111q17 77 47 129.5t73.5 85.5t101 47.5t130.5 14.5zM395 449q-29 0 -51.5 -5t-39 -16.5t-29 -30.5t-22.5 -47h239q1 10 1 18q0 40 -21 60.5t-77 20.5z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="912" 
d="M497 987h230l96 -140h-158l-60 72l-92 -72h-172zM550 818q96 0 165 -20t114 -56t66.5 -87t22.5 -113h-204q-10 51 -51.5 78t-130.5 27q-63 0 -106.5 -14t-74 -44t-50 -76.5t-33.5 -111.5q-14 -66 -14 -112.5t19 -76.5t60.5 -43.5t110.5 -13.5q54 0 93.5 7.5t66.5 24
t43.5 42.5t25.5 63h-241l34 164h437l-38 -178q-15 -72 -46.5 -126.5t-84.5 -92t-129 -56.5t-180 -19q-114 0 -192 25.5t-121 79.5t-48.5 107t-5.5 76q0 70 19 157q21 96 56 168.5t91 121.5t135.5 74t190.5 25z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="759" 
d="M401 802h198l88 -170h-159l-38 86l-77 -86h-172zM286 8q-69 0 -121 18t-82.5 56.5t-36 77.5t-5.5 60q0 45 12 100q16 77 45.5 128.5t70.5 82.5t93 44t114 13q56 0 106.5 -13.5t104.5 -36.5l31 34h129l-113 -529q-16 -75 -44 -124t-71.5 -78t-103 -41t-137.5 -12
q-34 0 -65.5 1t-64.5 3t-68.5 6.5t-77.5 11.5l30 142q44 -7 79 -11t64 -6.5t53.5 -3t46.5 -0.5q47 0 77.5 6.5t49.5 19.5t28.5 32t14.5 44l1 5q-42 -15 -81 -22.5t-79 -7.5zM238 299q-6 -30 -6 -53q0 -39 22 -63t92 -24q32 0 65.5 7.5t66.5 19.5l48 225q-28 12 -56.5 19
t-60.5 7q-39 0 -67.5 -7t-48.5 -23t-33.5 -42.5t-21.5 -65.5z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="912" 
d="M589 842q-66 0 -110 8t-69 25.5t-30 37.5t-5 34q0 18 5 40h140q-1 -5 -1 -10q0 -15 14 -23.5t69 -8.5q28 0 46.5 2t30 7t17.5 13t9 20h140q-8 -39 -24 -66.5t-45.5 -45t-74.5 -25.5t-112 -8zM550 818q96 0 165 -20t114 -56t66.5 -87t22.5 -113h-204q-10 51 -51.5 78
t-130.5 27q-63 0 -106.5 -14t-74 -44t-50 -76.5t-33.5 -111.5q-14 -66 -14 -112.5t19 -76.5t60.5 -43.5t110.5 -13.5q54 0 93.5 7.5t66.5 24t43.5 42.5t25.5 63h-241l34 164h437l-38 -178q-15 -72 -46.5 -126.5t-84.5 -92t-129 -56.5t-180 -19q-114 0 -192 25.5t-121 79.5
t-48.5 107t-5.5 76q0 70 19 157q21 96 56 168.5t91 121.5t135.5 74t190.5 25z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="759" 
d="M469 632q-112 0 -151 32.5t-39 84.5q0 22 6 48h140q-3 -13 -3 -23q0 -16 9 -27.5t54 -11.5t63 14.5t25 47.5h141q-9 -44 -24 -75.5t-42 -51.5t-70 -29t-109 -9zM286 8q-69 0 -121 18t-82.5 56.5t-36 77.5t-5.5 60q0 45 12 100q16 77 45.5 128.5t70.5 82.5t93 44t114 13
q56 0 106.5 -13.5t104.5 -36.5l31 34h129l-113 -529q-16 -75 -44 -124t-71.5 -78t-103 -41t-137.5 -12q-34 0 -65.5 1t-64.5 3t-68.5 6.5t-77.5 11.5l30 142q44 -7 79 -11t64 -6.5t53.5 -3t46.5 -0.5q47 0 77.5 6.5t49.5 19.5t28.5 32t14.5 44l1 5q-42 -15 -81 -22.5
t-79 -7.5zM238 299q-6 -30 -6 -53q0 -39 22 -63t92 -24q32 0 65.5 7.5t66.5 19.5l48 225q-28 12 -56.5 19t-60.5 7q-39 0 -67.5 -7t-48.5 -23t-33.5 -42.5t-21.5 -65.5z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="912" 
d="M514 978h189l-25 -122h-190zM550 818q96 0 165 -20t114 -56t66.5 -87t22.5 -113h-204q-10 51 -51.5 78t-130.5 27q-63 0 -106.5 -14t-74 -44t-50 -76.5t-33.5 -111.5q-14 -66 -14 -112.5t19 -76.5t60.5 -43.5t110.5 -13.5q54 0 93.5 7.5t66.5 24t43.5 42.5t25.5 63h-241
l34 164h437l-38 -178q-15 -72 -46.5 -126.5t-84.5 -92t-129 -56.5t-180 -19q-114 0 -192 25.5t-121 79.5t-48.5 107t-5.5 76q0 70 19 157q21 96 56 168.5t91 121.5t135.5 74t190.5 25z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="759" 
d="M403 791h190l-31 -146h-190zM286 8q-69 0 -121 18t-82.5 56.5t-36 77.5t-5.5 60q0 45 12 100q16 77 45.5 128.5t70.5 82.5t93 44t114 13q56 0 106.5 -13.5t104.5 -36.5l31 34h129l-113 -529q-16 -75 -44 -124t-71.5 -78t-103 -41t-137.5 -12q-34 0 -65.5 1t-64.5 3
t-68.5 6.5t-77.5 11.5l30 142q44 -7 79 -11t64 -6.5t53.5 -3t46.5 -0.5q47 0 77.5 6.5t49.5 19.5t28.5 32t14.5 44l1 5q-42 -15 -81 -22.5t-79 -7.5zM238 299q-6 -30 -6 -53q0 -39 22 -63t92 -24q32 0 65.5 7.5t66.5 19.5l48 225q-28 12 -56.5 19t-60.5 7q-39 0 -67.5 -7
t-48.5 -23t-33.5 -42.5t-21.5 -65.5z" />
    <glyph glyph-name="uni0122" unicode="&#x122;" horiz-adv-x="912" 
d="M550 818q96 0 165 -20t114 -56t66.5 -87t22.5 -113h-204q-10 51 -51.5 78t-130.5 27q-63 0 -106.5 -14t-74 -44t-50 -76.5t-33.5 -111.5q-14 -66 -14 -112.5t19 -76.5t60.5 -43.5t110.5 -13.5q54 0 93.5 7.5t66.5 24t43.5 42.5t25.5 63h-241l34 164h437l-38 -178
q-15 -72 -46.5 -126.5t-84.5 -92t-129 -56.5t-180 -19q-114 0 -192 25.5t-121 79.5t-48.5 107t-5.5 76q0 70 19 157q21 96 56 168.5t91 121.5t135.5 74t190.5 25zM290 -53h212l-201 -145h-180z" />
    <glyph glyph-name="uni0123" unicode="&#x123;" horiz-adv-x="759" 
d="M570 632h-212l209 170h179zM286 8q-69 0 -121 18t-82.5 56.5t-36 77.5t-5.5 60q0 45 12 100q16 77 45.5 128.5t70.5 82.5t93 44t114 13q56 0 106.5 -13.5t104.5 -36.5l31 34h129l-113 -529q-16 -75 -44 -124t-71.5 -78t-103 -41t-137.5 -12q-34 0 -65.5 1t-64.5 3
t-68.5 6.5t-77.5 11.5l30 142q44 -7 79 -11t64 -6.5t53.5 -3t46.5 -0.5q47 0 77.5 6.5t49.5 19.5t28.5 32t14.5 44l1 5q-42 -15 -81 -22.5t-79 -7.5zM238 299q-6 -30 -6 -53q0 -39 22 -63t92 -24q32 0 65.5 7.5t66.5 19.5l48 225q-28 12 -56.5 19t-60.5 7q-39 0 -67.5 -7
t-48.5 -23t-33.5 -42.5t-21.5 -65.5z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="947" 
d="M510 987h230l96 -140h-158l-60 72l-92 -72h-172zM195 802h196l-67 -313h387l66 313h196l-171 -802h-195l68 320h-387l-68 -320h-195z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="780" 
d="M426 987h230l96 -140h-158l-60 72l-92 -72h-172zM185 802h187l-53 -251q45 17 89.5 27t93.5 10q135 0 180 -55.5t45 -124.5q0 -32 -7 -68l-72 -340h-187l71 338q4 18 4 32q0 24 -15.5 41t-70.5 17q-40 0 -81.5 -9.5t-83.5 -26.5l-83 -392h-187z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="947" 
d="M148 579h-85l30 140h84l18 83h196l-18 -83h386l18 83h196l-18 -83h85l-30 -140h-85l-123 -579h-195l68 320h-387l-68 -320h-195zM711 489l19 90h-387l-19 -90h387z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="780" 
d="M147 623h-75l28 132h75l10 47h187l-10 -47h173l-28 -132h-173l-15 -72q45 17 89.5 27t93.5 10q135 0 180 -55.5t45 -124.5q0 -32 -7 -68l-72 -340h-187l71 338q4 18 4 32q0 24 -15.5 41t-70.5 17q-40 0 -81.5 -9.5t-83.5 -26.5l-83 -392h-187z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="365" 
d="M90 867q16 66 49 93t87 27q31 0 57 -6t47.5 -13.5t39.5 -14t33 -6.5t22.5 7t9.5 18l2 10h116l-4 -22q-14 -66 -48.5 -93t-88.5 -27q-31 0 -57 6.5t-47.5 14t-39.5 14t-33 6.5t-22 -7t-10 -20l-2 -10h-116zM195 802h196l-171 -802h-195z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="338" 
d="M31 663q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116zM137 572h187l-121 -572h-187z
" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="365" 
d="M126 974h407l-24 -116h-407zM195 802h196l-171 -802h-195z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="338" 
d="M74 780h401l-26 -124h-401zM137 572h187l-121 -572h-187z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="365" 
d="M311 842q-66 0 -110 8t-69 25.5t-30 37.5t-5 34q0 18 5 40h140q-1 -5 -1 -10q0 -15 14 -23.5t69 -8.5q28 0 46.5 2t30 7t17.5 13t9 20h140q-8 -39 -24 -66.5t-45.5 -45t-74.5 -25.5t-112 -8zM195 802h196l-171 -802h-195z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="338" 
d="M248 632q-112 0 -151 32.5t-39 84.5q0 22 6 48h140q-3 -13 -3 -23q0 -16 9 -27.5t54 -11.5t63 14.5t25 47.5h141q-9 -44 -24 -75.5t-42 -51.5t-70 -29t-109 -9zM137 572h187l-121 -572h-187z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="365" 
d="M195 802h196l-171 -802h-38q-32 -14 -51.5 -23t-30 -16.5t-15 -14.5t-6.5 -16q-1 -5 -1 -10q0 -11 8.5 -17t40.5 -6q19 0 37.5 2.5t38.5 4.5l-22 -106q-26 -5 -52 -8.5t-57 -3.5q-80 0 -108.5 22.5t-28.5 59.5q0 13 3 28q8 30 27.5 54t65.5 50h-6z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="338" 
d="M183 797h190l-31 -145h-190zM63 -70q-1 -5 -1 -10q0 -11 8.5 -17t40.5 -6q19 0 37.5 2.5t38.5 4.5l-22 -106q-26 -5 -52 -8.5t-57 -3.5q-80 0 -108.5 22.5t-28.5 59.5q0 13 3 28q8 31 28 54.5t66 49.5l121 572h187l-121 -572h-37q-32 -14 -51.5 -23t-30 -16.5t-15 -14.5
t-6.5 -16z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="365" 
d="M236 978h189l-25 -122h-190zM195 802h196l-171 -802h-195z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="338" 
d="M137 572h187l-121 -572h-187z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="836" 
d="M195 802h196l-171 -802h-195zM451 -14q-37 0 -62.5 1.5t-57.5 9.5l33 161q20 -2 41 -3t41 -1q27 0 45 4t30.5 13.5t20 25t12.5 37.5l120 568h195l-121 -572q-15 -71 -36 -118t-54 -75t-83 -39.5t-124 -11.5z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="675" 
d="M183 797h190l-31 -145h-190zM521 797h190l-31 -145h-190zM137 572h187l-121 -572h-187zM217 -61q14 -2 29 -2.5t28 -0.5q35 0 53.5 15t26.5 55l120 566h187l-120 -566q-12 -58 -29.5 -99.5t-46 -68.5t-70 -39.5t-101.5 -12.5q-33 0 -58 1.5t-49 7.5z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="471" 
d="M333 987h230l96 -140h-158l-60 72l-92 -72h-172zM86 -14q-37 0 -62.5 1.5t-57.5 9.5l33 161q20 -2 41 -3t41 -1q27 0 45 4t30.5 13.5t20 25t12.5 37.5l120 568h195l-121 -572q-15 -71 -36 -118t-54 -75t-83 -39.5t-124 -11.5z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="337" 
d="M179 802h198l88 -170h-159l-38 86l-77 -86h-172zM-122 -67q14 -2 29.5 -3t27.5 -1q35 0 53.5 16.5t27.5 60.5l120 566h187l-120 -566q-12 -58 -30 -99.5t-46.5 -68.5t-69.5 -39.5t-101 -12.5q-33 0 -58 1.5t-49 7.5z" />
    <glyph glyph-name="uni0136" unicode="&#x136;" horiz-adv-x="863" 
d="M195 802h196l-68 -321l368 321h255l-447 -386l283 -416h-226l-253 389l-83 -389h-195zM251 -53h212l-201 -145h-180z" />
    <glyph glyph-name="uni0137" unicode="&#x137;" horiz-adv-x="732" 
d="M185 802h187l-96 -454l259 224h236l-312 -269l194 -303h-215l-172 301l-64 -301h-187zM189 -53h212l-201 -145h-180z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="732" 
d="M137 572h186l-47 -224l259 224h244l-315 -271l193 -301h-219l-172 301l-64 -301h-187z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="657" 
d="M494 987h213l-167 -140h-189zM195 802h196l-136 -638h346l-34 -164h-542z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="337" 
d="M325 987h213l-167 -140h-189zM185 802h187l-170 -802h-187z" />
    <glyph glyph-name="uni013B" unicode="&#x13b;" horiz-adv-x="657" 
d="M195 802h196l-136 -638h346l-34 -164h-542zM197 -53h212l-201 -145h-180z" />
    <glyph glyph-name="uni013C" unicode="&#x13c;" horiz-adv-x="337" 
d="M185 802h187l-170 -802h-187zM-4 -53h212l-201 -145h-180z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="657" 
d="M195 802h196l-136 -638h346l-34 -164h-542zM482 802h165l-170 -252h-93z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="372" 
d="M185 802h187l-170 -802h-187zM461 802h165l-170 -252h-93z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="657" 
d="M195 802h196l-136 -638h346l-34 -164h-542zM482 570h192l-38 -183h-192z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="530" 
d="M185 802h187l-170 -802h-187zM349 377h192l-38 -183h-192z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="657" 
d="M84 278l-94 -40l37 170l93 40l75 354h196l-56 -261l157 67l-36 -170l-157 -67l-44 -207h346l-34 -164h-542z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="337" 
d="M55 189l-83 -37l32 152l83 36l98 462h187l-79 -371l83 36l-32 -151l-83 -36l-59 -280h-187z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="954" 
d="M633 987h213l-167 -140h-189zM195 802h226l247 -545l116 545h195l-170 -802h-232l-244 534l-113 -534h-195z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="780" 
d="M553 800h213l-211 -170h-179zM282 535q54 25 107 39t113 14q135 0 180.5 -54.5t45.5 -124.5q0 -32 -8 -69l-72 -340h-187l71 338q4 17 4 31q0 25 -16 42t-70 17q-42 0 -83 -11.5t-83 -29.5l-82 -387h-187l121 572h128z" />
    <glyph glyph-name="uni0145" unicode="&#x145;" horiz-adv-x="954" 
d="M195 802h226l247 -545l116 545h195l-170 -802h-232l-244 534l-113 -534h-195zM301 -53h212l-201 -145h-180z" />
    <glyph glyph-name="uni0146" unicode="&#x146;" horiz-adv-x="780" 
d="M282 535q54 25 107 39t113 14q135 0 180.5 -54.5t45.5 -124.5q0 -32 -8 -69l-72 -340h-187l71 338q4 17 4 31q0 25 -16 42t-70 17q-42 0 -83 -11.5t-83 -29.5l-82 -387h-187l121 572h128zM218 -53h212l-201 -145h-180z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="954" 
d="M386 987h158l61 -72l91 72h172l-156 -140h-230zM195 802h226l247 -545l116 545h195l-170 -802h-232l-244 534l-113 -534h-195z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="780" 
d="M277 802h158l40 -85l76 85h172l-160 -170h-198zM282 535q54 25 107 39t113 14q135 0 180.5 -54.5t45.5 -124.5q0 -32 -8 -69l-72 -340h-187l71 338q4 17 4 31q0 25 -16 42t-70 17q-42 0 -83 -11.5t-83 -29.5l-82 -387h-187l121 572h128z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="1009" 
d="M245 802h205l-201 -337h-171zM512 535q54 25 107 39t113 14q135 0 180.5 -54.5t45.5 -124.5q0 -32 -8 -69l-72 -340h-187l71 338q4 17 4 31q0 25 -16 42t-70 17q-42 0 -83 -11.5t-83 -29.5l-82 -387h-187l121 572h128z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="954" 
d="M195 802h239l243 -501l107 501h195l-170 -804q-13 -60 -33 -100.5t-53.5 -65t-83 -35.5t-121.5 -11q-19 0 -34.5 0.5t-29.5 2t-27.5 3.5t-29.5 4l33 158q20 -2 40.5 -3t42.5 -1q27 0 45 2t29.5 7.5t17 15.5t8.5 25v1l-274 561l-119 -562h-195z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="780" 
d="M324 -60q14 -2 29.5 -3t28.5 -1q35 0 53 14.5t26 55.5l71 332q4 19 4 34q0 25 -15.5 44t-70.5 19q-41 0 -82 -11.5t-83 -28.5l-83 -395h-187l121 572h128l18 -37q54 25 107 39t113 14q135 0 180 -55.5t45 -124.5q0 -32 -7 -68l-71 -334q-12 -58 -30 -99.5t-46.5 -68.5
t-70 -39.5t-101.5 -12.5q-33 0 -58.5 1.5t-48.5 7.5z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="924" 
d="M405 974h407l-24 -116h-407zM417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q115 0 191.5 -27t118 -82t46 -106.5t4.5 -71.5q0 -71 -20 -161q-21 -99 -56.5 -171.5t-92.5 -120t-138 -71t-193 -23.5z
M439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="738" 
d="M274 780h401l-26 -124h-401zM324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q84 0 144.5 -17t96 -56t41.5 -80t6 -64q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48t-143.5 -15zM341 131
q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="924" 
d="M590 842q-66 0 -110 8t-69 25.5t-30 37.5t-5 34q0 18 5 40h140q-1 -5 -1 -10q0 -15 14 -23.5t69 -8.5q28 0 46.5 2t30 7t17.5 13t9 20h140q-8 -39 -24 -66.5t-45.5 -45t-74.5 -25.5t-112 -8zM417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161
q21 99 56.5 171.5t92.5 120t138 71t193 23.5q115 0 191.5 -27t118 -82t46 -106.5t4.5 -71.5q0 -71 -20 -161q-21 -99 -56.5 -171.5t-92.5 -120t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5
q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="738" 
d="M448 632q-112 0 -151 32.5t-39 84.5q0 22 6 48h140q-3 -13 -3 -23q0 -16 9 -27.5t54 -11.5t63 14.5t25 47.5h141q-9 -44 -24 -75.5t-42 -51.5t-70 -29t-109 -9zM324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5
t142 15q84 0 144.5 -17t96 -56t41.5 -80t6 -64q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48t-143.5 -15zM341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69
q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="924" 
d="M481 987h197l-144 -140h-172zM748 987h197l-144 -140h-172zM417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q115 0 191.5 -27t118 -82t46 -106.5t4.5 -71.5q0 -71 -20 -161q-21 -99 -56.5 -171.5
t-92.5 -120t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="738" 
d="M366 802h197l-170 -170h-163zM609 802h196l-169 -170h-164zM324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q84 0 144.5 -17t96 -56t41.5 -80t6 -64q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48
t-143.5 -15zM341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1305" 
d="M553 1h-113q-120 0 -200 24.5t-123.5 76.5t-49.5 104.5t-6 79.5q0 62 15 140q22 96 56 167t86.5 117.5t126.5 69t177 22.5h839l-36 -167h-442l-32 -150h405l-34 -160h-405l-35 -165h442l-34 -160h-637v1zM587 160l101 475h-168q-61 0 -103.5 -13t-71 -41.5t-46 -72.5
t-30.5 -106q-13 -64 -13 -110t18.5 -75t59 -43t106.5 -14h147z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1157" 
d="M316 -16q-81 0 -139.5 17.5t-92.5 56.5t-40 80t-6 64q0 47 13 107q16 76 43.5 129t69.5 86.5t99.5 48.5t133.5 15q81 0 141 -23t92 -74q45 53 104.5 75.5t134.5 22.5q72 0 125.5 -18t85.5 -55.5t38 -77t6 -62.5q0 -43 -11 -96l-10 -53h-418v-14q0 -21 5 -37t20 -27
t40 -15t61 -4q31 0 59 1t57.5 3t61.5 6t72 10l-27 -139q-60 -14 -115 -20.5t-115 -6.5q-88 0 -147.5 20.5t-90.5 68.5q-41 -48 -101.5 -68.5t-148.5 -20.5zM853 450q-30 0 -52 -5t-39 -16.5t-29 -30.5t-22 -48h240q1 9 1 18q0 40 -21.5 61t-77.5 21zM341 131q37 0 63.5 8
t45 26.5t31 48t21.5 72.5q9 41 9 69q0 2 -0.5 17.5t-12.5 34t-36.5 26.5t-63.5 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-8 -38 -8 -66q0 -35 17.5 -62t94.5 -27z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="881" 
d="M597 987h213l-167 -140h-189zM195 802h378q90 0 154 -17t101.5 -55t45 -80t7.5 -69q0 -43 -11 -97q-24 -103 -70 -164t-130 -85l89 -235h-206l-72 217h-215l-46 -217h-195zM356 639l-55 -259h221q33 0 58 5t43.5 19.5t31.5 39.5t21 64q6 30 6 52q0 6 -1.5 22t-16.5 30.5
t-42 20.5t-67 6h-199z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="465" 
d="M425 800h213l-211 -170h-179zM136 572h128l17 -35q53 26 103.5 37t113.5 12l-33 -164q-48 -2 -92 -9t-88 -20l-83 -393h-187z" />
    <glyph glyph-name="uni0156" unicode="&#x156;" horiz-adv-x="881" 
d="M195 802h378q90 0 154 -17t101.5 -55t45 -80t7.5 -69q0 -43 -11 -97q-24 -103 -70 -164t-130 -85l89 -235h-206l-72 217h-215l-46 -217h-195zM356 639l-55 -259h221q33 0 58 5t43.5 19.5t31.5 39.5t21 64q6 30 6 52q0 6 -1.5 22t-16.5 30.5t-42 20.5t-67 6h-199zM258 -53
h212l-201 -145h-180z" />
    <glyph glyph-name="uni0157" unicode="&#x157;" horiz-adv-x="465" 
d="M136 572h128l17 -35q53 26 103.5 37t113.5 12l-33 -164q-48 -2 -92 -9t-88 -20l-83 -393h-187zM-7 -53h212l-201 -145h-180z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="881" 
d="M350 987h158l61 -72l91 72h172l-156 -140h-230zM195 802h378q90 0 154 -17t101.5 -55t45 -80t7.5 -69q0 -43 -11 -97q-24 -103 -70 -164t-130 -85l89 -235h-206l-72 217h-215l-46 -217h-195zM356 639l-55 -259h221q33 0 58 5t43.5 19.5t31.5 39.5t21 64q6 30 6 52
q0 6 -1.5 22t-16.5 30.5t-42 20.5t-67 6h-199z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="465" 
d="M150 802h158l40 -85l76 85h172l-160 -170h-198zM136 572h128l17 -35q53 26 103.5 37t113.5 12l-33 -164q-48 -2 -92 -9t-88 -20l-83 -393h-187z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="825" 
d="M579 987h213l-167 -140h-189zM58 199q78 -17 163 -32t169 -15q44 0 73 3.5t48 11.5t29 21.5t14 32.5q3 15 3 26q0 6 -1.5 16.5t-16.5 22t-45 20t-80 16.5q-84 12 -143.5 30t-94.5 49.5t-41.5 65.5t-6.5 56q0 37 10 83q10 47 33 86t66.5 67t112.5 43.5t171 15.5
q82 0 157.5 -8t156.5 -28l-36 -170q-85 19 -155.5 28.5t-145.5 9.5q-83 0 -120.5 -13t-45.5 -50q-3 -14 -3 -24q0 -8 2.5 -18t18.5 -19t46 -16t78 -15q85 -13 145.5 -30.5t95.5 -49t42 -66t7 -56.5q0 -38 -11 -87q-11 -51 -35.5 -92.5t-68.5 -70t-112.5 -44t-168.5 -15.5
q-90 0 -182 12t-164 33z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="679" 
d="M504 800h213l-211 -170h-179zM34 155q64 -14 136 -24t138 -10q35 0 57.5 1.5t36 5.5t20 10.5t8.5 16.5q2 8 2 14t-2.5 12.5t-14.5 13t-34.5 11t-58.5 8.5q-73 8 -123 20t-78 36t-33 50.5t-5 43.5q0 28 7 64q8 37 25 66.5t50.5 50.5t88 32t137.5 11q135 0 274 -27
l-29 -138q-69 14 -129.5 21t-117.5 7q-35 0 -57.5 -2t-36 -6t-20 -10.5t-8.5 -16.5q-2 -8 -2 -14q0 -5 2 -11t13.5 -11.5t34 -9t60.5 -8.5q73 -8 123 -21t78.5 -37t33.5 -50.5t5 -43.5q0 -28 -7 -63q-8 -38 -25.5 -68t-51 -51t-88.5 -32.5t-138 -11.5q-37 0 -77 2t-79.5 6
t-76.5 10.5t-67 14.5z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="825" 
d="M458 987h230l96 -140h-158l-60 72l-92 -72h-172zM58 199q78 -17 163 -32t169 -15q44 0 73 3.5t48 11.5t29 21.5t14 32.5q3 15 3 26q0 6 -1.5 16.5t-16.5 22t-45 20t-80 16.5q-84 12 -143.5 30t-94.5 49.5t-41.5 65.5t-6.5 56q0 37 10 83q10 47 33 86t66.5 67t112.5 43.5
t171 15.5q82 0 157.5 -8t156.5 -28l-36 -170q-85 19 -155.5 28.5t-145.5 9.5q-83 0 -120.5 -13t-45.5 -50q-3 -14 -3 -24q0 -8 2.5 -18t18.5 -19t46 -16t78 -15q85 -13 145.5 -30.5t95.5 -49t42 -66t7 -56.5q0 -38 -11 -87q-11 -51 -35.5 -92.5t-68.5 -70t-112.5 -44
t-168.5 -15.5q-90 0 -182 12t-164 33z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="679" 
d="M353 802h198l88 -170h-159l-38 86l-77 -86h-172zM34 155q64 -14 136 -24t138 -10q35 0 57.5 1.5t36 5.5t20 10.5t8.5 16.5q2 8 2 14t-2.5 12.5t-14.5 13t-34.5 11t-58.5 8.5q-73 8 -123 20t-78 36t-33 50.5t-5 43.5q0 28 7 64q8 37 25 66.5t50.5 50.5t88 32t137.5 11
q135 0 274 -27l-29 -138q-69 14 -129.5 21t-117.5 7q-35 0 -57.5 -2t-36 -6t-20 -10.5t-8.5 -16.5q-2 -8 -2 -14q0 -5 2 -11t13.5 -11.5t34 -9t60.5 -8.5q73 -8 123 -21t78.5 -37t33.5 -50.5t5 -43.5q0 -28 -7 -63q-8 -38 -25.5 -68t-51 -51t-88.5 -32.5t-138 -11.5
q-37 0 -77 2t-79.5 6t-76.5 10.5t-67 14.5z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="825" 
d="M58 199q78 -17 163 -32t169 -15q44 0 73 3.5t48 11.5t29 21.5t14 32.5q3 15 3 26q0 6 -1.5 16.5t-16.5 22t-45 20t-80 16.5q-84 12 -143.5 30t-94.5 49.5t-41.5 65.5t-6.5 56q0 37 10 83q10 47 33 86t66.5 67t112.5 43.5t171 15.5q82 0 157.5 -8t156.5 -28l-36 -170
q-85 19 -155.5 28.5t-145.5 9.5q-83 0 -120.5 -13t-45.5 -50q-3 -14 -3 -24q0 -8 2.5 -18t18.5 -19t46 -16t78 -15q85 -13 145.5 -30.5t95.5 -49t42 -66t7 -56.5q0 -38 -11 -87q-11 -50 -34 -90t-64.5 -68.5t-105 -45t-156.5 -18.5l-32 -26q56 -1 81 -18t25 -48
q0 -12 -3 -27q-4 -21 -14 -36t-28 -24.5t-44.5 -14t-65.5 -4.5q-29 0 -52 2t-46 7l14 65q27 -5 45.5 -6t39.5 -1q36 0 48 4.5t15 18.5q1 3 1 6q0 9 -8 13.5t-45 4.5q-17 0 -33 -1.5t-36 -3.5l13 57l41 33q-78 4 -152.5 15.5t-134.5 28.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="679" 
d="M34 155q64 -14 136 -24t138 -10q35 0 57.5 1.5t36 5.5t20 10.5t8.5 16.5q2 8 2 14t-2.5 12.5t-14.5 13t-34.5 11t-58.5 8.5q-73 8 -123 20t-78 36t-33 50.5t-5 43.5q0 28 7 64q8 37 25 66.5t50.5 50.5t88 32t137.5 11q135 0 274 -27l-29 -138q-69 14 -129.5 21t-117.5 7
q-35 0 -57.5 -2t-36 -6t-20 -10.5t-8.5 -16.5q-2 -8 -2 -14q0 -5 2 -11t13.5 -11.5t34 -9t60.5 -8.5q73 -8 123 -21t78.5 -37t33.5 -50.5t5 -43.5q0 -28 -7 -63q-8 -38 -25 -68t-50 -51t-86 -32.5t-133 -11.5l-32 -26q56 -1 81 -18t25 -48q0 -12 -3 -27q-4 -21 -14 -36
t-28 -24.5t-44.5 -14t-65.5 -4.5q-29 0 -52 2t-46 7l14 65q27 -5 45.5 -6t39.5 -1q36 0 48 4.5t15 18.5q1 3 1 6q0 9 -8 13.5t-45 4.5q-17 0 -33 -1.5t-36 -3.5l13 57l41 34q-60 3 -119.5 10.5t-105.5 20.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="825" 
d="M332 987h158l61 -72l91 72h172l-156 -140h-230zM58 199q78 -17 163 -32t169 -15q44 0 73 3.5t48 11.5t29 21.5t14 32.5q3 15 3 26q0 6 -1.5 16.5t-16.5 22t-45 20t-80 16.5q-84 12 -143.5 30t-94.5 49.5t-41.5 65.5t-6.5 56q0 37 10 83q10 47 33 86t66.5 67t112.5 43.5
t171 15.5q82 0 157.5 -8t156.5 -28l-36 -170q-85 19 -155.5 28.5t-145.5 9.5q-83 0 -120.5 -13t-45.5 -50q-3 -14 -3 -24q0 -8 2.5 -18t18.5 -19t46 -16t78 -15q85 -13 145.5 -30.5t95.5 -49t42 -66t7 -56.5q0 -38 -11 -87q-11 -51 -35.5 -92.5t-68.5 -70t-112.5 -44
t-168.5 -15.5q-90 0 -182 12t-164 33z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="679" 
d="M229 802h158l40 -85l76 85h172l-160 -170h-198zM34 155q64 -14 136 -24t138 -10q35 0 57.5 1.5t36 5.5t20 10.5t8.5 16.5q2 8 2 14t-2.5 12.5t-14.5 13t-34.5 11t-58.5 8.5q-73 8 -123 20t-78 36t-33 50.5t-5 43.5q0 28 7 64q8 37 25 66.5t50.5 50.5t88 32t137.5 11
q135 0 274 -27l-29 -138q-69 14 -129.5 21t-117.5 7q-35 0 -57.5 -2t-36 -6t-20 -10.5t-8.5 -16.5q-2 -8 -2 -14q0 -5 2 -11t13.5 -11.5t34 -9t60.5 -8.5q73 -8 123 -21t78.5 -37t33.5 -50.5t5 -43.5q0 -28 -7 -63q-8 -38 -25.5 -68t-51 -51t-88.5 -32.5t-138 -11.5
q-37 0 -77 2t-79.5 6t-76.5 10.5t-67 14.5z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="799" 
d="M295 0h-53l134 633h-271l35 169h739l-36 -169h-271l-135 -633h-56l-51 -42q56 -1 81 -18t25 -48q0 -12 -3 -27q-4 -21 -14 -36t-28 -24.5t-44.5 -14t-65.5 -4.5q-29 0 -52 2t-46 7l14 65q27 -5 45.5 -6t39.5 -1q36 0 48 4.5t15 18.5q1 3 1 6q0 9 -8 13.5t-45 4.5
q-17 0 -33 -1.5t-36 -3.5l13 57z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="487" 
d="M205 -3q-47 8 -76 25.5t-43 44.5t-14.5 46.5t-0.5 21.5q0 36 10 80l46 215h-78l23 108l86 34l31 145h187l-31 -145h160l-30 -142h-160l-47 -219q-4 -16 -4 -28q0 -18 11.5 -29.5t60.5 -11.5q20 0 39 1t39 3l-30 -145q-21 -4 -46 -6.5t-53 -2.5l-42 -34q56 -1 81 -18
t25 -48q0 -12 -3 -27q-4 -21 -14 -36t-28 -24.5t-44.5 -14t-65.5 -4.5q-29 0 -52 2t-46 7l14 65q27 -5 45.5 -6t39.5 -1q36 0 48 4.5t15 18.5q1 3 1 6q0 9 -8 13.5t-45 4.5q-17 0 -33 -1.5t-36 -3.5l13 57z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="799" 
d="M310 987h158l61 -72l91 72h172l-156 -140h-230zM376 633h-271l35 169h739l-36 -169h-271l-135 -633h-195z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="487" 
d="M481 868h165l-170 -252h-93zM283 -8q-72 0 -117 14t-67.5 42t-24.5 53.5t-2 36.5q0 34 9 77l46 215h-78l23 108l86 34l31 145h187l-31 -145h160l-30 -142h-160l-47 -219q-4 -16 -4 -28q0 -18 11.5 -29.5t60.5 -11.5q20 0 39 1t39 3l-30 -145q-21 -4 -46.5 -6.5
t-54.5 -2.5z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="799" 
d="M301 279h-156l29 140h157l45 214h-271l35 169h739l-36 -169h-271l-46 -214h156l-30 -140h-156l-59 -279h-195z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="487" 
d="M32 351h78l17 79h-78l23 108l86 34l31 145h187l-31 -145h160l-30 -142h-160l-17 -79h119l-28 -131h-119l-2 -9q-4 -16 -4 -28q0 -18 11.5 -29.5t60.5 -11.5q20 0 39 1t39 3l-30 -145q-21 -4 -46.5 -6.5t-54.5 -2.5q-72 0 -117 14t-67.5 42t-24.5 53.5t-2 36.5q0 34 9 77
l1 5h-78z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="923" 
d="M369 867q16 66 49 93t87 27q31 0 57 -6t47.5 -13.5t39.5 -14t33 -6.5t22.5 7t9.5 18l2 10h116l-4 -22q-14 -66 -48.5 -93t-88.5 -27q-31 0 -57 6.5t-47.5 14t-39.5 14t-33 6.5t-22 -7t-10 -20l-2 -10h-116zM421 -16q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64
q0 58 16 133l94 446h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h196l-96 -449q-22 -104 -52 -175t-79.5 -114t-124.5 -61.5t-187 -18.5z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="780" 
d="M252 663q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116zM280 -16q-135 0 -180 55.5
t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h187l-121 -572h-129l-18 37q-54 -25 -107 -39t-112 -14z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="923" 
d="M405 974h407l-24 -116h-407zM421 -16q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64q0 58 16 133l94 446h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h196l-96 -449q-22 -104 -52 -175t-79.5 -114t-124.5 -61.5
t-187 -18.5z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="780" 
d="M295 780h401l-26 -124h-401zM280 -16q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h187l-121 -572h-129l-18 37q-54 -25 -107 -39t-112 -14z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="923" 
d="M590 842q-66 0 -110 8t-69 25.5t-30 37.5t-5 34q0 18 5 40h140q-1 -5 -1 -10q0 -15 14 -23.5t69 -8.5q28 0 46.5 2t30 7t17.5 13t9 20h140q-8 -39 -24 -66.5t-45.5 -45t-74.5 -25.5t-112 -8zM421 -16q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64q0 58 16 133l94 446
h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h196l-96 -449q-22 -104 -52 -175t-79.5 -114t-124.5 -61.5t-187 -18.5z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="780" 
d="M469 632q-112 0 -151 32.5t-39 84.5q0 22 6 48h140q-3 -13 -3 -23q0 -16 9 -27.5t54 -11.5t63 14.5t25 47.5h141q-9 -44 -24 -75.5t-42 -51.5t-70 -29t-109 -9zM280 -16q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5
t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h187l-121 -572h-129l-18 37q-54 -25 -107 -39t-112 -14z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="923" 
d="M421 -16q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64q0 58 16 133l94 446h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h196l-96 -449q-22 -104 -52 -175t-79.5 -114t-124.5 -61.5t-187 -18.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="780" 
d="M466 617q-81 0 -111 23t-30 62q0 16 4 35q7 32 19 55t32 37t51 20.5t75 6.5q80 0 110.5 -23.5t30.5 -62.5q0 -16 -4 -34q-7 -33 -19 -55.5t-32.5 -36.5t-51 -20.5t-74.5 -6.5zM474 688q37 0 53 9.5t22 38.5q3 11 3 20q0 13 -9 21t-45 8q-37 0 -53 -9.5t-22 -38.5
q-2 -11 -2 -19q0 -14 8.5 -22t44.5 -8zM280 -16q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h187l-121 -572h-129l-18 37q-54 -25 -107 -39t-112 -14z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="923" 
d="M481 987h197l-144 -140h-172zM748 987h197l-144 -140h-172zM421 -16q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64q0 58 16 133l94 446h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h196l-96 -449
q-22 -104 -52 -175t-79.5 -114t-124.5 -61.5t-187 -18.5z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="780" 
d="M387 802h197l-170 -170h-163zM630 802h196l-169 -170h-164zM280 -16q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h187l-121 -572h-129l-18 37q-54 -25 -107 -39t-112 -14z
" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="923" 
d="M393 -70q-1 -5 -1 -10q0 -11 8.5 -17t40.5 -6q19 0 37.5 2.5t38.5 4.5l-22 -106q-26 -5 -52 -8.5t-57 -3.5q-80 0 -108.5 22.5t-28.5 59.5q0 13 3 28q7 27 23 48.5t51 43.5q-84 8 -138.5 33.5t-82.5 70.5t-30 83t-2 48q0 59 16 133l94 446h196l-95 -446q-13 -62 -13 -101
t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h196l-96 -449q-21 -99 -49 -168t-73.5 -112.5t-113 -64t-167.5 -23.5q-40 -19 -52.5 -30t-15.5 -25z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="780" 
d="M280 -16q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h187l-121 -572h-33q-34 -14 -55 -24t-32.5 -18t-16 -15t-6.5 -16q-1 -5 -1 -10q0 -11 8.5 -17t40.5 -6q19 0 37.5 2.5
t38.5 4.5l-22 -106q-26 -5 -52 -8.5t-57 -3.5q-80 0 -108.5 22.5t-28.5 59.5q0 13 3 28q5 19 14 35.5t25.5 31.5t42 30.5t62.5 31.5l-7 15q-54 -25 -107 -39t-112 -14z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="1337" 
d="M696 987h230l96 -140h-158l-60 72l-92 -72h-172zM134 802h202l3 -638h15l253 638h330l-19 -638h16l274 638h214l-354 -802h-328l11 637h-15l-260 -637h-328z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="1085" 
d="M548 802h198l88 -170h-159l-38 86l-77 -86h-172zM84 572h189l-11 -420h7l188 420h277l7 -420l8 1l168 419h207l-249 -572h-285l-13 419h-11l-192 -419h-284z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="856" 
d="M465 987h230l96 -140h-158l-60 72l-92 -72h-172zM335 313l-225 489h209l141 -313l275 313h229l-434 -489l-66 -313h-195z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="715" 
d="M368 802h198l88 -170h-159l-38 86l-77 -86h-172zM71 572h189l53 -407h20l226 406l208 1l-339 -594q-31 -56 -59.5 -92.5t-63 -59t-78.5 -31.5t-107 -9q-33 0 -65.5 2.5t-65.5 7.5l31 146q20 -3 41.5 -4.5t40.5 -1.5q33 0 55 2.5t36 8t23.5 15t16.5 23.5l16 28h-95z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="856" 
d="M386 978h149l-26 -122h-149zM618 978h150l-26 -122h-149zM335 313l-225 489h209l141 -313l275 313h229l-434 -489l-66 -313h-195z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="787" 
d="M561 987h213l-167 -140h-189zM13 163l515 470h-398l36 169h676l-35 -164l-512 -469h430l-36 -169h-711z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="664" 
d="M503 800h213l-211 -170h-179zM12 129l357 301h-281l31 142h549l-28 -129l-361 -301h310l-30 -142h-575z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="787" 
d="M457 978h189l-25 -122h-190zM13 163l515 470h-398l36 169h676l-35 -164l-512 -469h430l-36 -169h-711z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="664" 
d="M353 791h190l-31 -146h-190zM12 129l357 301h-281l31 142h549l-28 -129l-361 -301h310l-30 -142h-575z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="787" 
d="M314 987h158l61 -72l91 72h172l-156 -140h-230zM13 163l515 470h-398l36 169h676l-35 -164l-512 -469h430l-36 -169h-711z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="664" 
d="M227 802h158l40 -85l76 85h172l-160 -170h-198zM12 129l357 301h-281l31 142h549l-28 -129l-361 -301h310l-30 -142h-575z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="466" 
d="M-87 -61q14 -2 28.5 -2.5t28.5 -0.5q35 0 53.5 15t26.5 55l122 575q12 58 32.5 102t53 74t80 45.5t113.5 15.5q36 0 62 -2.5t57 -7.5l-30 -144q-19 2 -37.5 3t-38.5 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-122 -576q-12 -59 -29.5 -102t-46 -71t-70.5 -42
t-104 -14q-32 0 -55.5 1.5t-51.5 7.5z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="716" 
d="M99 477h192l22 106q13 61 34 105t57.5 73t92 43t137.5 14q36 0 62.5 -2t58.5 -7l-32 -150q-24 4 -47.5 5t-46.5 1q-31 0 -52.5 -4t-36 -14.5t-23 -28t-14.5 -44.5l-21 -97h202l-32 -147h-201l-68 -317q-12 -55 -31.5 -97.5t-53 -71t-85 -43.5t-127.5 -15q-23 0 -41 0.5
t-33.5 1.5t-29.5 3t-29 4l33 150q23 -4 45.5 -5t45.5 -1q31 0 51 4t33 14t20.5 27t13.5 42l64 304h-192z" />
    <glyph glyph-name="Ohorn" unicode="&#x1a0;" horiz-adv-x="924" 
d="M417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q88 0 156 -17h30q45 0 71 28.5t33 78.5h151q-11 -66 -46 -115t-95 -73q48 -55 53.5 -109.5t5.5 -78.5q0 -71 -19 -162q-21 -99 -56.5 -171.5t-92.5 -120
t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5z" />
    <glyph glyph-name="ohorn" unicode="&#x1a1;" horiz-adv-x="738" 
d="M324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q42 0 78 -4t67 -13h12q45 0 71 28.5t33 78.5h151q-12 -69 -49 -119t-102 -73q22 -38 24.5 -71.5t2.5 -44.5q0 -48 -13 -108q-16 -76 -44.5 -129t-72 -86
t-105 -48t-143.5 -15zM341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8z" />
    <glyph glyph-name="Uhorn" unicode="&#x1af;" horiz-adv-x="923" 
d="M421 -16q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64q0 58 16 133l94 446h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h89q38 5 60 33t28 73h151q-12 -69 -49 -119t-101 -73l-78 -363q-22 -104 -52 -175
t-79.5 -114t-124.5 -61.5t-187 -18.5z" />
    <glyph glyph-name="uhorn" unicode="&#x1b0;" horiz-adv-x="780" 
d="M280 -16q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h79q38 5 60 33t28 73h151q-12 -69 -48.5 -118.5t-100.5 -72.5l-103 -487h-129l-18 37q-54 -25 -107 -39t-112 -14z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="905" 
d="M609 987h213l-167 -140h-189zM406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31l-159 -293h225z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="768" 
d="M604 1069h213l-211 -170h-179zM460 617q-81 0 -111 23t-30 62q0 16 4 35q7 32 19 55t32 37t51 20.5t75 6.5q80 0 110.5 -23.5t30.5 -62.5q0 -16 -4 -34q-7 -33 -19 -55.5t-32.5 -36.5t-51 -20.5t-74.5 -6.5zM468 688q37 0 53 9.5t22 38.5q3 11 3 20q0 13 -9 21t-45 8
q-37 0 -53 -9.5t-22 -38.5q-2 -11 -2 -19q0 -14 8.5 -22t44.5 -8zM289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5
t-97.5 -11.5zM237 288q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="1313" 
d="M886 987h213l-167 -140h-189zM598 181h-287l-136 -181h-241l617 802h817l-35 -167h-443l-31 -147h404l-35 -167h-405l-34 -161h443l-35 -160h-637zM634 350l61 285h-41l-215 -285h195z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="1147" 
d="M711 782h213l-211 -170h-179zM253 -16q-60 0 -106 12t-75 38.5t-36 57t-7 52.5q0 29 7 64q8 36 25.5 63.5t47.5 46.5t74 28.5t106 9.5h191l3 14q3 12 3 22q0 5 -1.5 14t-15 17.5t-42 13t-77.5 4.5q-54 0 -110 -5t-117 -13l30 146q33 5 58 9t49.5 6t52.5 3t67 1
q93 0 148 -18t81 -51q40 37 93.5 53t123.5 16q73 0 126.5 -17.5t85 -55t37 -76.5t5.5 -62q0 -43 -10 -96l-10 -55h-418v-14q0 -20 5 -36t20 -26.5t40 -14.5t61 -4q31 0 59 0.5t57.5 3t61.5 6t72 9.5l-27 -139q-64 -16 -123 -21.5t-123 -5.5q-85 0 -144.5 15.5t-95.5 50.5
q-50 -32 -110.5 -49t-141.5 -17zM810 458q-30 0 -52.5 -5.5t-39 -18t-28.5 -33.5t-22 -52h240q1 11 1 20q0 43 -21 66t-78 23zM285 112q51 0 92 10.5t79 31.5q-2 17 -2.5 29t-0.5 16q0 16 2 34h-159q-38 0 -56 -12t-24 -40q-3 -12 -3 -22q0 -21 14.5 -34t57.5 -13z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="924" 
d="M619 987h213l-167 -140h-189zM4 91l73 59q-20 52 -20 120q0 1 0.5 37t19.5 125q21 99 56.5 171.5t92.5 120t138 71t193 23.5q169 0 256 -59l69 59l88 -107l-73 -59q20 -52 20 -120q0 -1 -0.5 -37t-19.5 -125q-21 -99 -56.5 -171.5t-92.5 -120t-138 -71t-193 -23.5
q-167 0 -255 58l-69 -58zM268 401q-6 -30 -9.5 -56t-4.5 -49l403 331q-41 20 -123 20q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5zM439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5q6 30 9.5 56t4.5 49l-402 -332q42 -19 121 -19z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="738" 
d="M532 800h213l-211 -170h-179zM-5 82l54 42q-11 34 -11 78q0 48 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q63 0 112.5 -9.5t85.5 -30.5l54 40l79 -98l-54 -42q11 -33 11 -77q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48t-143.5 -15q-62 0 -111 9.5
t-84 30.5l-54 -40zM237 286q-4 -19 -6 -37l239 181q-25 11 -72 11q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5zM341 131q49 0 78.5 15t47 40.5t26.5 60.5t16 75l-238 -180q23 -11 70 -11z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="825" 
d="M58 199q78 -17 163 -32t169 -15q44 0 73 3.5t48 11.5t29 21.5t14 32.5q3 15 3 26q0 6 -1.5 16.5t-16.5 22t-45 20t-80 16.5q-84 12 -143.5 30t-94.5 49.5t-41.5 65.5t-6.5 56q0 37 10 83q10 47 33 86t66.5 67t112.5 43.5t171 15.5q82 0 157.5 -8t156.5 -28l-36 -170
q-85 19 -155.5 28.5t-145.5 9.5q-83 0 -120.5 -13t-45.5 -50q-3 -14 -3 -24q0 -8 2.5 -18t18.5 -19t46 -16t78 -15q85 -13 145.5 -30.5t95.5 -49t42 -66t7 -56.5q0 -38 -11 -87q-11 -51 -35.5 -92.5t-68.5 -70t-112.5 -44t-168.5 -15.5q-90 0 -182 12t-164 33zM260 -53h212
l-201 -145h-180z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="679" 
d="M34 155q64 -14 136 -24t138 -10q35 0 57.5 1.5t36 5.5t20 10.5t8.5 16.5q2 8 2 14t-2.5 12.5t-14.5 13t-34.5 11t-58.5 8.5q-73 8 -123 20t-78 36t-33 50.5t-5 43.5q0 28 7 64q8 37 25 66.5t50.5 50.5t88 32t137.5 11q135 0 274 -27l-29 -138q-69 14 -129.5 21t-117.5 7
q-35 0 -57.5 -2t-36 -6t-20 -10.5t-8.5 -16.5q-2 -8 -2 -14q0 -5 2 -11t13.5 -11.5t34 -9t60.5 -8.5q73 -8 123 -21t78.5 -37t33.5 -50.5t5 -43.5q0 -28 -7 -63q-8 -38 -25.5 -68t-51 -51t-88.5 -32.5t-138 -11.5q-37 0 -77 2t-79.5 6t-76.5 10.5t-67 14.5zM181 -53h212
l-201 -145h-180z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="799" 
d="M376 633h-271l35 169h739l-36 -169h-271l-135 -633h-195zM229 -53h212l-201 -145h-180z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="487" 
d="M283 -8q-72 0 -117 14t-67.5 42t-24.5 53.5t-2 36.5q0 34 9 77l46 215h-78l23 108l86 34l31 145h187l-31 -145h160l-30 -142h-160l-47 -219q-4 -16 -4 -28q0 -18 11.5 -29.5t60.5 -11.5q20 0 39 1t39 3l-30 -145q-21 -4 -46.5 -6.5t-54.5 -2.5zM142 -53h212l-201 -145
h-180z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="337" 
d="M-122 -67q14 -2 29.5 -3t27.5 -1q35 0 53.5 16.5t27.5 60.5l120 566h187l-120 -566q-12 -58 -30 -99.5t-46.5 -68.5t-69.5 -39.5t-101 -12.5q-33 0 -58 1.5t-49 7.5z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="570" 
d="M296 802h198l88 -170h-159l-38 86l-77 -86h-172z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="570" 
d="M172 802h158l40 -85l76 85h172l-160 -170h-198z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="570" 
d="M364 632q-112 0 -151 32.5t-39 84.5q0 22 6 48h140q-3 -13 -3 -23q0 -16 9 -27.5t54 -11.5t63 14.5t25 47.5h141q-9 -44 -24 -75.5t-42 -51.5t-70 -29t-109 -9z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="570" 
d="M298 791h190l-31 -146h-190z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="570" 
d="M361 617q-81 0 -111 23t-30 62q0 16 4 35q7 32 19 55t32 37t51 20.5t75 6.5q80 0 110.5 -23.5t30.5 -62.5q0 -16 -4 -34q-7 -33 -19 -55.5t-32.5 -36.5t-51 -20.5t-74.5 -6.5zM369 688q37 0 53 9.5t22 38.5q3 11 3 20q0 13 -9 21t-45 8q-37 0 -53 -9.5t-22 -38.5
q-2 -11 -2 -19q0 -14 8.5 -22t44.5 -8z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="570" 
d="M212 -70q-1 -5 -1 -10q0 -11 8.5 -17t40.5 -6q19 0 37.5 2.5t38.5 4.5l-22 -106q-26 -5 -52 -8.5t-57 -3.5q-80 0 -108.5 22.5t-28.5 59.5q0 13 3 28q5 19 14 35.5t26.5 32t44 31t65.5 33.5l120 -17q-40 -17 -65 -28t-38.5 -20t-18.5 -16.5t-7 -16.5z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="570" 
d="M146 663q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="570" 
d="M226 802h197l-170 -170h-163zM469 802h196l-169 -170h-164z" />
    <glyph glyph-name="gravecomb" unicode="&#x300;" horiz-adv-x="0" 
d="M-149 802h200l104 -170h-165z" />
    <glyph glyph-name="acutecomb" unicode="&#x301;" horiz-adv-x="0" 
d="M163 802h213l-211 -170h-179z" />
    <glyph glyph-name="uni0302" unicode="&#x302;" horiz-adv-x="0" 
d="M11 802h198l88 -170h-159l-38 86l-77 -86h-172z" />
    <glyph glyph-name="tildecomb" unicode="&#x303;" horiz-adv-x="0" 
d="M-138 663q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116z" />
    <glyph glyph-name="uni0304" unicode="&#x304;" horiz-adv-x="0" 
d="M-95 780h401l-26 -124h-401z" />
    <glyph glyph-name="uni0306" unicode="&#x306;" horiz-adv-x="0" 
d="M79 632q-113 0 -151.5 32t-38.5 83q0 23 6 50h140q-3 -13 -3 -23q0 -16 9 -27.5t54 -11.5t63 14.5t25 47.5h141q-9 -44 -24 -75.5t-42 -51.5t-70 -29t-109 -9z" />
    <glyph glyph-name="uni0307" unicode="&#x307;" horiz-adv-x="0" 
d="M13 791h190l-31 -146h-190z" />
    <glyph glyph-name="uni0308" unicode="&#x308;" horiz-adv-x="0" 
d="M-69 791h141l-31 -146h-141zM144 791h141l-30 -146h-141z" />
    <glyph glyph-name="uni030A" unicode="&#x30a;" horiz-adv-x="0" 
d="M76 617q-81 0 -111 23t-30 62q0 16 4 35q7 32 19 55t32 37t51 20.5t75 6.5q80 0 110.5 -23.5t30.5 -62.5q0 -16 -4 -34q-7 -33 -19 -55.5t-32.5 -36.5t-51 -20.5t-74.5 -6.5zM84 688q37 0 53 9.5t22 38.5q3 11 3 20q0 13 -9 21t-45 8q-37 0 -53 -9.5t-22 -38.5
q-2 -11 -2 -19q0 -14 8.5 -22t44.5 -8z" />
    <glyph glyph-name="uni030B" unicode="&#x30b;" horiz-adv-x="0" 
d="M-3 802h197l-170 -170h-163zM240 802h196l-169 -170h-164z" />
    <glyph glyph-name="uni030C" unicode="&#x30c;" horiz-adv-x="0" 
d="M-113 802h158l40 -85l76 85h172l-160 -170h-198z" />
    <glyph glyph-name="uni0312" unicode="&#x312;" horiz-adv-x="0" 
d="M180 632h-212l209 170h179z" />
    <glyph glyph-name="uni0326" unicode="&#x326;" horiz-adv-x="0" 
d="M-171 -53h212l-201 -145h-180z" />
    <glyph glyph-name="uni0327" unicode="&#x327;" horiz-adv-x="0" 
d="M-101 3h86l-55 -45q28 0 49.5 -5t35.5 -15.5t17.5 -23.5t3.5 -23q0 -12 -3 -26q-4 -21 -14 -36t-28 -24.5t-44.5 -14t-65.5 -4.5q-29 0 -52 2t-46 7l14 65q27 -5 45.5 -6t39.5 -1q36 0 48 4.5t15 18.5q1 3 1 6q0 9 -8 13.5t-45 4.5q-17 0 -33 -1.5t-36 -3.5l13 57z" />
    <glyph glyph-name="uni0328" unicode="&#x328;" horiz-adv-x="0" 
d="M-116 -70q-1 -5 -1 -10q0 -11 8.5 -17t40.5 -6q19 0 37.5 2.5t38.5 4.5l-22 -106q-26 -5 -52 -8.5t-57 -3.5q-80 0 -108.5 22.5t-28.5 59.5q0 13 3 28q5 19 14 35.5t26.5 32t44 31t65.5 33.5l120 -17q-41 -17 -65.5 -28t-38 -20t-18.5 -16.5t-7 -16.5z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" 
d="M17 149h109q-45 52 -48.5 103.5t-3.5 69.5q0 72 18 161q18 87 47.5 150t74.5 104.5t106.5 61t143.5 19.5q96 0 159 -25t96 -74.5t35.5 -93.5t2.5 -58q0 -66 -17 -149q-20 -98 -57 -163.5t-90 -105.5h93l-31 -149h-302l27 133q35 9 61 31.5t45.5 57.5t34 81t26.5 103
q16 74 18.5 105.5t2.5 43.5q0 31 -8.5 56t-33.5 37.5t-67 12.5t-72 -12t-52 -41.5t-38.5 -78.5t-32.5 -123q-12 -57 -15.5 -91.5t-3.5 -54.5t3.5 -46.5t20 -49t46.5 -31.5l-28 -133h-302z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" 
d="M130 403h-68l36 169h687l-36 -169h-68l-86 -403h-195l86 403h-161l-85 -403h-195z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="1337" 
d="M597 987h199l83 -140h-174zM134 802h202l3 -638h15l253 638h330l-19 -638h16l274 638h214l-354 -802h-328l11 637h-15l-260 -637h-328z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="1085" 
d="M388 802h200l104 -170h-165zM84 572h189l-11 -420h7l188 420h277l7 -420l8 1l168 419h207l-249 -572h-285l-13 419h-11l-192 -419h-284z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="1337" 
d="M817 987h213l-167 -140h-189zM134 802h202l3 -638h15l253 638h330l-19 -638h16l274 638h214l-354 -802h-328l11 637h-15l-260 -637h-328z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="1085" 
d="M700 800h213l-211 -170h-179zM84 572h189l-11 -420h7l188 420h277l7 -420l8 1l168 419h207l-249 -572h-285l-13 419h-11l-192 -419h-284z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="1337" 
d="M617 978h149l-26 -122h-149zM849 978h150l-26 -122h-149zM134 802h202l3 -638h15l253 638h330l-19 -638h16l274 638h214l-354 -802h-328l11 637h-15l-260 -637h-328z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="1085" 
d="M468 791h141l-31 -146h-141zM681 791h141l-30 -146h-141zM84 572h189l-11 -420h7l188 420h277l7 -420l8 1l168 419h207l-249 -572h-285l-13 419h-11l-192 -419h-284z" />
    <glyph glyph-name="uni1EA0" unicode="&#x1ea0;" horiz-adv-x="905" 
d="M406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31l-159 -293h225zM310 -76h178l-26 -150h-178z" />
    <glyph glyph-name="uni1EA1" unicode="&#x1ea1;" horiz-adv-x="768" 
d="M289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25
t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71zM219 -76h178l-26 -150h-178z" />
    <glyph glyph-name="uni1EA2" unicode="&#x1ea2;" horiz-adv-x="905" 
d="M533 864q10 18 22.5 29.5t24 20.5t20.5 18t11 22q1 4 1 7q0 10 -8 18t-25 8q-18 0 -35.5 -12.5t-27.5 -24.5l-54 32q29 37 69 60.5t97 23.5q26 0 47 -7.5t35 -20t19.5 -26t5.5 -24.5q0 -6 -1 -13q-4 -23 -16.5 -38.5t-29.5 -28.5t-35 -26.5t-35 -33.5zM406 802h313
l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31l-159 -293h225z" />
    <glyph glyph-name="uni1EA3" unicode="&#x1ea3;" horiz-adv-x="768" 
d="M408 666q10 18 22.5 29.5t24 20.5t20.5 18t11 22q1 4 1 7q0 10 -8 18t-25 8q-18 0 -35.5 -12.5t-27.5 -24.5l-54 32q29 37 69 60.5t97 23.5q26 0 47 -7.5t35 -20t19.5 -26t5.5 -24.5q0 -6 -1 -13q-4 -23 -16.5 -38.5t-29.5 -28.5t-35 -26.5t-35 -33.5zM289 -16
q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5
q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z" />
    <glyph glyph-name="uni1EA4" unicode="&#x1ea4;" horiz-adv-x="905" 
d="M488 987h230l96 -140h-158l-60 72l-92 -72h-172zM406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31l-159 -293h225zM696 1185h213l-211 -170h-179z" />
    <glyph glyph-name="uni1EA5" unicode="&#x1ea5;" horiz-adv-x="768" 
d="M395 802h198l88 -170h-159l-38 86l-77 -86h-172zM289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288
q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71zM589 1000h213l-211 -170h-179z" />
    <glyph glyph-name="uni1EA6" unicode="&#x1ea6;" horiz-adv-x="905" 
d="M488 987h230l96 -140h-158l-60 72l-92 -72h-172zM406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31l-159 -293h225zM384 1187h200l104 -170h-165z" />
    <glyph glyph-name="uni1EA7" unicode="&#x1ea7;" horiz-adv-x="768" 
d="M395 802h198l88 -170h-159l-38 86l-77 -86h-172zM289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288
q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71zM277 1002h200l104 -170h-165z" />
    <glyph glyph-name="uni1EA8" unicode="&#x1ea8;" horiz-adv-x="905" 
d="M756 962q9 15 19.5 25t20 18t17 15.5t9.5 17.5v5q0 10 -6.5 17t-20.5 7q-15 0 -29.5 -10.5t-23.5 -21.5l-46 27q25 32 59 52t83 20q44 0 67 -21t23 -45q0 -6 -1 -12q-3 -19 -14 -32t-25 -24.5t-29.5 -23t-29.5 -28.5zM488 987h230l96 -140h-158l-60 72l-92 -72h-172z
M406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31l-159 -293h225z" />
    <glyph glyph-name="uni1EA9" unicode="&#x1ea9;" horiz-adv-x="768" 
d="M651 767q9 15 19.5 25t20 18t17 15.5t9.5 17.5v5q0 10 -6.5 17t-20.5 7q-15 0 -29.5 -10.5t-23.5 -21.5l-46 27q25 32 59 52t83 20q44 0 67 -21t23 -45q0 -6 -1 -12q-3 -19 -14 -32t-25 -24.5t-29.5 -23t-29.5 -28.5zM395 802h198l88 -170h-159l-38 86l-77 -86h-172z
M289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25
t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z" />
    <glyph glyph-name="uni1EAA" unicode="&#x1eaa;" horiz-adv-x="905" 
d="M488 987h230l96 -140h-158l-60 72l-92 -72h-172zM406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31l-159 -293h225zM395 1048q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26
q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116z" />
    <glyph glyph-name="uni1EAB" unicode="&#x1eab;" horiz-adv-x="768" 
d="M395 802h198l88 -170h-159l-38 86l-77 -86h-172zM289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288
q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71zM288 863q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11
h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116z" />
    <glyph glyph-name="uni1EAC" unicode="&#x1eac;" horiz-adv-x="905" 
d="M406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31l-159 -293h225zM310 -76h178l-26 -150h-178zM512 1032h198l88 -170h-159l-38 86l-77 -86h-172z" />
    <glyph glyph-name="uni1EAD" unicode="&#x1ead;" horiz-adv-x="768" 
d="M289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25
t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71zM219 -76h178l-26 -150h-178zM395 802h198l88 -170h-159l-38 86l-77 -86h-172z" />
    <glyph glyph-name="uni1EAE" unicode="&#x1eae;" horiz-adv-x="905" 
d="M580 842q-66 0 -110 8t-69 25.5t-30 37.5t-5 34q0 18 5 40h140q-1 -5 -1 -10q0 -15 14 -23.5t69 -8.5q28 0 46.5 2t30 7t17.5 13t9 20h140q-8 -39 -24 -66.5t-45.5 -45t-74.5 -25.5t-112 -8zM406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31
l-159 -293h225zM696 1185h213l-211 -170h-179z" />
    <glyph glyph-name="uni1EAF" unicode="&#x1eaf;" horiz-adv-x="768" 
d="M463 632q-112 0 -151 32.5t-39 84.5q0 22 6 48h140q-3 -13 -3 -23q0 -16 9 -27.5t54 -11.5t63 14.5t25 47.5h141q-9 -44 -24 -75.5t-42 -51.5t-70 -29t-109 -9zM289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5
q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z
M588 995h213l-211 -170h-179z" />
    <glyph glyph-name="uni1EB0" unicode="&#x1eb0;" horiz-adv-x="905" 
d="M580 842q-66 0 -110 8t-69 25.5t-30 37.5t-5 34q0 18 5 40h140q-1 -5 -1 -10q0 -15 14 -23.5t69 -8.5q28 0 46.5 2t30 7t17.5 13t9 20h140q-8 -39 -24 -66.5t-45.5 -45t-74.5 -25.5t-112 -8zM406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31
l-159 -293h225zM384 1187h200l104 -170h-165z" />
    <glyph glyph-name="uni1EB1" unicode="&#x1eb1;" horiz-adv-x="768" 
d="M463 632q-112 0 -151 32.5t-39 84.5q0 22 6 48h140q-3 -13 -3 -23q0 -16 9 -27.5t54 -11.5t63 14.5t25 47.5h141q-9 -44 -24 -75.5t-42 -51.5t-70 -29t-109 -9zM289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5
q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z
M276 997h200l104 -170h-165z" />
    <glyph glyph-name="uni1EB2" unicode="&#x1eb2;" horiz-adv-x="905" 
d="M599 1031q9 15 19.5 25t20 18t17 15.5t9.5 17.5v5q0 10 -6.5 17t-20.5 7q-15 0 -29.5 -10.5t-23.5 -21.5l-46 27q25 32 59 52t83 20q44 0 67 -21t23 -45q0 -6 -1 -12q-3 -19 -14 -32t-25 -24.5t-29.5 -23t-29.5 -28.5zM580 842q-66 0 -110 8t-69 25.5t-30 37.5t-5 34
q0 18 5 40h140q-1 -5 -1 -10q0 -15 14 -23.5t69 -8.5q28 0 46.5 2t30 7t17.5 13t9 20h140q-8 -39 -24 -66.5t-45.5 -45t-74.5 -25.5t-112 -8zM406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31l-159 -293h225z" />
    <glyph glyph-name="uni1EB3" unicode="&#x1eb3;" horiz-adv-x="768" 
d="M448 864q10 18 22.5 29.5t24 20.5t20.5 18t11 22q1 4 1 7q0 10 -8 18t-25 8q-18 0 -35.5 -12.5t-27.5 -24.5l-54 32q29 37 69 60.5t97 23.5q26 0 47 -7.5t35 -20t19.5 -26t5.5 -24.5q0 -6 -1 -13q-4 -23 -16.5 -38.5t-29.5 -28.5t-35 -26.5t-35 -33.5zM463 632
q-112 0 -151 32.5t-39 84.5q0 22 6 48h140q-3 -13 -3 -23q0 -16 9 -27.5t54 -11.5t63 14.5t25 47.5h141q-9 -44 -24 -75.5t-42 -51.5t-70 -29t-109 -9zM289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5
q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z
" />
    <glyph glyph-name="uni1EB4" unicode="&#x1eb4;" horiz-adv-x="905" 
d="M580 842q-66 0 -110 8t-69 25.5t-30 37.5t-5 34q0 18 5 40h140q-1 -5 -1 -10q0 -15 14 -23.5t69 -8.5q28 0 46.5 2t30 7t17.5 13t9 20h140q-8 -39 -24 -66.5t-45.5 -45t-74.5 -25.5t-112 -8zM406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31
l-159 -293h225zM395 1048q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116z" />
    <glyph glyph-name="uni1EB5" unicode="&#x1eb5;" horiz-adv-x="768" 
d="M463 632q-112 0 -151 32.5t-39 84.5q0 22 6 48h140q-3 -13 -3 -23q0 -16 9 -27.5t54 -11.5t63 14.5t25 47.5h141q-9 -44 -24 -75.5t-42 -51.5t-70 -29t-109 -9zM289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5
q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71z
M287 858q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116z" />
    <glyph glyph-name="uni1EB6" unicode="&#x1eb6;" horiz-adv-x="905" 
d="M406 802h313l109 -802h-199l-21 181h-336l-98 -181h-217zM588 349l-35 293h-31l-159 -293h225zM310 -76h178l-26 -150h-178zM580 862q-113 0 -151.5 32t-38.5 83q0 23 6 50h140q-3 -13 -3 -23q0 -16 9 -27.5t54 -11.5t63 14.5t25 47.5h141q-9 -44 -24 -75.5t-42 -51.5
t-70 -29t-109 -9z" />
    <glyph glyph-name="uni1EB7" unicode="&#x1eb7;" horiz-adv-x="768" 
d="M289 -16q-70 0 -122.5 19t-84 58.5t-37 80t-5.5 62.5q0 47 12 104q17 81 46.5 134.5t71 86t94 46t115.5 13.5q57 0 108.5 -13.5t105.5 -38.5l33 36h128l-121 -572h-128l-15 30q-55 -23 -103.5 -34.5t-97.5 -11.5zM237 288q-7 -34 -7 -60q0 -7 1.5 -25t16 -35.5t40 -25
t61.5 -7.5q32 0 65 7.5t66 19.5l53 248q-28 12 -58 19.5t-63 7.5q-39 0 -68 -7.5t-50 -24.5t-34.5 -46t-22.5 -71zM219 -76h178l-26 -150h-178zM463 632q-113 0 -151.5 32t-38.5 83q0 23 6 50h140q-3 -13 -3 -23q0 -16 9 -27.5t54 -11.5t63 14.5t25 47.5h141
q-9 -44 -24 -75.5t-42 -51.5t-70 -29t-109 -9z" />
    <glyph glyph-name="uni1EB8" unicode="&#x1eb8;" horiz-adv-x="777" 
d="M195 802h637l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441l-35 -167h-637zM231 -76h178l-26 -150h-178z" />
    <glyph glyph-name="uni1EB9" unicode="&#x1eb9;" horiz-adv-x="699" 
d="M403 588q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139q-65 -15 -123.5 -21t-122.5 -6q-87 0 -148.5 16.5t-96.5 55t-41 80t-6 64.5q0 49 13 111
q17 77 47 129.5t73.5 85.5t101 47.5t130.5 14.5zM395 449q-29 0 -51.5 -5t-39 -16.5t-29 -30.5t-22.5 -47h239q1 10 1 18q0 40 -21 60.5t-77 20.5zM219 -76h178l-26 -150h-178z" />
    <glyph glyph-name="uni1EBA" unicode="&#x1eba;" horiz-adv-x="777" 
d="M533 864q10 18 22.5 29.5t24 20.5t20.5 18t11 22q1 4 1 7q0 10 -8 18t-25 8q-18 0 -35.5 -12.5t-27.5 -24.5l-54 32q29 37 69 60.5t97 23.5q26 0 47 -7.5t35 -20t19.5 -26t5.5 -24.5q0 -6 -1 -13q-4 -23 -16.5 -38.5t-29.5 -28.5t-35 -26.5t-35 -33.5zM195 802h637
l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441l-35 -167h-637z" />
    <glyph glyph-name="uni1EBB" unicode="&#x1ebb;" horiz-adv-x="699" 
d="M382 664q10 18 22.5 29.5t24 20.5t20.5 18t11 22q1 4 1 7q0 10 -8 18t-25 8q-18 0 -35.5 -12.5t-27.5 -24.5l-54 32q29 37 69 60.5t97 23.5q26 0 47 -7.5t35 -20t19.5 -26t5.5 -24.5q0 -6 -1 -13q-4 -23 -16.5 -38.5t-29.5 -28.5t-35 -26.5t-35 -33.5zM403 588
q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139q-65 -15 -123.5 -21t-122.5 -6q-87 0 -148.5 16.5t-96.5 55t-41 80t-6 64.5q0 49 13 111q17 77 47 129.5
t73.5 85.5t101 47.5t130.5 14.5zM395 449q-29 0 -51.5 -5t-39 -16.5t-29 -30.5t-22.5 -47h239q1 10 1 18q0 40 -21 60.5t-77 20.5z" />
    <glyph glyph-name="uni1EBC" unicode="&#x1ebc;" horiz-adv-x="777" 
d="M195 802h637l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441l-35 -167h-637zM317 893q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18
t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116z" />
    <glyph glyph-name="uni1EBD" unicode="&#x1ebd;" horiz-adv-x="699" 
d="M403 588q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139q-65 -15 -123.5 -21t-122.5 -6q-87 0 -148.5 16.5t-96.5 55t-41 80t-6 64.5q0 49 13 111
q17 77 47 129.5t73.5 85.5t101 47.5t130.5 14.5zM395 449q-29 0 -51.5 -5t-39 -16.5t-29 -30.5t-22.5 -47h239q1 10 1 18q0 40 -21 60.5t-77 20.5zM222 663q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26
q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116z" />
    <glyph glyph-name="uni1EBE" unicode="&#x1ebe;" horiz-adv-x="777" 
d="M442 987h230l96 -140h-158l-60 72l-92 -72h-172zM195 802h637l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441l-35 -167h-637zM650 1185h213l-211 -170h-179z" />
    <glyph glyph-name="uni1EBF" unicode="&#x1ebf;" horiz-adv-x="699" 
d="M371 802h198l88 -170h-159l-38 86l-77 -86h-172zM403 588q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139q-65 -15 -123.5 -21t-122.5 -6q-87 0 -148.5 16.5
t-96.5 55t-41 80t-6 64.5q0 49 13 111q17 77 47 129.5t73.5 85.5t101 47.5t130.5 14.5zM395 449q-29 0 -51.5 -5t-39 -16.5t-29 -30.5t-22.5 -47h239q1 10 1 18q0 40 -21 60.5t-77 20.5zM566 1000h213l-211 -170h-179z" />
    <glyph glyph-name="uni1EC0" unicode="&#x1ec0;" horiz-adv-x="777" 
d="M442 987h230l96 -140h-158l-60 72l-92 -72h-172zM195 802h637l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441l-35 -167h-637zM338 1187h200l104 -170h-165z" />
    <glyph glyph-name="uni1EC1" unicode="&#x1ec1;" horiz-adv-x="699" 
d="M371 802h198l88 -170h-159l-38 86l-77 -86h-172zM403 588q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139q-65 -15 -123.5 -21t-122.5 -6q-87 0 -148.5 16.5
t-96.5 55t-41 80t-6 64.5q0 49 13 111q17 77 47 129.5t73.5 85.5t101 47.5t130.5 14.5zM395 449q-29 0 -51.5 -5t-39 -16.5t-29 -30.5t-22.5 -47h239q1 10 1 18q0 40 -21 60.5t-77 20.5zM254 1002h200l104 -170h-165z" />
    <glyph glyph-name="uni1EC2" unicode="&#x1ec2;" horiz-adv-x="777" 
d="M756 962q9 15 19.5 25t20 18t17 15.5t9.5 17.5v5q0 10 -6.5 17t-20.5 7q-15 0 -29.5 -10.5t-23.5 -21.5l-46 27q25 32 59 52t83 20q44 0 67 -21t23 -45q0 -6 -1 -12q-3 -19 -14 -32t-25 -24.5t-29.5 -23t-29.5 -28.5zM442 987h230l96 -140h-158l-60 72l-92 -72h-172z
M195 802h637l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441l-35 -167h-637z" />
    <glyph glyph-name="uni1EC3" unicode="&#x1ec3;" horiz-adv-x="699" 
d="M635 767q9 15 19.5 25t20 18t17 15.5t9.5 17.5v5q0 10 -6.5 17t-20.5 7q-15 0 -29.5 -10.5t-23.5 -21.5l-46 27q25 32 59 52t83 20q44 0 67 -21t23 -45q0 -6 -1 -12q-3 -19 -14 -32t-25 -24.5t-29.5 -23t-29.5 -28.5zM371 802h198l88 -170h-159l-38 86l-77 -86h-172z
M403 588q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139q-65 -15 -123.5 -21t-122.5 -6q-87 0 -148.5 16.5t-96.5 55t-41 80t-6 64.5q0 49 13 111
q17 77 47 129.5t73.5 85.5t101 47.5t130.5 14.5zM395 449q-29 0 -51.5 -5t-39 -16.5t-29 -30.5t-22.5 -47h239q1 10 1 18q0 40 -21 60.5t-77 20.5z" />
    <glyph glyph-name="uni1EC4" unicode="&#x1ec4;" horiz-adv-x="777" 
d="M442 987h230l96 -140h-158l-60 72l-92 -72h-172zM195 802h637l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441l-35 -167h-637zM349 1048q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26
q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116z" />
    <glyph glyph-name="uni1EC5" unicode="&#x1ec5;" horiz-adv-x="699" 
d="M371 802h198l88 -170h-159l-38 86l-77 -86h-172zM403 588q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139q-65 -15 -123.5 -21t-122.5 -6q-87 0 -148.5 16.5
t-96.5 55t-41 80t-6 64.5q0 49 13 111q17 77 47 129.5t73.5 85.5t101 47.5t130.5 14.5zM395 449q-29 0 -51.5 -5t-39 -16.5t-29 -30.5t-22.5 -47h239q1 10 1 18q0 40 -21 60.5t-77 20.5zM265 863q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5
q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116z" />
    <glyph glyph-name="uni1EC6" unicode="&#x1ec6;" horiz-adv-x="777" 
d="M195 802h637l-35 -167h-441l-32 -147h404l-35 -167h-404l-33 -154h441l-35 -167h-637zM231 -76h178l-26 -150h-178zM466 1032h198l88 -170h-159l-38 86l-77 -86h-172z" />
    <glyph glyph-name="uni1EC7" unicode="&#x1ec7;" horiz-adv-x="699" 
d="M403 588q74 0 129 -17t88 -54.5t39.5 -77t6.5 -63.5q0 -43 -11 -96l-10 -53h-419v-14q0 -21 5 -37t20 -26.5t40 -14.5t61 -4q32 0 60.5 0.5t58 3t62 6t72.5 9.5l-29 -139q-65 -15 -123.5 -21t-122.5 -6q-87 0 -148.5 16.5t-96.5 55t-41 80t-6 64.5q0 49 13 111
q17 77 47 129.5t73.5 85.5t101 47.5t130.5 14.5zM395 449q-29 0 -51.5 -5t-39 -16.5t-29 -30.5t-22.5 -47h239q1 10 1 18q0 40 -21 60.5t-77 20.5zM219 -76h178l-26 -150h-178zM371 802h198l88 -170h-159l-38 86l-77 -86h-172z" />
    <glyph glyph-name="uni1EC8" unicode="&#x1ec8;" horiz-adv-x="365" 
d="M269 864q10 18 22.5 29.5t24 20.5t20.5 18t11 22q1 4 1 7q0 10 -8 18t-25 8q-18 0 -35.5 -12.5t-27.5 -24.5l-54 32q29 37 69 60.5t97 23.5q26 0 47 -7.5t35 -20t19.5 -26t5.5 -24.5q0 -6 -1 -13q-4 -23 -16.5 -38.5t-29.5 -28.5t-35 -26.5t-35 -33.5zM195 802h196
l-171 -802h-195z" />
    <glyph glyph-name="uni1EC9" unicode="&#x1ec9;" horiz-adv-x="338" 
d="M199 668q10 18 22.5 29.5t24 20.5t20.5 18t11 22q1 4 1 7q0 10 -8 18t-25 8q-18 0 -35.5 -12.5t-27.5 -24.5l-54 32q29 37 69 60.5t97 23.5q26 0 47 -7.5t35 -20t19.5 -26t5.5 -24.5q0 -6 -1 -13q-4 -23 -16.5 -38.5t-29.5 -28.5t-35 -26.5t-35 -33.5zM137 572h187
l-121 -572h-187z" />
    <glyph glyph-name="uni1ECA" unicode="&#x1eca;" horiz-adv-x="365" 
d="M195 802h196l-171 -802h-195zM16 -76h178l-26 -150h-178z" />
    <glyph glyph-name="uni1ECB" unicode="&#x1ecb;" horiz-adv-x="338" 
d="M183 797h190l-31 -145h-190zM137 572h187l-121 -572h-187zM5 -76h178l-26 -150h-178z" />
    <glyph glyph-name="uni1ECC" unicode="&#x1ecc;" horiz-adv-x="924" 
d="M417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q115 0 191.5 -27t118 -82t46 -106.5t4.5 -71.5q0 -71 -20 -161q-21 -99 -56.5 -171.5t-92.5 -120t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5
t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5zM310 -76h178l-26 -150h-178z" />
    <glyph glyph-name="uni1ECD" unicode="&#x1ecd;" horiz-adv-x="738" 
d="M324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q84 0 144.5 -17t96 -56t41.5 -80t6 -64q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48t-143.5 -15zM341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5
q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8zM205 -76h178l-26 -150h-178z" />
    <glyph glyph-name="uni1ECE" unicode="&#x1ece;" horiz-adv-x="924" 
d="M533 864q10 18 22.5 29.5t24 20.5t20.5 18t11 22q1 4 1 7q0 10 -8 18t-25 8q-18 0 -35.5 -12.5t-27.5 -24.5l-54 32q29 37 69 60.5t97 23.5q26 0 47 -7.5t35 -20t19.5 -26t5.5 -24.5q0 -6 -1 -13q-4 -23 -16.5 -38.5t-29.5 -28.5t-35 -26.5t-35 -33.5zM417 -16
q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q115 0 191.5 -27t118 -82t46 -106.5t4.5 -71.5q0 -71 -20 -161q-21 -99 -56.5 -171.5t-92.5 -120t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5t50 76.5
t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5z" />
    <glyph glyph-name="uni1ECF" unicode="&#x1ecf;" horiz-adv-x="738" 
d="M400 668q10 18 22.5 29.5t24 20.5t20.5 18t11 22q1 4 1 7q0 10 -8 18t-25 8q-18 0 -35.5 -12.5t-27.5 -24.5l-54 32q29 37 69 60.5t97 23.5q26 0 47 -7.5t35 -20t19.5 -26t5.5 -24.5q0 -6 -1 -13q-4 -23 -16.5 -38.5t-29.5 -28.5t-35 -26.5t-35 -33.5zM324 -16
q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q84 0 144.5 -17t96 -56t41.5 -80t6 -64q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48t-143.5 -15zM341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5
t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8z" />
    <glyph glyph-name="uni1ED0" unicode="&#x1ed0;" horiz-adv-x="924" 
d="M498 987h230l96 -140h-158l-60 72l-92 -72h-172zM417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q115 0 191.5 -27t118 -82t46 -106.5t4.5 -71.5q0 -71 -20 -161q-21 -99 -56.5 -171.5t-92.5 -120
t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5zM705 1185h213l-211 -170h-179z" />
    <glyph glyph-name="uni1ED1" unicode="&#x1ed1;" horiz-adv-x="738" 
d="M380 802h198l88 -170h-159l-38 86l-77 -86h-172zM324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q84 0 144.5 -17t96 -56t41.5 -80t6 -64q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48t-143.5 -15z
M341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8zM574 1000h213l-211 -170h-179z" />
    <glyph glyph-name="uni1ED2" unicode="&#x1ed2;" horiz-adv-x="924" 
d="M498 987h230l96 -140h-158l-60 72l-92 -72h-172zM417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q115 0 191.5 -27t118 -82t46 -106.5t4.5 -71.5q0 -71 -20 -161q-21 -99 -56.5 -171.5t-92.5 -120
t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5zM393 1187h200l104 -170h-165z" />
    <glyph glyph-name="uni1ED3" unicode="&#x1ed3;" horiz-adv-x="738" 
d="M380 802h198l88 -170h-159l-38 86l-77 -86h-172zM324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q84 0 144.5 -17t96 -56t41.5 -80t6 -64q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48t-143.5 -15z
M341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8zM262 1002h200l104 -170h-165z" />
    <glyph glyph-name="uni1ED4" unicode="&#x1ed4;" horiz-adv-x="924" 
d="M791 962q9 15 19.5 25t20 18t17 15.5t9.5 17.5v5q0 10 -6.5 17t-20.5 7q-15 0 -29.5 -10.5t-23.5 -21.5l-46 27q25 32 59 52t83 20q44 0 67 -21t23 -45q0 -6 -1 -12q-3 -19 -14 -32t-25 -24.5t-29.5 -23t-29.5 -28.5zM498 987h230l96 -140h-158l-60 72l-92 -72h-172z
M417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q115 0 191.5 -27t118 -82t46 -106.5t4.5 -71.5q0 -71 -20 -161q-21 -99 -56.5 -171.5t-92.5 -120t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5
t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5z" />
    <glyph glyph-name="uni1ED5" unicode="&#x1ed5;" horiz-adv-x="738" 
d="M635 767q9 15 19.5 25t20 18t17 15.5t9.5 17.5v5q0 10 -6.5 17t-20.5 7q-15 0 -29.5 -10.5t-23.5 -21.5l-46 27q25 32 59 52t83 20q44 0 67 -21t23 -45q0 -6 -1 -12q-3 -19 -14 -32t-25 -24.5t-29.5 -23t-29.5 -28.5zM380 802h198l88 -170h-159l-38 86l-77 -86h-172z
M324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q84 0 144.5 -17t96 -56t41.5 -80t6 -64q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48t-143.5 -15zM341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5
q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8z" />
    <glyph glyph-name="uni1ED6" unicode="&#x1ed6;" horiz-adv-x="924" 
d="M498 987h230l96 -140h-158l-60 72l-92 -72h-172zM417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q115 0 191.5 -27t118 -82t46 -106.5t4.5 -71.5q0 -71 -20 -161q-21 -99 -56.5 -171.5t-92.5 -120
t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5zM404 1048q16 76 52.5 107.5t89.5 31.5
q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116z" />
    <glyph glyph-name="uni1ED7" unicode="&#x1ed7;" horiz-adv-x="738" 
d="M380 802h198l88 -170h-159l-38 86l-77 -86h-172zM324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q84 0 144.5 -17t96 -56t41.5 -80t6 -64q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48t-143.5 -15z
M341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8zM273 863q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5
t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116z" />
    <glyph glyph-name="uni1ED8" unicode="&#x1ed8;" horiz-adv-x="924" 
d="M417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q115 0 191.5 -27t118 -82t46 -106.5t4.5 -71.5q0 -71 -20 -161q-21 -99 -56.5 -171.5t-92.5 -120t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5
t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5zM310 -76h178l-26 -150h-178zM521 1032h198l88 -170h-159l-38 86l-77 -86h-172z" />
    <glyph glyph-name="uni1ED9" unicode="&#x1ed9;" horiz-adv-x="738" 
d="M324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q84 0 144.5 -17t96 -56t41.5 -80t6 -64q0 -48 -13 -109q-16 -76 -44.5 -129t-72 -86t-105 -48t-143.5 -15zM341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5
q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8zM205 -76h178l-26 -150h-178zM380 802h198l88 -170h-159l-38 86l-77 -86h-172z" />
    <glyph glyph-name="uni1EDA" unicode="&#x1eda;" horiz-adv-x="924" 
d="M417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q88 0 156 -17h30q45 0 71 28.5t33 78.5h151q-11 -66 -46 -115t-95 -73q48 -55 53.5 -109.5t5.5 -78.5q0 -71 -19 -162q-21 -99 -56.5 -171.5t-92.5 -120
t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5zM673 1030h213l-211 -170h-179z" />
    <glyph glyph-name="uni1EDB" unicode="&#x1edb;" horiz-adv-x="738" 
d="M324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q42 0 78 -4t67 -13h12q45 0 71 28.5t33 78.5h151q-12 -69 -49 -119t-102 -73q22 -38 24.5 -71.5t2.5 -44.5q0 -48 -13 -108q-16 -76 -44.5 -129t-72 -86
t-105 -48t-143.5 -15zM341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8zM532 800h213l-211 -170h-179z" />
    <glyph glyph-name="uni1EDC" unicode="&#x1edc;" horiz-adv-x="924" 
d="M417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q88 0 156 -17h30q45 0 71 28.5t33 78.5h151q-11 -66 -46 -115t-95 -73q48 -55 53.5 -109.5t5.5 -78.5q0 -71 -19 -162q-21 -99 -56.5 -171.5t-92.5 -120
t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5zM361 1032h200l104 -170h-165z" />
    <glyph glyph-name="uni1EDD" unicode="&#x1edd;" horiz-adv-x="738" 
d="M324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q42 0 78 -4t67 -13h12q45 0 71 28.5t33 78.5h151q-12 -69 -49 -119t-102 -73q22 -38 24.5 -71.5t2.5 -44.5q0 -48 -13 -108q-16 -76 -44.5 -129t-72 -86
t-105 -48t-143.5 -15zM341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8zM220 802h200l104 -170h-165z" />
    <glyph glyph-name="uni1EDE" unicode="&#x1ede;" horiz-adv-x="924" 
d="M533 864q10 18 22.5 29.5t24 20.5t20.5 18t11 22q1 4 1 7q0 10 -8 18t-25 8q-18 0 -35.5 -12.5t-27.5 -24.5l-54 32q29 37 69 60.5t97 23.5q26 0 47 -7.5t35 -20t19.5 -26t5.5 -24.5q0 -6 -1 -13q-4 -23 -16.5 -38.5t-29.5 -28.5t-35 -26.5t-35 -33.5zM417 -16
q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q88 0 156 -17h30q45 0 71 28.5t33 78.5h151q-11 -66 -46 -115t-95 -73q48 -55 53.5 -109.5t5.5 -78.5q0 -71 -19 -162q-21 -99 -56.5 -171.5t-92.5 -120t-138 -71
t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5z" />
    <glyph glyph-name="uni1EDF" unicode="&#x1edf;" horiz-adv-x="738" 
d="M400 668q10 18 22.5 29.5t24 20.5t20.5 18t11 22q1 4 1 7q0 10 -8 18t-25 8q-18 0 -35.5 -12.5t-27.5 -24.5l-54 32q29 37 69 60.5t97 23.5q26 0 47 -7.5t35 -20t19.5 -26t5.5 -24.5q0 -6 -1 -13q-4 -23 -16.5 -38.5t-29.5 -28.5t-35 -26.5t-35 -33.5zM324 -16
q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q42 0 78 -4t67 -13h12q45 0 71 28.5t33 78.5h151q-12 -69 -49 -119t-102 -73q22 -38 24.5 -71.5t2.5 -44.5q0 -48 -13 -108q-16 -76 -44.5 -129t-72 -86t-105 -48
t-143.5 -15zM341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8z" />
    <glyph glyph-name="uni1EE0" unicode="&#x1ee0;" horiz-adv-x="924" 
d="M417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q88 0 156 -17h30q45 0 71 28.5t33 78.5h151q-11 -66 -46 -115t-95 -73q48 -55 53.5 -109.5t5.5 -78.5q0 -71 -19 -162q-21 -99 -56.5 -171.5t-92.5 -120
t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5zM372 893q16 76 52.5 107.5t89.5 31.5
q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116z" />
    <glyph glyph-name="uni1EE1" unicode="&#x1ee1;" horiz-adv-x="738" 
d="M324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q42 0 78 -4t67 -13h12q45 0 71 28.5t33 78.5h151q-12 -69 -49 -119t-102 -73q22 -38 24.5 -71.5t2.5 -44.5q0 -48 -13 -108q-16 -76 -44.5 -129t-72 -86
t-105 -48t-143.5 -15zM341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8zM231 663q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5
t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116z" />
    <glyph glyph-name="uni1EE2" unicode="&#x1ee2;" horiz-adv-x="924" 
d="M417 -16q-115 0 -191.5 27t-118 82t-46 106.5t-4.5 71.5q0 71 20 161q21 99 56.5 171.5t92.5 120t138 71t193 23.5q88 0 156 -17h30q45 0 71 28.5t33 78.5h151q-11 -66 -46 -115t-95 -73q48 -55 53.5 -109.5t5.5 -78.5q0 -71 -19 -162q-21 -99 -56.5 -171.5t-92.5 -120
t-138 -71t-193 -23.5zM439 155q65 0 109 13.5t74.5 43.5t50 76.5t33.5 112.5t14 112.5t-19 76.5t-59.5 43.5t-107.5 13.5q-64 0 -108.5 -13.5t-74.5 -43.5t-49.5 -76.5t-33.5 -112.5t-14 -112.5t19 -76.5t59 -43.5t107 -13.5zM292 -76h178l-26 -150h-178z" />
    <glyph glyph-name="uni1EE3" unicode="&#x1ee3;" horiz-adv-x="738" 
d="M324 -16q-83 0 -143 17.5t-95.5 56.5t-41.5 80t-6 64q0 47 13 107q16 76 44.5 129t72 86.5t104.5 48.5t142 15q42 0 78 -4t67 -13h12q45 0 71 28.5t33 78.5h151q-12 -69 -49 -119t-102 -73q22 -38 24.5 -71.5t2.5 -44.5q0 -48 -13 -108q-16 -76 -44.5 -129t-72 -86
t-105 -48t-143.5 -15zM341 131q37 0 63.5 8.5t45 27t31 48t21.5 71.5q9 43 9 72.5t-12 48t-37 26.5t-64 8q-37 0 -63 -8t-45 -26.5t-31.5 -48t-21.5 -72.5q-9 -41 -9 -69q0 -2 0.5 -17.5t12.5 -34t36.5 -26.5t63.5 -8zM205 -76h178l-26 -150h-178z" />
    <glyph glyph-name="uni1EE4" unicode="&#x1ee4;" horiz-adv-x="923" 
d="M421 -16q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64q0 58 16 133l94 446h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h196l-96 -449q-22 -104 -52 -175t-79.5 -114t-124.5 -61.5t-187 -18.5zM281 -76h178
l-26 -150h-178z" />
    <glyph glyph-name="uni1EE5" unicode="&#x1ee5;" horiz-adv-x="780" 
d="M280 -16q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h187l-121 -572h-129l-18 37q-54 -25 -107 -39t-112 -14zM205 -76h178l-26 -150h-178z" />
    <glyph glyph-name="uni1EE6" unicode="&#x1ee6;" horiz-adv-x="923" 
d="M533 864q10 18 22.5 29.5t24 20.5t20.5 18t11 22q1 4 1 7q0 10 -8 18t-25 8q-18 0 -35.5 -12.5t-27.5 -24.5l-54 32q29 37 69 60.5t97 23.5q26 0 47 -7.5t35 -20t19.5 -26t5.5 -24.5q0 -6 -1 -13q-4 -23 -16.5 -38.5t-29.5 -28.5t-35 -26.5t-35 -33.5zM421 -16
q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64q0 58 16 133l94 446h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h196l-96 -449q-22 -104 -52 -175t-79.5 -114t-124.5 -61.5t-187 -18.5z" />
    <glyph glyph-name="uni1EE7" unicode="&#x1ee7;" horiz-adv-x="780" 
d="M400 668q10 18 22.5 29.5t24 20.5t20.5 18t11 22q1 4 1 7q0 10 -8 18t-25 8q-18 0 -35.5 -12.5t-27.5 -24.5l-54 32q29 37 69 60.5t97 23.5q26 0 47 -7.5t35 -20t19.5 -26t5.5 -24.5q0 -6 -1 -13q-4 -23 -16.5 -38.5t-29.5 -28.5t-35 -26.5t-35 -33.5zM280 -16
q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h187l-121 -572h-129l-18 37q-54 -25 -107 -39t-112 -14z" />
    <glyph glyph-name="uni1EE8" unicode="&#x1ee8;" horiz-adv-x="923" 
d="M421 -16q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64q0 58 16 133l94 446h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h89q38 5 60 33t28 73h151q-12 -69 -49 -119t-101 -73l-78 -363q-22 -104 -52 -175
t-79.5 -114t-124.5 -61.5t-187 -18.5zM674 1030h213l-211 -170h-179z" />
    <glyph glyph-name="uni1EE9" unicode="&#x1ee9;" horiz-adv-x="780" 
d="M280 -16q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h79q38 5 60 33t28 73h151q-12 -69 -48.5 -118.5t-100.5 -72.5l-103 -487h-129l-18 37q-54 -25 -107 -39t-112 -14z
M553 800h213l-211 -170h-179z" />
    <glyph glyph-name="uni1EEA" unicode="&#x1eea;" horiz-adv-x="923" 
d="M421 -16q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64q0 58 16 133l94 446h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h89q38 5 60 33t28 73h151q-12 -69 -49 -119t-101 -73l-78 -363q-22 -104 -52 -175
t-79.5 -114t-124.5 -61.5t-187 -18.5zM362 1032h200l104 -170h-165z" />
    <glyph glyph-name="uni1EEB" unicode="&#x1eeb;" horiz-adv-x="780" 
d="M280 -16q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h79q38 5 60 33t28 73h151q-12 -69 -48.5 -118.5t-100.5 -72.5l-103 -487h-129l-18 37q-54 -25 -107 -39t-112 -14z
M241 802h200l104 -170h-165z" />
    <glyph glyph-name="uni1EEC" unicode="&#x1eec;" horiz-adv-x="923" 
d="M533 864q10 18 22.5 29.5t24 20.5t20.5 18t11 22q1 4 1 7q0 10 -8 18t-25 8q-18 0 -35.5 -12.5t-27.5 -24.5l-54 32q29 37 69 60.5t97 23.5q26 0 47 -7.5t35 -20t19.5 -26t5.5 -24.5q0 -6 -1 -13q-4 -23 -16.5 -38.5t-29.5 -28.5t-35 -26.5t-35 -33.5zM421 -16
q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64q0 58 16 133l94 446h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h89q38 5 60 33t28 73h151q-12 -69 -49 -119t-101 -73l-78 -363q-22 -104 -52 -175t-79.5 -114
t-124.5 -61.5t-187 -18.5z" />
    <glyph glyph-name="uni1EED" unicode="&#x1eed;" horiz-adv-x="780" 
d="M400 668q10 18 22.5 29.5t24 20.5t20.5 18t11 22q1 4 1 7q0 10 -8 18t-25 8q-18 0 -35.5 -12.5t-27.5 -24.5l-54 32q29 37 69 60.5t97 23.5q26 0 47 -7.5t35 -20t19.5 -26t5.5 -24.5q0 -6 -1 -13q-4 -23 -16.5 -38.5t-29.5 -28.5t-35 -26.5t-35 -33.5zM280 -16
q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h79q38 5 60 33t28 73h151q-12 -69 -48.5 -118.5t-100.5 -72.5l-103 -487h-129l-18 37q-54 -25 -107 -39t-112 -14z" />
    <glyph glyph-name="uni1EEE" unicode="&#x1eee;" horiz-adv-x="923" 
d="M421 -16q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64q0 58 16 133l94 446h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h89q38 5 60 33t28 73h151q-12 -69 -49 -119t-101 -73l-78 -363q-22 -104 -52 -175
t-79.5 -114t-124.5 -61.5t-187 -18.5zM373 893q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12
h-116z" />
    <glyph glyph-name="uni1EEF" unicode="&#x1eef;" horiz-adv-x="780" 
d="M280 -16q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h79q38 5 60 33t28 73h151q-12 -69 -48.5 -118.5t-100.5 -72.5l-103 -487h-129l-18 37q-54 -25 -107 -39t-112 -14z
M252 663q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116z" />
    <glyph glyph-name="uni1EF0" unicode="&#x1ef0;" horiz-adv-x="923" 
d="M421 -16q-112 0 -186 20.5t-113.5 65.5t-44 89t-4.5 64q0 58 16 133l94 446h196l-95 -446q-13 -62 -13 -101t18 -61t56.5 -30t102.5 -8q57 0 93.5 8t60.5 30t39 60.5t28 98.5l95 449h89q38 5 60 33t28 73h151q-12 -69 -49 -119t-101 -73l-78 -363q-22 -104 -52 -175
t-79.5 -114t-124.5 -61.5t-187 -18.5zM281 -76h178l-26 -150h-178z" />
    <glyph glyph-name="uni1EF1" unicode="&#x1ef1;" horiz-adv-x="780" 
d="M280 -16q-135 0 -180 55.5t-45 124.5q0 32 7 68l72 340h187l-72 -338q-3 -17 -3 -30q0 -25 15.5 -42.5t70.5 -17.5q41 0 82.5 11.5t83.5 29.5l82 387h79q38 5 60 33t28 73h151q-12 -69 -48.5 -118.5t-100.5 -72.5l-103 -487h-129l-18 37q-54 -25 -107 -39t-112 -14z
M205 -76h178l-26 -150h-178z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="856" 
d="M366 987h199l83 -140h-174zM335 313l-225 489h209l141 -313l275 313h229l-434 -489l-66 -313h-195z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="715" 
d="M208 802h200l104 -170h-165zM71 572h189l53 -407h20l226 406l208 1l-339 -594q-31 -56 -59.5 -92.5t-63 -59t-78.5 -31.5t-107 -9q-33 0 -65.5 2.5t-65.5 7.5l31 146q20 -3 41.5 -4.5t40.5 -1.5q33 0 55 2.5t36 8t23.5 15t16.5 23.5l16 28h-95z" />
    <glyph glyph-name="uni1EF4" unicode="&#x1ef4;" horiz-adv-x="856" 
d="M335 313l-225 489h209l141 -313l275 313h229l-434 -489l-66 -313h-195zM263 -76h178l-26 -150h-178z" />
    <glyph glyph-name="uni1EF5" unicode="&#x1ef5;" horiz-adv-x="715" 
d="M71 572h189l53 -407h20l226 406l208 1l-339 -594q-31 -56 -59.5 -92.5t-63 -59t-78.5 -31.5t-107 -9q-33 0 -65.5 2.5t-65.5 7.5l31 146q20 -3 41.5 -4.5t40.5 -1.5q33 0 55 2.5t36 8t23.5 15t16.5 23.5l16 28h-95zM412 -76h178l-26 -150h-178z" />
    <glyph glyph-name="uni1EF6" unicode="&#x1ef6;" horiz-adv-x="856" 
d="M481 864q10 18 22.5 29.5t24 20.5t20.5 18t11 22q1 4 1 7q0 10 -8 18t-25 8q-18 0 -35.5 -12.5t-27.5 -24.5l-54 32q29 37 69 60.5t97 23.5q26 0 47 -7.5t35 -20t19.5 -26t5.5 -24.5q0 -6 -1 -13q-4 -23 -16.5 -38.5t-29.5 -28.5t-35 -26.5t-35 -33.5zM335 313l-225 489
h209l141 -313l275 313h229l-434 -489l-66 -313h-195z" />
    <glyph glyph-name="uni1EF7" unicode="&#x1ef7;" horiz-adv-x="715" 
d="M400 668q10 18 22.5 29.5t24 20.5t20.5 18t11 22q1 4 1 7q0 10 -8 18t-25 8q-18 0 -35.5 -12.5t-27.5 -24.5l-54 32q29 37 69 60.5t97 23.5q26 0 47 -7.5t35 -20t19.5 -26t5.5 -24.5q0 -6 -1 -13q-4 -23 -16.5 -38.5t-29.5 -28.5t-35 -26.5t-35 -33.5zM71 572h189
l53 -407h20l226 406l208 1l-339 -594q-31 -56 -59.5 -92.5t-63 -59t-78.5 -31.5t-107 -9q-33 0 -65.5 2.5t-65.5 7.5l31 146q20 -3 41.5 -4.5t40.5 -1.5q33 0 55 2.5t36 8t23.5 15t16.5 23.5l16 28h-95z" />
    <glyph glyph-name="uni1EF8" unicode="&#x1ef8;" horiz-adv-x="856" 
d="M335 313l-225 489h209l141 -313l275 313h229l-434 -489l-66 -313h-195zM339 893q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8
q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116z" />
    <glyph glyph-name="uni1EF9" unicode="&#x1ef9;" horiz-adv-x="715" 
d="M71 572h189l53 -407h20l226 406l208 1l-339 -594q-31 -56 -59.5 -92.5t-63 -59t-78.5 -31.5t-107 -9q-33 0 -65.5 2.5t-65.5 7.5l31 146q20 -3 41.5 -4.5t40.5 -1.5q33 0 55 2.5t36 8t23.5 15t16.5 23.5l16 28h-95zM219 663q16 76 52.5 107.5t89.5 31.5q26 0 50 -8.5
t45.5 -18.5t39.5 -18.5t33 -8.5q19 0 26.5 10t11.5 28l2 11h117l-5 -26q-15 -76 -52 -107.5t-90 -31.5q-26 0 -50 8t-45.5 18t-39.5 18t-33 8q-20 0 -27.5 -9.5t-10.5 -26.5l-3 -12h-116z" />
    <glyph glyph-name="uni2000" unicode="&#x2000;" 
 />
    <glyph glyph-name="uni2001" unicode="&#x2001;" horiz-adv-x="1520" 
 />
    <glyph glyph-name="uni2002" unicode="&#x2002;" 
 />
    <glyph glyph-name="uni2003" unicode="&#x2003;" horiz-adv-x="1520" 
 />
    <glyph glyph-name="uni2004" unicode="&#x2004;" horiz-adv-x="507" 
 />
    <glyph glyph-name="uni2005" unicode="&#x2005;" horiz-adv-x="380" 
 />
    <glyph glyph-name="uni2006" unicode="&#x2006;" horiz-adv-x="253" 
 />
    <glyph glyph-name="uni2007" unicode="&#x2007;" horiz-adv-x="716" 
 />
    <glyph glyph-name="uni2008" unicode="&#x2008;" horiz-adv-x="380" 
 />
    <glyph glyph-name="uni2009" unicode="&#x2009;" horiz-adv-x="190" 
 />
    <glyph glyph-name="uni200A" unicode="&#x200a;" horiz-adv-x="152" 
 />
    <glyph glyph-name="uni200B" unicode="&#x200b;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni200C" unicode="&#x200c;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni200D" unicode="&#x200d;" horiz-adv-x="0" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" 
d="M-16 209l34 155h760l-34 -155h-760z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1520" 
d="M-16 209l34 155h1520l-34 -155h-1520z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="380" 
d="M283 465h-205l200 337h171z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="380" 
d="M245 802h205l-201 -337h-171z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="380" 
d="M123 186h205l-201 -337h-171z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="628" 
d="M283 465h-205l200 337h171zM550 465h-205l200 337h171z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="627" 
d="M245 802h205l-201 -337h-171zM512 802h205l-201 -337h-171z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="628" 
d="M123 186h205l-201 -337h-171zM390 186h205l-201 -337h-171z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" 
d="M291 309l20 94h-200l36 169h200l49 230h187l-49 -230h201l-35 -169h-202l-20 -94l-116 -455h-147z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" 
d="M78 241h199l37 174h-200l33 157h200l49 230h187l-49 -230h201l-33 -157h-201l-37 -174h201l-34 -157h-200l-49 -230h-187l49 230h-201z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="502" 
d="M230 101q-47 0 -82 11t-56 35.5t-24.5 50t-3.5 38.5q0 30 8 67q18 90 68.5 128.5t134.5 38.5q46 0 80.5 -11t55 -35t24.5 -49.5t4 -41.5q0 -28 -7 -63q-20 -91 -70.5 -130t-131.5 -39z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="898" 
d="M73 183h192l-38 -183h-192zM341 183h192l-38 -183h-192zM610 183h192l-38 -183h-192z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1336" 
d="M267 467q-102 0 -137.5 35.5t-35.5 91.5q0 29 7 65q9 44 26 74t43 49t62.5 27.5t85.5 8.5q51 0 88 -10t58 -32.5t24.5 -47t3.5 -39.5q0 -28 -7 -64q-9 -43 -26 -73.5t-43 -49t-62.5 -27t-86.5 -8.5zM178 0h-182l784 802h184zM279 568q34 0 48.5 16t22.5 58q6 23 6 39
q0 12 -6 23.5t-43 11.5q-34 0 -48.5 -16t-22.5 -58q-6 -23 -6 -39q0 -12 6 -23.5t43 -11.5zM640 -15q-102 0 -137.5 35.5t-35.5 91.5q0 28 7 64q18 87 68.5 123.5t148.5 36.5q51 0 87.5 -10t58 -33t25 -47.5t3.5 -38.5q0 -28 -7 -64q-9 -43 -26 -73.5t-43 -49t-62.5 -27
t-86.5 -8.5zM1067 -15q-102 0 -138 35.5t-36 90.5q0 29 8 65q18 87 68 123.5t148 36.5q103 0 139 -36t36 -92q0 -29 -7 -65q-9 -43 -26 -73.5t-43 -49t-63 -27t-86 -8.5zM652 86q34 0 48.5 16t22.5 57q5 23 5 39q0 13 -5.5 24.5t-43.5 11.5q-34 0 -48 -16t-23 -59
q-5 -22 -5 -37q0 -13 6 -24.5t43 -11.5zM1079 86q34 0 48.5 16t22.5 57q5 23 5 39q0 13 -5.5 24.5t-43.5 11.5q-34 0 -48.5 -16t-22.5 -59q-5 -23 -5 -38q0 -12 5.5 -23.5t43.5 -11.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="377" 
d="M215 519h191l-192 -233l92 -231h-189l-93 232z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="377" 
d="M164 55h-190l191 232l-92 232h189l94 -232z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="263" 
d="M456 802h185l-787 -802h-182z" />
    <glyph glyph-name="uni2060" unicode="&#x2060;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni2070" unicode="&#x2070;" horiz-adv-x="416" 
d="M351 1009q108 0 137 -48t29 -106q0 -46 -13 -106q-11 -52 -27.5 -93t-43 -68.5t-65.5 -42t-95 -14.5q-109 0 -137 48t-28 105q0 46 13 107q11 53 27.5 93.5t42.5 68t65 42t95 14.5zM287 642q17 0 29 5.5t20.5 20t16 39.5t15.5 63q8 39 10.5 58t2.5 29q0 9 -2 19.5t-12 16
t-29 5.5q-17 0 -29 -5.5t-21 -20t-16 -39.5t-15 -63q-8 -39 -10.5 -58t-2.5 -29q0 -9 2 -19.5t12 -16t29 -5.5z" />
    <glyph glyph-name="uni2074" unicode="&#x2074;" horiz-adv-x="416" 
d="M510 802l-55 -262h-136l17 79h-248l22 106l204 275h155l-205 -274h94l16 76h136z" />
    <glyph glyph-name="uni2075" unicode="&#x2075;" horiz-adv-x="416" 
d="M110 664q36 -9 74 -14t77 -5q23 0 39 1t25.5 4.5t15 11.5t7.5 22q1 7 1 13q0 14 -10 22t-45 8h-159l57 273h334l-23 -110h-195l-11 -54h26q105 0 137 -34t32 -84q0 -22 -5 -48q-10 -48 -29.5 -75.5t-50 -42t-71.5 -18t-93 -3.5q-48 0 -91.5 6.5t-65.5 12.5z" />
    <glyph glyph-name="uni2076" unicode="&#x2076;" horiz-adv-x="416" 
d="M289 531q-117 0 -148.5 49t-31.5 110q0 45 12 103q12 57 30.5 98t47.5 67.5t70.5 38.5t98.5 12q49 0 87 -6t61 -14l-23 -113q-29 8 -60 13.5t-66 5.5q-22 0 -37.5 -2t-27 -8t-18.5 -18t-13 -32q15 5 32 7.5t35 2.5q92 0 122 -37t30 -85q0 -24 -6 -52q-16 -76 -62 -108
t-133 -32zM301 735q-28 0 -55 -9q-5 -26 -5 -44q0 -16 6.5 -29.5t43.5 -13.5q26 0 38 11.5t17 34.5q2 11 2 20q0 13 -7.5 21.5t-39.5 8.5z" />
    <glyph glyph-name="uni2077" unicode="&#x2077;" horiz-adv-x="416" 
d="M171 1000h382l-23 -106l-265 -354h-172l280 350h-226z" />
    <glyph glyph-name="uni2078" unicode="&#x2078;" horiz-adv-x="416" 
d="M280 531q-48 0 -86.5 7t-63.5 24t-32.5 38.5t-7.5 40.5q0 17 4 37q8 40 32.5 64.5t61.5 35.5q-29 14 -38 34t-9 40q0 19 5 43q13 61 61 87.5t139 26.5q47 0 84.5 -6.5t62 -22.5t32 -35.5t7.5 -36.5q0 -15 -4 -33q-9 -45 -32.5 -70t-58.5 -39q33 -12 43.5 -33t10.5 -43
q0 -18 -5 -41q-14 -63 -63.5 -90.5t-142.5 -27.5zM326 820q30 0 42.5 10t16.5 31q2 8 2 14q0 11 -8 17.5t-41 6.5q-30 0 -42.5 -8.5t-17.5 -29.5q-1 -6 -1 -11q0 -13 8 -21.5t41 -8.5zM287 639q29 0 44.5 9t21.5 34q1 7 1 13q0 15 -10 24.5t-43 9.5q-30 0 -45 -11t-20 -36
q-2 -8 -2 -15q0 -13 10.5 -20.5t42.5 -7.5z" />
    <glyph glyph-name="uni2079" unicode="&#x2079;" horiz-adv-x="416" 
d="M330 1009q118 0 149.5 -49t31.5 -110q0 -45 -12 -103q-12 -57 -31 -98t-47.5 -67.5t-70 -38.5t-98.5 -12q-49 0 -87 6t-61 14l23 113q29 -8 60 -13.5t66 -5.5q22 0 37.5 2t27 8t18.5 18t13 32q-15 -5 -32 -7.5t-36 -2.5q-92 0 -121.5 37t-29.5 84q0 24 6 53
q16 77 61.5 108.5t132.5 31.5zM274 855q-2 -11 -2 -20q0 -13 7.5 -21.5t39.5 -8.5q26 0 53 8q6 28 6 46q0 15 -6 28.5t-44 13.5q-26 0 -37.5 -11.5t-16.5 -34.5z" />
    <glyph glyph-name="uni2080" unicode="&#x2080;" horiz-adv-x="417" 
d="M195 271q108 0 137 -48t29 -106q0 -46 -13 -106q-11 -53 -27.5 -93.5t-43 -68t-65.5 -42t-95 -14.5q-109 0 -137 48t-28 105q0 46 13 107q11 53 27.5 93.5t42.5 68t65 42t95 14.5zM131 -96q17 0 29 5.5t20.5 20t16 39.5t15.5 63q8 39 10.5 58t2.5 29q0 9 -2 19.5t-12 16
t-29 5.5q-17 0 -29 -5.5t-21 -20t-16 -39.5t-15 -63q-8 -39 -10.5 -58t-2.5 -29q0 -9 2 -19.5t12 -16t29 -5.5z" />
    <glyph glyph-name="uni2081" unicode="&#x2081;" horiz-adv-x="356" 
d="M173 262h137l-98 -460h-139l66 310l-116 -54l-38 115z" />
    <glyph glyph-name="uni2082" unicode="&#x2082;" horiz-adv-x="417" 
d="M193 271q98 0 137.5 -29.5t39.5 -76.5q0 -18 -5 -40q-6 -24 -14 -41t-22.5 -31t-37 -27.5t-56.5 -30.5l-123 -61q-11 -5 -15 -10.5t-5 -12.5h222l-23 -109h-361l25 123q8 36 29.5 59t66.5 44l129 62q29 14 37 20t10 15q1 5 1 9q0 12 -13 17t-51 5q-33 0 -74.5 -4.5
t-73.5 -10.5l24 114q33 8 68 12t85 4z" />
    <glyph glyph-name="uni2083" unicode="&#x2083;" horiz-adv-x="417" 
d="M-47 -76q36 -8 67.5 -13t71.5 -5q29 0 47 1.5t28.5 5t15 10t7.5 17.5q2 8 2 14q0 14 -9 21t-36 7h-133l22 108h134q24 0 35.5 6t15.5 26q1 6 1 11q0 13 -12 19.5t-59 6.5q-24 0 -42.5 -1t-35 -3t-33 -4.5t-36.5 -6.5l23 111q26 6 66 11t88 5q114 0 151.5 -29.5
t37.5 -75.5q0 -18 -5 -39q-10 -45 -33 -66t-60 -31q56 -27 56 -87q0 -15 -4 -33q-7 -33 -22 -55.5t-42.5 -36t-68.5 -19.5t-101 -6q-55 0 -95 6t-65 13z" />
    <glyph glyph-name="uni2084" unicode="&#x2084;" horiz-adv-x="417" 
d="M354 64l-55 -262h-136l17 79h-248l22 106l204 275h155l-205 -274h94l16 76h136z" />
    <glyph glyph-name="uni2085" unicode="&#x2085;" horiz-adv-x="417" 
d="M-46 -74q36 -9 74 -14t77 -5q23 0 39 1t25.5 4.5t15 11.5t7.5 22q1 7 1 13q0 14 -10 22t-45 8h-159l57 273h334l-23 -110h-195l-11 -54h26q105 0 137 -34t32 -84q0 -22 -5 -48q-10 -48 -29.5 -75.5t-50 -42t-71.5 -18t-93 -3.5q-48 0 -91.5 6.5t-65.5 12.5z" />
    <glyph glyph-name="uni2086" unicode="&#x2086;" horiz-adv-x="417" 
d="M133 -207q-117 0 -148.5 49t-31.5 110q0 45 12 103q12 57 30.5 98t47.5 67.5t70.5 38.5t98.5 12q49 0 87 -6t61 -14l-23 -113q-29 8 -60 13.5t-66 5.5q-22 0 -37.5 -2t-27 -8t-18.5 -18t-13 -32q15 5 32 7.5t35 2.5q92 0 122 -37t30 -85q0 -24 -6 -52q-16 -77 -62 -108.5
t-133 -31.5zM145 -3q-28 0 -55 -9q-5 -26 -5 -44q0 -16 6.5 -29.5t43.5 -13.5q26 0 38 11.5t17 34.5q2 11 2 20q0 13 -7.5 21.5t-39.5 8.5z" />
    <glyph glyph-name="uni2087" unicode="&#x2087;" horiz-adv-x="417" 
d="M15 262h382l-23 -106l-265 -354h-172l280 350h-226z" />
    <glyph glyph-name="uni2088" unicode="&#x2088;" horiz-adv-x="417" 
d="M124 -207q-48 0 -86.5 7t-63.5 24t-32.5 38.5t-7.5 40.5q0 17 4 37q8 40 32.5 64.5t61.5 35.5q-29 14 -38 34t-9 40q0 19 5 43q13 61 61 87.5t139 26.5q47 0 84.5 -6.5t62 -22.5t32 -35.5t7.5 -36.5q0 -15 -4 -33q-9 -45 -32.5 -70t-58.5 -39q33 -12 43.5 -33t10.5 -43
q0 -18 -5 -41q-14 -63 -63.5 -90.5t-142.5 -27.5zM170 82q30 0 42.5 10t16.5 31q2 8 2 14q0 11 -8 17.5t-41 6.5q-30 0 -42.5 -8.5t-17.5 -29.5q-1 -6 -1 -11q0 -13 8 -21.5t41 -8.5zM131 -99q29 0 44.5 9t21.5 34q1 7 1 13q0 15 -10 24.5t-43 9.5q-30 0 -45 -11t-20 -36
q-2 -8 -2 -15q0 -13 10.5 -20.5t42.5 -7.5z" />
    <glyph glyph-name="uni2089" unicode="&#x2089;" horiz-adv-x="417" 
d="M174 271q118 0 149.5 -49t31.5 -110q0 -45 -12 -103q-12 -57 -31 -98t-47.5 -67.5t-70 -38.5t-98.5 -12q-49 0 -87 6t-61 14l23 113q29 -8 60 -13.5t66 -5.5q22 0 37.5 2t27 8t18.5 18t13 32q-15 -5 -32 -7.5t-36 -2.5q-92 0 -121.5 37t-29.5 84q0 24 6 53
q16 77 61.5 108.5t132.5 31.5zM118 117q-2 -11 -2 -20q0 -13 7.5 -21.5t39.5 -8.5q26 0 53 8q6 28 6 46q0 15 -6 28.5t-44 13.5q-26 0 -37.5 -11.5t-16.5 -34.5z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="716" 
d="M49 366h87q5 32 12 66q0 1 1 2v4q1 1 1 2h-86l28 132h93q23 66 54 112.5t75 76.5t103.5 43.5t139.5 13.5q74 0 130 -7.5t94 -15.5l-33 -155q-25 5 -48 9t-47 7t-51.5 4.5t-61.5 1.5q-33 0 -58 -4t-44.5 -14t-34 -27.5t-26.5 -44.5h304l-28 -132h-316q0 -2 -2 -8l-3 -17
q-1 -1 -1 -3v-2q0 -1 -1 -3q-2 -11 -4 -21l-4 -20h315l-28 -132h-304q1 -55 29.5 -74t94.5 -19q34 0 62 1.5t53 4.5t49.5 7t52.5 10l-33 -156q-31 -8 -84 -16t-123 -8q-74 0 -127 15.5t-87 46.5t-50 78t-17 110h-105z" />
    <glyph glyph-name="uni2113" unicode="&#x2113;" 
d="M373 -15q-43 0 -79 9t-62.5 29t-42.5 52.5t-18 80.5q-19 -10 -38.5 -20t-40.5 -21l-46 135q41 20 78 39t71 36l59 276q12 59 30.5 100t47 66.5t70.5 37.5t102 12q50 0 89.5 -13.5t64 -41t30 -56t5.5 -46.5q0 -29 -8 -64q-9 -40 -32.5 -80t-66 -83t-105.5 -89.5
t-151 -98.5v-2q-6 -31 -6 -53q0 -23 9.5 -44t57.5 -21q38 0 73 25.5t72 68.5l97 -90q-57 -70 -118 -107t-142 -37zM368 421q69 44 110 87t52 95q3 15 3 26q0 48 -49 48q-35 0 -52.5 -20t-25.5 -58z" />
    <glyph glyph-name="uni2116" unicode="&#x2116;" horiz-adv-x="1476" 
d="M195 802h231l166 -478l101 478h200l-170 -802h-237l-162 467l-100 -467h-199zM910 521q15 74 42.5 122.5t67 78t90 41.5t111.5 12q80 0 134.5 -16.5t84.5 -53t34.5 -73.5t4.5 -55q0 -46 -12 -103q-15 -73 -42.5 -122.5t-67 -78.5t-90 -41.5t-111.5 -12.5
q-79 0 -133.5 16.5t-85 53t-34.5 73.5t-4 56q0 45 11 103zM1282 497q7 35 7 60t-11.5 46.5t-65.5 21.5q-27 0 -46 -7t-32 -22.5t-22 -39.5t-17 -59q-7 -34 -7 -58q0 -26 11.5 -48t65.5 -22q27 0 46 7t32 22.5t22.5 40t16.5 58.5zM855 161h526l-34 -161h-526z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="924" 
d="M215 698h-98l20 104h314l-19 -104h-97l-63 -297h-120zM516 802h149l34 -192l114 192h164l-85 -401h-119l45 214l-92 -149h-104l-28 148l-45 -213h-118z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="924" 
d="M273 347l-46 -215q59 -49 189 -49q55 0 100 6.5t81 21.5t64.5 39t51.5 58h125q-30 -59 -69.5 -101.5t-91.5 -69.5t-118.5 -40t-151.5 -13q-111 0 -185.5 26.5t-114.5 81.5t-44.5 107t-4.5 72q0 71 19 161q20 96 57 168t95.5 120.5t141.5 73t195 24.5q118 0 193.5 -26
t114.5 -79.5t42 -102t3 -63.5q0 -72 -19 -165l-7 -35h-620zM558 720q-131 0 -218 -58l-47 -218h407l49 233q-30 22 -76.5 32.5t-114.5 10.5z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" 
d="M34 286l319 320l103 -103l-136 -133h440v-167h-442l138 -135l-103 -102z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" 
d="M380 643l320 -320l-103 -103l-134 137l1 -357h-168l1 358l-134 -138l-103 103z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" 
d="M310 63l148 144h-458v159h457l-147 143l96 97l320 -320l-320 -320z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" 
d="M157 347l143 -147v372h160v-372l144 147l96 -97l-320 -320l-320 320z" />
    <glyph glyph-name="uni2196" unicode="&#x2196;" 
d="M91 563h452v-137l-203 1l321 -321l-113 -113l-321 321v-204h-136v453z" />
    <glyph glyph-name="uni2197" unicode="&#x2197;" 
d="M532 120v202l-320 -320l-113 113l321 321h-203v136h452v-452h-137z" />
    <glyph glyph-name="uni2198" unicode="&#x2198;" 
d="M669 0h-452v136h203l-321 321l113 113l320 -320v202h137v-452z" />
    <glyph glyph-name="uni2199" unicode="&#x2199;" 
d="M227 452v-203l321 321l113 -113l-321 -321h203v-136h-452v452h136z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="746" 
d="M697 285q-18 -82 -47 -139.5t-72.5 -93.5t-103 -52t-136.5 -16q-84 0 -145.5 16.5t-98.5 55.5t-43.5 81.5t-6.5 66.5q0 51 14 117q26 123 107.5 183t230.5 60q47 0 88 -10q-32 67 -89 126t-131 122h223q143 -110 183 -211.5t40 -188.5q0 -57 -13 -117zM509 301
q6 27 8.5 52t2.5 48q-25 10 -51 15.5t-55 5.5q-39 0 -67.5 -7t-49 -24t-34.5 -44.5t-23 -68.5q-8 -37 -8 -64q0 -3 0.5 -18.5t12.5 -33t37 -25.5t65 -8q36 0 62 9.5t45 30.5t32 53.5t23 78.5z" />
    <glyph glyph-name="uni2206" unicode="&#x2206;" 
d="M351 802h276l56 -802h-729zM486 163l-17 494h-13l-228 -494h258z" />
    <glyph glyph-name="product" unicode="&#x220f;" 
d="M179 632h-68l35 170h688l-36 -170h-69l-156 -741h-196l157 741h-160l-158 -741h-195z" />
    <glyph glyph-name="summation" unicode="&#x2211;" 
d="M4 26l338 339l-202 302l28 135h644l-35 -166h-400l193 -290l-286 -289h370l-35 -166h-644z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M126 361h542l-32 -150h-542z" />
    <glyph glyph-name="radical" unicode="&#x221a;" 
d="M111 572h247l-5 -370l355 785h169l-452 -987h-223l5 427h-126z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" 
d="M747 349q-12 -59 -33 -96t-48.5 -58t-60 -28.5t-68.5 -7.5q-54 0 -92.5 29.5t-62.5 83.5q-93 -113 -200 -113q-37 0 -67 12t-48.5 40.5t-21.5 58.5t-3 42q0 42 12 97q13 60 34 97t48 58t59.5 28t68.5 7q55 0 93.5 -29.5t61.5 -82.5q46 56 95 84t105 28q36 0 66.5 -12
t49.5 -40.5t21.5 -57.5t2.5 -40q0 -42 -12 -100zM315 429q-10 23 -25 38.5t-41 15.5q-61 0 -82 -104q-6 -29 -6 -50q0 -54 42 -54q25 0 47.5 16t42.5 41l41 51zM486 330q10 -23 25 -39t41 -16q59 0 82 104q6 29 6 49q0 55 -42 55q-25 0 -47.5 -15.5t-42.5 -40.5l-42 -51z
" />
    <glyph glyph-name="integral" unicode="&#x222b;" 
d="M389 15q-17 -56 -37.5 -99.5t-51.5 -73t-76 -44.5t-112 -15h-20q-23 0 -45.5 2t-54.5 8l31 148q35 -6 68 -6h10q25 0 41.5 3t27 11.5t17.5 22t13 33.5l178 580q17 56 37.5 99.5t51.5 73t76 45t112 15.5q37 0 63 -2.5t57 -7.5l-30 -145q-20 2 -39.5 3t-39.5 1
q-25 0 -41 -3.5t-27 -11.5t-18 -21.5t-13 -34.5z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M125 357q19 89 60.5 128.5t117.5 39.5q36 0 67 -11t57 -23.5t48 -23.5t41 -11t27.5 11.5t13.5 33.5l3 16h141l-7 -33q-17 -89 -59.5 -128t-120.5 -39q-36 0 -66 11t-56 23.5t-48 23.5t-41 11q-20 0 -28 -11.5t-13 -32.5l-3 -18h-141zM71 105q10 45 24.5 76.5t36 52
t50.5 30t68 9.5q36 0 67 -11t57 -23.5t48 -23.5t41 -11t27.5 11.5t12.5 33.5l4 16h141l-7 -33q-17 -89 -59.5 -128t-120.5 -39q-36 0 -66.5 11t-56 23.5t-47.5 23.5t-41 11q-20 0 -28 -11.5t-13 -32.5l-3 -18h-141z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M98 228h154l76 118h-205l32 150h271l49 76h183l-49 -76h88l-32 -150h-154l-77 -118h206l-33 -151h-271l-50 -77h-182l50 77h-89z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M160 522l563 98l-32 -152l-423 -63l-4 -17l396 -63l-30 -145l-522 97zM81 151h542l-32 -151h-542z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M651 277l-563 -97l32 152l423 63l4 17l-396 63l30 145l522 -98zM81 151h542l-32 -151h-542z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" 
d="M108 401l291 401h182l121 -401l-291 -401h-182zM350 159l175 243l-65 241l-175 -243z" />
    <glyph glyph-name="uni25CC" unicode="&#x25cc;" horiz-adv-x="672" 
d="M336 -10q-61 0 -115 23.5t-94 63.5t-63.5 94t-23.5 115t23.5 115t63.5 94t94 63.5t115 23.5t115 -23.5t94 -63.5t63.5 -94t23.5 -115t-23.5 -115t-63.5 -94t-94 -63.5t-115 -23.5zM336 572q-59 0 -111 -22.5t-91 -61.5t-61.5 -91t-22.5 -111t22.5 -111t61.5 -91t91 -61.5
t111 -22.5t111 22.5t91 61.5t61.5 91t22.5 111t-22.5 111t-61.5 91t-91 61.5t-111 22.5z" />
    <glyph glyph-name="uniFEFF" unicode="&#xfeff;" horiz-adv-x="0" 
 />
    <glyph glyph-name="glyph1" horiz-adv-x="0" 
 />
    <glyph glyph-name="ampersand.1" horiz-adv-x="849" 
d="M509 66q-23 -21 -48 -36.5t-54 -25.5t-64 -15t-79 -5q-67 0 -116.5 14.5t-79.5 45.5t-36 64.5t-6 54.5q0 36 10 80q16 78 71.5 128.5t148.5 62.5q-17 25 -28.5 48t-17 47t-5.5 50q0 2 0.5 17.5t7.5 50.5q9 43 29 75t55 53.5t89 32t131 10.5q67 0 117.5 -7.5t82.5 -15.5
l-32 -153q-40 8 -81.5 13t-83.5 5q-63 0 -89.5 -12.5t-33.5 -45.5q-3 -16 -3 -30q0 -13 5 -30t24 -45l115 -166l132 153h198l-239 -284l139 -200h-214zM221 221q-5 -23 -5 -41q0 -24 13 -42.5t63 -18.5q20 0 36.5 4.5t31.5 13.5t30 23.5t31 33.5l-96 139q-25 -2 -42 -9
t-29 -20.5t-19.5 -33.5t-13.5 -49z" />
    <glyph glyph-name="f.1" horiz-adv-x="466" 
d="M-88 -61q14 -2 29 -2.5t29 -0.5q35 0 53.5 14.5t26.5 55.5l90 424h-79l23 108l86 34l2 9q12 58 32.5 102t53 74t79.5 45.5t114 15.5q36 0 62 -2.5t57 -7.5l-29 -144q-20 2 -38.5 3t-38.5 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h160l-30 -142h-160
l-88 -415q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-32 0 -55.5 1.5t-51.5 7.5z" />
    <glyph glyph-name="commaaccentbelow" horiz-adv-x="570" 
d="M177 -53h212l-201 -145h-180z" />
    <glyph glyph-name="commaturnedabove" horiz-adv-x="570" 
d="M396 632h-212l209 170h179z" />
    <glyph glyph-name="gravecomb.case" horiz-adv-x="0" 
d="M-63 987h199l83 -140h-174z" />
    <glyph glyph-name="acutecomb.case" horiz-adv-x="0" 
d="M157 987h213l-167 -140h-189z" />
    <glyph glyph-name="uni030B.case" horiz-adv-x="0" 
d="M19 987h197l-144 -140h-172zM286 987h197l-144 -140h-172z" />
    <glyph glyph-name="uni0302.case" horiz-adv-x="0" 
d="M36 987h230l96 -140h-158l-60 72l-92 -72h-172z" />
    <glyph glyph-name="uni030C.case" horiz-adv-x="0" 
d="M-90 987h158l61 -72l91 72h172l-156 -140h-230z" />
    <glyph glyph-name="uni0306.case" horiz-adv-x="0" 
d="M128 842q-66 0 -110 8t-69 25.5t-30 37.5t-5 34q0 18 5 40h140q-1 -5 -1 -10q0 -15 14 -23.5t69 -8.5q28 0 46.5 2t30 7t17.5 13t9 20h140q-8 -39 -24 -66.5t-45.5 -45t-74.5 -25.5t-112 -8z" />
    <glyph glyph-name="uni030A.case" horiz-adv-x="0" 
d="M122 832q-81 0 -111.5 21.5t-30.5 58.5q0 15 4 33q6 30 17.5 50.5t32 33.5t50.5 19t74 6q80 0 110.5 -21.5t30.5 -59.5q0 -14 -3 -30q-7 -30 -18.5 -51.5t-31.5 -34.5t-50 -19t-74 -6zM129 903q38 0 54 8t21 33q2 7 2 13q0 13 -9.5 19.5t-46.5 6.5q-38 0 -53.5 -8
t-20.5 -31q-2 -8 -2 -14q0 -13 9.5 -20t45.5 -7z" />
    <glyph glyph-name="tildecomb.case" horiz-adv-x="0" 
d="M-93 867q16 66 49 93t87 27q31 0 57 -6t47.5 -13.5t39.5 -14t33 -6.5t22.5 7t9.5 18l2 10h116l-4 -22q-14 -66 -48.5 -93t-88.5 -27q-31 0 -57 6.5t-47.5 14t-39.5 14t-33 6.5t-22 -7t-10 -20l-2 -10h-116z" />
    <glyph glyph-name="uni0307.case" horiz-adv-x="0" 
d="M53 978h189l-25 -122h-190z" />
    <glyph glyph-name="uni0308.case" horiz-adv-x="0" 
d="M-43 978h149l-26 -122h-149zM189 978h150l-26 -122h-149z" />
    <glyph glyph-name="uni0304.case" horiz-adv-x="0" 
d="M-57 974h407l-24 -116h-407z" />
    <glyph glyph-name="uni030C.alt" horiz-adv-x="0" 
d="M199 802h165l-170 -252h-93z" />
    <glyph glyph-name="uni0327.1" horiz-adv-x="0" 
d="M-135 -198h-164l174 204h159z" />
    <glyph glyph-name="Ccedilla.1" horiz-adv-x="851" 
d="M325 -12q-90 9 -148 39t-88 83.5t-31 96t-1 49.5q0 74 20 170q21 98 55 171t87 122.5t129 74t182 24.5q91 0 156.5 -19.5t108 -55.5t63 -87t21.5 -114h-205q-8 53 -45.5 79t-117.5 26q-58 0 -97.5 -13.5t-67.5 -43.5t-46 -76.5t-32 -112.5t-14.5 -91t-0.5 -27
q0 -44 16.5 -72.5t54.5 -42t101 -13.5q38 0 66.5 6t50.5 19t39 33t32 47h206q-25 -70 -59.5 -121.5t-83 -86t-114.5 -51.5t-155 -17l-32 -26q56 -1 81 -18t25 -48q0 -12 -3 -27q-4 -21 -14 -36t-28 -24.5t-44.5 -14t-65.5 -4.5q-29 0 -52 2t-46 7l14 65q27 -5 45.5 -6
t39.5 -1q36 0 48 4.5t15 18.5q1 3 1 6q0 9 -8 13.5t-45 4.5q-17 0 -33 -1.5t-36 -3.5l13 57z" />
    <glyph glyph-name="germandbls.cap" horiz-adv-x="1650" 
d="M58 199q78 -17 163 -32t169 -15q44 0 73 3.5t48 11.5t29 21.5t14 32.5q3 15 3 26q0 6 -1.5 16.5t-16.5 22t-45 20t-80 16.5q-84 12 -143.5 30t-94.5 49.5t-41.5 65.5t-6.5 56q0 37 10 83q10 47 33 86t66.5 67t112.5 43.5t171 15.5q82 0 157.5 -8t156.5 -28l-36 -170
q-85 19 -155.5 28.5t-145.5 9.5q-83 0 -120.5 -13t-45.5 -50q-3 -14 -3 -24q0 -8 2.5 -18t18.5 -19t46 -16t78 -15q85 -13 145.5 -30.5t95.5 -49t42 -66t7 -56.5q0 -38 -11 -87q-11 -51 -35.5 -92.5t-68.5 -70t-112.5 -44t-168.5 -15.5q-90 0 -182 12t-164 33zM883 199
q78 -17 163 -32t169 -15q44 0 73 3.5t48 11.5t29 21.5t14 32.5q3 15 3 26q0 6 -1.5 16.5t-16.5 22t-45 20t-80 16.5q-84 12 -143.5 30t-94.5 49.5t-41.5 65.5t-6.5 56q0 37 10 83q10 47 33 86t66.5 67t112.5 43.5t171 15.5q82 0 157.5 -8t156.5 -28l-36 -170
q-85 19 -155.5 28.5t-145.5 9.5q-83 0 -120.5 -13t-45.5 -50q-3 -14 -3 -24q0 -8 2.5 -18t18.5 -19t46 -16t78 -15q85 -13 145.5 -30.5t95.5 -49t42 -66t7 -56.5q0 -38 -11 -87q-11 -51 -35.5 -92.5t-68.5 -70t-112.5 -44t-168.5 -15.5q-90 0 -182 12t-164 33z" />
    <glyph glyph-name="ccedilla.1" horiz-adv-x="650" 
d="M253 -12q-65 7 -111 27.5t-71.5 59t-28 74t-2.5 49.5q0 50 12 113q17 76 47 129t75 86t106 47.5t140 14.5q62 0 118 -7t113 -20l-30 -148q-31 5 -57 9t-50.5 6.5t-48 3.5t-49.5 1q-43 0 -73 -7t-50.5 -23.5t-33 -44.5t-21.5 -70q-8 -38 -8 -64q0 -4 1 -20.5t15 -33.5
t41 -24t67 -7q27 0 51.5 1t50.5 3.5t54.5 6t62.5 8.5l-30 -147q-109 -27 -208 -27l-32 -26q56 -1 81 -18t25 -48q0 -12 -3 -27q-4 -21 -14 -36t-28 -24.5t-44.5 -14t-65.5 -4.5q-29 0 -52 2t-46 7l14 65q27 -5 45.5 -6t39.5 -1q36 0 48 4.5t15 18.5q1 3 1 6q0 9 -8 13.5
t-45 4.5q-17 0 -33 -1.5t-36 -3.5l13 57z" />
    <glyph glyph-name="i.trk" horiz-adv-x="338" 
d="M182 791h190l-31 -146h-190zM137 572h187l-121 -572h-187z" />
    <glyph glyph-name="f_b.1" horiz-adv-x="1200" 
d="M-92 -61q14 -2 29 -2.5t28 -0.5q35 0 54 14.5t27 55.5l90 424h-79l23 108l86 34l2 9q12 58 32.5 102t53 74t79.5 45.5t114 15.5q29 0 50 -2.5t45 -7.5l-30 -144q-23 4 -52 4q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h136l-31 -142h-135l-88 -415
q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-33 0 -56 1.5t-51 7.5zM617 802h187l-52 -244q43 15 82 22.5t78 7.5q70 0 123 -19t84.5 -58.5t37 -80t5.5 -62.5q0 -47 -12 -104q-17 -81 -47 -134.5t-71.5 -86t-94 -46t-115.5 -13.5q-57 0 -108 13.5t-106 37.5l-33 -35
h-128zM852 437q-32 0 -65 -7.5t-66 -19.5l-52 -247q28 -13 58 -20t62 -7q40 0 69 7.5t49.5 24.5t34 45.5t22.5 70.5q7 34 7 60q0 7 -1.5 25t-16 35.5t-40 25t-61.5 7.5z" />
    <glyph glyph-name="f_f.1" horiz-adv-x="850" 
d="M-88 -61q14 -2 29 -2.5t29 -0.5q35 0 53.5 14.5t26.5 55.5l90 424h-79l23 108l86 34l2 9q12 58 32.5 102t53 74t79.5 45.5t114 15.5q32 0 55 -2.5t49 -7.5l-29 -144q-16 2 -31 3t-31 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h198l2 9q12 58 32.5 102
t53 74t79.5 45.5t114 15.5q36 0 62.5 -2.5t56.5 -7.5l-29 -144q-20 2 -38.5 3t-38.5 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h159l-30 -142h-159l-88 -415q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-33 0 -56 1.5t-51 7.5l30 144q14 -2 29 -2.5
t28 -0.5q35 0 54 14.5t27 55.5l90 424h-198l-88 -415q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-32 0 -55.5 1.5t-51.5 7.5z" />
    <glyph glyph-name="f_h.1" horiz-adv-x="1212" 
d="M-92 -61q14 -2 29 -2.5t28 -0.5q35 0 54 14.5t27 55.5l90 424h-79l23 108l86 34l2 9q12 58 32.5 102t53 74t79.5 45.5t114 15.5q29 0 50 -2.5t45 -7.5l-30 -144q-23 4 -52 4q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h136l-31 -142h-135l-88 -415
q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-33 0 -56 1.5t-51 7.5zM617 802h187l-53 -251q45 17 89 27t93 10q135 0 180.5 -56t45.5 -125q0 -31 -7 -67l-73 -340h-186l71 338q4 17 4 31q0 25 -16 42t-70 17q-40 0 -81.5 -9.5t-83.5 -26.5l-83 -392h-187z" />
    <glyph glyph-name="fi.1" horiz-adv-x="751" 
d="M-92 -61q14 -2 29 -2.5t28 -0.5q35 0 54 14.5t27 55.5l90 424h-79l23 108l86 34l2 9q12 58 32.5 102t53 74t79.5 45.5t114 15.5q23 0 42 -2.5t34 -6.5l-29 -144q-18 3 -34 3q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h384l-121 -572h-187l91 430h-197
l-88 -415q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-33 0 -56 1.5t-51 7.5zM596 797h190l-31 -145h-189z" />
    <glyph glyph-name="f_j.1" horiz-adv-x="750" 
d="M-92 -61q14 -2 29 -2.5t28 -0.5q35 0 54 14.5t27 55.5l90 424h-79l23 108l86 34l2 9q12 58 32.5 102t53 74t79.5 45.5t114 15.5q23 0 41.5 -2.5t34.5 -6.5l-30 -144q-16 3 -33 3q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h383l-120 -566q-12 -58 -30 -99.5
t-46.5 -68.5t-70 -39.5t-100.5 -12.5q-33 0 -58.5 1.5t-49.5 7.5l29 138q14 -2 29.5 -3t28.5 -1q35 0 53 16.5t27 60.5l91 424h-196l-88 -415q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-33 0 -56 1.5t-51 7.5zM596 797h190l-31 -145h-189z" />
    <glyph glyph-name="f_k.1" horiz-adv-x="1164" 
d="M-92 -61q14 -2 29 -2.5t28 -0.5q35 0 54 14.5t27 55.5l90 424h-79l23 108l86 34l2 9q12 58 32.5 102t53 74t79.5 45.5t114 15.5q29 0 50 -2.5t45 -7.5l-30 -144q-23 4 -52 4q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h136l-31 -142h-135l-88 -415
q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-33 0 -56 1.5t-51 7.5zM617 802h187l-96 -454l259 224h236l-312 -269l194 -303h-215l-172 301l-64 -301h-187z" />
    <glyph glyph-name="fl.1" horiz-adv-x="768" 
d="M-92 -61q14 -2 29 -2.5t28 -0.5q35 0 54 14.5t27 55.5l90 424h-79l23 108l86 34l2 9q12 58 32.5 102t53 74t79.5 45.5t114 15.5q29 0 50 -2.5t45 -7.5l-30 -144q-23 4 -52 4q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h136l-31 -142h-135l-88 -415
q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-33 0 -56 1.5t-51 7.5zM617 802h187l-170 -802h-187z" />
    <glyph glyph-name="f_f_b.1" horiz-adv-x="1584" 
d="M-92 -61q14 -2 29 -2.5t28 -0.5q35 0 54 14.5t27 55.5l90 424h-79l23 108l86 34l2 9q12 58 32.5 102t53 74t79.5 45.5t114 15.5q32 0 55 -2.5t49 -7.5l-30 -144q-15 2 -30 3t-31 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h198l2 9q12 58 32 102t52.5 74
t80 45.5t114.5 15.5q29 0 50 -2.5t45 -7.5l-30 -144q-23 4 -52 4q-25 0 -42.5 -3.5t-29.5 -12t-19 -23.5t-12 -38l-4 -19h135l-31 -142h-134l-88 -415q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-33 0 -56 1.5t-51 7.5l30 144q14 -2 29 -2.5t28 -0.5q35 0 54 14.5
t27 55.5l90 424h-198l-88 -415q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-33 0 -56 1.5t-51 7.5zM1001 802h187l-52 -244q42 15 81 22.5t79 7.5q70 0 123 -19t84.5 -58.5t37 -80t5.5 -62.5q0 -47 -12 -104q-17 -81 -47 -134.5t-71.5 -86t-94 -46t-115.5 -13.5
q-57 0 -108.5 13.5t-105.5 38.5l-33 -36h-128zM1236 437q-32 0 -65.5 -7.5t-65.5 -19.5l-53 -247q29 -12 59 -19.5t62 -7.5q39 0 68 7.5t50 24.5t34.5 45.5t22.5 70.5q7 34 7 60q0 7 -1.5 25t-16 35.5t-40 25t-61.5 7.5z" />
    <glyph glyph-name="f_f_h.1" horiz-adv-x="1597" 
d="M-92 -61q14 -2 29 -2.5t28 -0.5q35 0 54 14.5t27 55.5l90 424h-79l23 108l86 34l2 9q12 58 32.5 102t53 74t79.5 45.5t114 15.5q32 0 55 -2.5t49 -7.5l-30 -144q-15 2 -30 3t-31 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h198l2 9q12 58 32 102t52.5 74
t80 45.5t114.5 15.5q31 0 54.5 -2.5t49.5 -7.5l-30 -144q-15 2 -30 3t-31 1q-25 0 -42.5 -3.5t-29.5 -12t-19 -23.5t-12 -38l-4 -19h144l-31 -142h-143l-88 -415q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-33 0 -56 1.5t-51 7.5l30 144q14 -2 29 -2.5t28 -0.5
q35 0 54 14.5t27 55.5l90 424h-198l-88 -415q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-33 0 -56 1.5t-51 7.5zM1001 802h187l-53 -251q45 17 89 27t93 10q135 0 180.5 -55.5t45.5 -124.5q0 -31 -8 -68l-72 -340h-186l71 338q3 17 3 30q0 25 -15.5 42.5t-69.5 17.5
q-41 0 -82 -9.5t-83 -26.5l-83 -392h-187z" />
    <glyph glyph-name="f_f_i.1" horiz-adv-x="1134" 
d="M-92 -61q14 -2 29 -2.5t28 -0.5q35 0 54 14.5t27 55.5l90 424h-79l23 108l86 34l2 9q12 58 32.5 102t53 74t79.5 45.5t114 15.5q32 0 55 -2.5t49 -7.5l-30 -144q-15 2 -30 3t-31 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h197l2 9q12 58 32 102t52.5 74
t80 45.5t114.5 15.5q23 0 41.5 -2.5t34.5 -6.5l-30 -144q-16 3 -34 3q-25 0 -42 -3.5t-29 -12t-19 -23.5t-12 -38l-4 -19h384l-122 -572h-187l92 430h-197l-88 -415q-12 -59 -30 -102t-46.5 -71t-70.5 -42t-103 -14q-33 0 -56 1.5t-51 7.5l30 144q14 -2 29 -2.5t28 -0.5
q35 0 53.5 14.5t26.5 55.5l91 424h-197l-88 -415q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-33 0 -56 1.5t-51 7.5zM981 797h188l-30 -145h-189z" />
    <glyph glyph-name="f_f_j.1" horiz-adv-x="1134" 
d="M-92 -61q14 -2 29 -2.5t28 -0.5q35 0 54 14.5t27 55.5l90 424h-79l23 108l86 34l2 9q12 58 32.5 102t53 74t79.5 45.5t114 15.5q32 0 55 -2.5t49 -7.5l-30 -144q-15 2 -30 3t-31 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h197l2 9q12 58 32 102t52.5 74
t80 45.5t114.5 15.5q23 0 42 -2.5t34 -6.5l-30 -144q-16 3 -34 3q-25 0 -42 -3.5t-29 -12t-19 -23.5t-12 -38l-4 -19h384l-120 -566q-12 -58 -30 -99.5t-46.5 -68.5t-70 -39.5t-100.5 -12.5q-34 0 -59 1.5t-48 7.5l30 144q13 -2 28.5 -2.5t27.5 -0.5q35 0 53.5 15t26.5 55
l91 424h-197l-88 -415q-12 -59 -30 -102t-46.5 -71t-70.5 -42t-103 -14q-33 0 -56 1.5t-51 7.5l30 144q14 -2 29 -2.5t28 -0.5q35 0 53.5 14.5t26.5 55.5l91 424h-197l-88 -415q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-33 0 -56 1.5t-51 7.5zM981 797h189l-31 -145
h-189z" />
    <glyph glyph-name="f_f_k.1" horiz-adv-x="1549" 
d="M-92 -61q14 -2 29 -2.5t28 -0.5q35 0 54 14.5t27 55.5l90 424h-79l23 108l86 34l2 9q12 58 32.5 102t53 74t79.5 45.5t114 15.5q32 0 55 -2.5t49 -7.5l-30 -144q-15 2 -30 3t-31 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h198l2 9q12 58 32 102t52.5 74
t80 45.5t114.5 15.5q29 0 50 -2.5t45 -7.5l-30 -144q-23 4 -52 4q-25 0 -42.5 -3.5t-29.5 -12t-19 -23.5t-12 -38l-4 -19h135l-31 -142h-134l-88 -415q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-33 0 -56 1.5t-51 7.5l30 144q14 -2 29 -2.5t28 -0.5q35 0 54 14.5
t27 55.5l90 424h-198l-88 -415q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-33 0 -56 1.5t-51 7.5zM1001 802h187l-96 -454l259 224h236l-312 -269l194 -303h-215l-172 301l-64 -301h-187z" />
    <glyph glyph-name="f_f_l.1" horiz-adv-x="1153" 
d="M-92 -61q14 -2 29 -2.5t28 -0.5q35 0 54 14.5t27 55.5l90 424h-79l23 108l86 34l2 9q12 58 32.5 102t53 74t79.5 45.5t114 15.5q32 0 55 -2.5t49 -7.5l-30 -144q-15 2 -30 3t-31 1q-25 0 -42 -3.5t-29 -12t-19.5 -23.5t-12.5 -38l-4 -19h198l2 9q12 58 32 102t52.5 74
t80 45.5t114.5 15.5q29 0 50 -2.5t45 -7.5l-30 -144q-23 4 -52 4q-25 0 -42.5 -3.5t-29.5 -12t-19 -23.5t-12 -38l-4 -19h135l-31 -142h-134l-88 -415q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-33 0 -56 1.5t-51 7.5l30 144q14 -2 29 -2.5t28 -0.5q35 0 54 14.5
t27 55.5l90 424h-198l-88 -415q-12 -59 -29.5 -102t-46 -71t-70.5 -42t-104 -14q-33 0 -56 1.5t-51 7.5zM1001 802h187l-170 -802h-187z" />
    <glyph glyph-name="exclamdown.case" horiz-adv-x="380" 
d="M349 619h-193l39 183h192zM263 223l-47 -223h-190l48 223l87 311h146z" />
    <glyph glyph-name="questiondown.case" horiz-adv-x="669" 
d="M588 627h-192l39 183h191zM543 408l-20 -94h-144q-51 0 -84 -4t-53 -13.5t-29 -25t-14 -37.5q-3 -14 -3 -25q0 -10 3 -22.5t21 -23t52.5 -15t90.5 -4.5q60 0 126 7.5t130 21.5l-31 -154q-27 -6 -59 -11t-65.5 -8.5t-67.5 -5.5t-65 -2q-187 0 -255 52.5t-68 133.5
q0 25 6 55q11 57 35 96.5t64 64.5t97 36t134 11h17l40 101h147z" />
    <glyph glyph-name="hyphen.case" horiz-adv-x="380" 
d="M41 324l32 155h318l-33 -155h-317z" />
    <glyph glyph-name="guilsinglleft.case" horiz-adv-x="377" 
d="M239 633h191l-192 -233l92 -231h-189l-93 232z" />
    <glyph glyph-name="guilsinglright.case" horiz-adv-x="377" 
d="M188 170h-190l191 232l-92 232h189l94 -232z" />
    <glyph glyph-name="guillemotleft.case" horiz-adv-x="663" 
d="M239 633h191l-192 -233l92 -231h-189l-93 232zM525 633h191l-192 -233l92 -231h-189l-93 232z" />
    <glyph glyph-name="guillemotright.case" horiz-adv-x="663" 
d="M188 170h-190l191 232l-92 232h189l94 -232zM474 170h-190l191 232l-92 232h189l94 -232z" />
    <glyph glyph-name="parenleft.case" horiz-adv-x="411" 
d="M144 -73q-23 46 -41.5 99t-27 109.5t-8.5 112.5q0 15 1.5 59.5t19.5 131.5q29 135 92.5 242t147.5 194h183q-40 -42 -77.5 -95.5t-69.5 -114.5t-57.5 -128t-40.5 -136q-17 -76 -19 -120.5t-2 -64.5q0 -46 6 -97t22 -101.5t37 -90.5h-166z" />
    <glyph glyph-name="parenright.case" horiz-adv-x="411" 
d="M317 875q23 -46 41.5 -99t26.5 -109t8 -112q0 -16 -1.5 -60.5t-19.5 -131.5q-29 -136 -92 -242t-148 -194h-183q40 42 77.5 95.5t69.5 114.5t57.5 128t40.5 136q17 76 19 120.5t2 64.5q0 46 -6 97t-22 101.5t-37 90.5h167z" />
    <glyph glyph-name="braceleft.case" horiz-adv-x="411" 
d="M304 -73h-61q-54 0 -95 10t-66.5 35t-30.5 53.5t-5 46.5q0 33 9 75l28 133q2 11 2 19q0 11 -5 20.5t-34 9.5h-29l31 145h31q27 0 38.5 10.5t16.5 35.5l38 180q11 51 27.5 85t42 54t62.5 28t88 8h113l-31 -146h-68q-29 0 -42 -9t-20 -40l-29 -138q-12 -59 -36 -96t-70 -55
q35 -19 43 -44t8 -45t-5 -42l-28 -129q-4 -17 -4 -28q0 -14 7.5 -21.5t40.5 -7.5h64z" />
    <glyph glyph-name="braceright.case" horiz-adv-x="411" 
d="M288 102q-11 -51 -27.5 -85t-42 -54t-62 -28t-87.5 -8h-113l30 146h67q30 0 43 9.5t20 40.5l29 137q13 59 36.5 96t68.5 56q-33 19 -41.5 44t-8.5 46q0 19 5 40l28 129q4 19 4 30.5t-5.5 17.5t-15.5 8t-27 2h-63l31 146h61q54 0 95 -10t66 -35t30 -53.5t5 -46.5
q0 -33 -9 -75l-28 -133q-2 -10 -2 -19q0 -10 5 -19.5t34 -9.5h29l-31 -145h-30q-28 0 -39.5 -10.5t-16.5 -35.5z" />
    <glyph glyph-name="bracketleft.case" horiz-adv-x="411" 
d="M189 875h316l-31 -146h-140l-140 -656h141l-31 -146h-317z" />
    <glyph glyph-name="bracketright.case" horiz-adv-x="411" 
d="M271 -73h-315l31 146h139l139 656h-139l31 146h315z" />
    <glyph glyph-name="endash.case" 
d="M8 324l34 155h760l-34 -155h-760z" />
    <glyph glyph-name="emdash.case" horiz-adv-x="1520" 
d="M8 324l34 155h1520l-34 -155h-1520z" />
    <glyph glyph-name="bullet.case" horiz-adv-x="502" 
d="M254 216q-47 0 -82 11t-56 35.5t-24.5 50t-3.5 38.5q0 30 8 67q18 90 68.5 128.5t134.5 38.5q46 0 80.5 -11t55 -35t24.5 -49.5t4 -41.5q0 -28 -7 -63q-20 -91 -70.5 -130t-131.5 -39z" />
    <glyph glyph-name="periodcentered.case" horiz-adv-x="380" 
d="M138 492h192l-38 -183h-192z" />
    <glyph glyph-name="at.case" horiz-adv-x="928" 
d="M396 -16q-95 0 -166.5 24.5t-113.5 79.5t-49 111.5t-7 85.5q0 70 19 160q20 94 51.5 163.5t85.5 116t134.5 69.5t198.5 23q221 0 296.5 -84t75.5 -202q0 -59 -15 -130l-37 -176q-17 -79 -55.5 -112.5t-113.5 -33.5q-88 0 -133.5 32.5t-45.5 91.5v8q-46 -20 -111 -20
q-34 0 -63.5 8t-49 25.5t-25.5 38.5t-6 37q0 18 5 39q11 56 47 84t112 28h148l2 9q2 7 2 13q0 3 -1 8.5t-11.5 10.5t-30.5 7.5t-55 2.5q-38 0 -75.5 -3.5t-79.5 -9.5l23 106q21 4 38.5 6.5t36 4.5t39.5 3t48 1q66 0 108 -10.5t64 -31t25 -41t3 -32.5q0 -23 -6 -50l-46 -218
q-2 -9 -2 -16q0 -12 6.5 -21.5t32.5 -9.5q21 0 31 9.5t14 27.5l39 184q15 75 15 128q0 10 -2 44t-32 69t-87.5 49t-146.5 14q-79 0 -134.5 -14.5t-93 -47.5t-61 -85.5t-40.5 -128.5q-17 -82 -17 -140q0 -12 2.5 -50t31.5 -75.5t82.5 -52t131.5 -14.5q17 0 36 1t36 3
l-25 -111q-33 -6 -83 -6zM441 287q58 0 101 23l13 52h-99q-25 0 -36 -8t-16 -28q-2 -8 -2 -14q0 -10 7 -17.5t32 -7.5z" />
    <glyph glyph-name="dollar.weight" horiz-adv-x="716" 
d="M60 229q48 -12 100 -23.5t106 -16.5l32 150q-57 10 -96.5 26.5t-61.5 44.5t-25.5 55t-3.5 42q0 32 8 72q9 43 28 76.5t51 57.5t79.5 38t111.5 18l20 93h152l-20 -95q48 -4 92 -12t87 -20l-30 -157q-50 12 -93.5 20t-88.5 13l-29 -136q60 -11 100.5 -29t62 -46.5
t23.5 -53.5t2 -34q0 -35 -10 -81t-27.5 -81.5t-49 -60.5t-79 -39.5t-116.5 -17.5l-20 -92h-152l20 95q-120 11 -205 41zM293 561q-3 -13 -3 -23q0 -11 5.5 -21t36.5 -18l23 111q-30 -5 -43.5 -16t-18.5 -33zM482 245q3 14 3 25q0 12 -5 24t-35 21l-27 -126q34 5 46.5 18
t17.5 38z" />
    <glyph glyph-name="cent.weight" horiz-adv-x="716" 
d="M603 802l-22 -101q41 -4 80 -9.5t80 -13.5l-33 -159q-48 8 -86.5 12.5t-74.5 6.5l-59 -274q39 2 81.5 5t96.5 11l-33 -153q-47 -11 -91 -16.5t-88 -7.5l-22 -103h-151l22 103q-70 6 -119.5 26.5t-78 59.5t-32.5 77t-4 56q0 48 12 107q15 75 43 126t70.5 83t101 47.5
t134.5 18.5l21 98h152zM272 406q-8 -37 -8 -64q0 -21 8.5 -44.5t64.5 -31.5l58 272q-30 -4 -50.5 -13.5t-34.5 -25.5t-22.5 -39t-15.5 -54z" />
    <glyph glyph-name="zeroslash" horiz-adv-x="716" 
d="M452 818q89 0 149 -25t91.5 -79t33 -101t1.5 -56q0 -79 -22 -186q-21 -96 -50 -168.5t-74 -121t-110 -73t-158 -24.5q-89 0 -149 25t-91.5 79t-33 101t-1.5 56q0 79 22 186q20 96 49 168.5t74 121t110.5 73t158.5 24.5zM249 401q-3 -14 -5.5 -26.5t-4.5 -24.5l272 289
q-13 11 -32 15t-45 4q-40 0 -68 -12t-48.5 -41.5t-36.5 -79t-32 -124.5zM331 144q40 0 68 12t48.5 41.5t36.5 79t32 124.5q3 14 5.5 26.5t4.5 24.5l-271 -289q13 -11 31.5 -15t44.5 -4z" />
    <glyph glyph-name="dollar.lt" 
d="M82 229q64 -17 136 -30.5t144 -13.5q41 0 67 3t41.5 9.5t23 18t10.5 29.5q3 14 3 25q0 5 -1.5 14.5t-14 19t-38.5 15.5t-69 11q-75 9 -127.5 24t-82.5 42.5t-35.5 58t-5.5 50.5q0 33 8 74q9 43 28 76.5t51 57.5t79.5 38t111.5 18l20 93h152l-20 -95q48 -4 92 -12t87 -20
l-30 -157q-69 17 -129 26.5t-125 9.5q-38 0 -64 -2t-42.5 -8t-25 -16t-11.5 -27q-3 -14 -3 -24q0 -5 1.5 -13t15 -16t39.5 -12.5t69 -10.5q79 -10 132 -26.5t82.5 -45.5t33.5 -57.5t4 -43.5q0 -35 -10 -81t-27.5 -81.5t-49 -60.5t-79 -39.5t-116.5 -17.5l-20 -92h-152l20 95
q-120 11 -205 41z" />
    <glyph glyph-name="dollar.lt.weight" 
d="M82 229q48 -12 100 -23.5t106 -16.5l32 150q-57 10 -96.5 26.5t-61.5 44.5t-25.5 55t-3.5 42q0 32 8 72q9 43 28 76.5t51 57.5t79.5 38t111.5 18l20 93h152l-20 -95q48 -4 92 -12t87 -20l-30 -157q-50 12 -93.5 20t-88.5 13l-29 -136q60 -11 100.5 -29t62 -46.5
t23.5 -53.5t2 -34q0 -35 -10 -81t-27.5 -81.5t-49 -60.5t-79 -39.5t-116.5 -17.5l-20 -92h-152l20 95q-120 11 -205 41zM315 561q-3 -13 -3 -23q0 -11 5.5 -21t36.5 -18l23 111q-30 -5 -43.5 -16t-18.5 -33zM504 245q3 14 3 25q0 12 -5 24t-35 21l-27 -126q34 5 46.5 18
t17.5 38z" />
    <glyph glyph-name="Euro.lt" 
d="M71 366h87q5 32 12 66q0 1 1 2v4q1 1 1 2h-86l28 132h93q23 66 54 112.5t75 76.5t103.5 43.5t139.5 13.5q74 0 130 -7.5t94 -15.5l-33 -155q-25 5 -48 9t-47 7t-51.5 4.5t-61.5 1.5q-33 0 -58 -4t-44.5 -14t-34 -27.5t-26.5 -44.5h304l-28 -132h-316q0 -2 -2 -8l-3 -17
q-1 -1 -1 -3v-2q0 -1 -1 -3q-2 -11 -4 -21l-4 -20h315l-28 -132h-304q1 -55 29.5 -74t94.5 -19q34 0 62 1.5t53 4.5t49.5 7t52.5 10l-33 -156q-31 -8 -84 -16t-123 -8q-74 0 -127 15.5t-87 46.5t-50 78t-17 110h-105z" />
    <glyph glyph-name="cent.lt" 
d="M625 802l-22 -102q36 -3 72 -8.5t72 -13.5l-33 -159q-36 6 -66.5 10t-58 7t-54.5 4t-56 1q-45 0 -73.5 -7t-47 -23t-29.5 -41.5t-19 -63.5q-9 -41 -9 -68.5t13 -44t39.5 -23.5t71.5 -7q30 0 57.5 1t57 3t61.5 5t71 8l-33 -153q-42 -11 -81.5 -16.5t-81.5 -7.5l-22 -103
h-151l22 104q-65 6 -111.5 27.5t-73 60.5t-30 75.5t-3.5 53.5q0 48 12 108q15 74 42 125t67.5 83t96 48t127.5 19l21 98h152z" />
    <glyph glyph-name="cent.lt.weight" 
d="M625 802l-22 -101q41 -4 80 -9.5t80 -13.5l-33 -159q-48 8 -86.5 12.5t-74.5 6.5l-59 -274q39 2 81.5 5t96.5 11l-33 -153q-47 -11 -91 -16.5t-88 -7.5l-22 -103h-151l22 103q-70 6 -119.5 26.5t-78 59.5t-32.5 77t-4 56q0 48 12 107q15 75 43 126t70.5 83t101 47.5
t134.5 18.5l21 98h152zM294 406q-8 -37 -8 -64q0 -21 8.5 -44.5t64.5 -31.5l58 272q-30 -4 -50.5 -13.5t-34.5 -25.5t-22.5 -39t-15.5 -54z" />
    <glyph glyph-name="sterling.lt" 
d="M93 156q38 0 58 17t30 62l20 95h-129l32 147h128l21 100q13 62 34 107t57 75t90.5 44.5t133.5 14.5q70 0 124 -8.5t90 -18.5l-33 -157q-40 11 -79.5 16.5t-95.5 5.5q-65 0 -96.5 -20t-41.5 -67l-20 -92h275l-31 -147h-275l-16 -76q-14 -62 -54 -98h352l-33 -156h-631
l32 156h58z" />
    <glyph glyph-name="yen.lt" 
d="M274 234h-198l28 132h198l15 74h-198l28 132h112l-112 230h192l95 -213l192 213h208l-212 -230h114l-28 -132h-199l-16 -74h199l-28 -132h-199l-50 -234h-191z" />
    <glyph glyph-name="florin.lt" 
d="M121 477h192l22 106q13 61 34 105t57.5 73t92 43t137.5 14q36 0 62.5 -2t58.5 -7l-32 -150q-24 4 -47.5 5t-46.5 1q-31 0 -52.5 -4t-36 -14.5t-23 -28t-14.5 -44.5l-21 -97h202l-32 -147h-201l-68 -317q-12 -55 -31.5 -97.5t-53 -71t-85 -43.5t-127.5 -15q-23 0 -41 0.5
t-33.5 1.5t-29.5 3t-29 4l33 150q23 -4 45.5 -5t45.5 -1q31 0 51 4t33 14t20.5 27t13.5 42l64 304h-192z" />
    <glyph glyph-name="zeroslash.lt" 
d="M474 818q89 0 149 -25t91.5 -79t33 -101t1.5 -56q0 -79 -22 -186q-21 -96 -50 -168.5t-74 -121t-110 -73t-158 -24.5q-89 0 -149 25t-91.5 79t-33 101t-1.5 56q0 79 22 186q20 96 49 168.5t74 121t110.5 73t158.5 24.5zM271 401q-3 -14 -5.5 -26.5t-4.5 -24.5l272 289
q-13 11 -32 15t-45 4q-40 0 -68 -12t-48.5 -41.5t-36.5 -79t-32 -124.5zM353 144q40 0 68 12t48.5 41.5t36.5 79t32 124.5q3 14 5.5 26.5t4.5 24.5l-271 -289q13 -11 31.5 -15t44.5 -4z" />
    <glyph glyph-name="zero.lt" 
d="M474 818q89 0 149 -25t91.5 -79t33 -101t1.5 -56q0 -79 -22 -186q-21 -96 -50 -168.5t-74 -121t-110 -73t-158 -24.5q-89 0 -149 25t-91.5 79t-33 101t-1.5 56q0 79 22 186q20 96 49 168.5t74 121t110.5 73t158.5 24.5zM353 144q40 0 68 12t48.5 41.5t36.5 79t32 124.5
t19.5 108.5t3.5 48.5q0 28 -7.5 52t-31.5 36t-66 12q-40 0 -68 -12t-48.5 -41.5t-36.5 -79t-32 -124.5t-19.5 -108.5t-3.5 -48.5q0 -28 7.5 -52t31.5 -36t66 -12z" />
    <glyph glyph-name="one.lt" 
d="M71 157h222l93 435l-203 -94l-54 162l305 142h188l-137 -645h193l-34 -157h-606z" />
    <glyph glyph-name="two.lt" 
d="M471 818q166 0 228.5 -49.5t62.5 -127.5q0 -30 -8 -65q-10 -41 -23 -69.5t-36.5 -51.5t-60 -45t-92.5 -51l-231 -119q-31 -17 -43 -30.5t-17 -37.5l-3 -15h411l-33 -157h-604l42 194q7 33 18.5 58.5t30.5 46.5t46.5 39.5t65.5 37.5l241 121q29 15 46.5 24.5t27.5 18
t14.5 17t7.5 21.5q3 12 3 22q0 26 -26 40t-107 14q-30 0 -63 -2t-66 -6t-64.5 -8.5t-58.5 -9.5l35 163q27 6 55 11t59 8.5t65.5 5.5t76.5 2z" />
    <glyph glyph-name="three.lt" 
d="M54 171q60 -14 116 -21.5t125 -7.5q57 0 93 4t57.5 14t31.5 27t16 42q5 20 5 36q0 29 -20 46.5t-87 17.5h-217l32 157h216q59 0 89 15t40 61q4 18 4 32q0 8 -2.5 21t-18.5 24.5t-46 16.5t-77 5q-39 0 -71 -1.5t-61 -4.5t-57.5 -8t-59.5 -11l33 155q46 11 111.5 19
t144.5 8q92 0 155.5 -15t100 -46t43.5 -64.5t7 -55.5q0 -32 -8 -70q-17 -77 -58 -114.5t-104 -53.5q61 -26 81 -67.5t20 -81.5q0 -29 -8 -63q-12 -56 -37 -95t-70 -63t-113.5 -34.5t-167.5 -10.5q-92 0 -159 10t-112 21z" />
    <glyph glyph-name="four.lt" 
d="M725 456l-97 -456h-188l33 153h-449l31 147l372 502h216l-367 -494h230l31 148h188z" />
    <glyph glyph-name="five.lt" 
d="M55 177q121 -29 260 -29q47 0 79 4t52.5 14t31.5 28t17 46q4 17 4 32q0 34 -24.5 53t-101.5 19h-258l97 458h549l-33 -158h-358l-30 -142h73q172 0 227.5 -57t55.5 -140q0 -38 -10 -85q-15 -76 -46 -122.5t-79.5 -72t-115.5 -33.5t-155 -8q-42 0 -81.5 3t-74.5 7.5
t-64 10t-49 10.5z" />
    <glyph glyph-name="six.lt" 
d="M356 -16q-98 0 -162 25t-97 78.5t-34.5 99.5t-1.5 55q0 77 22 181q23 108 55.5 183t81 122t116.5 68.5t162 21.5q81 0 143.5 -9.5t104.5 -23.5l-34 -160q-48 12 -100.5 20.5t-112.5 8.5q-46 0 -79 -7t-57.5 -25.5t-41 -50t-29.5 -80.5q32 11 69 18t80 7q150 0 203 -60.5
t53 -142.5q0 -39 -10 -87q-14 -66 -40 -112.5t-66.5 -75t-96 -41.5t-128.5 -13zM391 358q-37 0 -69 -6t-63 -17q-10 -56 -10.5 -76t-0.5 -23q0 -34 11 -56t37 -31.5t68 -9.5q60 0 90 24t43 85q5 22 5 39q0 8 -2.5 22.5t-15.5 26.5t-36.5 17t-56.5 5z" />
    <glyph glyph-name="seven.lt" 
d="M170 802h640l-31 -147l-493 -655h-239l509 644h-420z" />
    <glyph glyph-name="eight.lt" 
d="M341 -16q-82 0 -145.5 12.5t-103.5 41.5t-51 64.5t-11 64.5t8 65q16 75 59 119t110 63q-51 22 -68 57.5t-17 71.5q0 32 9 72q11 53 36 91.5t66 63.5t99.5 36.5t137.5 11.5t140 -12t100 -39.5t50 -60.5t11 -61q0 -27 -7 -58q-17 -79 -57.5 -123.5t-102.5 -66.5
q29 -10 50 -26t33 -40.5t13 -44t1 -24.5q0 -30 -8 -69q-12 -55 -38 -94.5t-69 -65t-103.5 -37.5t-141.5 -12zM424 477q35 0 59.5 5t41 15.5t26.5 28.5t15 43q4 17 4 31q0 5 -1.5 16t-13.5 22.5t-35.5 16.5t-61.5 5q-36 0 -61 -5t-41.5 -16.5t-26 -28.5t-15.5 -41
q-3 -15 -3 -28q0 -29 19.5 -46.5t93.5 -17.5zM352 141q74 0 109 22t46 75q4 17 4 31q0 32 -23 50.5t-99 18.5q-70 0 -106.5 -22t-48.5 -78q-4 -17 -4 -30q0 -30 22 -48.5t100 -18.5z" />
    <glyph glyph-name="nine.lt" 
d="M446 818q98 0 162 -25t97 -78t34.5 -99t1.5 -56q0 -77 -22 -181q-23 -108 -55.5 -183t-81.5 -122t-116.5 -68.5t-161.5 -21.5q-81 0 -144 9.5t-104 23.5l34 160q48 -12 100 -20.5t112 -8.5q46 0 79.5 7t57.5 25.5t41 50t29 80.5q-32 -11 -69 -18t-80 -7q-149 0 -202 60.5
t-53 142.5q0 39 10 87q14 66 39.5 112.5t66 75t96.5 41.5t129 13zM305 554q-5 -23 -5 -40q0 -34 22 -52t89 -18q37 0 69 6t63 17q11 56 11.5 75.5t0.5 21.5q0 35 -11.5 57.5t-37.5 32t-68 9.5q-60 0 -90.5 -24t-42.5 -85z" />
    <glyph glyph-name="dollar.ot" 
d="M82 226q64 -18 138.5 -30.5t156.5 -12.5q35 0 57 1.5t35.5 6t20 12.5t9.5 21q2 8 2 14q0 5 -2 11.5t-14.5 12.5t-38.5 11.5t-71 11.5q-74 10 -126.5 24t-83.5 37.5t-38.5 50t-7.5 47.5q0 24 7 55q9 45 28 76.5t50.5 52t77.5 31t109 13.5l19 90h152l-20 -93
q49 -5 92.5 -12.5t87.5 -17.5l-32 -151q-69 15 -137.5 24.5t-137.5 9.5q-33 0 -54.5 -1.5t-34.5 -5t-18.5 -9.5t-7.5 -16q-2 -7 -2 -12q0 -4 2 -9.5t15 -11t39.5 -10.5t71.5 -12q71 -10 123.5 -23t84.5 -36.5t40 -51.5t8 -51q0 -26 -7 -60q-10 -45 -28 -77.5t-49 -54
t-77.5 -33t-113.5 -14.5l-20 -94h-152l21 97q-60 5 -112.5 14t-94.5 23z" />
    <glyph glyph-name="dollar.ot.weight" 
d="M82 226q46 -13 97.5 -23.5t108.5 -15.5l23 108q-57 10 -97 24.5t-63 38t-27 47t-4 38.5q0 25 6 56q9 45 28 76.5t50.5 52t77.5 31t109 13.5l19 90h152l-20 -93q49 -5 92.5 -12.5t87.5 -17.5l-32 -151q-45 10 -90 17.5t-90 11.5l-20 -92q54 -10 94 -24t64 -37.5t29 -49
t5 -42.5q0 -26 -7 -59q-10 -45 -28 -77.5t-49 -54t-77.5 -33t-113.5 -14.5l-20 -94h-152l21 97q-60 5 -112.5 14t-94.5 23zM300 490q-1 -6 -1 -11q0 -9 5.5 -15.5t38.5 -14.5l16 72q-32 -2 -44 -9.5t-15 -21.5zM499 224q2 8 2 14q0 10 -6 17.5t-38 15.5l-18 -86q32 3 44 12
t16 27z" />
    <glyph glyph-name="Euro.ot" 
d="M83 427h87q21 78 51.5 133t77.5 90t112 51.5t155 16.5q72 0 125.5 -9.5t89.5 -17.5l-34 -155q-23 6 -45 10.5t-46.5 8t-52.5 5.5t-62 2q-41 0 -70.5 -6.5t-50.5 -22t-35 -41.5t-26 -65h313l-31 -147h-314q-5 -33 -5 -58q0 -6 1 -23t15.5 -32t42 -20.5t72.5 -5.5
q34 0 61.5 1.5t53 5t50 8.5t51.5 12l-33 -156q-38 -13 -93 -20.5t-121 -7.5q-82 0 -138.5 18.5t-90 56t-43.5 83t-10 87.5q0 24 3 51h-90z" />
    <glyph glyph-name="Euro.ot.weight" 
d="M61 320h87q3 24 9 51l3 15h-86l28 132h99q23 53 54.5 91t75.5 62t102 35.5t133 11.5q72 0 125.5 -9.5t89.5 -17.5l-34 -155q-23 6 -45 10.5t-46.5 8t-52.5 5.5t-62 2q-51 0 -83 -10t-54 -34h287l-28 -132h-315l-2 -8l-3 -15l-2 -8q-2 -9 -3.5 -18t-3.5 -17h315l-28 -131
h-295q9 -29 39 -38.5t88 -9.5q34 0 61.5 1.5t53 5t50 8.5t51.5 12l-33 -156q-38 -13 -93 -20.5t-121 -7.5q-137 0 -203.5 51t-76.5 154h-109z" />
    <glyph glyph-name="cent.ot" 
d="M604 702l-17 -79q41 -3 80.5 -8.5t79.5 -15.5l-34 -156q-39 6 -71.5 9.5t-63 6t-60 3t-61.5 0.5q-43 0 -72.5 -4t-50 -16t-32 -33t-18.5 -54q-5 -23 -5 -41q0 -7 2 -21t17 -27t43 -18.5t71 -5.5q33 0 64 0.5t62.5 3t67 6t77.5 9.5l-33 -156q-47 -11 -90.5 -17t-88.5 -8
l-17 -80h-151l17 81q-64 6 -113.5 25t-80 54t-37 72t-6.5 62q0 37 8 81q15 72 42.5 119t69.5 75.5t100 41t133 14.5l16 77h152z" />
    <glyph glyph-name="cent.ot.weight" 
d="M604 702l-17 -79q41 -3 80.5 -8.5t79.5 -15.5l-34 -156q-48 8 -86 11.5t-75 5.5l-46 -216q39 2 81 5.5t96 11.5l-33 -156q-47 -11 -90.5 -17t-88.5 -8l-17 -80h-151l17 81q-64 6 -113.5 25t-80 54t-37 72t-6.5 62q0 37 8 81q15 72 42.5 119t69.5 75.5t100 41t133 14.5
l16 77h152zM284 355q-5 -22 -5 -39q0 -24 12.5 -44t63.5 -27l46 215q-54 -5 -80 -28t-37 -77z" />
    <glyph glyph-name="sterling.ot" 
d="M92 152q39 0 59.5 19t30.5 66l9 43h-129l31 147h129l13 61q13 62 35.5 107t59.5 74t92 43t133 14q70 0 122 -9t86 -18l-35 -161q-38 12 -76 19t-93 7q-71 0 -102 -19.5t-40 -63.5l-11 -54h275l-32 -147h-275l-5 -25q-13 -63 -58 -103h355l-32 -152h-631l32 152h57z" />
    <glyph glyph-name="yen.ot" 
d="M94 320h198l14 66h-198l28 132h103l-113 184h192l107 -170l184 170h204l-193 -184h105l-29 -132h-199l-14 -66h199l-27 -131h-200l-40 -189h-191l40 189h-198z" />
    <glyph glyph-name="florin.ot" 
d="M110 427h192l13 60q12 58 34 102t58.5 74t90 45t128.5 15q22 0 39.5 -0.5t32.5 -2t29 -3.5t29 -4l-31 -151q-24 4 -47.5 6t-47.5 2q-31 0 -52 -4.5t-35.5 -15.5t-24 -29.5t-15.5 -47.5l-10 -46h202l-31 -147h-202l-56 -259q-12 -57 -33 -100.5t-54.5 -73.5t-81 -45.5
t-113.5 -15.5q-29 0 -50.5 0.5t-38 2t-31 3.5t-29.5 4l33 150q23 -4 45.5 -5.5t45.5 -1.5q30 0 50 4.5t33.5 15.5t22 29.5t14.5 47.5l52 244h-192z" />
    <glyph glyph-name="zeroslash.ot" 
d="M459 718q220 0 263 -159h3l-2 -2q10 -39 10 -87q0 -61 -16 -137q-20 -95 -50.5 -161.5t-76 -108t-110 -60.5t-151.5 -19q-215 0 -262 159h-3l2 2q-12 43 -12 96q0 2 0.5 30.5t14.5 97.5q20 95 52 161.5t78.5 108t110.5 60.5t149 19zM261 351q-2 -9 -3.5 -17.5t-3.5 -16.5
l251 231q-25 12 -70 12q-39 0 -66.5 -10t-47 -34t-34 -64.5t-26.5 -100.5zM352 142q39 0 66.5 10t47 34t34 64.5t26.5 100.5l2 8l3 16l2 8l-251 -229q26 -12 70 -12z" />
    <glyph glyph-name="zero.ot" 
d="M459 718q173 0 223.5 -76.5t50.5 -170.5q0 -62 -16 -138q-20 -95 -50.5 -161.5t-76 -108t-110 -60.5t-151.5 -19q-168 0 -221.5 77.5t-53.5 175.5q0 59 15 132q20 95 52 161.5t78.5 108t110.5 60.5t149 19zM352 142q39 0 66.5 10t47 34t34 64.5t26.5 100.5q13 61 15 85.5
t2 32.5q0 28 -9.5 49.5t-33.5 31.5t-65 10q-39 0 -66.5 -10t-47 -34t-34 -64.5t-26.5 -100.5q-13 -61 -15 -85.5t-2 -32.5q0 -28 9.5 -49.5t33.5 -31.5t65 -10z" />
    <glyph glyph-name="one.ot" 
d="M70 151h222l74 349l-202 -94l-52 157l301 139h187l-116 -551h193l-33 -151h-606z" />
    <glyph glyph-name="two.ot" 
d="M456 718q73 0 129.5 -11t92.5 -37.5t46.5 -59.5t10.5 -61q0 -27 -7 -61q-9 -43 -22.5 -74t-36 -52.5t-56.5 -37t-82 -29.5l-235 -70q-14 -5 -23 -9.5t-14.5 -10.5t-8.5 -14.5t-6 -20.5l-4 -19h416l-32 -151h-609l34 159q9 40 22 72.5t34.5 59t53.5 46t78 32.5l241 71
q32 11 43 22.5t16 33.5q2 9 2 16q0 8 -3 16.5t-16.5 15t-38 9t-60.5 2.5q-72 0 -144.5 -8t-128.5 -18l34 162q57 11 122.5 19t151.5 8z" />
    <glyph glyph-name="three.ot" 
d="M33 71q60 -14 116 -21.5t125 -7.5q57 0 93 4t57.5 14t31.5 27t16 42q5 20 5 36q0 29 -20 46.5t-87 17.5h-217l32 157h216q59 0 89 15t40 61q4 18 4 32q0 8 -2.5 21t-18.5 24.5t-46 16.5t-77 5q-39 0 -71 -1.5t-61 -4.5t-57.5 -8t-59.5 -11l33 155q46 11 111.5 19t144.5 8
q92 0 155.5 -15t100 -46t43.5 -64.5t7 -55.5q0 -32 -8 -70q-17 -77 -58 -114.5t-104 -53.5q61 -26 81 -67.5t20 -81.5q0 -29 -8 -63q-12 -56 -37 -95t-70 -63t-113.5 -34.5t-167.5 -10.5q-92 0 -159 10t-112 21z" />
    <glyph glyph-name="four.ot" 
d="M704 356l-97 -456h-188l33 153h-449l31 147l372 502h216l-367 -494h230l31 148h188z" />
    <glyph glyph-name="five.ot" 
d="M34 77q121 -29 260 -29q47 0 79 4t52.5 14t31.5 28t17 46q4 17 4 32q0 34 -24.5 53t-101.5 19h-258l97 458h549l-33 -158h-358l-30 -142h73q172 0 227.5 -57t55.5 -140q0 -38 -10 -85q-15 -76 -46 -122.5t-79.5 -72t-115.5 -33.5t-155 -8q-42 0 -81.5 3t-74.5 7.5t-64 10
t-49 10.5z" />
    <glyph glyph-name="six.ot" 
d="M356 -16q-98 0 -162 25t-97 78.5t-34.5 99.5t-1.5 55q0 77 22 181q23 108 55.5 183t81 122t116.5 68.5t162 21.5q81 0 143.5 -9.5t104.5 -23.5l-34 -160q-48 12 -100.5 20.5t-112.5 8.5q-46 0 -79 -7t-57.5 -25.5t-41 -50t-29.5 -80.5q32 11 69 18t80 7q150 0 203 -60.5
t53 -142.5q0 -39 -10 -87q-14 -66 -40 -112.5t-66.5 -75t-96 -41.5t-128.5 -13zM391 358q-37 0 -69 -6t-63 -17q-10 -56 -10.5 -76t-0.5 -23q0 -34 11 -56t37 -31.5t68 -9.5q60 0 90 24t43 85q5 22 5 39q0 8 -2.5 22.5t-15.5 26.5t-36.5 17t-56.5 5z" />
    <glyph glyph-name="seven.ot" 
d="M149 702h640l-31 -147l-493 -655h-239l509 644h-420z" />
    <glyph glyph-name="eight.ot" 
d="M341 -16q-82 0 -145.5 12.5t-103.5 41.5t-51 64.5t-11 64.5t8 65q16 75 59 119t110 63q-51 22 -68 57.5t-17 71.5q0 32 9 72q11 53 36 91.5t66 63.5t99.5 36.5t137.5 11.5t140 -12t100 -39.5t50 -60.5t11 -61q0 -27 -7 -58q-17 -79 -57.5 -123.5t-102.5 -66.5
q29 -10 50 -26t33 -40.5t13 -44t1 -24.5q0 -30 -8 -69q-12 -55 -38 -94.5t-69 -65t-103.5 -37.5t-141.5 -12zM424 477q35 0 59.5 5t41 15.5t26.5 28.5t15 43q4 17 4 31q0 5 -1.5 16t-13.5 22.5t-35.5 16.5t-61.5 5q-36 0 -61 -5t-41.5 -16.5t-26 -28.5t-15.5 -41
q-3 -15 -3 -28q0 -29 19.5 -46.5t93.5 -17.5zM352 141q74 0 109 22t46 75q4 17 4 31q0 32 -23 50.5t-99 18.5q-70 0 -106.5 -22t-48.5 -78q-4 -17 -4 -30q0 -30 22 -48.5t100 -18.5z" />
    <glyph glyph-name="nine.ot" 
d="M425 718q98 0 162 -25t97 -78t34.5 -99t1.5 -56q0 -77 -22 -181q-23 -108 -55.5 -183t-81.5 -122t-116.5 -68.5t-161.5 -21.5q-81 0 -144 9.5t-104 23.5l34 160q48 -12 100 -20.5t112 -8.5q46 0 79.5 7t57.5 25.5t41 50t29 80.5q-32 -11 -69 -18t-80 -7q-149 0 -202 60.5
t-53 142.5q0 39 10 87q14 66 39.5 112.5t66 75t96.5 41.5t129 13zM284 454q-5 -23 -5 -40q0 -34 22 -52t89 -18q37 0 69 6t63 17q11 56 11.5 75.5t0.5 21.5q0 35 -11.5 57.5t-37.5 32t-68 9.5q-60 0 -90.5 -24t-42.5 -85z" />
    <glyph glyph-name="dollar.op" horiz-adv-x="716" 
d="M60 226q64 -18 138.5 -30.5t156.5 -12.5q35 0 57 1.5t35.5 6t20 12.5t9.5 21q2 8 2 14q0 5 -2 11.5t-14.5 12.5t-38.5 11.5t-71 11.5q-74 10 -126.5 24t-83.5 37.5t-38.5 50t-7.5 47.5q0 24 7 55q9 45 28 76.5t50.5 52t77.5 31t109 13.5l19 90h152l-20 -93
q49 -5 92.5 -12.5t87.5 -17.5l-32 -151q-69 15 -137.5 24.5t-137.5 9.5q-33 0 -54.5 -1.5t-34.5 -5t-18.5 -9.5t-7.5 -16q-2 -7 -2 -12q0 -4 2 -9.5t15 -11t39.5 -10.5t71.5 -12q71 -10 123.5 -23t84.5 -36.5t40 -51.5t8 -51q0 -26 -7 -60q-10 -45 -28 -77.5t-49 -54
t-77.5 -33t-113.5 -14.5l-20 -94h-152l21 97q-60 5 -112.5 14t-94.5 23z" />
    <glyph glyph-name="dollar.op.weight" horiz-adv-x="716" 
d="M60 226q46 -13 97.5 -23.5t108.5 -15.5l23 108q-57 10 -97 24.5t-63 38t-27 47t-4 38.5q0 25 6 56q9 45 28 76.5t50.5 52t77.5 31t109 13.5l19 90h152l-20 -93q49 -5 92.5 -12.5t87.5 -17.5l-32 -151q-45 10 -90 17.5t-90 11.5l-20 -92q54 -10 94 -24t64 -37.5t29 -49
t5 -42.5q0 -26 -7 -59q-10 -45 -28 -77.5t-49 -54t-77.5 -33t-113.5 -14.5l-20 -94h-152l21 97q-60 5 -112.5 14t-94.5 23zM278 490q-1 -6 -1 -11q0 -9 5.5 -15.5t38.5 -14.5l16 72q-32 -2 -44 -9.5t-15 -21.5zM477 224q2 8 2 14q0 10 -6 17.5t-38 15.5l-18 -86q32 3 44 12
t16 27z" />
    <glyph glyph-name="Euro.op" horiz-adv-x="716" 
d="M61 427h87q21 78 51.5 133t77.5 90t112 51.5t155 16.5q72 0 125.5 -9.5t89.5 -17.5l-34 -155q-23 6 -45 10.5t-46.5 8t-52.5 5.5t-62 2q-41 0 -70.5 -6.5t-50.5 -22t-35 -41.5t-26 -65h313l-31 -147h-314q-5 -33 -5 -58q0 -6 1 -23t15.5 -32t42 -20.5t72.5 -5.5
q34 0 61.5 1.5t53 5t50 8.5t51.5 12l-33 -156q-38 -13 -93 -20.5t-121 -7.5q-82 0 -138.5 18.5t-90 56t-43.5 83t-10 87.5q0 24 3 51h-90z" />
    <glyph glyph-name="Euro.op.weight" horiz-adv-x="716" 
d="M39 320h87q3 24 9 51l3 15h-86l28 132h99q23 53 54.5 91t75.5 62t102 35.5t133 11.5q72 0 125.5 -9.5t89.5 -17.5l-34 -155q-23 6 -45 10.5t-46.5 8t-52.5 5.5t-62 2q-51 0 -83 -10t-54 -34h287l-28 -132h-315l-2 -8l-3 -15l-2 -8q-2 -9 -3.5 -18t-3.5 -17h315l-28 -131
h-295q9 -29 39 -38.5t88 -9.5q34 0 61.5 1.5t53 5t50 8.5t51.5 12l-33 -156q-38 -13 -93 -20.5t-121 -7.5q-137 0 -203.5 51t-76.5 154h-109z" />
    <glyph glyph-name="cent.op" horiz-adv-x="716" 
d="M582 702l-17 -79q41 -3 80.5 -8.5t79.5 -15.5l-34 -156q-39 6 -71.5 9.5t-63 6t-60 3t-61.5 0.5q-43 0 -72.5 -4t-50 -16t-32 -33t-18.5 -54q-5 -23 -5 -41q0 -7 2 -21t17 -27t43 -18.5t71 -5.5q33 0 64 0.5t62.5 3t67 6t77.5 9.5l-33 -156q-47 -11 -90.5 -17t-88.5 -8
l-17 -80h-151l17 81q-64 6 -113.5 25t-80 54t-37 72t-6.5 62q0 37 8 81q15 72 42.5 119t69.5 75.5t100 41t133 14.5l16 77h152z" />
    <glyph glyph-name="cent.op.weight" horiz-adv-x="716" 
d="M582 702l-17 -79q41 -3 80.5 -8.5t79.5 -15.5l-34 -156q-48 8 -86 11.5t-75 5.5l-46 -216q39 2 81 5.5t96 11.5l-33 -156q-47 -11 -90.5 -17t-88.5 -8l-17 -80h-151l17 81q-64 6 -113.5 25t-80 54t-37 72t-6.5 62q0 37 8 81q15 72 42.5 119t69.5 75.5t100 41t133 14.5
l16 77h152zM262 355q-5 -22 -5 -39q0 -24 12.5 -44t63.5 -27l46 215q-54 -5 -80 -28t-37 -77z" />
    <glyph glyph-name="sterling.op" horiz-adv-x="716" 
d="M70 152q39 0 59.5 19t30.5 66l9 43h-129l31 147h129l13 61q13 62 35.5 107t59.5 74t92 43t133 14q70 0 122 -9t86 -18l-35 -161q-38 12 -76 19t-93 7q-71 0 -102 -19.5t-40 -63.5l-11 -54h275l-32 -147h-275l-5 -25q-13 -63 -58 -103h355l-32 -152h-631l32 152h57z" />
    <glyph glyph-name="yen.op" horiz-adv-x="716" 
d="M72 320h198l14 66h-198l28 132h103l-113 184h192l107 -170l184 170h204l-193 -184h105l-29 -132h-199l-14 -66h199l-27 -131h-200l-40 -189h-191l40 189h-198z" />
    <glyph glyph-name="florin.op" horiz-adv-x="716" 
d="M88 427h192l13 60q12 58 34 102t58.5 74t90 45t128.5 15q22 0 39.5 -0.5t32.5 -2t29 -3.5t29 -4l-31 -151q-24 4 -47.5 6t-47.5 2q-31 0 -52 -4.5t-35.5 -15.5t-24 -29.5t-15.5 -47.5l-10 -46h202l-31 -147h-202l-56 -259q-12 -57 -33 -100.5t-54.5 -73.5t-81 -45.5
t-113.5 -15.5q-29 0 -50.5 0.5t-38 2t-31 3.5t-29.5 4l33 150q23 -4 45.5 -5.5t45.5 -1.5q30 0 50 4.5t33.5 15.5t22 29.5t14.5 47.5l52 244h-192z" />
    <glyph glyph-name="zeroslash.op" horiz-adv-x="716" 
d="M437 718q220 0 263 -159h3l-2 -2q10 -39 10 -87q0 -61 -16 -137q-20 -95 -50.5 -161.5t-76 -108t-110 -60.5t-151.5 -19q-215 0 -262 159h-3l2 2q-12 43 -12 96q0 2 0.5 30.5t14.5 97.5q20 95 52 161.5t78.5 108t110.5 60.5t149 19zM239 351q-2 -9 -3.5 -17.5t-3.5 -16.5
l251 231q-25 12 -70 12q-39 0 -66.5 -10t-47 -34t-34 -64.5t-26.5 -100.5zM330 142q39 0 66.5 10t47 34t34 64.5t26.5 100.5l2 8l3 16l2 8l-251 -229q26 -12 70 -12z" />
    <glyph glyph-name="zero.op" horiz-adv-x="716" 
d="M437 718q173 0 223.5 -76.5t50.5 -170.5q0 -62 -16 -138q-20 -95 -50.5 -161.5t-76 -108t-110 -60.5t-151.5 -19q-168 0 -221.5 77.5t-53.5 175.5q0 59 15 132q20 95 52 161.5t78.5 108t110.5 60.5t149 19zM330 142q39 0 66.5 10t47 34t34 64.5t26.5 100.5q13 61 15 85.5
t2 32.5q0 28 -9.5 49.5t-33.5 31.5t-65 10q-39 0 -66.5 -10t-47 -34t-34 -64.5t-26.5 -100.5q-13 -61 -15 -85.5t-2 -32.5q0 -28 9.5 -49.5t33.5 -31.5t65 -10z" />
    <glyph glyph-name="one.op" horiz-adv-x="588" 
d="M401 702h188l-149 -702h-191l106 500l-228 -105l-54 155z" />
    <glyph glyph-name="two.op" horiz-adv-x="716" 
d="M434 718q73 0 129.5 -11t92.5 -37.5t46.5 -59.5t10.5 -61q0 -27 -7 -61q-9 -43 -22.5 -74t-36 -52.5t-56.5 -37t-82 -29.5l-235 -70q-14 -5 -23 -9.5t-14.5 -10.5t-8.5 -14.5t-6 -20.5l-4 -19h416l-32 -151h-609l34 159q9 40 22 72.5t34.5 59t53.5 46t78 32.5l241 71
q32 11 43 22.5t16 33.5q2 9 2 16q0 8 -3 16.5t-16.5 15t-38 9t-60.5 2.5q-72 0 -144.5 -8t-128.5 -18l34 162q57 11 122.5 19t151.5 8z" />
    <glyph glyph-name="three.op" horiz-adv-x="716" 
d="M11 71q60 -14 116 -21.5t125 -7.5q57 0 93 4t57.5 14t31.5 27t16 42q5 20 5 36q0 29 -20 46.5t-87 17.5h-217l32 157h216q59 0 89 15t40 61q4 18 4 32q0 8 -2.5 21t-18.5 24.5t-46 16.5t-77 5q-39 0 -71 -1.5t-61 -4.5t-57.5 -8t-59.5 -11l33 155q46 11 111.5 19t144.5 8
q92 0 155.5 -15t100 -46t43.5 -64.5t7 -55.5q0 -32 -8 -70q-17 -77 -58 -114.5t-104 -53.5q61 -26 81 -67.5t20 -81.5q0 -29 -8 -63q-12 -56 -37 -95t-70 -63t-113.5 -34.5t-167.5 -10.5q-92 0 -159 10t-112 21z" />
    <glyph glyph-name="four.op" horiz-adv-x="716" 
d="M682 356l-97 -456h-188l33 153h-449l31 147l372 502h216l-367 -494h230l31 148h188z" />
    <glyph glyph-name="five.op" horiz-adv-x="716" 
d="M12 77q121 -29 260 -29q47 0 79 4t52.5 14t31.5 28t17 46q4 17 4 32q0 34 -24.5 53t-101.5 19h-258l97 458h549l-33 -158h-358l-30 -142h73q172 0 227.5 -57t55.5 -140q0 -38 -10 -85q-15 -76 -46 -122.5t-79.5 -72t-115.5 -33.5t-155 -8q-42 0 -81.5 3t-74.5 7.5t-64 10
t-49 10.5z" />
    <glyph glyph-name="six.op" horiz-adv-x="716" 
d="M334 -16q-98 0 -162 25t-97 78.5t-34.5 99.5t-1.5 55q0 77 22 181q23 108 55.5 183t81 122t116.5 68.5t162 21.5q81 0 143.5 -9.5t104.5 -23.5l-34 -160q-48 12 -100.5 20.5t-112.5 8.5q-46 0 -79 -7t-57.5 -25.5t-41 -50t-29.5 -80.5q32 11 69 18t80 7q150 0 203 -60.5
t53 -142.5q0 -39 -10 -87q-14 -66 -40 -112.5t-66.5 -75t-96 -41.5t-128.5 -13zM369 358q-37 0 -69 -6t-63 -17q-10 -56 -10.5 -76t-0.5 -23q0 -34 11 -56t37 -31.5t68 -9.5q60 0 90 24t43 85q5 22 5 39q0 8 -2.5 22.5t-15.5 26.5t-36.5 17t-56.5 5z" />
    <glyph glyph-name="seven.op" horiz-adv-x="716" 
d="M127 702h640l-31 -147l-493 -655h-239l509 644h-420z" />
    <glyph glyph-name="eight.op" horiz-adv-x="716" 
d="M319 -16q-82 0 -145.5 12.5t-103.5 41.5t-51 64.5t-11 64.5t8 65q16 75 59 119t110 63q-51 22 -68 57.5t-17 71.5q0 32 9 72q11 53 36 91.5t66 63.5t99.5 36.5t137.5 11.5t140 -12t100 -39.5t50 -60.5t11 -61q0 -27 -7 -58q-17 -79 -57.5 -123.5t-102.5 -66.5
q29 -10 50 -26t33 -40.5t13 -44t1 -24.5q0 -30 -8 -69q-12 -55 -38 -94.5t-69 -65t-103.5 -37.5t-141.5 -12zM402 477q35 0 59.5 5t41 15.5t26.5 28.5t15 43q4 17 4 31q0 5 -1.5 16t-13.5 22.5t-35.5 16.5t-61.5 5q-36 0 -61 -5t-41.5 -16.5t-26 -28.5t-15.5 -41
q-3 -15 -3 -28q0 -29 19.5 -46.5t93.5 -17.5zM330 141q74 0 109 22t46 75q4 17 4 31q0 32 -23 50.5t-99 18.5q-70 0 -106.5 -22t-48.5 -78q-4 -17 -4 -30q0 -30 22 -48.5t100 -18.5z" />
    <glyph glyph-name="nine.op" horiz-adv-x="716" 
d="M403 718q98 0 162 -25t97 -78t34.5 -99t1.5 -56q0 -77 -22 -181q-23 -108 -55.5 -183t-81.5 -122t-116.5 -68.5t-161.5 -21.5q-81 0 -144 9.5t-104 23.5l34 160q48 -12 100 -20.5t112 -8.5q46 0 79.5 7t57.5 25.5t41 50t29 80.5q-32 -11 -69 -18t-80 -7q-149 0 -202 60.5
t-53 142.5q0 39 10 87q14 66 39.5 112.5t66 75t96.5 41.5t129 13zM262 454q-5 -23 -5 -40q0 -34 22 -52t89 -18q37 0 69 6t63 17q11 56 11.5 75.5t0.5 21.5q0 35 -11.5 57.5t-37.5 32t-68 9.5q-60 0 -90.5 -24t-42.5 -85z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="417" 
d="M309 811q108 0 137 -48t29 -106q0 -46 -13 -106q-11 -52 -27.5 -93t-43 -68.5t-65.5 -42t-95 -14.5q-109 0 -137 48t-28 105q0 46 13 107q11 53 27.5 93.5t42.5 68t65 42t95 14.5zM245 444q17 0 29 5.5t20.5 20t16 39.5t15.5 63q8 39 10.5 58t2.5 29q0 9 -2 19.5t-12 16
t-29 5.5q-17 0 -29 -5.5t-21 -20t-16 -39.5t-15 -63q-8 -39 -10.5 -58t-2.5 -29q0 -9 2 -19.5t12 -16t29 -5.5z" />
    <glyph glyph-name="one.numr" horiz-adv-x="356" 
d="M287 802h137l-98 -460h-139l66 310l-116 -54l-38 115z" />
    <glyph glyph-name="two.numr" horiz-adv-x="417" 
d="M307 811q98 0 137.5 -29.5t39.5 -76.5q0 -18 -5 -40q-6 -24 -14 -41t-22.5 -31t-37 -27.5t-56.5 -30.5l-123 -61q-11 -5 -15 -10.5t-5 -12.5h222l-23 -109h-361l25 123q8 36 29.5 59t66.5 44l129 62q29 14 37 20t10 15q1 5 1 9q0 12 -13 17t-51 5q-33 0 -74.5 -4.5
t-73.5 -10.5l24 114q33 8 68 12t85 4z" />
    <glyph glyph-name="three.numr" horiz-adv-x="417" 
d="M67 464q36 -8 67.5 -13t71.5 -5q29 0 47 1.5t28.5 5t15 10.5t7.5 17q2 8 2 14q0 14 -9 21t-36 7h-133l22 108h134q24 0 35.5 6t15.5 26q1 6 1 11q0 13 -12 19.5t-59 6.5q-24 0 -42.5 -1t-35 -3t-33 -4.5t-36.5 -6.5l23 111q26 6 66 11t88 5q114 0 151.5 -29.5t37.5 -75.5
q0 -18 -5 -39q-10 -45 -33 -66t-60 -31q56 -27 56 -87q0 -15 -4 -33q-7 -33 -22 -55.5t-42.5 -36t-68.5 -19.5t-101 -6q-55 0 -95 6t-65 13z" />
    <glyph glyph-name="four.numr" horiz-adv-x="417" 
d="M468 604l-55 -262h-136l17 79h-248l22 106l204 275h155l-205 -274h94l16 76h136z" />
    <glyph glyph-name="five.numr" horiz-adv-x="417" 
d="M68 466q36 -9 74 -14t77 -5q23 0 39 1t25.5 4.5t15 11.5t7.5 22q1 7 1 13q0 14 -10 22t-45 8h-159l57 273h334l-23 -110h-195l-11 -54h26q105 0 137 -34t32 -84q0 -22 -5 -48q-10 -48 -29.5 -75.5t-50 -42t-71.5 -18t-93 -3.5q-48 0 -91.5 6.5t-65.5 12.5z" />
    <glyph glyph-name="six.numr" horiz-adv-x="417" 
d="M247 333q-117 0 -148.5 49t-31.5 110q0 45 12 103q12 57 30.5 98t47.5 67.5t70.5 38.5t98.5 12q49 0 87 -6t61 -14l-23 -113q-29 8 -60 13.5t-66 5.5q-22 0 -37.5 -2t-27 -8t-18.5 -18t-13 -32q15 5 32 7.5t35 2.5q92 0 122 -37t30 -85q0 -24 -6 -52q-16 -76 -62 -108
t-133 -32zM259 537q-28 0 -55 -9q-5 -26 -5 -44q0 -16 6.5 -29.5t43.5 -13.5q26 0 38 11.5t17 34.5q2 11 2 20q0 13 -7.5 21.5t-39.5 8.5z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="417" 
d="M129 802h382l-23 -106l-265 -354h-172l280 350h-226z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="417" 
d="M238 333q-48 0 -86.5 7t-63.5 24t-32.5 38.5t-7.5 40.5q0 17 4 37q8 40 32.5 64.5t61.5 35.5q-29 14 -38 34t-9 40q0 19 5 43q13 61 61 87.5t139 26.5q47 0 84.5 -6.5t62 -22.5t32 -35.5t7.5 -36.5q0 -15 -4 -33q-9 -45 -32.5 -70t-58.5 -39q33 -12 43.5 -33t10.5 -43
q0 -18 -5 -41q-14 -63 -63.5 -90.5t-142.5 -27.5zM284 622q30 0 42.5 10t16.5 31q2 8 2 14q0 11 -8 17.5t-41 6.5q-30 0 -42.5 -8.5t-17.5 -29.5q-1 -6 -1 -11q0 -13 8 -21.5t41 -8.5zM245 441q29 0 44.5 9t21.5 34q1 7 1 13q0 15 -10 24.5t-43 9.5q-30 0 -45 -11t-20 -36
q-2 -8 -2 -15q0 -13 10.5 -20.5t42.5 -7.5z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="417" 
d="M288 811q118 0 149.5 -49t31.5 -110q0 -45 -12 -103q-12 -57 -31 -98t-47.5 -67.5t-70 -38.5t-98.5 -12q-49 0 -87 6t-61 14l23 113q29 -8 60 -13.5t66 -5.5q22 0 37.5 2t27 8t18.5 18t13 32q-15 -5 -32 -7.5t-36 -2.5q-92 0 -121.5 37t-29.5 84q0 24 6 53
q16 77 61.5 108.5t132.5 31.5zM232 657q-2 -11 -2 -20q0 -13 7.5 -21.5t39.5 -8.5q26 0 53 8q6 28 6 46q0 15 -6 28.5t-44 13.5q-26 0 -37.5 -11.5t-16.5 -34.5z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="417" 
d="M237 469q108 0 137 -48t29 -106q0 -46 -13 -106q-11 -52 -27.5 -93t-43 -68.5t-65.5 -42t-95 -14.5q-109 0 -137 48t-28 105q0 46 13 107q11 53 27.5 93.5t42.5 68t65 42t95 14.5zM173 102q17 0 29 5.5t20.5 20t16 39.5t15.5 63q8 39 10.5 58t2.5 29q0 9 -2 19.5t-12 16
t-29 5.5q-17 0 -29 -5.5t-21 -20t-16 -39.5t-15 -63q-8 -39 -10.5 -58t-2.5 -29q0 -9 2 -19.5t12 -16t29 -5.5z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="356" 
d="M215 460h137l-98 -460h-139l66 310l-116 -54l-38 115z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="417" 
d="M235 469q98 0 137.5 -29.5t39.5 -76.5q0 -18 -5 -40q-6 -24 -14 -41t-22.5 -31t-37 -27.5t-56.5 -30.5l-123 -61q-11 -5 -15 -10.5t-5 -12.5h222l-23 -109h-361l25 123q8 36 29.5 59t66.5 44l129 62q29 14 37 20t10 15q1 5 1 9q0 12 -13 17t-51 5q-33 0 -74.5 -4.5
t-73.5 -10.5l24 114q33 8 68 12t85 4z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="417" 
d="M-5 122q36 -8 67.5 -13t71.5 -5q29 0 47 1.5t28.5 5t15 10.5t7.5 17q2 8 2 14q0 14 -9 21t-36 7h-133l22 108h134q24 0 35.5 6t15.5 26q1 6 1 11q0 13 -12 19.5t-59 6.5q-24 0 -42.5 -1t-35 -3t-33 -4.5t-36.5 -6.5l23 111q26 6 66 11t88 5q114 0 151.5 -29.5t37.5 -75.5
q0 -18 -5 -39q-10 -45 -33 -66t-60 -31q56 -27 56 -87q0 -15 -4 -33q-7 -33 -22 -55.5t-42.5 -36t-68.5 -19.5t-101 -6q-55 0 -95 6t-65 13z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="417" 
d="M396 262l-55 -262h-136l17 79h-248l22 106l204 275h155l-205 -274h94l16 76h136z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="417" 
d="M-4 124q36 -9 74 -14t77 -5q23 0 39 1t25.5 4.5t15 11.5t7.5 22q1 7 1 13q0 14 -10 22t-45 8h-159l57 273h334l-23 -110h-195l-11 -54h26q105 0 137 -34t32 -84q0 -22 -5 -48q-10 -48 -29.5 -75.5t-50 -42t-71.5 -18t-93 -3.5q-48 0 -91.5 6.5t-65.5 12.5z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="417" 
d="M175 -9q-117 0 -148.5 49t-31.5 110q0 45 12 103q12 57 30.5 98t47.5 67.5t70.5 38.5t98.5 12q49 0 87 -6t61 -14l-23 -113q-29 8 -60 13.5t-66 5.5q-22 0 -37.5 -2t-27 -8t-18.5 -18t-13 -32q15 5 32 7.5t35 2.5q92 0 122 -37t30 -85q0 -24 -6 -52q-16 -76 -62 -108
t-133 -32zM187 195q-28 0 -55 -9q-5 -26 -5 -44q0 -16 6.5 -29.5t43.5 -13.5q26 0 38 11.5t17 34.5q2 11 2 20q0 13 -7.5 21.5t-39.5 8.5z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="417" 
d="M57 460h382l-23 -106l-265 -354h-172l280 350h-226z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="417" 
d="M166 -9q-48 0 -86.5 7t-63.5 24t-32.5 38.5t-7.5 40.5q0 17 4 37q8 40 32.5 64.5t61.5 35.5q-29 14 -38 34t-9 40q0 19 5 43q13 61 61 87.5t139 26.5q47 0 84.5 -6.5t62 -22.5t32 -35.5t7.5 -36.5q0 -15 -4 -33q-9 -45 -32.5 -70t-58.5 -39q33 -12 43.5 -33t10.5 -43
q0 -18 -5 -41q-14 -63 -63.5 -90.5t-142.5 -27.5zM212 280q30 0 42.5 10t16.5 31q2 8 2 14q0 11 -8 17.5t-41 6.5q-30 0 -42.5 -8.5t-17.5 -29.5q-1 -6 -1 -11q0 -13 8 -21.5t41 -8.5zM173 99q29 0 44.5 9t21.5 34q1 7 1 13q0 15 -10 24.5t-43 9.5q-30 0 -45 -11t-20 -36
q-2 -8 -2 -15q0 -13 10.5 -20.5t42.5 -7.5z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="417" 
d="M216 469q118 0 149.5 -49t31.5 -110q0 -45 -12 -103q-12 -57 -31 -98t-47.5 -67.5t-70 -38.5t-98.5 -12q-49 0 -87 6t-61 14l23 113q29 -8 60 -13.5t66 -5.5q22 0 37.5 2t27 8t18.5 18t13 32q-15 -5 -32 -7.5t-36 -2.5q-92 0 -121.5 37t-29.5 84q0 24 6 53
q16 77 61.5 108.5t132.5 31.5zM160 315q-2 -11 -2 -20q0 -13 7.5 -21.5t39.5 -8.5q26 0 53 8q6 28 6 46q0 15 -6 28.5t-44 13.5q-26 0 -37.5 -11.5t-16.5 -34.5z" />
    <hkern u1="&#x23;" g2="nine.op" k="10" />
    <hkern u1="&#x23;" g2="four.op" k="72" />
    <hkern u1="&#x23;" u2="&#x37;" k="-24" />
    <hkern u1="&#x23;" u2="&#x34;" k="54" />
    <hkern u1="&#x28;" u2="&#x237;" k="-50" />
    <hkern u1="&#x28;" u2="j" k="-50" />
    <hkern u1="&#x2a;" u2="X" k="46" />
    <hkern u1="&#x2a;" u2="&#x2f;" k="110" />
    <hkern u1="&#x2f;" u2="&#xdf;" k="20" />
    <hkern u1="&#x2f;" u2="x" k="20" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="120" />
    <hkern u1="&#x31;" u2="&#x20ac;" k="10" />
    <hkern u1="&#x31;" u2="&#x192;" k="20" />
    <hkern u1="&#x31;" u2="&#xb0;" k="16" />
    <hkern u1="&#x31;" u2="&#x33;" k="20" />
    <hkern u1="&#x31;" u2="&#x31;" k="60" />
    <hkern u1="&#x32;" u2="&#x192;" k="60" />
    <hkern u1="&#x32;" u2="&#xa5;" k="18" />
    <hkern u1="&#x32;" u2="&#x3c;" k="12" />
    <hkern u1="&#x32;" u2="&#x37;" k="10" />
    <hkern u1="&#x32;" u2="&#x34;" k="12" />
    <hkern u1="&#x32;" u2="&#x32;" k="10" />
    <hkern u1="&#x32;" u2="&#x31;" k="10" />
    <hkern u1="&#x33;" u2="&#x3e;" k="24" />
    <hkern u1="&#x33;" u2="&#x39;" k="8" />
    <hkern u1="&#x33;" u2="&#x37;" k="10" />
    <hkern u1="&#x33;" u2="&#x34;" k="-8" />
    <hkern u1="&#x33;" u2="&#x33;" k="20" />
    <hkern u1="&#x33;" u2="&#x32;" k="8" />
    <hkern u1="&#x33;" u2="&#x31;" k="20" />
    <hkern u1="&#x34;" u2="&#x39;" k="4" />
    <hkern u1="&#x34;" u2="&#x31;" k="110" />
    <hkern u1="&#x35;" u2="&#x192;" k="2" />
    <hkern u1="&#x35;" u2="&#xb0;" k="28" />
    <hkern u1="&#x35;" u2="&#x39;" k="12" />
    <hkern u1="&#x35;" u2="&#x37;" k="30" />
    <hkern u1="&#x35;" u2="&#x35;" k="10" />
    <hkern u1="&#x35;" u2="&#x33;" k="30" />
    <hkern u1="&#x35;" u2="&#x32;" k="18" />
    <hkern u1="&#x35;" u2="&#x31;" k="70" />
    <hkern u1="&#x37;" u2="&#x192;" k="70" />
    <hkern u1="&#x37;" u2="&#xb0;" k="-20" />
    <hkern u1="&#x37;" u2="&#xa3;" k="50" />
    <hkern u1="&#x37;" u2="&#x3e;" k="90" />
    <hkern u1="&#x37;" u2="&#x3c;" k="140" />
    <hkern u1="&#x37;" u2="&#x35;" k="20" />
    <hkern u1="&#x37;" u2="&#x34;" k="88" />
    <hkern u1="&#x37;" u2="&#x33;" k="10" />
    <hkern u1="&#x37;" u2="&#x32;" k="40" />
    <hkern u1="&#x37;" u2="&#x23;" k="90" />
    <hkern u1="&#x3c;" g2="seven.op" k="12" />
    <hkern u1="&#x3c;" g2="three.op" k="4" />
    <hkern u1="&#x3c;" u2="&#x37;" k="40" />
    <hkern u1="&#x3c;" u2="&#x31;" k="90" />
    <hkern u1="&#x3e;" g2="nine.op" k="2" />
    <hkern u1="&#x3e;" g2="seven.op" k="110" />
    <hkern u1="&#x3e;" g2="five.op" k="2" />
    <hkern u1="&#x3e;" g2="three.op" k="6" />
    <hkern u1="&#x3e;" g2="one.op" k="12" />
    <hkern u1="&#x3e;" u2="&#x37;" k="100" />
    <hkern u1="&#x3e;" u2="&#x33;" k="4" />
    <hkern u1="&#x3e;" u2="&#x31;" k="25" />
    <hkern u1="&#x3f;" u2="X" k="30" />
    <hkern u1="&#x40;" u2="v" k="20" />
    <hkern u1="&#x40;" u2="X" k="20" />
    <hkern u1="&#x40;" u2="V" k="70" />
    <hkern u1="B" g2="bullet.case" k="8" />
    <hkern u1="B" u2="&#x2122;" k="40" />
    <hkern u1="B" u2="&#xae;" k="30" />
    <hkern u1="B" u2="v" k="20" />
    <hkern u1="B" u2="X" k="20" />
    <hkern u1="B" u2="V" k="34" />
    <hkern u1="F" g2="at.case" k="4" />
    <hkern u1="F" u2="&#xbf;" k="192" />
    <hkern u1="F" u2="&#xb0;" k="-16" />
    <hkern u1="F" u2="x" k="20" />
    <hkern u1="F" u2="v" k="10" />
    <hkern u1="F" u2="X" k="4" />
    <hkern u1="F" u2="&#x40;" k="4" />
    <hkern u1="F" u2="&#x3f;" k="-32" />
    <hkern u1="F" u2="&#x2f;" k="116" />
    <hkern u1="P" g2="at.case" k="-16" />
    <hkern u1="P" g2="bullet.case" k="-16" />
    <hkern u1="P" u2="v" k="-16" />
    <hkern u1="P" u2="X" k="58" />
    <hkern u1="P" u2="V" k="14" />
    <hkern u1="P" u2="&#x2f;" k="76" />
    <hkern u1="P" u2="&#x2a;" k="-32" />
    <hkern u1="V" g2="at.case" k="18" />
    <hkern u1="V" g2="bullet.case" k="50" />
    <hkern u1="V" g2="questiondown.case" k="92" />
    <hkern u1="V" u2="&#x2022;" k="70" />
    <hkern u1="V" u2="&#xdf;" k="30" />
    <hkern u1="V" u2="&#xbf;" k="136" />
    <hkern u1="V" u2="&#xb0;" k="-16" />
    <hkern u1="V" u2="&#xa9;" k="30" />
    <hkern u1="V" u2="&#xa1;" k="50" />
    <hkern u1="V" u2="x" k="40" />
    <hkern u1="V" u2="v" k="38" />
    <hkern u1="V" u2="V" k="-16" />
    <hkern u1="V" u2="&#x40;" k="50" />
    <hkern u1="V" u2="&#x2f;" k="60" />
    <hkern u1="V" u2="&#x23;" k="92" />
    <hkern u1="X" g2="at.case" k="80" />
    <hkern u1="X" g2="bullet.case" k="120" />
    <hkern u1="X" u2="&#x2022;" k="40" />
    <hkern u1="X" u2="&#xae;" k="20" />
    <hkern u1="X" u2="&#xa9;" k="40" />
    <hkern u1="X" u2="x" k="-8" />
    <hkern u1="X" u2="v" k="50" />
    <hkern u1="X" u2="&#x40;" k="20" />
    <hkern u1="X" u2="&#x2a;" k="42" />
    <hkern u1="Y" u2="&#xf0;" k="80" />
    <hkern u1="\" u2="&#xdf;" k="20" />
    <hkern u1="\" u2="v" k="60" />
    <hkern u1="\" u2="V" k="100" />
    <hkern u1="v" u2="&#x2022;" k="20" />
    <hkern u1="v" u2="v" k="-20" />
    <hkern u1="v" u2="X" k="50" />
    <hkern u1="v" u2="V" k="40" />
    <hkern u1="v" u2="&#x2f;" k="60" />
    <hkern u1="x" u2="&#x2122;" k="40" />
    <hkern u1="x" u2="&#x2022;" k="40" />
    <hkern u1="x" u2="x" k="-16" />
    <hkern u1="x" u2="\" k="20" />
    <hkern u1="x" u2="X" k="-8" />
    <hkern u1="x" u2="V" k="50" />
    <hkern u1="&#xa1;" u2="V" k="80" />
    <hkern u1="&#xa3;" u2="&#x37;" k="10" />
    <hkern u1="&#xa3;" u2="&#x31;" k="30" />
    <hkern u1="&#xa5;" u2="&#x34;" k="16" />
    <hkern u1="&#xa5;" u2="&#x32;" k="28" />
    <hkern u1="&#xa5;" u2="&#x31;" k="30" />
    <hkern u1="&#xa9;" u2="X" k="40" />
    <hkern u1="&#xa9;" u2="V" k="50" />
    <hkern u1="&#xae;" u2="X" k="20" />
    <hkern u1="&#xb0;" g2="seven.op" k="-20" />
    <hkern u1="&#xb0;" g2="four.op" k="108" />
    <hkern u1="&#xb0;" u2="&#x37;" k="-10" />
    <hkern u1="&#xb0;" u2="&#x34;" k="80" />
    <hkern u1="&#xb0;" u2="&#x32;" k="20" />
    <hkern u1="&#xbf;" u2="&#xdf;" k="50" />
    <hkern u1="&#xbf;" u2="x" k="50" />
    <hkern u1="&#xbf;" u2="v" k="70" />
    <hkern u1="&#xbf;" u2="X" k="40" />
    <hkern u1="&#xbf;" u2="V" k="130" />
    <hkern u1="&#xbf;" u2="&#x237;" k="-100" />
    <hkern u1="&#xbf;" u2="j" k="-90" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="80" />
    <hkern u1="&#xde;" g2="at.case" k="-24" />
    <hkern u1="&#xde;" g2="bullet.case" k="-16" />
    <hkern u1="&#xde;" g2="parenleft.case" k="-30" />
    <hkern u1="&#xde;" u2="&#xbf;" k="30" />
    <hkern u1="&#xde;" u2="v" k="-16" />
    <hkern u1="&#xde;" u2="X" k="74" />
    <hkern u1="&#xde;" u2="V" k="48" />
    <hkern u1="&#xde;" u2="&#x40;" k="-8" />
    <hkern u1="&#xde;" u2="&#x2f;" k="46" />
    <hkern u1="&#xde;" u2="&#x2a;" k="-8" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="30" />
    <hkern u1="&#xdf;" u2="&#xb0;" k="10" />
    <hkern u1="&#xdf;" u2="&#xae;" k="20" />
    <hkern u1="&#xdf;" u2="x" k="10" />
    <hkern u1="&#xdf;" u2="v" k="10" />
    <hkern u1="&#xdf;" u2="\" k="20" />
    <hkern u1="&#xdf;" u2="X" k="24" />
    <hkern u1="&#xdf;" u2="V" k="40" />
    <hkern u1="&#xdf;" u2="&#x2f;" k="20" />
    <hkern u1="&#xdf;" u2="&#x23;" k="-16" />
    <hkern u1="&#xf0;" u2="&#x2122;" k="30" />
    <hkern u1="&#xf0;" u2="&#xb0;" k="30" />
    <hkern u1="&#xf0;" u2="&#xae;" k="20" />
    <hkern u1="&#xf0;" u2="x" k="36" />
    <hkern u1="&#xf0;" u2="v" k="20" />
    <hkern u1="&#xf0;" u2="\" k="40" />
    <hkern u1="&#xf0;" u2="X" k="36" />
    <hkern u1="&#xf0;" u2="V" k="74" />
    <hkern u1="&#xf0;" u2="&#x2a;" k="10" />
    <hkern u1="&#x13d;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x13d;" u2="&#x21a;" k="80" />
    <hkern u1="&#x13d;" u2="&#x17d;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x17b;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x179;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x178;" k="80" />
    <hkern u1="&#x13d;" u2="&#x176;" k="80" />
    <hkern u1="&#x13d;" u2="&#x166;" k="80" />
    <hkern u1="&#x13d;" u2="&#x164;" k="80" />
    <hkern u1="&#x13d;" u2="&#x162;" k="80" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="80" />
    <hkern u1="&#x13d;" u2="Z" k="-20" />
    <hkern u1="&#x13d;" u2="Y" k="80" />
    <hkern u1="&#x13d;" u2="V" k="60" />
    <hkern u1="&#x13d;" u2="T" k="80" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-40" />
    <hkern u1="&#x165;" u2="]" k="-40" />
    <hkern u1="&#x165;" u2="&#x29;" k="-40" />
    <hkern u1="&#x176;" u2="&#xf0;" k="80" />
    <hkern u1="&#x178;" u2="&#xf0;" k="80" />
    <hkern u1="&#x192;" u2="&#x37;" k="20" />
    <hkern u1="&#x192;" u2="&#x34;" k="46" />
    <hkern u1="&#x192;" u2="&#x33;" k="2" />
    <hkern u1="&#x192;" u2="&#x32;" k="26" />
    <hkern u1="&#x192;" u2="&#x31;" k="40" />
    <hkern u1="&#x1ef2;" u2="&#xf0;" k="80" />
    <hkern u1="&#x2022;" u2="x" k="40" />
    <hkern u1="&#x2022;" u2="v" k="20" />
    <hkern u1="&#x2022;" u2="X" k="40" />
    <hkern u1="&#x2022;" u2="V" k="70" />
    <hkern u1="&#x2044;" g2="seven.dnom" k="-40" />
    <hkern u1="&#x2044;" g2="four.dnom" k="20" />
    <hkern u1="&#x20ac;" u2="&#x31;" k="10" />
    <hkern u1="&#x2116;" u2="&#x37;" k="10" />
    <hkern g1="questiondown.case" u2="V" k="80" />
    <hkern g1="bullet.case" u2="X" k="110" />
    <hkern g1="bullet.case" u2="V" k="70" />
    <hkern g1="at.case" u2="X" k="60" />
    <hkern g1="at.case" u2="V" k="45" />
    <hkern g1="sterling.op" g2="nine.op" k="10" />
    <hkern g1="sterling.op" g2="three.op" k="20" />
    <hkern g1="sterling.op" g2="one.op" k="20" />
    <hkern g1="yen.op" g2="nine.op" k="10" />
    <hkern g1="yen.op" g2="four.op" k="16" />
    <hkern g1="florin.op" g2="nine.op" k="10" />
    <hkern g1="florin.op" g2="seven.op" k="30" />
    <hkern g1="florin.op" g2="five.op" k="28" />
    <hkern g1="florin.op" g2="four.op" k="38" />
    <hkern g1="florin.op" g2="three.op" k="20" />
    <hkern g1="florin.op" g2="two.op" k="20" />
    <hkern g1="florin.op" g2="one.op" k="30" />
    <hkern g1="one.op" g2="three.op" k="30" />
    <hkern g1="one.op" g2="one.op" k="30" />
    <hkern g1="two.op" g2="florin.op" k="6" />
    <hkern g1="two.op" u2="&#x3c;" k="12" />
    <hkern g1="three.op" g2="nine.op" k="5" />
    <hkern g1="three.op" g2="one.op" k="20" />
    <hkern g1="three.op" g2="florin.op" k="20" />
    <hkern g1="three.op" g2="yen.op" k="20" />
    <hkern g1="four.op" g2="nine.op" k="10" />
    <hkern g1="four.op" g2="seven.op" k="10" />
    <hkern g1="four.op" g2="one.op" k="110" />
    <hkern g1="four.op" u2="&#xb0;" k="70" />
    <hkern g1="five.op" g2="nine.op" k="20" />
    <hkern g1="five.op" g2="seven.op" k="40" />
    <hkern g1="five.op" g2="one.op" k="50" />
    <hkern g1="five.op" g2="florin.op" k="12" />
    <hkern g1="five.op" u2="&#xb0;" k="10" />
    <hkern g1="five.op" u2="&#x3c;" k="2" />
    <hkern g1="five.op" u2="&#x23;" k="-30" />
    <hkern g1="seven.op" g2="five.op" k="20" />
    <hkern g1="seven.op" g2="four.op" k="80" />
    <hkern g1="seven.op" g2="florin.op" k="70" />
    <hkern g1="seven.op" u2="&#xb0;" k="-20" />
    <hkern g1="seven.op" u2="&#x3e;" k="30" />
    <hkern g1="seven.op" u2="&#x3c;" k="70" />
    <hkern g1="nine.op" g2="nine.op" k="20" />
    <hkern g1="nine.op" g2="seven.op" k="50" />
    <hkern g1="nine.op" g2="five.op" k="20" />
    <hkern g1="nine.op" g2="three.op" k="15" />
    <hkern g1="nine.op" g2="one.op" k="20" />
    <hkern g1="nine.op" u2="&#xb0;" k="20" />
    <hkern g1="two.numr" u2="&#x2044;" k="-40" />
    <hkern g1="four.numr" u2="&#x2044;" k="-30" />
    <hkern g1="seven.numr" u2="&#x2044;" k="60" />
    <hkern g1="germandbls"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="germandbls"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="germandbls"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="germandbls"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="germandbls"
	g2="comma,quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="germandbls"
	g2="hyphen,periodcentered,endash,emdash"
	k="-8" />
    <hkern g1="germandbls"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="8" />
    <hkern g1="germandbls"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="70" />
    <hkern g1="germandbls"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="germandbls"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="germandbls"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="germandbls"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="germandbls"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="4" />
    <hkern g1="germandbls"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="16" />
    <hkern g1="germandbls"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="4" />
    <hkern g1="germandbls"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="4" />
    <hkern g1="six,six.op"
	g2="quotedbl,quotesingle"
	k="66" />
    <hkern g1="six,six.op"
	g2="period,ellipsis"
	k="38" />
    <hkern g1="six,six.op"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="six,six.op"
	g2="three.op"
	k="35" />
    <hkern g1="six,six.op"
	g2="five"
	k="20" />
    <hkern g1="six,six.op"
	g2="three"
	k="30" />
    <hkern g1="six,six.op"
	g2="nine.op"
	k="50" />
    <hkern g1="six,six.op"
	g2="five.op"
	k="20" />
    <hkern g1="six,six.op"
	g2="two"
	k="18" />
    <hkern g1="six,six.op"
	g2="ordfeminine,ordmasculine"
	k="38" />
    <hkern g1="six,six.op"
	g2="cent,cent.weight"
	k="16" />
    <hkern g1="six,six.op"
	g2="one"
	k="70" />
    <hkern g1="six,six.op"
	g2="Euro"
	k="8" />
    <hkern g1="six,six.op"
	g2="one.op"
	k="50" />
    <hkern g1="six,six.op"
	g2="dollar,dollar.weight"
	k="28" />
    <hkern g1="six,six.op"
	g2="degree"
	k="38" />
    <hkern g1="six,six.op"
	g2="seven.op"
	k="55" />
    <hkern g1="six,six.op"
	g2="florin"
	k="20" />
    <hkern g1="six,six.op"
	g2="nine"
	k="38" />
    <hkern g1="six,six.op"
	g2="florin.op"
	k="4" />
    <hkern g1="six,six.op"
	g2="yen"
	k="46" />
    <hkern g1="six,six.op"
	g2="seven"
	k="68" />
    <hkern g1="six,six.op"
	g2="zeroslash.op,zero.op"
	k="5" />
    <hkern g1="six,six.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="70" />
    <hkern g1="six,six.op"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="six,six.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="six,six.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="twosuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="twosuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="two.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="two.op"
	g2="plus,divide,minus"
	k="30" />
    <hkern g1="two.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="6" />
    <hkern g1="four"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="four"
	g2="ordfeminine,ordmasculine"
	k="40" />
    <hkern g1="four"
	g2="cent,cent.weight"
	k="20" />
    <hkern g1="four"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="quotedbl,quotesingle"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="quotedbl,quotesingle"
	g2="three.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="five"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="three"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="nine.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="five.op"
	k="30" />
    <hkern g1="quotedbl,quotesingle"
	g2="two"
	k="30" />
    <hkern g1="quotedbl,quotesingle"
	g2="two.op"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="slash"
	k="180" />
    <hkern g1="quotedbl,quotesingle"
	g2="four.op"
	k="130" />
    <hkern g1="quotedbl,quotesingle"
	g2="four"
	k="70" />
    <hkern g1="quotedbl,quotesingle"
	g2="x"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="zeroslash.op,zero.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="quotedbl,quotesingle"
	g2="eight,eight.op"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="60" />
    <hkern g1="quotedbl,quotesingle"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="V"
	k="60" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="X"
	k="100" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="26" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="92" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="two"
	g2="cent,cent.weight"
	k="26" />
    <hkern g1="two"
	g2="dollar,dollar.weight"
	k="10" />
    <hkern g1="two"
	g2="plus,divide,minus"
	k="66" />
    <hkern g1="two"
	g2="zero,six,zeroslash,six.op"
	k="10" />
    <hkern g1="two"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="10" />
    <hkern g1="threesuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="threesuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="Euro.op,Euro.op.weight"
	g2="three.op"
	k="2" />
    <hkern g1="nine.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="nine.op"
	g2="period,ellipsis"
	k="38" />
    <hkern g1="nine.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="nine.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-12" />
    <hkern g1="eth"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="eth"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="eth"
	g2="ae,aeacute"
	k="5" />
    <hkern g1="eth"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="eth"
	g2="z,zacute,zdotaccent,zcaron"
	k="16" />
    <hkern g1="eth"
	g2="hyphen,periodcentered,endash,emdash"
	k="-16" />
    <hkern g1="eth"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="eth"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="72" />
    <hkern g1="eth"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="eth"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="30" />
    <hkern g1="eth"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="36" />
    <hkern g1="eth"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="eth"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="12" />
    <hkern g1="eth"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="4" />
    <hkern g1="eth"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="24" />
    <hkern g1="eth"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-4" />
    <hkern g1="eth"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="4" />
    <hkern g1="uni2075"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2075"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="one"
	g2="quotedbl,quotesingle"
	k="12" />
    <hkern g1="one"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="one"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="one"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="18" />
    <hkern g1="five.op"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="five.op"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="five.op"
	g2="ordfeminine,ordmasculine"
	k="30" />
    <hkern g1="five.op"
	g2="cent.op,cent.op.weight"
	k="30" />
    <hkern g1="five.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="five.op"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="five.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="five.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="30" />
    <hkern g1="uni2077"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2077"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="zeroslash.op,zero.op"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="zeroslash.op,zero.op"
	g2="period,ellipsis"
	k="38" />
    <hkern g1="zeroslash.op,zero.op"
	g2="three.op"
	k="40" />
    <hkern g1="zeroslash.op,zero.op"
	g2="nine.op"
	k="5" />
    <hkern g1="zeroslash.op,zero.op"
	g2="five.op"
	k="20" />
    <hkern g1="zeroslash.op,zero.op"
	g2="one.op"
	k="10" />
    <hkern g1="zeroslash.op,zero.op"
	g2="seven.op"
	k="40" />
    <hkern g1="zeroslash.op,zero.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="68" />
    <hkern g1="zeroslash.op,zero.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="three"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="three"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="three"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="three"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="three.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="three.op"
	g2="cent.op,cent.op.weight"
	k="20" />
    <hkern g1="three.op"
	g2="Euro.op,Euro.op.weight"
	k="-2" />
    <hkern g1="three.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="three.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="uni2078"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2078"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="v"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="v"
	g2="ae,aeacute"
	k="15" />
    <hkern g1="v"
	g2="guillemotleft,guilsinglleft"
	k="18" />
    <hkern g1="v"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="v"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="v"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="v"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="96" />
    <hkern g1="v"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="v"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-10" />
    <hkern g1="v"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="62" />
    <hkern g1="v"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="v"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-16" />
    <hkern g1="v"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-4" />
    <hkern g1="v"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="v"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="26" />
    <hkern g1="v"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-12" />
    <hkern g1="v"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="v"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="4" />
    <hkern g1="eight,eight.op"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="eight,eight.op"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="eight,eight.op"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="eight,eight.op"
	g2="nine.op"
	k="5" />
    <hkern g1="eight,eight.op"
	g2="one"
	k="10" />
    <hkern g1="eight,eight.op"
	g2="Euro"
	k="-2" />
    <hkern g1="eight,eight.op"
	g2="cent.op,cent.op.weight"
	k="10" />
    <hkern g1="eight,eight.op"
	g2="numbersign"
	k="-10" />
    <hkern g1="eight,eight.op"
	g2="seven"
	k="10" />
    <hkern g1="eight,eight.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="eight,eight.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="Thorn"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="Thorn"
	g2="period,ellipsis"
	k="60" />
    <hkern g1="Thorn"
	g2="guilsinglleft.case,guillemotleft.case"
	k="-24" />
    <hkern g1="Thorn"
	g2="guilsinglright.case,guillemotright.case"
	k="-16" />
    <hkern g1="Thorn"
	g2="AE,AEacute"
	k="56" />
    <hkern g1="Thorn"
	g2="J,Jcircumflex"
	k="26" />
    <hkern g1="Thorn"
	g2="comma,quotesinglbase,quotedblbase"
	k="100" />
    <hkern g1="Thorn"
	g2="hyphen,periodcentered,endash,emdash"
	k="-24" />
    <hkern g1="Thorn"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="Thorn"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-52" />
    <hkern g1="Thorn"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="92" />
    <hkern g1="Thorn"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-24" />
    <hkern g1="Thorn"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-16" />
    <hkern g1="Thorn"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="48" />
    <hkern g1="Thorn"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="Thorn"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-16" />
    <hkern g1="Thorn"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-16" />
    <hkern g1="Thorn"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="28" />
    <hkern g1="Thorn"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-8" />
    <hkern g1="Thorn"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-16" />
    <hkern g1="Thorn"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-16" />
    <hkern g1="Thorn"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-16" />
    <hkern g1="V"
	g2="period,ellipsis"
	k="140" />
    <hkern g1="V"
	g2="ae,aeacute"
	k="70" />
    <hkern g1="V"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="V"
	g2="guillemotleft,guilsinglleft"
	k="68" />
    <hkern g1="V"
	g2="guilsinglleft.case,guillemotleft.case"
	k="56" />
    <hkern g1="V"
	g2="guilsinglright.case,guillemotright.case"
	k="34" />
    <hkern g1="V"
	g2="AE,AEacute"
	k="92" />
    <hkern g1="V"
	g2="J,Jcircumflex"
	k="66" />
    <hkern g1="V"
	g2="guillemotright,guilsinglright"
	k="48" />
    <hkern g1="V"
	g2="comma,quotesinglbase,quotedblbase"
	k="160" />
    <hkern g1="V"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="V"
	g2="hyphen,periodcentered,endash,emdash"
	k="70" />
    <hkern g1="V"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="60" />
    <hkern g1="V"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-8" />
    <hkern g1="V"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="V"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="V"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-8" />
    <hkern g1="V"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-8" />
    <hkern g1="V"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="38" />
    <hkern g1="V"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="38" />
    <hkern g1="V"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="78" />
    <hkern g1="V"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="28" />
    <hkern g1="V"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="70" />
    <hkern g1="V"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="50" />
    <hkern g1="V"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="48" />
    <hkern g1="V"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="44" />
    <hkern g1="V"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="V"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="64" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="V"
	k="48" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="116" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-24" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="78" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="8" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="x"
	k="46" />
    <hkern g1="guillemotright,guilsinglright"
	g2="V"
	k="70" />
    <hkern g1="guillemotright,guilsinglright"
	g2="X"
	k="26" />
    <hkern g1="guillemotright,guilsinglright"
	g2="v"
	k="18" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="120" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="98" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="guillemotright,guilsinglright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="18" />
    <hkern g1="asterisk"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="asterisk"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="asterisk"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="16" />
    <hkern g1="asterisk"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="asterisk"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="asterisk"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="56" />
    <hkern g1="asterisk"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="asterisk"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="F"
	g2="period,ellipsis"
	k="130" />
    <hkern g1="F"
	g2="ae,aeacute"
	k="30" />
    <hkern g1="F"
	g2="AE,AEacute"
	k="108" />
    <hkern g1="F"
	g2="J,Jcircumflex"
	k="80" />
    <hkern g1="F"
	g2="comma,quotesinglbase,quotedblbase"
	k="130" />
    <hkern g1="F"
	g2="z,zacute,zdotaccent,zcaron"
	k="22" />
    <hkern g1="F"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-16" />
    <hkern g1="F"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-8" />
    <hkern g1="F"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-32" />
    <hkern g1="F"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="F"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="24" />
    <hkern g1="F"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="76" />
    <hkern g1="F"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-4" />
    <hkern g1="F"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="10" />
    <hkern g1="onesuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="onesuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="two"
	k="20" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="four.op"
	k="110" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="four"
	k="80" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="seven"
	k="-10" />
    <hkern g1="dollar.op,dollar.op.weight"
	g2="three.op"
	k="6" />
    <hkern g1="dollar.op,dollar.op.weight"
	g2="nine.op"
	k="10" />
    <hkern g1="dollar.op,dollar.op.weight"
	g2="one.op"
	k="70" />
    <hkern g1="seven"
	g2="period,ellipsis"
	k="160" />
    <hkern g1="seven"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="seven"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="seven"
	g2="cent,cent.weight"
	k="28" />
    <hkern g1="seven"
	g2="dollar,dollar.weight"
	k="28" />
    <hkern g1="seven"
	g2="comma,quotesinglbase,quotedblbase"
	k="180" />
    <hkern g1="seven"
	g2="plus,divide,minus"
	k="160" />
    <hkern g1="seven"
	g2="hyphen,periodcentered,endash,emdash"
	k="60" />
    <hkern g1="seven"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="102" />
    <hkern g1="x"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="x"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="x"
	g2="guillemotleft,guilsinglleft"
	k="46" />
    <hkern g1="x"
	g2="z,zacute,zdotaccent,zcaron"
	k="-8" />
    <hkern g1="x"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="x"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="x"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="98" />
    <hkern g1="x"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="8" />
    <hkern g1="x"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="42" />
    <hkern g1="x"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="x"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-4" />
    <hkern g1="x"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="4" />
    <hkern g1="x"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-16" />
    <hkern g1="x"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="x"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="14" />
    <hkern g1="x"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="15" />
    <hkern g1="x"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="32" />
    <hkern g1="X"
	g2="ae,aeacute"
	k="30" />
    <hkern g1="X"
	g2="guillemotleft,guilsinglleft"
	k="28" />
    <hkern g1="X"
	g2="guilsinglleft.case,guillemotleft.case"
	k="98" />
    <hkern g1="X"
	g2="guilsinglright.case,guillemotright.case"
	k="66" />
    <hkern g1="X"
	g2="AE,AEacute"
	k="-8" />
    <hkern g1="X"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="X"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="108" />
    <hkern g1="X"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="40" />
    <hkern g1="X"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="68" />
    <hkern g1="X"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="68" />
    <hkern g1="X"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="X"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="X"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="12" />
    <hkern g1="X"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-8" />
    <hkern g1="X"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="X"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="50" />
    <hkern g1="X"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="X"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="48" />
    <hkern g1="X"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="60" />
    <hkern g1="B"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="B"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="B"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="56" />
    <hkern g1="B"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="22" />
    <hkern g1="B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="12" />
    <hkern g1="B"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="B"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="6" />
    <hkern g1="B"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="B"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="five"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="five"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="five"
	g2="ordfeminine,ordmasculine"
	k="30" />
    <hkern g1="five"
	g2="dollar,dollar.weight"
	k="18" />
    <hkern g1="five"
	g2="comma,quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="five"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="K,uni0136"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="K,uni0136"
	g2="ae,aeacute"
	k="60" />
    <hkern g1="K,uni0136"
	g2="degree"
	k="30" />
    <hkern g1="K,uni0136"
	g2="guillemotleft,guilsinglleft"
	k="48" />
    <hkern g1="K,uni0136"
	g2="guilsinglleft.case,guillemotleft.case"
	k="136" />
    <hkern g1="K,uni0136"
	g2="guilsinglright.case,guillemotright.case"
	k="86" />
    <hkern g1="K,uni0136"
	g2="v"
	k="100" />
    <hkern g1="K,uni0136"
	g2="at.case"
	k="90" />
    <hkern g1="K,uni0136"
	g2="bullet"
	k="100" />
    <hkern g1="K,uni0136"
	g2="bullet.case"
	k="120" />
    <hkern g1="K,uni0136"
	g2="copyright"
	k="70" />
    <hkern g1="K,uni0136"
	g2="registered"
	k="30" />
    <hkern g1="K,uni0136"
	g2="at"
	k="20" />
    <hkern g1="K,uni0136"
	g2="questiondown.case"
	k="56" />
    <hkern g1="K,uni0136"
	g2="germandbls"
	k="18" />
    <hkern g1="K,uni0136"
	g2="asterisk"
	k="50" />
    <hkern g1="K,uni0136"
	g2="hyphen,periodcentered,endash,emdash"
	k="108" />
    <hkern g1="K,uni0136"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="140" />
    <hkern g1="K,uni0136"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="60" />
    <hkern g1="K,uni0136"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="62" />
    <hkern g1="K,uni0136"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="K,uni0136"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="100" />
    <hkern g1="K,uni0136"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="70" />
    <hkern g1="K,uni0136"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="K,uni0136"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="38" />
    <hkern g1="K,uni0136"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="38" />
    <hkern g1="K,uni0136"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="75" />
    <hkern g1="K,uni0136"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="50" />
    <hkern g1="K,uni0136"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="78" />
    <hkern g1="K,uni0136"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="20" />
    <hkern g1="K,uni0136"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="70" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="V"
	k="36" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="X"
	k="70" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="82" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="period,ellipsis"
	g2="quotedbl,quotesingle"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="three.op"
	k="10" />
    <hkern g1="period,ellipsis"
	g2="nine.op"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="five.op"
	k="30" />
    <hkern g1="period,ellipsis"
	g2="one"
	k="218" />
    <hkern g1="period,ellipsis"
	g2="four"
	k="16" />
    <hkern g1="period,ellipsis"
	g2="one.op"
	k="210" />
    <hkern g1="period,ellipsis"
	g2="V"
	k="140" />
    <hkern g1="period,ellipsis"
	g2="seven.op"
	k="120" />
    <hkern g1="period,ellipsis"
	g2="seven"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="v"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="asterisk"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="zeroslash.op,zero.op"
	k="38" />
    <hkern g1="period,ellipsis"
	g2="eight,eight.op"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-16" />
    <hkern g1="period,ellipsis"
	g2="zero,six,zeroslash,six.op"
	k="65" />
    <hkern g1="period,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="168" />
    <hkern g1="period,ellipsis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="150" />
    <hkern g1="period,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="period,ellipsis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="50" />
    <hkern g1="period,ellipsis"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="33" />
    <hkern g1="slash"
	g2="period,ellipsis"
	k="130" />
    <hkern g1="slash"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="slash"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="slash"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="slash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-16" />
    <hkern g1="slash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-16" />
    <hkern g1="slash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="slash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="28" />
    <hkern g1="slash"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="slash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="slash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="60" />
    <hkern g1="slash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="slash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="28" />
    <hkern g1="slash"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="seven.op"
	g2="period,ellipsis"
	k="180" />
    <hkern g1="seven.op"
	g2="zeroslash.op,zero.op"
	k="10" />
    <hkern g1="seven.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="190" />
    <hkern g1="seven.op"
	g2="plus,divide,minus"
	k="92" />
    <hkern g1="seven.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="30" />
    <hkern g1="seven.op"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="30" />
    <hkern g1="P"
	g2="period,ellipsis"
	k="100" />
    <hkern g1="P"
	g2="ae,aeacute"
	k="5" />
    <hkern g1="P"
	g2="guilsinglright.case,guillemotright.case"
	k="-8" />
    <hkern g1="P"
	g2="AE,AEacute"
	k="114" />
    <hkern g1="P"
	g2="J,Jcircumflex"
	k="58" />
    <hkern g1="P"
	g2="guillemotright,guilsinglright"
	k="-16" />
    <hkern g1="P"
	g2="comma,quotesinglbase,quotedblbase"
	k="170" />
    <hkern g1="P"
	g2="hyphen,periodcentered,endash,emdash"
	k="-8" />
    <hkern g1="P"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="P"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-24" />
    <hkern g1="P"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="36" />
    <hkern g1="P"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-32" />
    <hkern g1="P"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-32" />
    <hkern g1="P"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="4" />
    <hkern g1="P"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-16" />
    <hkern g1="P"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-16" />
    <hkern g1="P"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="56" />
    <hkern g1="P"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-8" />
    <hkern g1="P"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-24" />
    <hkern g1="P"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-16" />
    <hkern g1="P"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-8" />
    <hkern g1="cent,cent.weight"
	g2="three"
	k="4" />
    <hkern g1="cent,cent.weight"
	g2="one"
	k="10" />
    <hkern g1="cent,cent.weight"
	g2="seven"
	k="10" />
    <hkern g1="dcaron,lcaron"
	g2="x"
	k="-50" />
    <hkern g1="dcaron,lcaron"
	g2="V"
	k="-162" />
    <hkern g1="dcaron,lcaron"
	g2="X"
	k="-140" />
    <hkern g1="dcaron,lcaron"
	g2="AE,AEacute"
	k="38" />
    <hkern g1="dcaron,lcaron"
	g2="J,Jcircumflex"
	k="-2" />
    <hkern g1="dcaron,lcaron"
	g2="v"
	k="-30" />
    <hkern g1="dcaron,lcaron"
	g2="germandbls"
	k="-68" />
    <hkern g1="dcaron,lcaron"
	g2="asterisk"
	k="-102" />
    <hkern g1="dcaron,lcaron"
	g2="parenright"
	k="-120" />
    <hkern g1="dcaron,lcaron"
	g2="bracketright,braceright"
	k="-140" />
    <hkern g1="dcaron,lcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="-22" />
    <hkern g1="dcaron,lcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-128" />
    <hkern g1="dcaron,lcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-140" />
    <hkern g1="dcaron,lcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-50" />
    <hkern g1="dcaron,lcaron"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-100" />
    <hkern g1="dcaron,lcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-146" />
    <hkern g1="dcaron,lcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-140" />
    <hkern g1="dcaron,lcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-30" />
    <hkern g1="dcaron,lcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-22" />
    <hkern g1="dcaron,lcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="16" />
    <hkern g1="dcaron,lcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="-70" />
    <hkern g1="dcaron,lcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="dcaron,lcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-110" />
    <hkern g1="dcaron,lcaron"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-40" />
    <hkern g1="dcaron,lcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-68" />
    <hkern g1="dcaron,lcaron"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="-92" />
    <hkern g1="dcaron,lcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="40" />
    <hkern g1="dcaron,lcaron"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,uni2116"
	k="-78" />
    <hkern g1="four.op"
	g2="quotedbl,quotesingle"
	k="60" />
    <hkern g1="four.op"
	g2="ordfeminine,ordmasculine"
	k="50" />
    <hkern g1="four.op"
	g2="plus,divide,minus"
	k="40" />
    <hkern g1="four.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="four.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="uni2074"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2074"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="cent.op,cent.op.weight"
	g2="three.op"
	k="20" />
    <hkern g1="cent.op,cent.op.weight"
	g2="one.op"
	k="10" />
    <hkern g1="uni2076"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2076"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="colon,semicolon"
	g2="V"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="seven"
	k="20" />
    <hkern g1="colon,semicolon"
	g2="eight,eight.op"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="100" />
    <hkern g1="colon,semicolon"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="quotedbl,quotesingle"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="three.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="nine.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="five.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="one"
	k="158" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="one.op"
	k="160" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="V"
	k="120" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="X"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="seven.op"
	k="90" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="seven"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="v"
	k="40" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="asterisk"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-16" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="zero,six,zeroslash,six.op"
	k="14" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="168" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="130" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="70" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="60" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="4" />
    <hkern g1="uni2070,uni2079"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2070,uni2079"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="zero,nine,zeroslash"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="period,ellipsis"
	k="25" />
    <hkern g1="zero,nine,zeroslash"
	g2="three"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="two"
	k="10" />
    <hkern g1="zero,nine,zeroslash"
	g2="one"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="degree"
	k="8" />
    <hkern g1="zero,nine,zeroslash"
	g2="seven"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="zero,nine,zeroslash"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="hyphen,periodcentered,endash,emdash"
	k="-30" />
    <hkern g1="zero,nine,zeroslash"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="zero,nine,zeroslash"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="20" />
    <hkern g1="plus,divide,minus"
	g2="three.op"
	k="50" />
    <hkern g1="plus,divide,minus"
	g2="five"
	k="4" />
    <hkern g1="plus,divide,minus"
	g2="three"
	k="50" />
    <hkern g1="plus,divide,minus"
	g2="nine.op"
	k="2" />
    <hkern g1="plus,divide,minus"
	g2="five.op"
	k="30" />
    <hkern g1="plus,divide,minus"
	g2="two"
	k="20" />
    <hkern g1="plus,divide,minus"
	g2="one"
	k="180" />
    <hkern g1="plus,divide,minus"
	g2="four.op"
	k="30" />
    <hkern g1="plus,divide,minus"
	g2="one.op"
	k="100" />
    <hkern g1="plus,divide,minus"
	g2="seven.op"
	k="140" />
    <hkern g1="plus,divide,minus"
	g2="seven"
	k="108" />
    <hkern g1="plus,divide,minus"
	g2="zero,six,zeroslash,six.op"
	k="18" />
    <hkern g1="registered"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="registered"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-24" />
    <hkern g1="registered"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-16" />
    <hkern g1="registered"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-8" />
    <hkern g1="registered"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="registered"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="90" />
    <hkern g1="registered"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="three.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="nine.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="five.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="two"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="two.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="slash"
	k="150" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="four.op"
	k="160" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="four"
	k="110" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="one.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="V"
	k="-16" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="v"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="zeroslash.op,zero.op"
	k="70" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="eight,eight.op"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="z,zacute,zdotaccent,zcaron"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="zero,six,zeroslash,six.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-16" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="70" />
    <hkern g1="questiondown"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="questiondown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="158" />
    <hkern g1="questiondown"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="questiondown"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="136" />
    <hkern g1="questiondown"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="110" />
    <hkern g1="questiondown"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="40" />
    <hkern g1="questiondown"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="70" />
    <hkern g1="questiondown"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="60" />
    <hkern g1="questiondown"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="30" />
    <hkern g1="questiondown"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="questiondown"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="70" />
    <hkern g1="questiondown"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="58" />
    <hkern g1="questiondown"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="60" />
    <hkern g1="questiondown"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="60" />
    <hkern g1="questiondown"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="50" />
    <hkern g1="questiondown"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="60" />
    <hkern g1="questiondown"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,uni2116"
	k="60" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="period,ellipsis"
	k="118" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="slash"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="V"
	k="38" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="X"
	k="58" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="degree"
	k="-16" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="v"
	k="-8" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="bullet"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="registered"
	k="-8" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="germandbls"
	k="8" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="asterisk"
	k="-16" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="138" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="8" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="trademark"
	k="40" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="106" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-16" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="36" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="32" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-8" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="12" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="46" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-16" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-4" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="14" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="V"
	k="30" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="X"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="J,Jcircumflex"
	k="16" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="bullet"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="bullet.case"
	k="-8" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="registered"
	k="16" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-8" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="trademark"
	k="38" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="backslash"
	k="30" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="54" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-8" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="26" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="2" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="4" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="6" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-8" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="Euro"
	g2="zero,six,zeroslash,six.op"
	k="10" />
    <hkern g1="degree"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="degree"
	g2="zero,six,zeroslash,six.op"
	k="-12" />
    <hkern g1="degree"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="16" />
    <hkern g1="degree"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-24" />
    <hkern g1="degree"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-16" />
    <hkern g1="degree"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-8" />
    <hkern g1="degree"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="degree"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="90" />
    <hkern g1="degree"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="8" />
    <hkern g1="degree"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="ae,aeacute"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="V"
	k="70" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="numbersign"
	k="-20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="bullet"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="trademark"
	k="50" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="backslash"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="148" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="64" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="4" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="bullet.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="58" />
    <hkern g1="bullet.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="114" />
    <hkern g1="bullet.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="98" />
    <hkern g1="bullet.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="bullet.case"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="bullet.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="questiondown.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-16" />
    <hkern g1="questiondown.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="70" />
    <hkern g1="questiondown.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="questiondown.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="questiondown.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-20" />
    <hkern g1="questiondown.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="questiondown.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="period,ellipsis"
	k="-16" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="x"
	k="-8" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="numbersign"
	k="-16" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="J,Jcircumflex"
	k="-18" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="v"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="bullet.case"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="-8" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="60" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="question"
	k="-24" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-8" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="-8" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="f,longs,f_f,f.1"
	g2="quotedbl,quotesingle"
	k="-12" />
    <hkern g1="f,longs,f_f,f.1"
	g2="period,ellipsis"
	k="98" />
    <hkern g1="f,longs,f_f,f.1"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="f,longs,f_f,f.1"
	g2="slash"
	k="60" />
    <hkern g1="f,longs,f_f,f.1"
	g2="x"
	k="-8" />
    <hkern g1="f,longs,f_f,f.1"
	g2="V"
	k="-18" />
    <hkern g1="f,longs,f_f,f.1"
	g2="degree"
	k="-34" />
    <hkern g1="f,longs,f_f,f.1"
	g2="guillemotleft,guilsinglleft"
	k="26" />
    <hkern g1="f,longs,f_f,f.1"
	g2="numbersign"
	k="20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="v"
	k="-16" />
    <hkern g1="f,longs,f_f,f.1"
	g2="bullet"
	k="20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="registered"
	k="-28" />
    <hkern g1="f,longs,f_f,f.1"
	g2="asterisk"
	k="-26" />
    <hkern g1="f,longs,f_f,f.1"
	g2="comma,quotesinglbase,quotedblbase"
	k="98" />
    <hkern g1="f,longs,f_f,f.1"
	g2="z,zacute,zdotaccent,zcaron"
	k="-4" />
    <hkern g1="f,longs,f_f,f.1"
	g2="hyphen,periodcentered,endash,emdash"
	k="20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="trademark"
	k="-14" />
    <hkern g1="f,longs,f_f,f.1"
	g2="backslash"
	k="-44" />
    <hkern g1="f,longs,f_f,f.1"
	g2="question"
	k="-26" />
    <hkern g1="f,longs,f_f,f.1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-12" />
    <hkern g1="f,longs,f_f,f.1"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-34" />
    <hkern g1="f,longs,f_f,f.1"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-24" />
    <hkern g1="f,longs,f_f,f.1"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-50" />
    <hkern g1="f,longs,f_f,f.1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-16" />
    <hkern g1="f,longs,f_f,f.1"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-16" />
    <hkern g1="f,longs,f_f,f.1"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-12" />
    <hkern g1="f,longs,f_f,f.1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="46" />
    <hkern g1="f,longs,f_f,f.1"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-34" />
    <hkern g1="f,longs,f_f,f.1"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="4" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="three.op"
	k="60" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="three"
	k="6" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="nine.op"
	k="-10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="one"
	k="170" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="four"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="x"
	k="50" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="one.op"
	k="22" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="V"
	k="70" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="X"
	k="50" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="seven.op"
	k="100" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="seven"
	k="70" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="v"
	k="10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="zeroslash.op,zero.op"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="eight,eight.op"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="zero,six,zeroslash,six.op"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="122" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="90" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="2" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-8" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-16" />
    <hkern g1="numbersign"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="numbersign"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="numbersign"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-16" />
    <hkern g1="numbersign"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-28" />
    <hkern g1="numbersign"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="40" />
    <hkern g1="numbersign"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="104" />
    <hkern g1="numbersign"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="numbersign"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="38" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="V"
	k="70" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="X"
	k="82" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="134" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="100" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="22" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-8" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-16" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="ae,aeacute"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="slash"
	k="60" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="x"
	k="-4" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="V"
	k="38" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="X"
	k="28" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="degree"
	k="-16" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="guillemotleft,guilsinglleft"
	k="18" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="numbersign"
	k="24" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="v"
	k="-16" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="bullet"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="at"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="100" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="trademark"
	k="28" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="backslash"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="96" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-8" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-8" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="24" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="26" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-12" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="4" />
    <hkern g1="at.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="90" />
    <hkern g1="at.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="40" />
    <hkern g1="at.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="at.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="at.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-8" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="period,ellipsis"
	k="146" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="ae,aeacute"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="slash"
	k="140" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="x"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="V"
	k="-8" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="X"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="degree"
	k="-24" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guillemotleft,guilsinglleft"
	k="94" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="numbersign"
	k="72" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guilsinglleft.case,guillemotleft.case"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="AE,AEacute"
	k="150" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="J,Jcircumflex"
	k="68" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guillemotright,guilsinglright"
	k="78" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="v"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="at.case"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bullet"
	k="130" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bullet.case"
	k="90" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="copyright"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="registered"
	k="-16" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="at"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="questiondown.case"
	k="8" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="germandbls"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="comma,quotesinglbase,quotedblbase"
	k="148" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bracketright,braceright"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="z,zacute,zdotaccent,zcaron"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="hyphen,periodcentered,endash,emdash"
	k="90" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="trademark"
	k="-16" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="backslash"
	k="-16" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="question"
	k="-40" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-24" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-8" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="questiondown"
	k="154" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="exclamdown"
	k="64" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="88" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="78" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="40" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="24" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="80" />
    <hkern g1="question"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-24" />
    <hkern g1="question"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="74" />
    <hkern g1="exclamdown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="106" />
    <hkern g1="exclamdown"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="70" />
    <hkern g1="exclamdown"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="8" />
    <hkern g1="exclamdown"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="exclamdown"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="ae,aeacute"
	k="40" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="x"
	k="-8" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="V"
	k="70" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="bullet"
	k="50" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="copyright"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="hyphen,periodcentered,endash,emdash"
	k="70" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="trademark"
	k="30" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="backslash"
	k="40" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="118" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="56" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="14" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="14" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-8" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="26" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="16" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="24" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="44" />
    <hkern g1="exclamdown.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="period,ellipsis"
	k="164" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ae,aeacute"
	k="130" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="colon,semicolon"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="slash"
	k="114" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="x"
	k="96" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="V"
	k="-8" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotleft,guilsinglleft"
	k="118" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="numbersign"
	k="104" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guilsinglleft.case,guillemotleft.case"
	k="92" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guilsinglright.case,guillemotright.case"
	k="72" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="AE,AEacute"
	k="169" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J,Jcircumflex"
	k="74" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotright,guilsinglright"
	k="112" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="v"
	k="92" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at.case"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="bullet"
	k="150" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="bullet.case"
	k="82" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="copyright"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at"
	k="98" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="germandbls"
	k="48" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="asterisk"
	k="16" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="164" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="134" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="118" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="128" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="backslash"
	k="-16" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-32" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="82" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="42" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="92" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="92" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="questiondown"
	k="199" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="exclamdown"
	k="106" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="130" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="52" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="92" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="124" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="108" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="46" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="16" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="126" />
    <hkern g1="bullet"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="162" />
    <hkern g1="bullet"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="130" />
    <hkern g1="bullet"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="bullet"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="bullet"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="bullet"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="bullet"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="quotedbl,quotesingle"
	k="170" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="period,ellipsis"
	k="-16" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="slash"
	k="-16" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="V"
	k="92" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="X"
	k="-24" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="degree"
	k="130" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="numbersign"
	k="-32" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="guilsinglleft.case,guillemotleft.case"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="AE,AEacute"
	k="-32" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="J,Jcircumflex"
	k="-26" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="v"
	k="50" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="at.case"
	k="18" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="bullet"
	k="20" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="bullet.case"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="copyright"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="registered"
	k="150" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="asterisk"
	k="166" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="comma,quotesinglbase,quotedblbase"
	k="-24" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="z,zacute,zdotaccent,zcaron"
	k="-20" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="hyphen,periodcentered,endash,emdash"
	k="-12" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-24" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="104" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="trademark"
	k="180" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="backslash"
	k="110" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="question"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="148" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="100" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="172" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="46" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="45" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="-16" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-16" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="32" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="slash"
	k="48" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="x"
	k="4" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="V"
	k="28" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="X"
	k="38" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="J,Jcircumflex"
	k="12" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="guillemotright,guilsinglright"
	k="-8" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="at.case"
	k="-8" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="bullet"
	k="-8" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="asterisk"
	k="-16" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="comma,quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="hyphen,periodcentered,endash,emdash"
	k="-16" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="8" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-16" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="trademark"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="backslash"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="64" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-16" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="24" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-8" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="questiondown"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="16" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-12" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-8" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ae,aeacute"
	k="45" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="colon,semicolon"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="slash"
	k="56" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="V"
	k="-8" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="numbersign"
	k="56" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guilsinglleft.case,guillemotleft.case"
	k="38" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guilsinglright.case,guillemotright.case"
	k="22" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="AE,AEacute"
	k="88" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex"
	k="32" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotright,guilsinglright"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="at.case"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet.case"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="copyright"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="registered"
	k="-8" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="at"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="germandbls"
	k="16" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="90" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,periodcentered,endash,emdash"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="22" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="trademark"
	k="-16" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-8" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="questiondown"
	k="80" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="exclamdown"
	k="2" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="28" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="38" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="4" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="backslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="150" />
    <hkern g1="backslash"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="40" />
    <hkern g1="backslash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="140" />
    <hkern g1="backslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="78" />
    <hkern g1="backslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="36" />
    <hkern g1="backslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="backslash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="backslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="backslash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="backslash"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="40" />
    <hkern g1="backslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="70" />
    <hkern g1="backslash"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="38" />
    <hkern g1="backslash"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="ae,aeacute"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="slash"
	k="32" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="V"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="X"
	k="18" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="degree"
	k="-8" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="v"
	k="-4" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="bullet"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="8" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="backslash"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="98" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="50" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-8" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="4" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-18" />
    <hkern g1="copyright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="90" />
    <hkern g1="copyright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="copyright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="copyright"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="copyright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="38" />
    <hkern g1="at"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="126" />
    <hkern g1="at"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="at"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="at"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="period,ellipsis"
	k="-8" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="ae,aeacute"
	k="5" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="V"
	k="50" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="numbersign"
	k="-16" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="guillemotright,guilsinglright"
	k="-8" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="bullet"
	k="14" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="hyphen,periodcentered,endash,emdash"
	k="6" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-16" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="backslash"
	k="20" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="110" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="46" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="14" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-8" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="4" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="6" />
    <hkern g1="bracketleft,braceleft"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="ae,aeacute"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="x"
	k="12" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="V"
	k="73" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="X"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="degree"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="v"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="germandbls"
	k="4" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="asterisk"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="hyphen,periodcentered,endash,emdash"
	k="-8" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="trademark"
	k="50" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="backslash"
	k="50" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="124" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="8" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="24" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="14" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="8" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="4" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="8" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="three.op"
	k="30" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="one"
	k="110" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="one.op"
	k="12" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="seven.op"
	k="50" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="seven"
	k="130" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="zero,six,zeroslash,six.op"
	k="20" />
    <hkern g1="trademark"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="trademark"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="slash"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="x"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="V"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="X"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="J,Jcircumflex"
	k="8" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="v"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="bullet.case"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="registered"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="asterisk"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="hyphen,periodcentered,endash,emdash"
	k="-8" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="8" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="trademark"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="backslash"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="50" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="18" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="14" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="slash"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="V"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="X"
	k="34" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="degree"
	k="-16" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="guillemotleft,guilsinglleft"
	k="-24" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="numbersign"
	k="-16" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="registered"
	k="-16" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="asterisk"
	k="-16" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="comma,quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="hyphen,periodcentered,endash,emdash"
	k="-32" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="trademark"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="backslash"
	k="28" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="44" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-8" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="16" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="4" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="slash"
	k="-8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="V"
	k="58" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="numbersign"
	k="-16" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="registered"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="z,zacute,zdotaccent,zcaron"
	k="-4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="trademark"
	k="20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="116" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="34" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="34" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="-4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-16" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="quotedbl,quotesingle"
	k="80" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="x"
	k="-16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="V"
	k="82" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="X"
	k="-8" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="degree"
	k="120" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="numbersign"
	k="-16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guilsinglleft.case,guillemotleft.case"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guilsinglright.case,guillemotright.case"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="AE,AEacute"
	k="-24" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="J,Jcircumflex"
	k="-10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="v"
	k="54" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="at.case"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="bullet"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="bullet.case"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="copyright"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="registered"
	k="110" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="asterisk"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="54" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="trademark"
	k="120" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="backslash"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="question"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="120" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="106" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="48" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="52" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="24" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="34" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="24" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="35" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="x"
	k="-4" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="numbersign"
	k="-16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="AE,AEacute"
	k="-16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="J,Jcircumflex"
	k="-8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="v"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="at.case"
	k="4" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="-16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="2" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="4" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="quotedbl,quotesingle"
	k="60" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="slash"
	k="-8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="x"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="V"
	k="80" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="X"
	k="-8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="degree"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="numbersign"
	k="-24" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="guillemotright,guilsinglright"
	k="-8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="v"
	k="8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="registered"
	k="60" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="asterisk"
	k="30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="hyphen,periodcentered,endash,emdash"
	k="-16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="trademark"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="backslash"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="138" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="72" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-4" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-8" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="V"
	k="56" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="degree"
	k="30" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="v"
	k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="registered"
	k="50" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="asterisk"
	k="40" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="trademark"
	k="80" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="backslash"
	k="40" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="132" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="84" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="28" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="period,ellipsis"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="slash"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="x"
	k="24" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="X"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="J,Jcircumflex"
	k="16" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="guillemotright,guilsinglright"
	k="8" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="hyphen,periodcentered,endash,emdash"
	k="-8" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-8" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="4" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="4" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-4" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="period,ellipsis"
	k="50" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="slash"
	k="68" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="x"
	k="32" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="V"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="X"
	k="48" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="degree"
	k="8" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="J,Jcircumflex"
	k="8" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="v"
	k="4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="at.case"
	k="-8" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="asterisk"
	k="-10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="8" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-16" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="trademark"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="backslash"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="72" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="44" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-12" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-4" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="x"
	k="24" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="V"
	k="78" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="X"
	k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="degree"
	k="80" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="numbersign"
	k="-16" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="v"
	k="24" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="registered"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="asterisk"
	k="50" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="comma,quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="hyphen,periodcentered,endash,emdash"
	k="-16" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="8" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="trademark"
	k="70" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="backslash"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="148" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="24" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="4" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="4" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-4" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-4" />
    <hkern g1="d,i,j,l,igrave,iacute,icircumflex,idieresis,dcroat,itilde,imacron,ibreve,iogonek,ij,jcircumflex,lacute,uni013C,lslash,f_f_i,f_f_l,i.trk,f_j,fl,f_f_j"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="16" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="V"
	k="40" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="trademark"
	k="30" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="backslash"
	k="10" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="126" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
  </font>
</defs></svg>

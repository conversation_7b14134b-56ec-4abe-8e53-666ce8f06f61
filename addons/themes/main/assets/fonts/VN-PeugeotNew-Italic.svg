<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Wed Jun 23 16:36:23 2021
 By <PERSON>eksey,,,
Copyright (c) 2019 by Peugeot. All rights reserved.
</metadata>
<defs>
<font id="VN-PeugeotNew-Italic" horiz-adv-x="760" >
  <font-face 
    font-family="VN-Peugeot New"
    font-weight="400"
    font-style="italic"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 0 0 0 0 0 0 0 0"
    ascent="802"
    descent="-198"
    x-height="559"
    cap-height="802"
    bbox="-282 -258 1568 1220"
    underline-thickness="100"
    underline-position="-248"
    slope="-12"
    unicode-range="U+0020-FEFF"
  />
<missing-glyph horiz-adv-x="584" 
d="M109 802h584l-212 -1000h-584zM302 195l33 85q54 0 91.5 10t62.5 28t39 43.5t21 56.5q5 23 5 43q0 58 -47.5 96t-190.5 38q-45 0 -96 -5.5t-91 -13.5l-26 -124q48 9 95.5 14.5t89.5 5.5q40 0 63.5 -2t35 -7.5t13 -12t1.5 -10.5q0 -7 -2 -15q-3 -12 -7 -21t-13.5 -14
t-25.5 -7.5t-42 -2.5h-109l-15 -70l-5 -115h120zM278 0l33 153h-154l-33 -153h154z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="752" 
d="M62 0l99 468h-68l15 70l72 21l11 50q11 53 29 92t46.5 65t70 39t98.5 13q25 0 45.5 -2t43.5 -5l-20 -92q-17 2 -32 3t-32 1q-33 0 -56.5 -5.5t-40.5 -18t-28 -33.5t-17 -52l-12 -55h240l11 50q11 53 29 92t46.5 65t70 39t98.5 13q29 0 54 -1.5t52 -5.5l-19 -92
q-20 2 -40 3t-42 1q-33 0 -56.5 -5.5t-40.5 -18t-28 -33.5t-17 -52l-12 -55h187l-20 -91h-186l-98 -468h-106l98 468h-240l-98 -468h-107z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="1002" 
d="M53 0l100 468h-69l16 70l72 21l11 50q11 53 29 92t46.5 65t70 39t98.5 13q25 0 45.5 -2t43.5 -5l-19 -92q-17 2 -33.5 3t-33.5 1q-32 0 -55.5 -5.5t-40 -18t-27 -33.5t-17.5 -52l-12 -55h239l11 50q11 53 29 92t46 65t68.5 39t97.5 13q19 0 35.5 -2t30.5 -5l-18 -92
q-11 2 -22 3t-23 1q-31 0 -54 -5.5t-39.5 -18t-27 -33.5t-16.5 -52l-12 -55h347l-119 -559h-106l99 468h-240l-99 -468h-106l99 468h-239l-99 -468h-107zM910 788h111l-20 -98h-111z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="1016" 
d="M53 0l100 468h-67l14 70l72 21l11 50q11 53 29 92t46.5 65t70 39t98.5 13q25 0 45.5 -2t43.5 -5l-19 -92q-17 2 -32.5 3t-32.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h240l11 50q11 53 29 92t46.5 65t70 39t98.5 13q23 0 42.5 -2t41.5 -5l-19 -92
q-15 2 -29.5 3t-30.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h165l-19 -91h-165l-99 -468h-106l99 468h-240l-99 -468h-107zM930 802h105l-170 -802h-106z" />
    <glyph glyph-name="f_b" unicode="fb" horiz-adv-x="1105" 
d="M53 0l100 468h-69l16 70l72 21l11 50q11 53 29 92t46.5 65t70 39t98.5 13q23 0 42.5 -2t41.5 -5l-19 -92q-15 2 -29.5 3t-30.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h165l-20 -91h-164l-99 -468h-107zM584 802h106l-57 -269q45 19 91.5 30.5
t92.5 11.5q68 0 118 -18t80 -56t35 -77t5 -59q0 -47 -12 -104q-16 -75 -43 -128t-66.5 -86t-91 -48t-116.5 -15q-57 0 -110 15t-102 37l-26 -36h-74zM788 477q-47 0 -92 -12t-84 -29l-66 -314q34 -17 74.5 -28.5t88.5 -11.5q99 0 152.5 44t75.5 147q9 43 9 75
q0 11 -2.5 35.5t-21.5 48.5t-53 34.5t-81 10.5z" />
    <glyph glyph-name="f_h" unicode="fh" horiz-adv-x="1122" 
d="M53 0l100 468h-69l16 70l72 21l11 50q11 53 29 92t46.5 65t70 39t98.5 13q23 0 42.5 -2t41.5 -5l-19 -92q-15 2 -29.5 3t-30.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h165l-20 -91h-164l-99 -468h-107zM584 802h106l-58 -275q50 21 104 34.5
t110 13.5q126 0 168 -50t42 -113q0 -26 -6 -56l-76 -356h-105l75 356q4 18 4 33q0 34 -23.5 59.5t-105.5 25.5q-56 0 -108.5 -15t-100.5 -36l-90 -423h-106z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="657" 
d="M53 0l100 468h-65l12 70l72 21l11 50q11 53 28.5 92t45.5 65t68.5 39t97.5 13q20 0 36.5 -2t31.5 -5l-19 -92q-11 2 -22 3t-23 1q-31 0 -54 -5.5t-39 -18t-26.5 -33.5t-17.5 -52l-12 -55h346l-119 -559h-105l99 468h-240l-99 -468h-107zM564 788h112l-20 -98h-113z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="656" 
d="M53 0l100 468h-69l16 70l72 21l11 50q11 53 28.5 92t45.5 65t68.5 39t97.5 13q20 0 36.5 -2t31.5 -5l-19 -92q-11 2 -22 3t-23 1q-31 0 -54 -5.5t-39 -18t-26.5 -33.5t-17.5 -52l-12 -55h346l-124 -583q-11 -51 -27 -87t-40 -59t-59 -33.5t-83 -10.5q-25 0 -49.5 1
t-44.5 5l20 94q14 -2 33 -3t33 -1q48 0 74 20t37 74l104 492h-239l-99 -468h-107zM564 788h112l-20 -98h-113z" />
    <glyph glyph-name="f_k" unicode="fk" horiz-adv-x="1046" 
d="M53 0l100 468h-69l16 70l72 21l11 50q11 53 29 92t46.5 65t70 39t98.5 13q23 0 42.5 -2t41.5 -5l-17 -92q-15 2 -30 3t-32 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h165l-20 -91h-164l-99 -468h-107zM584 802h106l-100 -474l337 231h147l-370 -253
l252 -306h-131l-240 306l-65 -306h-106z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="670" 
d="M53 0l100 468h-69l16 70l72 21l11 50q11 53 29 92t46.5 65t70 39t98.5 13q23 0 42.5 -2t41.5 -5l-19 -92q-15 2 -29.5 3t-30.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h165l-20 -91h-164l-99 -468h-107zM584 802h106l-170 -802h-106z" />
    <glyph glyph-name="f_f_b" unicode="ffb" horiz-adv-x="1451" 
d="M53 0l100 468h-67l14 70l72 21l11 50q11 53 29 92t46.5 65t70 39t98.5 13q25 0 45.5 -2t43.5 -5l-17 -92q-17 2 -33.5 3t-33.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h240l11 50q11 53 29 92t46.5 65t70 39t98.5 13q23 0 42.5 -2t41.5 -5l-17 -92
q-15 2 -30 3t-32 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h165l-19 -91h-165l-99 -468h-106l99 468h-240l-99 -468h-107zM929 802h106l-57 -269q46 19 92.5 30.5t92.5 11.5q68 0 118 -18t80 -56t35 -76.5t5 -58.5q0 -46 -13 -105q-16 -75 -43 -128
t-66.5 -86t-91 -48t-116.5 -15q-57 0 -109.5 15t-101.5 38l-27 -37h-74zM1133 477q-46 0 -91 -12t-84 -29l-67 -314q35 -17 75.5 -28.5t88.5 -11.5q50 0 87.5 10.5t65 33.5t46 59t29.5 88q9 43 9 75q0 11 -2.5 35.5t-21.5 48.5t-53 34.5t-82 10.5z" />
    <glyph glyph-name="f_f_h" unicode="ffh" horiz-adv-x="1469" 
d="M53 0l100 468h-66l13 70l72 21l11 50q11 53 29 92t46.5 65t70 39t98.5 13q25 0 45.5 -2t43.5 -5l-19 -92q-17 2 -32.5 3t-32.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h240l11 50q11 53 29 92t46.5 65t70 39t98.5 13q23 0 42.5 -2t41.5 -5l-19 -92
q-15 2 -29.5 3t-30.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h165l-19 -91h-165l-99 -468h-106l99 468h-240l-99 -468h-107zM929 802h106l-58 -275q50 21 104 34.5t110 13.5q126 0 169 -50t43 -112q0 -26 -7 -57l-75 -356h-107l76 356q4 18 4 33
q0 34 -23 59.5t-106 25.5q-56 0 -109 -15t-101 -36l-90 -423h-106z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="1002" 
d="M53 0l100 468h-68l15 70l72 21l11 50q11 53 29 92t46.5 65t70 39t98.5 13q25 0 45.5 -2t43.5 -5l-19 -92q-17 2 -32.5 3t-32.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h239l11 50q11 53 29 92t46 65t68.5 39t97.5 13q19 0 35.5 -2t30.5 -5l-18 -92
q-11 2 -21 3t-22 1q-32 0 -55 -5.5t-39.5 -18t-27.5 -33.5t-17 -52l-12 -55h347l-124 -583q-11 -51 -27 -87t-40 -59t-59 -33.5t-83 -10.5q-25 0 -49.5 1t-44.5 5l20 94q14 -2 33 -3t33 -1q47 0 74 20.5t37 73.5l104 492h-240l-99 -468h-106l99 468h-239l-99 -468h-107z
M910 788h111l-20 -98h-111z" />
    <glyph glyph-name="f_f_k" unicode="ffk" horiz-adv-x="1392" 
d="M53 0l100 468h-66l13 70l72 21l11 50q11 53 29 92t46.5 65t70 39t98.5 13q25 0 45.5 -2t43.5 -5l-17 -80q-17 2 -33.5 3.5t-33.5 1.5q-65 0 -96.5 -27t-45.5 -95l-12 -55h240l11 50q11 53 29 92t46.5 65t70 39t98.5 13q23 0 42.5 -2t41.5 -5l-17 -80q-15 2 -30 3.5
t-32 1.5q-33 0 -56.5 -6.5t-40 -20.5t-27 -37.5t-18.5 -57.5l-12 -55h165l-19 -91h-165l-99 -468h-106l99 468h-240l-99 -468h-107zM929 802h106l-98 -464l329 221h152l-365 -249l257 -310h-135l-244 311l-66 -311h-106z" />
    <glyph glyph-name=".notdef" horiz-adv-x="584" 
d="M109 802h584l-212 -1000h-584zM302 195l33 85q54 0 91.5 10t62.5 28t39 43.5t21 56.5q5 23 5 43q0 58 -47.5 96t-190.5 38q-45 0 -96 -5.5t-91 -13.5l-26 -124q48 9 95.5 14.5t89.5 5.5q40 0 63.5 -2t35 -7.5t13 -12t1.5 -10.5q0 -7 -2 -15q-3 -12 -7 -21t-13.5 -14
t-25.5 -7.5t-42 -2.5h-109l-15 -70l-5 -115h120zM278 0l33 153h-154l-33 -153h154z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni000D" horiz-adv-x="321" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="285" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="380" 
d="M207 612l40 190h107l-40 -190l-88 -363h-85zM95 116h121l-25 -116h-120z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="574" 
d="M246 802h115l-75 -293h-90zM458 802h115l-75 -293h-90z" />
    <glyph glyph-name="numbersign" unicode="#" 
d="M134 223h-112l43 77h112l111 202h-112l43 77h112l123 223h94l-123 -223h159l123 223h95l-123 -223h113l-44 -77h-112l-112 -202h114l-44 -77h-113l-123 -223h-92l123 223h-160l-123 -223h-95zM431 300l111 202h-159l-112 -202h160z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="700" 
d="M59 169q42 -13 98 -24.5t119 -15.5l50 235q-62 11 -103.5 27t-64 41.5t-25.5 50.5t-3 37q0 32 9 74q9 43 28 75t51.5 53.5t80 33t113.5 13.5l20 92h89l-20 -94q54 -4 101.5 -12.5t85.5 -20.5l-17 -100q-43 11 -91.5 20.5t-99.5 12.5l-46 -215q63 -12 105 -28.5t64.5 -43
t25 -51t2.5 -35.5q0 -34 -10 -78q-10 -47 -28.5 -81t-50 -56.5t-79.5 -33.5t-118 -13l-20 -92h-89l20 94q-68 5 -122 15.5t-94 24.5zM238 583q-5 -23 -5 -40q0 -4 1 -15.5t13.5 -24t37 -20.5t63.5 -16l43 201q-77 -3 -110.5 -22.5t-42.5 -62.5zM521 225q5 24 5 42
q0 4 -1 16.5t-13 26.5t-36.5 23.5t-63.5 16.5l-47 -222q41 2 68.5 8t45.5 18t27.5 29.5t14.5 41.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="868" 
d="M261 474q-94 0 -126.5 34.5t-32.5 87.5q0 27 7 62q19 87 66 123.5t138 36.5q47 0 80.5 -10t53 -32t23 -45t3.5 -35q0 -28 -8 -62q-19 -87 -66 -123.5t-138 -36.5zM155 0h-105l717 802h107zM269 546q47 0 70 23t34 77q5 24 5 42q0 23 -11.5 40t-60.5 17q-48 0 -71 -22.5
t-34 -76.5q-5 -24 -5 -41q0 -24 11.5 -41.5t61.5 -17.5zM611 -16q-94 0 -126.5 34.5t-32.5 87.5q0 27 7 62q19 87 66 123.5t138 36.5q47 0 80.5 -10t53 -32t23 -45t3.5 -35q0 -28 -8 -62q-19 -87 -66 -123.5t-138 -36.5zM619 56q47 0 70 23t34 77q5 24 5 42q0 23 -11.5 40
t-60.5 17q-48 0 -71 -22.5t-34 -76.5q-5 -24 -5 -41q0 -24 11.5 -41.5t61.5 -17.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="770" 
d="M713 67l-66 -67l-140 131l-252 233q-24 -6 -45 -18q-28 -18 -45 -46q-8 -15 -15.5 -33t-12.5 -41t-5 -44q0 -9 1 -17q4 -32 30.5 -46.5t54.5 -19.5q20 -4 45.5 -5.5t57.5 -1.5q20 0 42.5 1t45.5 4t45 9t39 16l69 -61q-3 -2 -5.5 -4t-6.5 -4q-27 -17 -58 -27.5t-64 -16
t-66 -7.5t-64 -2q-33 0 -76.5 2.5t-83 14t-70 35t-38.5 65.5q-4 16 -4 34q0 29 9 64q12 58 43 109t87 76q10 4 20 6.5t20 4.5l-55 51l-2 4q0 1 -1 2l-2 2q0 1 -1 2q-13 23 -13 61q0 31 9 71q14 61 45 98.5t73 59t92.5 29.5t103.5 11q22 -1 48 -3t51.5 -7t49 -14t40.5 -25
q14 -11 25.5 -29.5t12.5 -36.5v-9q0 -3 1 -5q1 -8 1 -16t-1 -20.5t-6 -32.5q-22 -88 -66 -132q-3 -3 -6.5 -5t-6.5 -4q-17 -14 -45 -28q-24 -12 -63 -26t-97 -26l-53 55q24 3 54.5 8.5t60 15.5t54.5 25t39 39q8 12 14 27.5t11 35.5q5 18 5 35q0 10 -2 19q-4 22 -20.5 35
t-37.5 19.5t-44 8.5t-41 3q-23 -1 -47.5 -4t-48 -9.5t-43.5 -19t-34 -33.5q-16 -24 -20 -43t-4 -33q0 -8 2 -23t12.5 -34t26.5 -33l102 -97l186 -173q3 5 15 24t26.5 46t27.5 59.5t18 65.5h99q-10 -43 -26 -87q-14 -38 -36.5 -84t-55.5 -88z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="381" 
d="M246 802h115l-75 -293h-90z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="368" 
d="M155 -146q-26 41 -46 91.5t-29 106.5t-9 115q0 11 1 53.5t19 129.5q29 138 94 251t160 201h109q-46 -42 -87 -94.5t-74.5 -113t-59 -128t-40.5 -138.5q-21 -96 -21 -181q0 -46 12 -127.5t69 -165.5h-98z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="368" 
d="M235 802q26 -41 46 -91.5t29 -106.5t9 -115q0 -11 -1 -53.5t-19 -129.5q-29 -138 -94 -251t-160 -201h-110q46 42 87 94.5t74.5 113t59 128t40.5 138.5q21 96 21 181q0 46 -12 127.5t-69 165.5h99z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="435" 
d="M279 552l-118 -155l-57 50l132 133l-146 43l32 73l140 -67l26 173h82l-54 -179l159 73l19 -78l-169 -41l82 -137l-69 -40z" />
    <glyph glyph-name="plus" unicode="+" 
d="M131 323h211l50 236h96l-50 -236h214l-20 -87h-213l-50 -236h-96l50 236h-212z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="380" 
d="M141 123h118l-167 -294h-99z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="380" 
d="M32 230l20 100h302l-21 -100h-301z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="380" 
d="M97 116h120l-24 -116h-120z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="396" 
d="M391 802h108l-471 -948h-107z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="700" 
d="M448 818q81 0 137 -23t85 -74.5t30.5 -97.5t1.5 -55q0 -78 -22 -185q-22 -103 -51.5 -178.5t-73 -124.5t-104 -72.5t-146.5 -23.5q-81 0 -137 23t-85 74.5t-30.5 97.5t-1.5 55q0 78 22 185q22 104 51.5 179t73 124t104.5 72.5t146 23.5zM314 87q56 0 96 16.5t70 53.5
t52 97t40 147q19 88 20.5 122t1.5 42q0 49 -15 83.5t-49.5 50.5t-90.5 16q-55 0 -95.5 -16.5t-70.5 -53.5t-52 -97t-40 -147q-19 -88 -20.5 -122t-1.5 -42q0 -49 15 -83.5t49.5 -50.5t90.5 -16z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="555" 
d="M434 802h105l-171 -802h-108l144 679l-262 -118l-34 94z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="700" 
d="M443 818q158 0 209 -47t51 -116q0 -27 -6 -58q-9 -38 -20 -64t-31 -48t-52 -42.5t-82 -47.5l-258 -138q-27 -15 -44.5 -26.5t-28.5 -23.5t-17 -27t-11 -36l-9 -45h452l-21 -99h-561l34 159q8 35 18.5 61t28 47t44 39.5t64.5 38.5l268 143q34 18 55 30.5t33.5 24t18.5 25
t10 33.5q3 18 3 32q0 11 -3 26.5t-22.5 29t-54.5 20t-89 6.5q-61 0 -124 -7.5t-114 -18.5l21 103q51 11 106.5 18.5t131.5 7.5z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="700" 
d="M31 102q55 -13 113 -20t126 -7t111.5 7.5t70.5 23.5t41 41.5t22 61.5q7 32 7 55q0 8 -2 25.5t-19 34.5t-50 24.5t-84 7.5h-190l19 101h188q44 0 77 5t56.5 17t37.5 33.5t21 54.5q6 30 6 55q0 9 -2 28.5t-21.5 39t-56 29t-93.5 9.5q-71 0 -125.5 -6.5t-108.5 -18.5l18 89
q46 11 104.5 18.5t130.5 7.5q165 0 217.5 -51t52.5 -125q0 -34 -9 -74q-16 -77 -58 -115t-105 -52q64 -19 84.5 -59t20.5 -82q0 -33 -9 -74q-12 -54 -34.5 -92.5t-63 -62.5t-102 -35.5t-152.5 -11.5q-86 0 -148 8t-110 20z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="700" 
d="M656 456l-97 -456h-106l38 179h-477l18 85l400 538h126l-387 -522h341l37 176h107z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="700" 
d="M33 114q107 -27 252 -27q60 0 100 8t65.5 24.5t40 42t21.5 61.5q6 26 6 48q0 48 -34.5 76t-138.5 28h-226l91 427h498l-21 -102h-390l-48 -223h118q155 0 209.5 -52t54.5 -132q0 -37 -10 -82q-13 -64 -38.5 -108t-67 -70t-101.5 -37.5t-143 -11.5q-84 0 -149.5 8.5
t-110.5 19.5z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="700" 
d="M323 -16q-89 0 -147.5 23.5t-88 75.5t-30 95.5t-0.5 46.5q0 82 24 196q24 110 55.5 186t77.5 123t111 67.5t155 20.5q71 0 124.5 -8.5t94.5 -20.5l-21 -101q-43 11 -92.5 19t-109.5 8q-63 0 -107 -12t-75.5 -42t-54 -81.5t-40.5 -129.5q42 17 89.5 28.5t110.5 11.5
q65 0 115 -16t81 -49t38.5 -69t7.5 -60q0 -34 -9 -75q-13 -66 -38 -111.5t-62.5 -73t-89 -40t-119.5 -12.5zM374 390q-60 0 -105.5 -10.5t-90.5 -27.5q-15 -76 -15 -127.5t17.5 -83t54.5 -45.5t96 -14q41 0 73 7.5t55.5 25t39.5 47t25 73.5q5 26 5 48q0 14 -4 34.5
t-23.5 38.5t-52 26t-75.5 8z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="700" 
d="M164 802h596l-18 -84l-543 -718h-138l545 700h-465z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="700" 
d="M300 -16q-157 0 -216 47t-59 121q0 30 8 65q19 88 63.5 133t116.5 62q-53 18 -72 53t-19 74q0 30 8 68q11 54 34 93.5t61 65.5t94 39t134 13q151 0 206 -46t55 -117q0 -28 -7 -62q-17 -85 -58.5 -129t-106.5 -62q30 -8 53 -23.5t36 -40t14.5 -45t1.5 -28.5q0 -29 -8 -67
q-12 -55 -35.5 -95t-63.5 -66.5t-99 -39.5t-141 -13zM387 457q50 0 85.5 6t60 20t39.5 39t23 62q5 24 5 44q0 7 -1.5 23t-18 32t-49.5 24t-87 8q-52 0 -88.5 -8t-60.5 -24.5t-38 -41t-21 -57.5q-5 -25 -5 -44q0 -10 2.5 -26.5t20.5 -30.5t51 -20t82 -6zM309 87q55 0 94 8
t65 24.5t41.5 42t22.5 59.5q5 24 5 43q0 12 -3.5 30t-23 33.5t-55 21.5t-87.5 6q-51 0 -89 -6t-65 -21.5t-43.5 -41.5t-25.5 -65q-5 -24 -5 -43q0 -9 2 -25.5t20.5 -33t54 -24.5t92.5 -8z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="700" 
d="M425 818q89 0 147.5 -23.5t88 -75.5t30.5 -96t1 -49q0 -80 -25 -193q-24 -110 -55.5 -186t-77.5 -123t-111 -67.5t-155 -20.5q-71 0 -124.5 8.5t-94.5 20.5l21 101q43 -11 92.5 -19t109.5 -8q63 0 107 12t75.5 42t54 81.5t40.5 129.5q-42 -17 -89.5 -28.5t-110.5 -11.5
q-65 0 -115 16t-81 49t-38.5 69t-7.5 60q0 34 9 75q14 66 38.5 111.5t62 73t89 40t119.5 12.5zM224 567q-5 -26 -5 -47q0 -14 4 -35t23.5 -39t52 -26t75.5 -8q60 0 105.5 10.5t90.5 27.5q15 76 15 127.5t-17.5 83t-54.5 45.5t-96 14q-41 0 -73 -7.5t-55.5 -25t-39.5 -47
t-25 -73.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="380" 
d="M200 558h120l-24 -116h-120zM106 116h120l-24 -116h-120z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="362" 
d="M191 558h120l-24 -116h-120zM122 123h118l-167 -294h-99z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M132 338l566 190l-21 -95l-465 -156l399 -156l-19 -89l-484 191z" />
    <glyph glyph-name="equal" unicode="=" 
d="M162 472h520l-18 -88h-521zM99 177h521l-20 -88h-520z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M633 223l-565 -191l20 95l465 156l-399 156l19 89l485 -190z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="627" 
d="M199 386l14 70h115q55 0 92.5 6.5t62 21t38 37.5t20.5 56q5 25 5 46q0 10 -2.5 27.5t-22.5 34.5t-58.5 24.5t-100.5 7.5q-60 0 -120.5 -7.5t-114.5 -19.5l18 101q50 11 110 18.5t125 7.5q163 0 218.5 -49.5t55.5 -125.5q0 -30 -7 -66q-11 -56 -33.5 -95.5t-59 -64
t-88 -35.5t-122.5 -11h-40l-37 -121h-85zM137 116h119l-24 -116h-119z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="930" 
d="M391 72q-34 0 -63 8t-47.5 25.5t-24 38t-5.5 35.5q0 19 5 41q13 60 49 87t105 27h187l7 26q3 11 3 19q0 6 -2 14.5t-15.5 16.5t-40.5 11.5t-72 3.5q-41 0 -77.5 -4t-74.5 -9l20 83q19 3 35 5.5t33 4.5t36 3t43 1q64 0 105.5 -9.5t64 -28.5t26.5 -39.5t4 -32.5
q0 -21 -6 -46l-47 -223q-3 -14 -3 -25t4.5 -24t38.5 -13q32 0 43.5 15t16.5 36l36 169q16 77 16 133q0 15 -3.5 54t-35 76.5t-89 53t-141.5 15.5q-85 0 -144.5 -16.5t-100 -52t-65.5 -89.5t-41 -129q-18 -86 -18 -150q0 -16 3.5 -60t35.5 -87t89.5 -60.5t138.5 -17.5
q16 0 37 1t37 3l-22 -85q-15 -3 -37 -4t-42 -1q-94 0 -165 24t-113 79.5t-48.5 112.5t-6.5 84q0 74 20 170q20 92 51 160t83 113t129 67.5t188 22.5q104 0 178 -24t116.5 -74.5t49.5 -102.5t7 -80q0 -60 -16 -136l-36 -167q-6 -30 -16.5 -54.5t-27.5 -41.5t-41.5 -26
t-60.5 -9q-79 0 -114.5 28.5t-35.5 74.5q0 6 1 12q-28 -14 -63 -23.5t-81 -9.5zM414 149q42 0 77.5 11t62.5 25l22 76h-135q-41 0 -61 -11t-27 -39q-2 -10 -2 -18q0 -20 14 -32t49 -12z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="828" 
d="M433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331l-57 379h-26l-216 -379h299z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="819" 
d="M220 802h332q71 0 123 -11.5t82.5 -38.5t37 -57.5t6.5 -51.5q0 -32 -9 -72q-17 -77 -59.5 -116.5t-105.5 -52.5q66 -15 91 -55t25 -86q0 -30 -8 -66q-13 -60 -37 -97.5t-61 -59.5t-88 -30t-118 -8h-381zM310 698l-51 -241h248q37 0 65.5 5t49.5 18.5t35 37t22 59.5
q5 23 5 41q0 10 -3 26t-19.5 29.5t-45 19t-68.5 5.5h-238zM236 352l-52 -248h247q44 0 77 4.5t56.5 17.5t38.5 35.5t22 59.5q6 25 6 46q0 38 -25.5 61.5t-102.5 23.5h-267z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="835" 
d="M533 818q154 0 230 -65.5t81 -186.5h-116q-8 74 -57 110t-148 36q-71 0 -122.5 -17t-89 -54t-63 -96.5t-43.5 -143.5q-17 -78 -17 -135q0 -4 1 -36t24.5 -69t71 -54t121.5 -17q47 0 84.5 8t68 25.5t54.5 45.5t44 68h118q-27 -66 -61 -113.5t-79.5 -78.5t-104 -46
t-133.5 -15q-103 0 -172 24.5t-106 77t-40 100.5t-3 63q0 72 20 167q23 105 57.5 181t86.5 125t124 72.5t169 23.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="868" 
d="M50 0l170 802h285q107 0 179 -23.5t111 -74t43.5 -99.5t4.5 -71q0 -65 -17 -148q-22 -102 -55.5 -175.5t-84.5 -120t-122 -68.5t-169 -22h-345zM183 104h221q68 0 119.5 15.5t89 51.5t62 92.5t41.5 137.5q15 72 15 125q0 7 -1 38t-25.5 66.5t-73.5 51.5t-126 16h-195z
" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="746" 
d="M220 802h563l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247h452l-22 -104h-563z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="711" 
d="M220 802h553l-22 -104h-441l-55 -257h408l-21 -103h-409l-72 -338h-111z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="875" 
d="M543 818q159 0 238 -66.5t83 -185.5h-116q-8 72 -59.5 109t-155.5 37q-73 0 -127 -17t-92.5 -54.5t-64.5 -96.5t-44 -143q-16 -76 -16 -132q0 -6 1 -38.5t25.5 -69.5t74 -54t126.5 -17q66 0 115 11t83.5 35.5t56.5 65t34 99.5l4 23h-260l22 104h371l-31 -142
q-17 -79 -48 -136t-79.5 -94t-117 -54.5t-160.5 -17.5q-103 0 -173.5 24.5t-109 77t-42.5 102t-4 68.5q0 71 19 161q23 104 58 180t88.5 125t127.5 72.5t173 23.5z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="897" 
d="M220 802h112l-74 -345h458l73 345h111l-170 -802h-111l75 352h-458l-75 -352h-111z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="329" 
d="M220 802h112l-171 -802h-111z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="404" 
d="M44 -14q-26 0 -44 1.5t-39 6.5l20 98q15 -2 30.5 -3t30.5 -1q33 0 55 6t37 20t24 36.5t16 55.5l127 596h111l-127 -600q-13 -61 -30.5 -102t-44.5 -66.5t-67 -36.5t-99 -11z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="779" 
d="M220 802h111l-73 -346l450 346h158l-497 -382l331 -420h-139l-314 408l-86 -408h-111z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="653" 
d="M220 802h112l-151 -709h399l-20 -93h-510z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1027" 
d="M219 802h158l128 -506h13l344 506h170l-170 -802h-111l136 644l-324 -472h-138l-126 482l-138 -654h-112z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="906" 
d="M219 802h134l305 -657l140 657h111l-171 -802h-136l-303 653l-138 -653h-112z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="895" 
d="M402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q104 0 174 -25t108 -78.5t41.5 -102.5t3.5 -66q0 -71 -20 -163q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54
t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="804" 
d="M220 802h319q84 0 143.5 -16t94 -51.5t40.5 -74t6 -60.5q0 -43 -11 -97q-16 -73 -41.5 -123t-63.5 -80t-92 -43t-128 -13h-274l-52 -244h-111zM310 698l-75 -350h250q47 0 82.5 6.5t61.5 25t43.5 51.5t28.5 86q8 37 8 65q0 11 -3 33.5t-23.5 43.5t-57 30t-90.5 9h-225z
" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="895" 
d="M402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q104 0 174 -25t108 -78.5t41.5 -102.5t3.5 -66q0 -71 -20 -163q-23 -113 -62.5 -191t-99.5 -126l47 -84l-87 -51l-47 85q-83 -32 -202 -32zM414 90
q41 0 74.5 4.5t62.5 15.5l-77 139l88 51l76 -137q38 38 63 96t43 142q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="834" 
d="M220 802h319q84 0 143 -16t93.5 -51t41.5 -74t7 -64q0 -40 -10 -89q-13 -56 -31.5 -98t-44 -71t-59.5 -47t-79 -27l101 -265h-122l-90 256h-273l-55 -256h-111zM310 698l-72 -338h249q47 0 82.5 6t61.5 24t43.5 50t28.5 84q7 33 7 59q0 13 -3.5 35t-24.5 42.5t-58.5 29
t-93.5 8.5h-220z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="789" 
d="M64 136q60 -17 139 -30t167 -13q53 0 90 6t62 18.5t39 33.5t21 51q6 25 6 44q0 7 -1.5 21t-20.5 31.5t-56.5 31t-100.5 25.5q-83 15 -137.5 37t-83.5 54t-32.5 60.5t-3.5 43.5q0 34 9 76q10 44 30 79.5t58 60.5t97 38.5t147 13.5t156 -9.5t131 -26.5l-23 -110
q-63 17 -130 27t-146 10q-54 0 -91.5 -5.5t-62 -17t-37.5 -29.5t-18 -42q-5 -25 -5 -44q0 -7 1.5 -21.5t19.5 -31t54.5 -28t96.5 -23.5q86 -16 142 -36.5t86 -51.5t34 -60t4 -45q0 -36 -10 -82q-12 -52 -34.5 -91t-61.5 -65t-98 -39t-143 -13q-97 0 -177 11.5t-140 29.5z
" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="755" 
d="M411 698h-286l20 104h687l-22 -104h-287l-149 -698h-111z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="869" 
d="M400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5q0 50 13 111l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h112l-106 -498q-19 -87 -46 -148t-70.5 -99.5t-107 -55.5
t-156.5 -17z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="828" 
d="M139 802h113l107 -702h28l400 702h125l-464 -802h-185z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1269" 
d="M147 802h114l33 -702h19l327 702h204l29 -702h20l330 702h122l-380 -802h-197l-33 702h-18l-332 -702h-198z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="832" 
d="M-18 0l389 414l-206 388h129l158 -310l285 310h151l-372 -399l211 -403h-125l-170 326l-298 -326h-152z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="764" 
d="M335 317l-209 485h119l159 -382l322 382h135l-414 -483l-68 -319h-112z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="744" 
d="M0 93l604 604h-450l24 105h612l-20 -94l-603 -603h503l-23 -105h-668z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="368" 
d="M193 802h261l-18 -90h-161l-163 -768h160l-19 -90h-261z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="396" 
d="M122 802h100l75 -948h-100z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="368" 
d="M196 -146h-260l19 90h159l163 768h-159l19 90h260z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M434 802h115l111 -510h-90l-93 420l-271 -420h-100z" />
    <glyph glyph-name="underscore" unicode="_" 
d="M-101 -198l21 100h760l-21 -100h-760z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="570" 
d="M272 802h115l99 -170h-95z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="725" 
d="M290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5
q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="725" 
d="M204 802h106l-57 -269q46 19 92.5 30.5t91.5 11.5q68 0 118 -18t80 -56t35 -77t5 -59q0 -47 -12 -104q-16 -75 -43 -128t-66.5 -86t-91 -48t-116.5 -15q-57 0 -110 15t-102 37l-26 -36h-74zM408 477q-47 0 -92 -12t-84 -29l-66 -314q34 -17 74.5 -28.5t88.5 -11.5
q50 0 87.5 10.5t65.5 33t46 59t29 88.5q9 44 9 77q0 10 -2 34t-21.5 48t-53 34.5t-81.5 10.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="618" 
d="M400 476q-51 0 -89 -10.5t-65.5 -33t-45.5 -59t-29 -88.5q-9 -44 -9 -77q0 -10 2 -34t21 -47.5t53.5 -33.5t83.5 -10q57 0 105 5.5t100 14.5l-17 -93q-42 -12 -93 -19t-106 -7q-76 0 -130.5 16.5t-86 53.5t-36.5 76.5t-5 60.5q0 48 12 108q16 74 43.5 126t69 85.5t97 49
t128.5 15.5q63 0 115 -7.5t97 -18.5l-17 -94q-45 9 -93 15t-105 6z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="725" 
d="M290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q54 0 103 -13.5t96 -33.5l58 274h106l-170 -802h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -76q0 -11 2.5 -35.5t22 -48.5t53 -34.5
t81.5 -10.5q46 0 91 12.5t84 30.5l67 314q-35 17 -75.5 29t-88.5 12q-49 0 -87 -10.5t-65.5 -33.5t-46 -59.5t-29.5 -89.5z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="671" 
d="M394 575q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450q-3 -23 -3 -42q0 -21 5 -45t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93q-50 -12 -103.5 -19t-122.5 -7q-76 0 -130.5 16.5t-86.5 53.5t-37 76.5t-5 60.5q0 48 12 108
q17 74 44.5 126t68 85.5t94.5 49t124 15.5zM390 481q-84 0 -133 -33.5t-75 -113.5h345q3 19 3 35q0 47 -27 79.5t-113 32.5z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="406" 
d="M161 468h-69l16 70l72 21l11 50q11 53 29 92t46.5 65t69.5 39t98 13q30 0 55 -2t52 -5l-19 -92q-20 2 -40 3t-42 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h187l-20 -91h-186l-99 -468h-107z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="721" 
d="M288 -4q-67 0 -116.5 17.5t-79 55t-34.5 75.5t-5 58q0 45 12 102q16 74 43 125.5t66.5 84t90.5 47t115 14.5q56 0 108.5 -14.5t100.5 -37.5l26 36h75l-112 -528q-14 -67 -38.5 -113t-63 -74t-92 -40t-125.5 -12q-34 0 -63.5 1t-58 3.5t-57.5 6.5t-63 11l20 94
q35 -6 64 -10.5t55 -7t50.5 -3.5t50.5 -1q54 0 91 8t61.5 25t38.5 43.5t22 63.5l2 11q-45 -18 -91.5 -29.5t-92.5 -11.5zM171 291q-9 -41 -9 -73q0 -11 2.5 -35t21 -47t52 -33.5t80.5 -10.5t91.5 12.5t83.5 29.5l65 304q-35 17 -74.5 28t-86.5 11q-98 0 -151.5 -42.5
t-74.5 -143.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="743" 
d="M204 802h106l-58 -275q50 21 104 34.5t110 13.5q126 0 168.5 -50.5t42.5 -112.5q0 -26 -6 -56l-76 -356h-106l75 356q4 18 4 33q0 34 -23 59.5t-106 25.5q-54 0 -107 -10.5t-100 -28.5l-92 -435h-106z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="291" 
d="M192 752h112l-20 -98h-113zM153 559h106l-119 -559h-105z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="290" 
d="M198 788h112l-20 -98h-113zM-148 -114q14 -2 32 -3t32 -1q48 0 74.5 20t37.5 74l124 583h107l-124 -583q-10 -51 -26 -87t-40.5 -59t-59.5 -33.5t-83 -10.5q-26 0 -50 1t-43 5z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="666" 
d="M204 802h106l-98 -463l328 220h148l-365 -248l258 -311h-131l-244 310l-66 -310h-106z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="290" 
d="M204 802h106l-170 -802h-106z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1153" 
d="M239 521q51 23 103.5 38.5t107.5 15.5q70 0 115 -18.5t67 -53.5q59 29 122 50.5t130 21.5q63 0 105.5 -15t66.5 -43.5t28 -56t4 -42.5q0 -29 -7 -62l-76 -356h-106l75 356q4 19 4 35q0 33 -20.5 58t-95.5 25q-55 0 -107.5 -16.5t-100.5 -38.5q0 -31 -7 -62l-76 -357h-106
l75 356q4 19 4 35q0 33 -20.5 58t-94.5 25q-54 0 -103.5 -15.5t-95.5 -35.5l-90 -423h-106l119 559h74z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="743" 
d="M238 521q53 23 110.5 38.5t117.5 15.5q126 0 168.5 -50t42.5 -112q0 -27 -6 -57l-76 -356h-106l75 356q4 18 4 33q0 34 -23 59.5t-106 25.5q-56 0 -108.5 -15t-100.5 -36l-90 -423h-106l118 559h75z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="708" 
d="M308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5q75 0 129.5 -17t86 -54.5t37 -76.5t5.5 -60q0 -46 -12 -102q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5zM317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5
q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="725" 
d="M152 559h75l9 -33q50 21 100.5 35t100.5 14q68 0 118 -18t80 -56t35 -77t5 -59q0 -47 -12 -104q-16 -75 -43 -128t-66.5 -86t-91 -48t-116.5 -15q-54 0 -103.5 13.5t-96.5 33.5l-49 -229h-105zM408 477q-47 0 -92 -12t-84 -29l-67 -314q35 -17 75.5 -29t88.5 -12
q50 0 87.5 10.5t65.5 33.5t46 59.5t29 88.5q9 44 9 77q0 10 -2 34t-21.5 48t-53 34.5t-81.5 10.5z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="725" 
d="M290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-162 -757h-105l47 223q-45 -18 -91.5 -29.5t-92.5 -11.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5
t81.5 -10.5q46 0 91 12t84 29l67 315q-35 17 -75.5 28.5t-88.5 11.5q-49 0 -86.5 -10.5t-65.5 -33t-46.5 -59t-29.5 -89.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="418" 
d="M152 559h75l11 -37q48 23 99 34.5t109 12.5l-20 -102q-53 -2 -101.5 -11.5t-93.5 -25.5l-91 -430h-106z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="639" 
d="M33 106q50 -12 114.5 -21t129.5 -9q44 0 74 2.5t50 9.5t31 19.5t15 31.5q3 17 3 29q0 6 -1.5 16t-16 20t-44 17t-78.5 15q-69 9 -113 21.5t-67 33.5t-26 43t-3 35q0 26 6 60q7 38 23 65.5t47 45.5t79.5 26.5t120.5 8.5q68 0 125.5 -7.5t110.5 -18.5l-20 -93
q-51 12 -102.5 19t-113.5 7q-46 0 -77 -3t-50.5 -10t-29 -18.5t-12.5 -29.5q-4 -16 -4 -27q0 -5 1.5 -14t16.5 -17.5t44.5 -14.5t79.5 -14q68 -10 112.5 -23t68 -35t27 -44.5t3.5 -35.5q0 -28 -8 -64q-8 -38 -26 -66t-49.5 -46t-80 -27t-117.5 -9q-73 0 -143 8t-119 22z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="454" 
d="M263 -12q-61 0 -100 12t-59 36t-22 46t-2 31q0 30 8 68l61 287h-69l16 70l72 21l29 134h106l-28 -134h186l-20 -91h-186l-61 -289q-5 -22 -5 -37q0 -4 1 -15t11.5 -22.5t31.5 -16t54 -4.5q40 0 79 6l-21 -96q-20 -2 -39.5 -4t-42.5 -2z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="743" 
d="M279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h107l-119 -559h-74l-11 38q-53 -23 -110.5 -38.5t-117.5 -15.5z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="673" 
d="M83 559h108l97 -478h2l300 478h119l-350 -559h-162z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1030" 
d="M90 559h107l33 -479l255 479h171l49 -479l2 1l236 478h118l-280 -559h-168l-53 478h-2l-257 -478h-168z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="659" 
d="M-39 0l309 292l-169 267h115l125 -202l208 202h136l-294 -280l181 -279h-118l-135 216l-221 -216h-137z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="672" 
d="M83 559h108l91 -460h19l288 460h119l-390 -616q-27 -45 -52 -75t-54.5 -48t-65.5 -26t-85 -8q-23 0 -46 1.5t-46 5.5l19 92q17 -2 33 -3t32 -1q31 0 54.5 3.5t41.5 11.5t32 21.5t27 33.5l34 55h-48z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="640" 
d="M16 74l430 394h-335l20 91h497l-15 -75l-433 -394h361l-19 -90h-522z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="368" 
d="M253 -145h-37q-49 0 -84 9t-55 30.5t-23 45t-3 35.5q0 30 8 68l33 155q5 24 5.5 32.5t0.5 9.5q0 15 -6.5 25t-21 14.5t-39.5 4.5h-25l19 90h26q24 0 40 4.5t27 14.5t17.5 26t11.5 40l42 199q9 41 22.5 68.5t35.5 44.5t54 24t76 7h77l-18 -90h-52q-47 0 -67.5 -13.5
t-28.5 -50.5l-38 -178q-13 -62 -35.5 -97.5t-66.5 -50.5q48 -21 48 -83q0 -23 -6 -51l-33 -153q-6 -25 -6 -42q0 -1 0.5 -10.5t9 -19t26 -13t45.5 -3.5h40z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="329" 
d="M226 802h100l-201 -948h-100z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="368" 
d="M202 -1q-9 -41 -23 -69t-36 -44.5t-53.5 -23.5t-75.5 -7h-77l18 90h51q47 0 67.5 14t28.5 51l38 178q13 61 35.5 96t65.5 50q-47 24 -47 87q0 22 6 49l34 152q6 26 6 43q0 1 -0.5 10.5t-9.5 19.5t-26.5 13.5t-45.5 3.5h-40l19 90h38q49 0 84 -9t55 -30.5t23 -44.5t3 -35
q0 -30 -9 -69l-32 -154q-5 -24 -5.5 -32.5t-0.5 -9.5q0 -15 6.5 -25t21 -14.5t38.5 -4.5h26l-20 -90h-26q-24 0 -40 -4.5t-27 -14.5t-17.5 -26t-11.5 -40z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M70 256q17 88 59 122.5t108 34.5q35 0 64.5 -12t55 -29.5t48.5 -38.5t45 -38.5t44 -29.5t46 -12q36 0 54 25.5t29 79.5l9 47h81l-15 -80q-16 -83 -58 -118.5t-108 -35.5q-35 0 -65 12t-55.5 29.5t-48 38.5t-44.5 38.5t-44 29.5t-46 12q-37 0 -54.5 -25t-28.5 -79l-9 -48
h-82z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="285" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="380" 
d="M277 443h-121l25 116h120zM165 -53l-40 -190h-107l40 190l88 363h85z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="700" 
d="M547 802l-23 -106q50 -2 95.5 -8.5t87.5 -14.5l-21 -101q-48 9 -92 14t-92 7l-81 -383q53 2 101.5 6.5t99.5 13.5l-21 -98q-95 -21 -202 -24l-23 -108h-88l23 110q-71 5 -120 25.5t-76 59t-30.5 75t-3.5 52.5q0 47 12 106q16 75 44 126t70 82.5t99 45.5t130 16l22 104h89
zM203 414q-10 -48 -10 -83q0 -2 0.5 -21t15 -43.5t45 -37.5t79.5 -17l81 381q-49 -2 -84.5 -13t-60.5 -32t-40.5 -54t-25.5 -80z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="700" 
d="M67 96q51 0 74.5 21t33.5 67l37 175h-143l19 89h143l34 160q11 53 30 92.5t51.5 65.5t80 39t116.5 13q68 0 119.5 -8t89.5 -18l-21 -98q-40 11 -84.5 17t-102.5 6q-85 0 -124 -28t-51 -84l-33 -157h308l-19 -89h-308l-35 -165q-8 -33 -22 -57t-37 -41h403l-21 -96h-612
l20 96h54z" />
    <glyph glyph-name="currency" unicode="&#xa4;" 
d="M86 97l108 93q-12 27 -12 62q0 31 9 68q16 82 52 128l-72 95l62 44l69 -91q47 21 116 21q38 0 68 -6.5t53 -20.5l116 99l51 -58l-119 -105q7 -21 7 -49q0 -29 -8 -65q-9 -43 -22 -75.5t-32 -55.5l71 -95l-66 -47l-68 94q-44 -19 -111 -19q-71 0 -113 23l-109 -97z
M362 195q59 0 90 27.5t45 93.5q6 28 6 48q0 29 -15.5 50.5t-74.5 21.5q-58 0 -89.5 -27t-45.5 -93q-6 -27 -6 -48q0 -29 16 -51t74 -22z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="700" 
d="M291 253h-224l19 89h223l26 120h-224l19 89h158l-149 251h117l143 -248l264 248h122l-261 -251h163l-19 -89h-224l-26 -120h225l-19 -89h-224l-54 -253h-109z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="329" 
d="M148 432l78 370h100l-78 -370h-100zM104 224h100l-79 -370h-100z" />
    <glyph glyph-name="section" unicode="&#xa7;" 
d="M45 -70q48 -17 109 -30t130 -13q49 0 83 5t56 16t34 29t18 44q5 21 5 38q0 5 -1.5 18t-14 28.5t-38.5 28t-70 24.5l-107 30q-38 10 -68.5 24.5t-49.5 37t-23.5 45.5t-4.5 38q0 24 6 52q18 88 116 134q-26 29 -30 55.5t-4 41.5q0 30 8 67q9 45 28 77.5t51 54.5t80.5 32.5
t117.5 10.5q71 0 131 -10t108 -24l-20 -94q-43 14 -98 20.5t-119 6.5q-44 0 -75.5 -4.5t-52 -15t-32.5 -27.5t-17 -42q-4 -19 -4 -35q0 -5 1 -17t13.5 -27t39 -26.5t69.5 -23.5l107 -30q38 -10 68.5 -24.5t49.5 -37.5t23.5 -46t4.5 -38q0 -24 -6 -52q-9 -44 -39 -78t-78 -56
q27 -29 30.5 -56t3.5 -41q0 -33 -9 -74q-9 -46 -27.5 -79.5t-52 -56t-84.5 -33.5t-124 -11q-78 0 -145.5 13.5t-117.5 31.5zM419 203q50 -14 82 -31q51 32 63 84q5 23 5 40q0 5 -1.5 18t-10.5 27.5t-24 23t-34 12.5l-142 36q-25 7 -46 14t-38 16q-25 -16 -40.5 -36
t-20.5 -46q-4 -22 -4 -39q0 -5 1 -18t10 -27t24 -22.5t34 -13.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="570" 
d="M242 768h87l-20 -98h-87zM453 768h88l-21 -98h-87z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="895" 
d="M90 432q21 101 54 173.5t85.5 120t128.5 70t183 22.5q103 0 176 -24.5t114 -78.5t46.5 -107.5t5.5 -79.5q0 -69 -18 -158q-21 -100 -55 -173t-87 -120.5t-129 -70t-182 -22.5q-104 0 -177 24.5t-113.5 78.5t-45.5 107t-5 75q0 71 19 163zM777 380q18 82 18 143
q0 12 -2.5 51.5t-32.5 81.5t-85.5 61t-137.5 19t-142 -17.5t-102.5 -55.5t-70 -97.5t-45.5 -143.5q-17 -81 -17 -141q0 -13 2.5 -53t32.5 -82t85.5 -61t136.5 -19q82 0 142 17.5t102.5 55.5t70.5 97.5t45 143.5zM426 145q-112 0 -149.5 51t-37.5 120q0 42 11 97
q14 64 36 110t54.5 76t77 44t103.5 14q88 0 139 -37.5t52 -119.5h-102q-4 35 -28.5 51t-66.5 16q-36 0 -63 -9t-46.5 -29t-32.5 -51.5t-23 -76.5q-8 -39 -8 -68q0 -38 18 -68t73 -30t83 15.5t47 52.5h107q-18 -41 -39 -71t-49.5 -49.5t-66.5 -28.5t-89 -9z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="432" 
d="M248 474q-80 0 -110.5 36t-30.5 87q0 27 7 61q19 90 66 125t120 35q32 0 62 -8t60 -22l18 20h63l-69 -325h-63l-8 18q-31 -13 -59 -20t-56 -7zM206 647q-5 -23 -5 -40q0 -25 14.5 -42t61.5 -17q20 0 41 5t41 13l35 159q-18 8 -37.5 13.5t-40.5 5.5q-50 0 -74.5 -21.5
t-35.5 -75.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="572" 
d="M241 512h111l-207 -234l107 -230h-109l-109 232zM480 512h111l-207 -234l107 -230h-109l-109 232z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M162 472h520l-18 -88h-1l-63 -295h-88l63 295h-432z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="448" 
d="M102 625q11 51 27.5 87.5t43 60t64.5 34.5t90 11q105 0 138 -41.5t33 -98.5q0 -36 -10 -83q-11 -51 -28.5 -87.5t-44 -60t-64.5 -35t-89 -11.5q-106 0 -138 42t-32 98q0 37 10 84zM444 599q8 41 8 71q0 6 -1 25.5t-16 40.5t-42 30.5t-68 9.5t-71 -9t-51.5 -27.5t-35 -48
t-22.5 -71.5q-8 -40 -8 -70q0 -44 23 -76t104 -32q41 0 71 9t51.5 27.5t35 48.5t22.5 72zM229 732h96q48 0 67.5 -17t19.5 -45q0 -14 -4 -31q-14 -62 -69 -72l30 -79h-52l-27 76h-48l-16 -76h-49zM269 687l-17 -79h53q23 0 35 8t17 28q2 10 2 17q0 13 -8.5 19.5t-28.5 6.5
h-53z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="570" 
d="M223 756h330l-15 -73h-330z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="413" 
d="M261 474q-94 0 -126.5 34.5t-32.5 87.5q0 27 7 62q19 87 66 123.5t138 36.5q47 0 80.5 -10t53 -32t23 -45t3.5 -35q0 -28 -8 -62q-19 -87 -66 -123.5t-138 -36.5zM269 546q47 0 70 23t34 77q5 24 5 42q0 23 -11.5 40t-60.5 17q-48 0 -71 -22.5t-34 -76.5q-5 -24 -5 -41
q0 -24 11.5 -41.5t61.5 -17.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M147 402h216l33 157h88l-33 -157h217l-19 -87h-217l-33 -157h-88l33 157h-215zM80 88h520l-18 -88h-521z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="405" 
d="M348 1009q94 0 127 -28t33 -70q0 -17 -4 -36q-5 -23 -12 -38.5t-20 -28.5t-33 -25.5t-51 -28.5l-138 -72q-23 -12 -30.5 -21.5t-10.5 -27.5l-3 -16h243l-16 -77h-339l22 105q4 19 11 33t17.5 26t26 22.5t37.5 21.5l143 74q18 9 28.5 15t16.5 12t9 12t5 15q2 8 2 15
q0 17 -16 27.5t-67 10.5q-35 0 -73 -5t-68 -10l16 80q31 7 64 11t80 4z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="405" 
d="M110 625q33 -8 65.5 -12t72.5 -4q35 0 57 3t35.5 9.5t20 18t9.5 28.5q3 13 3 24q0 19 -12.5 31t-57.5 12h-119l15 77h119q40 0 59.5 10t26.5 41q2 12 2 21q0 22 -15 35t-72 13q-44 0 -75 -3.5t-65 -11.5l16 77q26 6 62 10.5t81 4.5q103 0 135.5 -29t32.5 -73
q0 -19 -5 -42q-9 -44 -32.5 -65.5t-59.5 -30.5q35 -13 46.5 -37t11.5 -47q0 -17 -5 -38q-7 -32 -21 -54t-38.5 -36t-62.5 -20t-94 -6q-51 0 -89 5t-64 12z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="570" 
d="M403 802h124l-191 -170h-104z" />
    <glyph glyph-name="uni00B5" unicode="&#xb5;" 
d="M175 559h106l-77 -366q-2 -13 -2 -24q0 -37 23.5 -62t96.5 -25q100 0 184 37l93 440h106l-119 -559h-73l-10 34q-47 -24 -95 -37t-98 -13q-51 0 -85.5 15t-52.5 43l-51 -240h-106z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" 
d="M298 299q-54 2 -91.5 17.5t-57.5 48t-21.5 61.5t-1.5 37q0 47 14 112q14 61 35.5 104t54.5 70.5t77.5 40t104.5 12.5h336l-201 -948h-98l181 856h-147l-181 -856h-100z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="380" 
d="M106 338h120l-24 -116h-120z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="570" 
d="M203 2h59l-71 -60h12q55 0 77 -16t22 -43q0 -11 -3 -25q-4 -19 -12 -32.5t-23 -22.5t-39 -13t-59 -4q-26 0 -46.5 2.5t-37.5 6.5l11 48q20 -5 37 -7t38 -2q22 0 36 1.5t22 5.5t12 10t5 15q1 5 1 10q0 2 -0.5 6t-6.5 7.5t-18.5 5.5t-33.5 2q-19 0 -36.5 -1t-33.5 -4l10 44
z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="321" 
d="M336 1000h92l-98 -460h-94l76 357l-136 -62l-28 79z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="418" 
d="M261 474q-94 0 -126.5 34.5t-32.5 87.5q0 27 7 62q19 87 66 123.5t138 36.5q47 0 80.5 -10t53 -32t23 -45t3.5 -35q0 -28 -8 -62q-19 -87 -66 -123.5t-138 -36.5zM269 546q47 0 70 23t34 77q5 24 5 42q0 23 -11.5 40t-60.5 17q-48 0 -71 -22.5t-34 -76.5q-5 -24 -5 -41
q0 -24 11.5 -41.5t61.5 -17.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="572" 
d="M94 48h-111l207 233l-106 231h108l110 -232zM333 48h-111l207 233l-106 231h108l110 -232z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="935" 
d="M294 802h92l-98 -460h-94l76 357l-136 -62l-28 79zM757 802h107l-719 -802h-105zM901 262l-55 -262h-93l20 93h-263l15 72l219 295h106l-218 -292h156l20 94h93z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="935" 
d="M294 802h92l-98 -460h-94l76 357l-136 -62l-28 79zM757 802h107l-719 -802h-105zM763 469q94 0 127 -28t33 -70q0 -17 -4 -36q-5 -23 -12 -38.5t-20 -28.5t-33 -25.5t-51 -28.5l-138 -72q-23 -12 -30.5 -21.5t-10.5 -27.5l-3 -16h243l-16 -77h-339l22 105q4 19 11 33
t17.5 26t26 22.5t37.5 21.5l143 74q18 9 28.5 15t16.5 12t9 12t5 15q2 8 2 15q0 17 -16 27.5t-67 10.5q-35 0 -73 -5t-68 -10l16 80q31 7 64 11t80 4z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1019" 
d="M68 427q33 -8 65.5 -12t72.5 -4q35 0 57 3t35.5 9.5t20 18t9.5 28.5q3 13 3 24q0 19 -12.5 31t-57.5 12h-119l15 77h119q40 0 59.5 10t26.5 41q2 12 2 21q0 22 -15 35t-72 13q-44 0 -75 -3.5t-65 -11.5l16 77q26 6 62 10.5t81 4.5q103 0 135.5 -29t32.5 -73q0 -19 -5 -42
q-9 -44 -32.5 -65.5t-59.5 -30.5q35 -13 46.5 -37t11.5 -47q0 -17 -5 -38q-7 -32 -21 -54t-38.5 -36t-62.5 -20t-94 -6q-51 0 -89 5t-64 12zM841 802h107l-719 -802h-105zM985 262l-55 -262h-93l20 93h-263l15 72l219 295h106l-218 -292h156l20 94h93z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="627" 
d="M490 443h-119l24 116h119zM428 173l-14 -70h-115q-55 0 -92.5 -6.5t-62 -21t-38 -37.5t-20.5 -56q-5 -25 -5 -46q0 -10 2.5 -27.5t22.5 -34.5t59 -24.5t100 -7.5q60 0 120.5 7.5t114.5 19.5l-18 -101q-50 -11 -110 -18.5t-125 -7.5q-163 0 -218.5 49.5t-55.5 125.5
q0 30 7 66q11 56 33.5 95.5t59 64t88.5 35.5t122 11h40l37 121h85z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="828" 
d="M396 987h115l80 -140h-98zM433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331l-57 379h-26l-216 -379h299z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="828" 
d="M615 987h126l-157 -140h-110zM433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331l-57 379h-26l-216 -379h299z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="828" 
d="M499 987h133l102 -140h-91l-81 97l-122 -97h-101zM433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331l-57 379h-26l-216 -379h299z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="828" 
d="M350 879q12 52 38 76t72 24q30 0 56 -10.5t48.5 -22.5t41.5 -22.5t34 -10.5q20 0 30 11t14 29l4 21h69l-5 -27q-19 -99 -111 -99q-30 0 -56 10.5t-48.5 23t-41 23t-34.5 10.5q-20 0 -29.5 -11t-14.5 -31l-4 -21h-69zM433 802h184l125 -802h-112l-34 228h-373l-130 -228
h-124zM581 331l-57 379h-26l-216 -379h299z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="828" 
d="M403 960h91l-18 -85h-91zM624 960h91l-18 -85h-91zM433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331l-57 379h-26l-216 -379h299z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="828" 
d="M433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331l-57 379h-26l-216 -379h299z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1242" 
d="M592 227h-331l-185 -227h-137l656 802h683l-21 -104h-453l-51 -242h418l-22 -105h-418l-55 -259h452l-21 -92h-563zM615 331l78 367h-48l-299 -367h269z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="835" 
d="M533 818q154 0 230 -65.5t81 -186.5h-116q-8 74 -57 110t-148 36q-71 0 -122.5 -17t-89 -54t-63 -96.5t-43.5 -143.5q-17 -78 -17 -135q0 -4 1 -36t24.5 -69t71 -54t121.5 -17q47 0 84.5 8t68 25.5t54.5 45.5t44 68h118q-26 -63 -58 -109t-74 -77.5t-96 -47.5t-123 -19
l-149 -182h-98l154 185q-84 7 -139.5 36t-84 81t-30 94.5t-1.5 51.5q0 72 20 166q23 105 57.5 181t86.5 125t124 72.5t169 23.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="746" 
d="M378 987h115l80 -140h-98zM220 802h563l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247h452l-22 -104h-563z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="746" 
d="M597 987h126l-157 -140h-110zM220 802h563l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247h452l-22 -104h-563z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="746" 
d="M481 987h133l102 -140h-91l-81 97l-122 -97h-101zM220 802h563l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247h452l-22 -104h-563z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="746" 
d="M385 960h91l-18 -85h-91zM606 960h91l-18 -85h-91zM220 802h563l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247h452l-22 -104h-563z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="329" 
d="M147 987h115l80 -140h-98zM220 802h112l-171 -802h-111z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="329" 
d="M366 987h126l-157 -140h-110zM220 802h112l-171 -802h-111z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="329" 
d="M250 987h133l102 -140h-91l-81 97l-122 -97h-101zM220 802h112l-171 -802h-111z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="329" 
d="M154 960h91l-18 -85h-91zM375 960h91l-18 -85h-91zM220 802h112l-171 -802h-111z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="868" 
d="M37 456h110l73 346h285q107 0 179 -23.5t111 -74t43.5 -99.5t4.5 -71q0 -65 -17 -148q-22 -102 -55.5 -175.5t-84.5 -120t-122 -68.5t-169 -22h-345l74 351h-107zM183 104h221q68 0 119.5 15.5t89 51.5t62 92.5t41.5 137.5q15 72 15 125q0 7 -1 38t-25.5 66.5t-73.5 51.5
t-126 16h-195l-52 -242h199l-22 -105h-199z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="906" 
d="M388 879q12 52 38 76t72 24q30 0 56 -10.5t48.5 -22.5t41.5 -22.5t34 -10.5q20 0 30 11t14 29l4 21h69l-5 -27q-19 -99 -111 -99q-30 0 -56 10.5t-48.5 23t-41 23t-34.5 10.5q-20 0 -29.5 -11t-14.5 -31l-4 -21h-69zM219 802h134l305 -657l140 657h111l-171 -802h-136
l-303 653l-138 -653h-112z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="895" 
d="M430 987h115l80 -140h-98zM402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q104 0 174 -25t108 -78.5t41.5 -102.5t3.5 -66q0 -71 -20 -163q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5z
M414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="895" 
d="M649 987h126l-157 -140h-110zM402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q104 0 174 -25t108 -78.5t41.5 -102.5t3.5 -66q0 -71 -20 -163q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5z
M414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="895" 
d="M533 987h133l102 -140h-91l-81 97l-122 -97h-101zM402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q104 0 174 -25t108 -78.5t41.5 -102.5t3.5 -66q0 -71 -20 -163q-22 -106 -58 -181.5t-90 -124
t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="895" 
d="M384 879q12 52 38 76t72 24q30 0 56 -10.5t48.5 -22.5t41.5 -22.5t34 -10.5q20 0 30 11t14 29l4 21h69l-5 -27q-19 -99 -111 -99q-30 0 -56 10.5t-48.5 23t-41 23t-34.5 10.5q-20 0 -29.5 -11t-14.5 -31l-4 -21h-69zM402 -16q-103 0 -173 25t-107.5 78t-41 102.5
t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q104 0 174 -25t108 -78.5t41.5 -102.5t3.5 -66q0 -71 -20 -163q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5
t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="895" 
d="M437 960h91l-18 -85h-91zM658 960h91l-18 -85h-91zM402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q104 0 174 -25t108 -78.5t41.5 -102.5t3.5 -66q0 -71 -20 -163q-22 -106 -58 -181.5t-90 -124
t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M88 87l232 199l-145 196l71 58l143 -194l229 196l54 -68l-231 -199l147 -199l-72 -57l-145 196l-228 -196z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="895" 
d="M18 56l81 71q-22 54 -22 130q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q169 0 249 -68l76 68l60 -72l-81 -70q23 -54 23 -130q0 -72 -20 -163q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5q-85 0 -146 16t-102 51l-76 -67zM205 401
q-16 -74 -16 -129q0 -33 6 -59l511 457q-52 42 -172 42q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5zM414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 75 16 129q0 34 -6 60l-510 -460q55 -40 170 -40z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="869" 
d="M417 987h115l80 -140h-98zM400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5q0 50 13 111l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h112l-106 -498q-19 -87 -46 -148
t-70.5 -99.5t-107 -55.5t-156.5 -17z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="869" 
d="M636 987h126l-157 -140h-110zM400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5q0 50 13 111l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h112l-106 -498q-19 -87 -46 -148
t-70.5 -99.5t-107 -55.5t-156.5 -17z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="869" 
d="M520 987h133l102 -140h-91l-81 97l-122 -97h-101zM400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5q0 50 13 111l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h112l-106 -498
q-19 -87 -46 -148t-70.5 -99.5t-107 -55.5t-156.5 -17z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="869" 
d="M424 960h91l-18 -85h-91zM645 960h91l-18 -85h-91zM400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5q0 50 13 111l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h112l-106 -498
q-19 -87 -46 -148t-70.5 -99.5t-107 -55.5t-156.5 -17z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="764" 
d="M584 987h126l-157 -140h-110zM335 317l-209 485h119l159 -382l322 382h135l-414 -483l-68 -319h-112z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="804" 
d="M332 802l-26 -122h208q84 0 143.5 -16t94 -51.5t40.5 -74t6 -60.5q0 -43 -11 -97q-16 -72 -41 -120.5t-63.5 -78t-93 -42t-128.5 -12.5h-273l-27 -128h-111l170 802h112zM284 575l-73 -342h249q47 0 82.5 6t61.5 24t43.5 49.5t28.5 82.5q8 37 8 65q0 11 -3 33.5t-24 43.5
t-57.5 29.5t-90.5 8.5h-225z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="710" 
d="M363 -4q-38 0 -63 2t-53 7l20 94q22 -4 41.5 -6t45.5 -2q41 0 71 6.5t51 21.5t34.5 40t22.5 63q6 29 6 52q0 41 -25.5 70t-112.5 29h-76l20 88h73q40 0 69 7t49.5 22t33.5 39t20 59q5 23 5 41q0 41 -28.5 67t-116.5 26q-45 0 -77 -7t-53.5 -22.5t-34 -39.5t-19.5 -58
l-126 -595h-106l126 595q12 54 32 96t54.5 70t86.5 42.5t127 14.5q243 0 243 -168q0 -29 -7 -62q-17 -77 -55.5 -119t-104.5 -55q31 -7 54.5 -23t37 -42t15 -48t1.5 -29q0 -32 -9 -73q-11 -55 -32 -93.5t-54 -63t-78.5 -35.5t-107.5 -11z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="725" 
d="M272 802h115l99 -170h-95zM290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5
t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="725" 
d="M556 796h124l-191 -170h-104zM290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5
t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="725" 
d="M417 802h116l89 -170h-92l-62 118l-113 -118h-100zM290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285
q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="725" 
d="M254 675q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70zM290 -16q-67 0 -117.5 18t-80.5 56
t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314
q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="725" 
d="M318 768h87l-20 -98h-87zM529 768h88l-21 -98h-87zM290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285
q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="725" 
d="M440 617q-67 0 -97 22.5t-30 59.5q0 14 4 31q13 60 50.5 85t110.5 25q34 0 59.5 -6t42 -19.5t20.5 -29.5t4 -28q0 -14 -3 -30q-14 -60 -51.5 -85t-109.5 -25zM444 677q38 0 59.5 11t28.5 40q2 9 2 16q0 17 -12.5 26.5t-48.5 9.5q-38 0 -59 -11t-27 -40q-2 -10 -2 -18
q0 -16 12 -25t47 -9zM290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5
t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1131" 
d="M254 -16q-56 0 -98.5 11.5t-69 37t-32.5 53.5t-6 47q0 27 7 59q17 76 72 108t163 32h227l8 38q3 17 3 31q0 9 -2 23t-19 28.5t-50.5 21.5t-90.5 7q-54 0 -105.5 -5t-105.5 -13l17 94q29 4 52 7.5t46.5 5.5t49.5 3.5t60 1.5q102 0 156 -23.5t72 -68.5q39 48 96 70t137 22
q141 0 186 -59.5t45 -137.5q0 -39 -9 -84l-9 -48h-449q-3 -23 -3 -42q0 -20 5 -44.5t24.5 -43.5t53 -27t80.5 -8q33 0 62 1.5t56 4t54 6.5t57 10l-15 -93q-50 -13 -103.5 -19.5t-122.5 -6.5q-78 0 -133.5 17.5t-87.5 58.5q-59 -37 -124.5 -56.5t-153.5 -19.5zM833 498
q-43 0 -76 -9t-58 -28.5t-43.5 -51.5t-31.5 -77h346q4 25 4 47q0 48 -26.5 83.5t-114.5 35.5zM270 68q71 0 125 15.5t106 44.5q-5 25 -6 43t-1 25q0 23 4 50h-204q-65 0 -97 -15.5t-40 -54.5q-3 -17 -3 -32q0 -35 25 -55.5t91 -20.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="618" 
d="M400 476q-51 0 -89 -10.5t-65.5 -33t-45.5 -59t-29 -88.5q-9 -44 -9 -77q0 -10 2 -34t21 -47.5t53.5 -33.5t83.5 -10q57 0 105 5.5t100 14.5l-17 -93q-75 -21 -171 -26l-149 -182h-98l154 185q-58 7 -100 27.5t-65 58t-25.5 71.5t-2.5 48q0 47 12 107q16 74 43.5 126
t69 85.5t97 49t128.5 15.5q63 0 115 -7.5t97 -18.5l-17 -94q-45 9 -93 15t-105 6z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="671" 
d="M257 802h115l99 -170h-95zM394 575q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450q-3 -23 -3 -42q0 -21 5 -45t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93q-50 -12 -103.5 -19t-122.5 -7q-76 0 -130.5 16.5t-86.5 53.5t-37 76.5
t-5 60.5q0 48 12 108q17 74 44.5 126t68 85.5t94.5 49t124 15.5zM390 481q-84 0 -133 -33.5t-75 -113.5h345q3 19 3 35q0 47 -27 79.5t-113 32.5z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="671" 
d="M541 796h124l-191 -170h-104zM394 575q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450q-3 -23 -3 -42q0 -21 5 -45t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93q-50 -12 -103.5 -19t-122.5 -7q-76 0 -130.5 16.5t-86.5 53.5t-37 76.5
t-5 60.5q0 48 12 108q17 74 44.5 126t68 85.5t94.5 49t124 15.5zM390 481q-84 0 -133 -33.5t-75 -113.5h345q3 19 3 35q0 47 -27 79.5t-113 32.5z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="671" 
d="M402 802h116l89 -170h-92l-62 118l-113 -118h-100zM394 575q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450q-3 -23 -3 -42q0 -21 5 -45t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93q-50 -12 -103.5 -19t-122.5 -7q-76 0 -130.5 16.5
t-86.5 53.5t-37 76.5t-5 60.5q0 48 12 108q17 74 44.5 126t68 85.5t94.5 49t124 15.5zM390 481q-84 0 -133 -33.5t-75 -113.5h345q3 19 3 35q0 47 -27 79.5t-113 32.5z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="671" 
d="M303 768h87l-20 -98h-87zM514 768h88l-21 -98h-87zM394 575q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450q-3 -23 -3 -42q0 -21 5 -45t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93q-50 -12 -103.5 -19t-122.5 -7q-76 0 -130.5 16.5
t-86.5 53.5t-37 76.5t-5 60.5q0 48 12 108q17 74 44.5 126t68 85.5t94.5 49t124 15.5zM390 481q-84 0 -133 -33.5t-75 -113.5h345q3 19 3 35q0 47 -27 79.5t-113 32.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="291" 
d="M54 802h115l99 -170h-95zM153 559h106l-119 -559h-105z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="291" 
d="M338 796h124l-191 -170h-104zM153 559h106l-119 -559h-105z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="291" 
d="M199 802h116l89 -170h-92l-62 118l-113 -118h-100zM153 559h106l-119 -559h-105z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="291" 
d="M100 768h87l-20 -98h-87zM311 768h88l-21 -98h-87zM153 559h106l-119 -559h-105z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="703" 
d="M263 670l154 48q-45 41 -110 84h132q20 -14 38 -27t35 -27l177 55l18 -66l-140 -44q71 -81 83 -149.5t12 -111.5q0 -67 -17 -146q-18 -82 -45.5 -139.5t-67.5 -93.5t-94 -52.5t-125 -16.5q-76 0 -131.5 16.5t-88 54t-37.5 76.5t-5 58q0 49 14 112q29 141 106.5 207.5
t215.5 66.5q39 0 76 -7t72 -20q-20 60 -65 115l-187 -59zM539 298q8 41 11.5 71t3.5 53q0 11 -1 22q-33 15 -72 25t-85 10q-48 0 -85 -11t-65 -35t-47 -63t-31 -95q-9 -40 -9 -71q0 -50 28 -86.5t131 -36.5q48 0 84 12t62 38t44.5 67.5t30.5 99.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="743" 
d="M262 675q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70zM238 521q53 23 110.5 38.5t117.5 15.5
q126 0 168.5 -50t42.5 -112q0 -27 -6 -57l-76 -356h-106l75 356q4 18 4 33q0 34 -23 59.5t-106 25.5q-56 0 -108.5 -15t-100.5 -36l-90 -423h-106l118 559h75z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="708" 
d="M263 802h115l99 -170h-95zM308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5q75 0 129.5 -17t86 -54.5t37 -76.5t5.5 -60q0 -46 -12 -102q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5zM317 81q50 0 87 11
t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="708" 
d="M547 796h124l-191 -170h-104zM308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5q75 0 129.5 -17t86 -54.5t37 -76.5t5.5 -60q0 -46 -12 -102q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5zM317 81q50 0 87 11
t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="708" 
d="M408 802h116l89 -170h-92l-62 118l-113 -118h-100zM308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5q75 0 129.5 -17t86 -54.5t37 -76.5t5.5 -60q0 -46 -12 -102q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5z
M317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="708" 
d="M245 675q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70zM308 -16q-74 0 -128.5 17.5t-86 55
t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5q75 0 129.5 -17t86 -54.5t37 -76.5t5.5 -60q0 -46 -12 -102q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5zM317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5
q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="708" 
d="M309 768h87l-20 -98h-87zM520 768h88l-21 -98h-87zM308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5q75 0 129.5 -17t86 -54.5t37 -76.5t5.5 -60q0 -46 -12 -102q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5z
M317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M385 529h97l-19 -96h-97zM131 323h521l-20 -87h-521zM300 125h97l-20 -95h-97z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="708" 
d="M6 46l62 52q-15 36 -16 63.5t-1 33.5q0 44 12 99q16 76 43 129.5t68 87t97 49t130 15.5q63 0 111 -11.5t81 -37.5l62 49l50 -62l-63 -52q17 -39 17 -93q0 -46 -12 -103q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5q-62 0 -109.5 11.5t-79.5 37.5l-62 -49zM169 279
q-9 -45 -9 -77q0 -17 2 -30l342 281q-37 24 -111 24q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5zM317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q8 42 8 74q0 17 -2 32l-341 -281q37 -23 111 -23z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="743" 
d="M281 802h115l99 -170h-95zM279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h107l-119 -559h-74l-11 38q-53 -23 -110.5 -38.5t-117.5 -15.5z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="743" 
d="M564 796h124l-191 -170h-104zM279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h107l-119 -559h-74l-11 38q-53 -23 -110.5 -38.5t-117.5 -15.5z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="743" 
d="M426 802h116l89 -170h-92l-62 118l-113 -118h-100zM279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h107l-119 -559h-74l-11 38q-53 -23 -110.5 -38.5t-117.5 -15.5z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="743" 
d="M327 768h87l-20 -98h-87zM538 768h88l-21 -98h-87zM279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h107l-119 -559h-74l-11 38q-53 -23 -110.5 -38.5t-117.5 -15.5z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="672" 
d="M529 796h124l-191 -170h-104zM83 559h108l91 -460h19l288 460h119l-390 -616q-27 -45 -52 -75t-54.5 -48t-65.5 -26t-85 -8q-23 0 -46 1.5t-46 5.5l19 92q17 -2 33 -3t32 -1q31 0 54.5 3.5t41.5 11.5t32 21.5t27 33.5l34 55h-48z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="725" 
d="M204 802h106l-57 -269q46 19 92.5 30.5t91.5 11.5q68 0 118 -18t80 -56t35 -77t5 -59q0 -47 -12 -104q-16 -75 -43 -128t-66.5 -86t-91 -48t-116.5 -15q-54 0 -103.5 13.5t-96.5 33.5l-49 -229h-105zM408 475q-47 0 -92 -12t-84 -29l-66 -309q34 -17 74.5 -29t88.5 -12
q50 0 87.5 10.5t65.5 33t46 58.5t29 87q9 43 9 75q0 11 -2.5 35t-21.5 47.5t-52.5 34t-81.5 10.5z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="672" 
d="M291 768h87l-20 -98h-87zM502 768h88l-21 -98h-87zM83 559h108l91 -460h19l288 460h119l-390 -616q-27 -45 -52 -75t-54.5 -48t-65.5 -26t-85 -8q-23 0 -46 1.5t-46 5.5l19 92q17 -2 33 -3t32 -1q31 0 54.5 3.5t41.5 11.5t32 21.5t27 33.5l34 55h-48z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="828" 
d="M381 949h352l-15 -68h-352zM433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331l-57 379h-26l-216 -379h299z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="725" 
d="M300 756h330l-15 -73h-330zM290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5
t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="828" 
d="M539 842q-53 0 -88.5 8t-55.5 25.5t-23 36t-3 30.5q0 20 5 45h82q-3 -15 -3 -26q0 -4 1 -13t11 -17.5t30 -12.5t53 -4q65 0 92.5 16.5t35.5 56.5h81q-8 -39 -22 -66.5t-38.5 -45t-62.5 -25.5t-95 -8zM433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331
l-57 379h-26l-216 -379h299z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="725" 
d="M446 643q-96 0 -126.5 28t-30.5 72q0 20 5 44h82q-4 -17 -4 -30q0 -1 0.5 -9t9 -17t26 -13t46.5 -4q57 0 80.5 16.5t31.5 56.5h83q-8 -39 -21 -66.5t-36 -44.5t-58 -25t-88 -8zM290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86
t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33
t-46 -59t-29.5 -89.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="828" 
d="M433 802h184l125 -802h-37q-33 -19 -54.5 -33t-34 -25t-18.5 -21.5t-9 -22.5q-1 -7 -1 -13q0 -14 10.5 -23t42.5 -9q21 0 38.5 2.5t33.5 4.5l-14 -64q-19 -5 -39 -7.5t-46 -2.5q-63 0 -86.5 19.5t-23.5 50.5q0 11 3 25q3 17 11 32t22.5 30t36 31t52.5 35l-33 219h-373
l-130 -228h-124zM581 331l-57 379h-26l-216 -379h299z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="725" 
d="M290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-16q-33 -19 -54.5 -33t-34 -25t-18.5 -21.5t-9 -22.5q-1 -7 -1 -13q0 -14 10.5 -23t42.5 -9q21 0 38.5 2.5
t33.5 4.5l-14 -64q-19 -5 -39 -7.5t-46 -2.5q-63 0 -86.5 19.5t-23.5 50.5q0 11 3 25q4 18 12.5 33.5t24.5 31.5t40 33t58 38l-5 15q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314
q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="835" 
d="M633 987h126l-157 -140h-110zM533 818q154 0 230 -65.5t81 -186.5h-116q-8 74 -57 110t-148 36q-71 0 -122.5 -17t-89 -54t-63 -96.5t-43.5 -143.5q-17 -78 -17 -135q0 -4 1 -36t24.5 -69t71 -54t121.5 -17q47 0 84.5 8t68 25.5t54.5 45.5t44 68h118q-27 -66 -61 -113.5
t-79.5 -78.5t-104 -46t-133.5 -15q-103 0 -172 24.5t-106 77t-40 100.5t-3 63q0 72 20 167q23 105 57.5 181t86.5 125t124 72.5t169 23.5z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="618" 
d="M533 796h124l-191 -170h-104zM400 476q-51 0 -89 -10.5t-65.5 -33t-45.5 -59t-29 -88.5q-9 -44 -9 -77q0 -10 2 -34t21 -47.5t53.5 -33.5t83.5 -10q57 0 105 5.5t100 14.5l-17 -93q-42 -12 -93 -19t-106 -7q-76 0 -130.5 16.5t-86 53.5t-36.5 76.5t-5 60.5q0 48 12 108
q16 74 43.5 126t69 85.5t97 49t128.5 15.5q63 0 115 -7.5t97 -18.5l-17 -94q-45 9 -93 15t-105 6z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="835" 
d="M517 987h133l102 -140h-91l-81 97l-122 -97h-101zM533 818q154 0 230 -65.5t81 -186.5h-116q-8 74 -57 110t-148 36q-71 0 -122.5 -17t-89 -54t-63 -96.5t-43.5 -143.5q-17 -78 -17 -135q0 -4 1 -36t24.5 -69t71 -54t121.5 -17q47 0 84.5 8t68 25.5t54.5 45.5t44 68h118
q-27 -66 -61 -113.5t-79.5 -78.5t-104 -46t-133.5 -15q-103 0 -172 24.5t-106 77t-40 100.5t-3 63q0 72 20 167q23 105 57.5 181t86.5 125t124 72.5t169 23.5z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="618" 
d="M395 802h116l89 -170h-92l-62 118l-113 -118h-100zM400 476q-51 0 -89 -10.5t-65.5 -33t-45.5 -59t-29 -88.5q-9 -44 -9 -77q0 -10 2 -34t21 -47.5t53.5 -33.5t83.5 -10q57 0 105 5.5t100 14.5l-17 -93q-42 -12 -93 -19t-106 -7q-76 0 -130.5 16.5t-86 53.5t-36.5 76.5
t-5 60.5q0 48 12 108q16 74 43.5 126t69 85.5t97 49t128.5 15.5q63 0 115 -7.5t97 -18.5l-17 -94q-45 9 -93 15t-105 6z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="835" 
d="M521 960h112l-18 -85h-112zM533 818q154 0 230 -65.5t81 -186.5h-116q-8 74 -57 110t-148 36q-71 0 -122.5 -17t-89 -54t-63 -96.5t-43.5 -143.5q-17 -78 -17 -135q0 -4 1 -36t24.5 -69t71 -54t121.5 -17q47 0 84.5 8t68 25.5t54.5 45.5t44 68h118q-27 -66 -61 -113.5
t-79.5 -78.5t-104 -46t-133.5 -15q-103 0 -172 24.5t-106 77t-40 100.5t-3 63q0 72 20 167q23 105 57.5 181t86.5 125t124 72.5t169 23.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="618" 
d="M389 768h112l-20 -98h-113zM400 476q-51 0 -89 -10.5t-65.5 -33t-45.5 -59t-29 -88.5q-9 -44 -9 -77q0 -10 2 -34t21 -47.5t53.5 -33.5t83.5 -10q57 0 105 5.5t100 14.5l-17 -93q-42 -12 -93 -19t-106 -7q-76 0 -130.5 16.5t-86 53.5t-36.5 76.5t-5 60.5q0 48 12 108
q16 74 43.5 126t69 85.5t97 49t128.5 15.5q63 0 115 -7.5t97 -18.5l-17 -94q-45 9 -93 15t-105 6z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="835" 
d="M386 987h92l80 -97l123 97h101l-162 -140h-132zM533 818q154 0 230 -65.5t81 -186.5h-116q-8 74 -57 110t-148 36q-71 0 -122.5 -17t-89 -54t-63 -96.5t-43.5 -143.5q-17 -78 -17 -135q0 -4 1 -36t24.5 -69t71 -54t121.5 -17q47 0 84.5 8t68 25.5t54.5 45.5t44 68h118
q-27 -66 -61 -113.5t-79.5 -78.5t-104 -46t-133.5 -15q-103 0 -172 24.5t-106 77t-40 100.5t-3 63q0 72 20 167q23 105 57.5 181t86.5 125t124 72.5t169 23.5z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="618" 
d="M269 802h92l62 -118l113 118h100l-161 -170h-116zM400 476q-51 0 -89 -10.5t-65.5 -33t-45.5 -59t-29 -88.5q-9 -44 -9 -77q0 -10 2 -34t21 -47.5t53.5 -33.5t83.5 -10q57 0 105 5.5t100 14.5l-17 -93q-42 -12 -93 -19t-106 -7q-76 0 -130.5 16.5t-86 53.5t-36.5 76.5
t-5 60.5q0 48 12 108q16 74 43.5 126t69 85.5t97 49t128.5 15.5q63 0 115 -7.5t97 -18.5l-17 -94q-45 9 -93 15t-105 6z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="868" 
d="M388 987h92l80 -97l123 97h101l-162 -140h-132zM50 0l170 802h285q107 0 179 -23.5t111 -74t43.5 -99.5t4.5 -71q0 -65 -17 -148q-22 -102 -55.5 -175.5t-84.5 -120t-122 -68.5t-169 -22h-345zM183 104h221q68 0 119.5 15.5t89 51.5t62 92.5t41.5 137.5q15 72 15 125
q0 7 -1 38t-25.5 66.5t-73.5 51.5t-126 16h-195z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="725" 
d="M290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q54 0 103 -13.5t96 -33.5l58 274h106l-170 -802h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM866 802h96l-155 -255h-57zM170 285q-9 -43 -9 -76q0 -11 2.5 -35.5
t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 30.5l67 314q-35 17 -75.5 29t-88.5 12q-49 0 -87 -10.5t-65.5 -33.5t-46 -59.5t-29.5 -89.5z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="868" 
d="M37 456h110l73 346h285q107 0 179 -23.5t111 -74t43.5 -99.5t4.5 -71q0 -65 -17 -148q-22 -102 -55.5 -175.5t-84.5 -120t-122 -68.5t-169 -22h-345l74 351h-107zM183 104h221q68 0 119.5 15.5t89 51.5t62 92.5t41.5 137.5q15 72 15 125q0 7 -1 38t-25.5 66.5t-73.5 51.5
t-126 16h-195l-52 -242h199l-22 -105h-199z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="725" 
d="M290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q54 0 103 -13.5t96 -33.5l23 107h-205l19 88h204l17 79h106l-17 -79h93l-19 -88h-92l-135 -635h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -76
q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 30.5l67 314q-35 17 -75.5 29t-88.5 12q-49 0 -87 -10.5t-65.5 -33.5t-46 -59.5t-29.5 -89.5z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="746" 
d="M363 949h352l-15 -68h-352zM220 802h563l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247h452l-22 -104h-563z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="671" 
d="M285 756h330l-15 -73h-330zM394 575q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450q-3 -23 -3 -42q0 -21 5 -45t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93q-50 -12 -103.5 -19t-122.5 -7q-76 0 -130.5 16.5t-86.5 53.5t-37 76.5
t-5 60.5q0 48 12 108q17 74 44.5 126t68 85.5t94.5 49t124 15.5zM390 481q-84 0 -133 -33.5t-75 -113.5h345q3 19 3 35q0 47 -27 79.5t-113 32.5z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="746" 
d="M521 842q-53 0 -88.5 8t-55.5 25.5t-23 36t-3 30.5q0 20 5 45h82q-3 -15 -3 -26q0 -4 1 -13t11 -17.5t30 -12.5t53 -4q65 0 92.5 16.5t35.5 56.5h81q-8 -39 -22 -66.5t-38.5 -45t-62.5 -25.5t-95 -8zM220 802h563l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247h452
l-22 -104h-563z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="671" 
d="M431 643q-96 0 -126.5 28t-30.5 72q0 20 5 44h82q-4 -17 -4 -30q0 -1 0.5 -9t9 -17t26 -13t46.5 -4q57 0 80.5 16.5t31.5 56.5h83q-8 -39 -21 -66.5t-36 -44.5t-58 -25t-88 -8zM394 575q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450q-3 -23 -3 -42q0 -21 5 -45
t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93q-50 -12 -103.5 -19t-122.5 -7q-76 0 -130.5 16.5t-86.5 53.5t-37 76.5t-5 60.5q0 48 12 108q17 74 44.5 126t68 85.5t94.5 49t124 15.5zM390 481q-84 0 -133 -33.5t-75 -113.5h345q3 19 3 35
q0 47 -27 79.5t-113 32.5z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="746" 
d="M485 960h112l-18 -85h-112zM220 802h563l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247h452l-22 -104h-563z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="671" 
d="M396 768h112l-20 -98h-113zM394 575q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450q-3 -23 -3 -42q0 -21 5 -45t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93q-50 -12 -103.5 -19t-122.5 -7q-76 0 -130.5 16.5t-86.5 53.5t-37 76.5
t-5 60.5q0 48 12 108q17 74 44.5 126t68 85.5t94.5 49t124 15.5zM390 481q-84 0 -133 -33.5t-75 -113.5h345q3 19 3 35q0 47 -27 79.5t-113 32.5z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="746" 
d="M220 802h563l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247h452l-22 -104h-38q-33 -19 -54.5 -33t-34 -25t-18.5 -21.5t-9 -22.5q-1 -7 -1 -13q0 -14 10.5 -23t42.5 -9q21 0 38.5 2.5t33.5 4.5l-14 -64q-19 -5 -39 -7.5t-46 -2.5q-63 0 -86.5 19.5t-23.5 50.5
q0 11 3 25q7 31 30.5 58t77.5 61h-435z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="671" 
d="M380 -102q-1 -7 -1 -13q0 -14 10.5 -23t42.5 -9q21 0 38.5 2.5t33.5 4.5l-14 -64q-19 -5 -39 -7.5t-46 -2.5q-63 0 -86.5 19.5t-23.5 50.5q0 11 3 25q6 29 25.5 53t61.5 53q-34 -3 -75 -3q-76 0 -130.5 16.5t-86.5 53.5t-37 76.5t-5 60.5q0 48 12 108q17 74 44.5 126
t68 85.5t94.5 49t124 15.5q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450q-3 -23 -3 -42q0 -21 5 -45t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93l-37 -8q-34 -20 -56 -34t-35 -25.5t-19 -22t-9 -22.5zM390 481q-84 0 -133 -33.5
t-75 -113.5h345q3 19 3 35q0 47 -27 79.5t-113 32.5z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="746" 
d="M350 987h92l80 -97l123 97h101l-162 -140h-132zM220 802h563l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247h452l-22 -104h-563z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="671" 
d="M276 802h92l62 -118l113 118h100l-161 -170h-116zM394 575q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450q-3 -23 -3 -42q0 -21 5 -45t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93q-50 -12 -103.5 -19t-122.5 -7q-76 0 -130.5 16.5
t-86.5 53.5t-37 76.5t-5 60.5q0 48 12 108q17 74 44.5 126t68 85.5t94.5 49t124 15.5zM390 481q-84 0 -133 -33.5t-75 -113.5h345q3 19 3 35q0 47 -27 79.5t-113 32.5z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="875" 
d="M528 987h133l102 -140h-91l-81 97l-122 -97h-101zM543 818q159 0 238 -66.5t83 -185.5h-116q-8 72 -59.5 109t-155.5 37q-73 0 -127 -17t-92.5 -54.5t-64.5 -96.5t-44 -143q-16 -76 -16 -132q0 -6 1 -38.5t25.5 -69.5t74 -54t126.5 -17q66 0 115 11t83.5 35.5t56.5 65
t34 99.5l4 23h-260l22 104h371l-31 -142q-17 -79 -48 -136t-79.5 -94t-117 -54.5t-160.5 -17.5q-103 0 -173.5 24.5t-109 77t-42.5 102t-4 68.5q0 71 19 161q23 104 58 180t88.5 125t127.5 72.5t173 23.5z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="721" 
d="M424 802h116l89 -170h-92l-62 118l-113 -118h-100zM288 -4q-67 0 -116.5 17.5t-79 55t-34.5 75.5t-5 58q0 45 12 102q16 74 43 125.5t66.5 84t90.5 47t115 14.5q56 0 108.5 -14.5t100.5 -37.5l26 36h75l-112 -528q-14 -67 -38.5 -113t-63 -74t-92 -40t-125.5 -12
q-34 0 -63.5 1t-58 3.5t-57.5 6.5t-63 11l20 94q35 -6 64 -10.5t55 -7t50.5 -3.5t50.5 -1q54 0 91 8t61.5 25t38.5 43.5t22 63.5l2 11q-45 -18 -91.5 -29.5t-92.5 -11.5zM171 291q-9 -41 -9 -73q0 -11 2.5 -35t21 -47t52 -33.5t80.5 -10.5t91.5 12.5t83.5 29.5l65 304
q-35 17 -74.5 28t-86.5 11q-98 0 -151.5 -42.5t-74.5 -143.5z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="875" 
d="M568 842q-53 0 -88.5 8t-55.5 25.5t-23 36t-3 30.5q0 20 5 45h82q-3 -15 -3 -26q0 -4 1 -13t11 -17.5t30 -12.5t53 -4q65 0 92.5 16.5t35.5 56.5h81q-8 -39 -22 -66.5t-38.5 -45t-62.5 -25.5t-95 -8zM543 818q159 0 238 -66.5t83 -185.5h-116q-8 72 -59.5 109t-155.5 37
q-73 0 -127 -17t-92.5 -54.5t-64.5 -96.5t-44 -143q-16 -76 -16 -132q0 -6 1 -38.5t25.5 -69.5t74 -54t126.5 -17q66 0 115 11t83.5 35.5t56.5 65t34 99.5l4 23h-260l22 104h371l-31 -142q-17 -79 -48 -136t-79.5 -94t-117 -54.5t-160.5 -17.5q-103 0 -173.5 24.5t-109 77
t-42.5 102t-4 68.5q0 71 19 161q23 104 58 180t88.5 125t127.5 72.5t173 23.5z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="721" 
d="M453 643q-96 0 -126.5 28t-30.5 72q0 20 5 44h82q-4 -17 -4 -30q0 -1 0.5 -9t9 -17t26 -13t46.5 -4q57 0 80.5 16.5t31.5 56.5h83q-8 -39 -21 -66.5t-36 -44.5t-58 -25t-88 -8zM288 -4q-67 0 -116.5 17.5t-79 55t-34.5 75.5t-5 58q0 45 12 102q16 74 43 125.5t66.5 84
t90.5 47t115 14.5q56 0 108.5 -14.5t100.5 -37.5l26 36h75l-112 -528q-14 -67 -38.5 -113t-63 -74t-92 -40t-125.5 -12q-34 0 -63.5 1t-58 3.5t-57.5 6.5t-63 11l20 94q35 -6 64 -10.5t55 -7t50.5 -3.5t50.5 -1q54 0 91 8t61.5 25t38.5 43.5t22 63.5l2 11
q-45 -18 -91.5 -29.5t-92.5 -11.5zM171 291q-9 -41 -9 -73q0 -11 2.5 -35t21 -47t52 -33.5t80.5 -10.5t91.5 12.5t83.5 29.5l65 304q-35 17 -74.5 28t-86.5 11q-98 0 -151.5 -42.5t-74.5 -143.5z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="875" 
d="M532 960h112l-18 -85h-112zM543 818q159 0 238 -66.5t83 -185.5h-116q-8 72 -59.5 109t-155.5 37q-73 0 -127 -17t-92.5 -54.5t-64.5 -96.5t-44 -143q-16 -76 -16 -132q0 -6 1 -38.5t25.5 -69.5t74 -54t126.5 -17q66 0 115 11t83.5 35.5t56.5 65t34 99.5l4 23h-260
l22 104h371l-31 -142q-17 -79 -48 -136t-79.5 -94t-117 -54.5t-160.5 -17.5q-103 0 -173.5 24.5t-109 77t-42.5 102t-4 68.5q0 71 19 161q23 104 58 180t88.5 125t127.5 72.5t173 23.5z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="721" 
d="M418 768h112l-20 -98h-113zM288 -4q-67 0 -116.5 17.5t-79 55t-34.5 75.5t-5 58q0 45 12 102q16 74 43 125.5t66.5 84t90.5 47t115 14.5q56 0 108.5 -14.5t100.5 -37.5l26 36h75l-112 -528q-14 -67 -38.5 -113t-63 -74t-92 -40t-125.5 -12q-34 0 -63.5 1t-58 3.5
t-57.5 6.5t-63 11l20 94q35 -6 64 -10.5t55 -7t50.5 -3.5t50.5 -1q54 0 91 8t61.5 25t38.5 43.5t22 63.5l2 11q-45 -18 -91.5 -29.5t-92.5 -11.5zM171 291q-9 -41 -9 -73q0 -11 2.5 -35t21 -47t52 -33.5t80.5 -10.5t91.5 12.5t83.5 29.5l65 304q-35 17 -74.5 28t-86.5 11
q-98 0 -151.5 -42.5t-74.5 -143.5z" />
    <glyph glyph-name="uni0122" unicode="&#x122;" horiz-adv-x="875" 
d="M543 818q159 0 238 -66.5t83 -185.5h-116q-8 72 -59.5 109t-155.5 37q-73 0 -127 -17t-92.5 -54.5t-64.5 -96.5t-44 -143q-16 -76 -16 -132q0 -6 1 -38.5t25.5 -69.5t74 -54t126.5 -17q66 0 115 11t83.5 35.5t56.5 65t34 99.5l4 23h-260l22 104h371l-31 -142
q-17 -79 -48 -136t-79.5 -94t-117 -54.5t-160.5 -17.5q-103 0 -173.5 24.5t-109 77t-42.5 102t-4 68.5q0 71 19 161q23 104 58 180t88.5 125t127.5 72.5t173 23.5zM319 -53h126l-176 -145h-105z" />
    <glyph glyph-name="uni0123" unicode="&#x123;" horiz-adv-x="721" 
d="M506 632h-123l189 170h104zM288 -4q-67 0 -116.5 17.5t-79 55t-34.5 75.5t-5 58q0 45 12 102q16 74 43 125.5t66.5 84t90.5 47t115 14.5q56 0 108.5 -14.5t100.5 -37.5l26 36h75l-112 -528q-14 -67 -38.5 -113t-63 -74t-92 -40t-125.5 -12q-34 0 -63.5 1t-58 3.5
t-57.5 6.5t-63 11l20 94q35 -6 64 -10.5t55 -7t50.5 -3.5t50.5 -1q54 0 91 8t61.5 25t38.5 43.5t22 63.5l2 11q-45 -18 -91.5 -29.5t-92.5 -11.5zM171 291q-9 -41 -9 -73q0 -11 2.5 -35t21 -47t52 -33.5t80.5 -10.5t91.5 12.5t83.5 29.5l65 304q-35 17 -74.5 28t-86.5 11
q-98 0 -151.5 -42.5t-74.5 -143.5z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="897" 
d="M534 987h133l102 -140h-91l-81 97l-122 -97h-101zM220 802h112l-74 -345h458l73 345h111l-170 -802h-111l75 352h-458l-75 -352h-111z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="743" 
d="M457 987h133l102 -140h-91l-81 97l-122 -97h-101zM204 802h106l-58 -275q50 21 104 34.5t110 13.5q126 0 168.5 -50.5t42.5 -112.5q0 -26 -6 -56l-76 -356h-106l75 356q4 18 4 33q0 34 -23 59.5t-106 25.5q-54 0 -107 -10.5t-100 -28.5l-92 -435h-106z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="897" 
d="M174 583h-109l23 104h108l24 115h112l-25 -115h458l24 115h111l-24 -115h108l-23 -104h-107l-124 -583h-111l75 352h-458l-75 -352h-111zM716 457l27 126h-458l-27 -126h458z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="743" 
d="M169 635h-92l18 88h92l17 79h106l-17 -79h205l-18 -88h-205l-23 -108q50 21 104 34.5t110 13.5q126 0 168.5 -50.5t42.5 -112.5q0 -26 -6 -56l-76 -356h-106l75 356q4 18 4 33q0 34 -23 59.5t-106 25.5q-54 0 -107 -10.5t-100 -28.5l-92 -435h-106z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="329" 
d="M101 879q12 52 38 76t72 24q30 0 56 -10.5t48.5 -22.5t41.5 -22.5t34 -10.5q20 0 30 11t14 29l4 21h69l-5 -27q-19 -99 -111 -99q-30 0 -56 10.5t-48.5 23t-41 23t-34.5 10.5q-20 0 -29.5 -11t-14.5 -31l-4 -21h-69zM220 802h112l-171 -802h-111z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="291" 
d="M36 675q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70zM153 559h106l-119 -559h-105z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="329" 
d="M132 949h352l-15 -68h-352zM220 802h112l-171 -802h-111z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="291" 
d="M82 756h330l-15 -73h-330zM153 559h106l-119 -559h-105z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="329" 
d="M290 842q-53 0 -88.5 8t-55.5 25.5t-23 36t-3 30.5q0 20 5 45h82q-3 -15 -3 -26q0 -4 1 -13t11 -17.5t30 -12.5t53 -4q65 0 92.5 16.5t35.5 56.5h81q-8 -39 -22 -66.5t-38.5 -45t-62.5 -25.5t-95 -8zM220 802h112l-171 -802h-111z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="291" 
d="M228 643q-96 0 -126.5 28t-30.5 72q0 20 5 44h82q-4 -17 -4 -30q0 -1 0.5 -9t9 -17t26 -13t46.5 -4q57 0 80.5 16.5t31.5 56.5h83q-8 -39 -21 -66.5t-36 -44.5t-58 -25t-88 -8zM153 559h106l-119 -559h-105z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="329" 
d="M23 -102q-1 -7 -1 -13q0 -14 10.5 -23t42.5 -9q21 0 38.5 2.5t33.5 4.5l-14 -64q-19 -5 -39 -7.5t-46 -2.5q-63 0 -86.5 19.5t-23.5 50.5q0 11 3 25q7 32 31 58.5t78 61.5l170 801h112l-171 -802h-22q-33 -19 -54.5 -33t-34 -25t-18.5 -21.5t-9 -22.5z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="291" 
d="M198 788h112l-20 -98h-113zM3 -102q-1 -7 -1 -13q0 -14 10.5 -23t42.5 -9q21 0 38.5 2.5t33.5 4.5l-14 -64q-19 -5 -39 -7.5t-46 -2.5q-63 0 -86.5 19.5t-23.5 50.5q0 11 3 25q7 33 32 60.5t83 62.5l117 555h106l-119 -559h-21q-33 -19 -54.5 -33t-34 -25t-18.5 -21.5
t-9 -22.5z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="329" 
d="M254 960h112l-18 -85h-112zM220 802h112l-171 -802h-111z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="291" 
d="M153 559h106l-119 -559h-105z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="733" 
d="M220 802h112l-171 -802h-111zM373 -14q-26 0 -44 1.5t-39 6.5l20 98q15 -2 30.5 -3t30.5 -1q33 0 55 6t37 20t24 36.5t16 55.5l127 596h111l-127 -600q-13 -61 -30.5 -102t-44.5 -66.5t-67 -36.5t-99 -11z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="582" 
d="M198 788h112l-20 -98h-113zM489 788h112l-20 -98h-113zM153 559h106l-119 -559h-105zM143 -114q14 -2 32 -3t32 -1q48 0 74.5 20t37.5 74l124 583h107l-124 -583q-10 -51 -26 -87t-40.5 -59t-59.5 -33.5t-83 -10.5q-26 0 -50 1t-43 5z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="404" 
d="M329 987h133l102 -140h-91l-81 97l-122 -97h-101zM44 -14q-26 0 -44 1.5t-39 6.5l20 98q15 -2 30.5 -3t30.5 -1q33 0 55 6t37 20t24 36.5t16 55.5l127 596h111l-127 -600q-13 -61 -30.5 -102t-44.5 -66.5t-67 -36.5t-99 -11z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="290" 
d="M199 802h116l89 -170h-92l-62 118l-113 -118h-100zM-151 -127q14 -2 33 -3t33 -1q48 0 74.5 23.5t39.5 83.5l123 583h107l-124 -583q-11 -51 -27 -87t-40 -59t-59 -33.5t-83 -10.5q-25 0 -49.5 1t-44.5 5z" />
    <glyph glyph-name="uni0136" unicode="&#x136;" horiz-adv-x="779" 
d="M220 802h111l-73 -346l450 346h158l-497 -382l331 -420h-139l-314 408l-86 -408h-111zM261 -53h126l-176 -145h-105z" />
    <glyph glyph-name="uni0137" unicode="&#x137;" horiz-adv-x="666" 
d="M204 802h106l-98 -463l328 220h148l-365 -248l258 -311h-131l-244 310l-66 -310h-106zM203 -53h126l-176 -145h-105z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="666" 
d="M153 559h106l-47 -220l328 220h164l-370 -251l255 -308h-139l-244 310l-66 -310h-106z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="653" 
d="M559 987h126l-157 -140h-110zM220 802h112l-151 -709h399l-20 -93h-510z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="290" 
d="M347 987h126l-157 -140h-110zM204 802h106l-170 -802h-106z" />
    <glyph glyph-name="uni013B" unicode="&#x13b;" horiz-adv-x="653" 
d="M220 802h112l-151 -709h399l-20 -93h-510zM246 -53h126l-176 -145h-105z" />
    <glyph glyph-name="uni013C" unicode="&#x13c;" horiz-adv-x="290" 
d="M204 802h106l-170 -802h-106zM17 -53h126l-176 -145h-105z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="653" 
d="M220 802h112l-151 -709h399l-20 -93h-510zM465 802h96l-155 -255h-57z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="290" 
d="M204 802h106l-170 -802h-106zM433 802h96l-155 -255h-57z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="653" 
d="M220 802h112l-151 -709h399l-20 -93h-510zM470 506h120l-24 -116h-120z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="412" 
d="M204 802h106l-170 -802h-106zM302 338h120l-24 -116h-120z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="653" 
d="M116 311l-120 -53l27 125l119 53l78 366h112l-66 -312l210 93l-26 -125l-211 -93l-58 -272h399l-20 -93h-510z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="290" 
d="M85 242l-102 -46l21 99l102 46l98 461h106l-87 -409l102 45l-21 -99l-102 -45l-62 -294h-106z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="906" 
d="M653 987h126l-157 -140h-110zM219 802h134l305 -657l140 657h111l-171 -802h-136l-303 653l-138 -653h-112z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="743" 
d="M564 796h124l-191 -170h-104zM238 521q53 23 110.5 38.5t117.5 15.5q126 0 168.5 -50t42.5 -112q0 -27 -6 -57l-76 -356h-106l75 356q4 18 4 33q0 34 -23 59.5t-106 25.5q-56 0 -108.5 -15t-100.5 -36l-90 -423h-106l118 559h75z" />
    <glyph glyph-name="uni0145" unicode="&#x145;" horiz-adv-x="906" 
d="M219 802h134l305 -657l140 657h111l-171 -802h-136l-303 653l-138 -653h-112zM312 -53h126l-176 -145h-105z" />
    <glyph glyph-name="uni0146" unicode="&#x146;" horiz-adv-x="743" 
d="M238 521q53 23 110.5 38.5t117.5 15.5q126 0 168.5 -50t42.5 -112q0 -27 -6 -57l-76 -356h-106l75 356q4 18 4 33q0 34 -23 59.5t-106 25.5q-56 0 -108.5 -15t-100.5 -36l-90 -423h-106l118 559h75zM243 -53h126l-176 -145h-105z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="906" 
d="M406 987h92l80 -97l123 97h101l-162 -140h-132zM219 802h134l305 -657l140 657h111l-171 -802h-136l-303 653l-138 -653h-112z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="743" 
d="M299 802h92l62 -118l113 118h100l-161 -170h-116zM238 521q53 23 110.5 38.5t117.5 15.5q126 0 168.5 -50t42.5 -112q0 -27 -6 -57l-76 -356h-106l75 356q4 18 4 33q0 34 -23 59.5t-106 25.5q-56 0 -108.5 -15t-100.5 -36l-90 -423h-106l118 559h75z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="901" 
d="M284 802h119l-166 -295h-100zM397 521q53 23 110.5 38.5t117.5 15.5q126 0 168.5 -50t42.5 -112q0 -27 -6 -57l-76 -356h-106l75 356q4 18 4 33q0 34 -23 59.5t-106 25.5q-56 0 -108.5 -15t-100.5 -36l-90 -423h-106l118 559h75z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="906" 
d="M219 802h139l306 -630l134 630h111l-176 -830q-11 -51 -27.5 -86.5t-44 -57.5t-67.5 -32t-97 -10q-29 0 -47 2t-38 5l19 91q15 -2 30.5 -3t32.5 -1q33 0 55 4.5t36 15.5t22.5 29t14.5 46l6 26l-326 669l-141 -670h-112z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="743" 
d="M306 -114q14 -2 33.5 -3t33.5 -1q48 0 73.5 20t37.5 74l80 380q5 21 5 39q0 36 -23.5 64t-106.5 28q-54 0 -106.5 -14.5t-99.5 -34.5l-93 -438h-106l118 559h75l11 -38q53 23 110.5 38.5t117.5 15.5q126 0 168.5 -50.5t42.5 -112.5q0 -26 -6 -56l-81 -380
q-11 -51 -27 -87t-40 -59t-59 -33.5t-83 -10.5q-26 0 -50.5 1t-44.5 5z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="895" 
d="M415 949h352l-15 -68h-352zM402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q104 0 174 -25t108 -78.5t41.5 -102.5t3.5 -66q0 -71 -20 -163q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5z
M414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="708" 
d="M291 756h330l-15 -73h-330zM308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5q75 0 129.5 -17t86 -54.5t37 -76.5t5.5 -60q0 -46 -12 -102q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5zM317 81q50 0 87 11
t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="895" 
d="M573 842q-53 0 -88.5 8t-55.5 25.5t-23 36t-3 30.5q0 20 5 45h82q-3 -15 -3 -26q0 -4 1 -13t11 -17.5t30 -12.5t53 -4q65 0 92.5 16.5t35.5 56.5h81q-8 -39 -22 -66.5t-38.5 -45t-62.5 -25.5t-95 -8zM402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162
q23 105 58.5 180.5t89.5 124t128 71.5t174 23q104 0 174 -25t108 -78.5t41.5 -102.5t3.5 -66q0 -71 -20 -163q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17
q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="708" 
d="M437 643q-96 0 -126.5 28t-30.5 72q0 20 5 44h82q-4 -17 -4 -30q0 -1 0.5 -9t9 -17t26 -13t46.5 -4q57 0 80.5 16.5t31.5 56.5h83q-8 -39 -21 -66.5t-36 -44.5t-58 -25t-88 -8zM308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87
t97 49t130 15.5q75 0 129.5 -17t86 -54.5t37 -76.5t5.5 -60q0 -46 -12 -102q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5zM317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5
q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="895" 
d="M537 987h118l-145 -140h-101zM756 987h118l-144 -140h-102zM402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q104 0 174 -25t108 -78.5t41.5 -102.5t3.5 -66q0 -71 -20 -163q-22 -106 -58 -181.5
t-90 -124t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="708" 
d="M424 802h117l-170 -170h-96zM632 802h116l-170 -170h-96zM308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5q75 0 129.5 -17t86 -54.5t37 -76.5t5.5 -60q0 -46 -12 -102q-16 -76 -43 -129.5t-68 -87t-97 -49
t-131 -15.5zM317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1285" 
d="M95 416q23 103 56 176t84 119.5t122.5 68.5t169.5 22h795l-21 -104h-452l-53 -248h419l-20 -91h-418l-57 -267h452l-20 -92h-736q-106 0 -178.5 23.5t-111.5 74t-43.5 99.5t-4.5 71q0 65 17 148zM609 92l128 606h-211q-74 0 -127 -16t-90.5 -51.5t-61.5 -92t-41 -136.5
q-16 -76 -16 -131q0 -6 1 -38.5t25.5 -69.5t74 -54t125.5 -17h193z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1152" 
d="M305 -16q-73 0 -126.5 17.5t-85 55t-37 76.5t-5.5 60q0 45 12 101q16 76 43 129.5t66.5 87t94 49t125.5 15.5q91 0 150.5 -30t82.5 -96q42 66 105 96t150 30q231 0 231 -196q0 -40 -9 -87l-9 -45h-449q-3 -23 -3 -42q0 -21 5 -45.5t24.5 -44t53 -27.5t80.5 -8
q33 0 62 1.5t56 4t54 6.5t57 10l-15 -92q-48 -12 -100 -19t-118 -7q-93 0 -152 27.5t-81 93.5q-40 -64 -103 -92.5t-159 -28.5zM872 482q-84 0 -133 -34t-75 -114h345q3 19 3 36q0 47 -27 79.5t-113 32.5zM317 81q50 0 87 11t63.5 35t44 61.5t29.5 90.5q9 42 9 75
q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="834" 
d="M618 987h126l-157 -140h-110zM220 802h319q84 0 143 -16t93.5 -51t41.5 -74t7 -64q0 -40 -10 -89q-13 -56 -31.5 -98t-44 -71t-59.5 -47t-79 -27l101 -265h-122l-90 256h-273l-55 -256h-111zM310 698l-72 -338h249q47 0 82.5 6t61.5 24t43.5 50t28.5 84q7 33 7 59
q0 13 -3.5 35t-24.5 42.5t-58.5 29t-93.5 8.5h-220z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="418" 
d="M439 796h124l-191 -170h-104zM152 559h75l11 -37q48 23 99 34.5t109 12.5l-20 -102q-53 -2 -101.5 -11.5t-93.5 -25.5l-91 -430h-106z" />
    <glyph glyph-name="uni0156" unicode="&#x156;" horiz-adv-x="834" 
d="M220 802h319q84 0 143 -16t93.5 -51t41.5 -74t7 -64q0 -40 -10 -89q-13 -56 -31.5 -98t-44 -71t-59.5 -47t-79 -27l101 -265h-122l-90 256h-273l-55 -256h-111zM310 698l-72 -338h249q47 0 82.5 6t61.5 24t43.5 50t28.5 84q7 33 7 59q0 13 -3.5 35t-24.5 42.5t-58.5 29
t-93.5 8.5h-220zM283 -53h126l-176 -145h-105z" />
    <glyph glyph-name="uni0157" unicode="&#x157;" horiz-adv-x="418" 
d="M152 559h75l11 -37q48 23 99 34.5t109 12.5l-20 -102q-53 -2 -101.5 -11.5t-93.5 -25.5l-91 -430h-106zM15 -53h126l-176 -145h-105z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="834" 
d="M371 987h92l80 -97l123 97h101l-162 -140h-132zM220 802h319q84 0 143 -16t93.5 -51t41.5 -74t7 -64q0 -40 -10 -89q-13 -56 -31.5 -98t-44 -71t-59.5 -47t-79 -27l101 -265h-122l-90 256h-273l-55 -256h-111zM310 698l-72 -338h249q47 0 82.5 6t61.5 24t43.5 50t28.5 84
q7 33 7 59q0 13 -3.5 35t-24.5 42.5t-58.5 29t-93.5 8.5h-220z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="418" 
d="M174 802h92l62 -118l113 118h100l-161 -170h-116zM152 559h75l11 -37q48 23 99 34.5t109 12.5l-20 -102q-53 -2 -101.5 -11.5t-93.5 -25.5l-91 -430h-106z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="789" 
d="M600 987h126l-157 -140h-110zM64 136q60 -17 139 -30t167 -13q53 0 90 6t62 18.5t39 33.5t21 51q6 25 6 44q0 7 -1.5 21t-20.5 31.5t-56.5 31t-100.5 25.5q-83 15 -137.5 37t-83.5 54t-32.5 60.5t-3.5 43.5q0 34 9 76q10 44 30 79.5t58 60.5t97 38.5t147 13.5t156 -9.5
t131 -26.5l-23 -110q-63 17 -130 27t-146 10q-54 0 -91.5 -5.5t-62 -17t-37.5 -29.5t-18 -42q-5 -25 -5 -44q0 -7 1.5 -21.5t19.5 -31t54.5 -28t96.5 -23.5q86 -16 142 -36.5t86 -51.5t34 -60t4 -45q0 -36 -10 -82q-12 -52 -34.5 -91t-61.5 -65t-98 -39t-143 -13
q-97 0 -177 11.5t-140 29.5z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="639" 
d="M512 796h124l-191 -170h-104zM33 106q50 -12 114.5 -21t129.5 -9q44 0 74 2.5t50 9.5t31 19.5t15 31.5q3 17 3 29q0 6 -1.5 16t-16 20t-44 17t-78.5 15q-69 9 -113 21.5t-67 33.5t-26 43t-3 35q0 26 6 60q7 38 23 65.5t47 45.5t79.5 26.5t120.5 8.5q68 0 125.5 -7.5
t110.5 -18.5l-20 -93q-51 12 -102.5 19t-113.5 7q-46 0 -77 -3t-50.5 -10t-29 -18.5t-12.5 -29.5q-4 -16 -4 -27q0 -5 1.5 -14t16.5 -17.5t44.5 -14.5t79.5 -14q68 -10 112.5 -23t68 -35t27 -44.5t3.5 -35.5q0 -28 -8 -64q-8 -38 -26 -66t-49.5 -46t-80 -27t-117.5 -9
q-73 0 -143 8t-119 22z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="789" 
d="M484 987h133l102 -140h-91l-81 97l-122 -97h-101zM64 136q60 -17 139 -30t167 -13q53 0 90 6t62 18.5t39 33.5t21 51q6 25 6 44q0 7 -1.5 21t-20.5 31.5t-56.5 31t-100.5 25.5q-83 15 -137.5 37t-83.5 54t-32.5 60.5t-3.5 43.5q0 34 9 76q10 44 30 79.5t58 60.5t97 38.5
t147 13.5t156 -9.5t131 -26.5l-23 -110q-63 17 -130 27t-146 10q-54 0 -91.5 -5.5t-62 -17t-37.5 -29.5t-18 -42q-5 -25 -5 -44q0 -7 1.5 -21.5t19.5 -31t54.5 -28t96.5 -23.5q86 -16 142 -36.5t86 -51.5t34 -60t4 -45q0 -36 -10 -82q-12 -52 -34.5 -91t-61.5 -65t-98 -39
t-143 -13q-97 0 -177 11.5t-140 29.5z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="639" 
d="M373 802h116l89 -170h-92l-62 118l-113 -118h-100zM33 106q50 -12 114.5 -21t129.5 -9q44 0 74 2.5t50 9.5t31 19.5t15 31.5q3 17 3 29q0 6 -1.5 16t-16 20t-44 17t-78.5 15q-69 9 -113 21.5t-67 33.5t-26 43t-3 35q0 26 6 60q7 38 23 65.5t47 45.5t79.5 26.5t120.5 8.5
q68 0 125.5 -7.5t110.5 -18.5l-20 -93q-51 12 -102.5 19t-113.5 7q-46 0 -77 -3t-50.5 -10t-29 -18.5t-12.5 -29.5q-4 -16 -4 -27q0 -5 1.5 -14t16.5 -17.5t44.5 -14.5t79.5 -14q68 -10 112.5 -23t68 -35t27 -44.5t3.5 -35.5q0 -28 -8 -64q-8 -38 -26 -66t-49.5 -46t-80 -27
t-117.5 -9q-73 0 -143 8t-119 22z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="789" 
d="M64 136q60 -17 139 -30t167 -13q53 0 90 6t62 18.5t39 33.5t21 51q6 25 6 44q0 7 -1.5 21t-20.5 31.5t-56.5 31t-100.5 25.5q-83 15 -137.5 37t-83.5 54t-32.5 60.5t-3.5 43.5q0 34 9 76q10 44 30 79.5t58 60.5t97 38.5t147 13.5t156 -9.5t131 -26.5l-23 -110
q-63 17 -130 27t-146 10q-54 0 -91.5 -5.5t-62 -17t-37.5 -29.5t-18 -42q-5 -25 -5 -44q0 -7 1.5 -21.5t19.5 -31t54.5 -28t96.5 -23.5q86 -16 142 -36.5t86 -51.5t34 -60t4 -45q0 -36 -10 -82q-12 -52 -34.5 -91t-61.5 -65t-98 -39t-143 -13h-1l-50 -42h12q55 0 77 -16
t22 -43q0 -11 -3 -25q-4 -19 -12 -32.5t-23 -22.5t-39 -13t-59 -4q-26 0 -46.5 2.5t-37.5 6.5l11 48q20 -5 37 -7t38 -2q22 0 36 1.5t22 5.5t12 10t5 15q1 5 1 10q0 2 -0.5 6t-6.5 7.5t-18.5 5.5t-33.5 2q-19 0 -36.5 -1t-33.5 -4l10 44l58 49q-78 4 -142.5 14.5
t-115.5 25.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="639" 
d="M33 106q50 -12 114.5 -21t129.5 -9q44 0 74 2.5t50 9.5t31 19.5t15 31.5q3 17 3 29q0 6 -1.5 16t-16 20t-44 17t-78.5 15q-69 9 -113 21.5t-67 33.5t-26 43t-3 35q0 26 6 60q7 38 23 65.5t47 45.5t79.5 26.5t120.5 8.5q68 0 125.5 -7.5t110.5 -18.5l-20 -93
q-51 12 -102.5 19t-113.5 7q-46 0 -77 -3t-50.5 -10t-29 -18.5t-12.5 -29.5q-4 -16 -4 -27q0 -5 1.5 -14t16.5 -17.5t44.5 -14.5t79.5 -14q68 -10 112.5 -23t68 -35t27 -44.5t3.5 -35.5q0 -28 -8 -64q-8 -38 -26 -66t-49.5 -46t-79 -27t-116.5 -9l-50 -42h12q55 0 77 -16
t22 -43q0 -11 -3 -25q-4 -19 -12 -32.5t-23 -22.5t-39 -13t-59 -4q-26 0 -46.5 2.5t-37.5 6.5l11 48q20 -5 37 -7t38 -2q22 0 36 1.5t22 5.5t12 10t5 15q1 5 1 10q0 2 -0.5 6t-6.5 7.5t-18.5 5.5t-33.5 2q-19 0 -36.5 -1t-33.5 -4l10 44l58 49q-59 3 -112.5 10.5t-93.5 18.5
z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="789" 
d="M353 987h92l80 -97l123 97h101l-162 -140h-132zM64 136q60 -17 139 -30t167 -13q53 0 90 6t62 18.5t39 33.5t21 51q6 25 6 44q0 7 -1.5 21t-20.5 31.5t-56.5 31t-100.5 25.5q-83 15 -137.5 37t-83.5 54t-32.5 60.5t-3.5 43.5q0 34 9 76q10 44 30 79.5t58 60.5t97 38.5
t147 13.5t156 -9.5t131 -26.5l-23 -110q-63 17 -130 27t-146 10q-54 0 -91.5 -5.5t-62 -17t-37.5 -29.5t-18 -42q-5 -25 -5 -44q0 -7 1.5 -21.5t19.5 -31t54.5 -28t96.5 -23.5q86 -16 142 -36.5t86 -51.5t34 -60t4 -45q0 -36 -10 -82q-12 -52 -34.5 -91t-61.5 -65t-98 -39
t-143 -13q-97 0 -177 11.5t-140 29.5z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="639" 
d="M247 802h92l62 -118l113 118h100l-161 -170h-116zM33 106q50 -12 114.5 -21t129.5 -9q44 0 74 2.5t50 9.5t31 19.5t15 31.5q3 17 3 29q0 6 -1.5 16t-16 20t-44 17t-78.5 15q-69 9 -113 21.5t-67 33.5t-26 43t-3 35q0 26 6 60q7 38 23 65.5t47 45.5t79.5 26.5t120.5 8.5
q68 0 125.5 -7.5t110.5 -18.5l-20 -93q-51 12 -102.5 19t-113.5 7q-46 0 -77 -3t-50.5 -10t-29 -18.5t-12.5 -29.5q-4 -16 -4 -27q0 -5 1.5 -14t16.5 -17.5t44.5 -14.5t79.5 -14q68 -10 112.5 -23t68 -35t27 -44.5t3.5 -35.5q0 -28 -8 -64q-8 -38 -26 -66t-49.5 -46t-80 -27
t-117.5 -9q-73 0 -143 8t-119 22z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="755" 
d="M293 0h-30l148 698h-286l20 104h687l-22 -104h-287l-149 -698h-22l-69 -58h12q55 0 77 -16t22 -43q0 -11 -3 -25q-4 -19 -12 -32.5t-23 -22.5t-39 -13t-59 -4q-26 0 -46.5 2.5t-37.5 6.5l11 48q20 -5 37 -7t38 -2q22 0 36 1.5t22 5.5t12 10t5 15q1 5 1 10q0 2 -0.5 6
t-6.5 7.5t-18.5 5.5t-33.5 2q-19 0 -36.5 -1t-33.5 -4l10 44z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="454" 
d="M193 -7q-79 13 -96 49t-17 74q0 29 8 65l61 287h-69l16 70l72 21l29 134h106l-28 -134h186l-20 -91h-186l-61 -289q-5 -22 -5 -37q0 -4 1 -15t11.5 -22.5t31.5 -16t54 -4.5q40 0 79 6l-21 -96q-49 -6 -98 -6l-55 -46h12q55 0 77 -16t22 -43q0 -11 -3 -25
q-4 -19 -12 -32.5t-23 -22.5t-39 -13t-59 -4q-26 0 -46.5 2.5t-37.5 6.5l11 48q20 -5 37 -7t38 -2q22 0 36 1.5t22 5.5t12 10t5 15q1 5 1 10q0 2 -0.5 6t-6.5 7.5t-18.5 5.5t-33.5 2q-19 0 -36.5 -1t-33.5 -4l10 44z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="755" 
d="M332 987h92l80 -97l123 97h101l-162 -140h-132zM411 698h-286l20 104h687l-22 -104h-287l-149 -698h-111z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="454" 
d="M448 860h96l-155 -255h-57zM263 -12q-61 0 -100 12t-59 36t-22 46t-2 31q0 30 8 68l61 287h-69l16 70l72 21l29 134h106l-28 -134h186l-20 -91h-186l-61 -289q-5 -22 -5 -37q0 -4 1 -15t11.5 -22.5t31.5 -16t54 -4.5q40 0 79 6l-21 -96q-20 -2 -39.5 -4t-42.5 -2z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="755" 
d="M332 324h-181l22 104h181l57 270h-286l20 104h687l-22 -104h-287l-58 -270h181l-23 -104h-180l-69 -324h-111z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="454" 
d="M51 327h68l30 141h-69l16 70l72 21l29 134h106l-28 -134h186l-20 -91h-186l-30 -141h150l-19 -88h-149l-13 -60q-5 -22 -5 -37q0 -4 1 -15t11.5 -22.5t31.5 -16t54 -4.5q40 0 79 6l-21 -96q-20 -2 -39.5 -4t-42.5 -2q-61 0 -100 12t-59 36t-22 46t-2 31q0 30 8 68l12 58
h-67z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="869" 
d="M371 879q12 52 38 76t72 24q30 0 56 -10.5t48.5 -22.5t41.5 -22.5t34 -10.5q20 0 30 11t14 29l4 21h69l-5 -27q-19 -99 -111 -99q-30 0 -56 10.5t-48.5 23t-41 23t-34.5 10.5q-20 0 -29.5 -11t-14.5 -31l-4 -21h-69zM400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5
q0 50 13 111l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h112l-106 -498q-19 -87 -46 -148t-70.5 -99.5t-107 -55.5t-156.5 -17z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="743" 
d="M263 675q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70zM279 -16q-126 0 -168.5 50t-42.5 112
q0 26 6 56l76 357h106l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h107l-119 -559h-74l-11 38q-53 -23 -110.5 -38.5t-117.5 -15.5z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="869" 
d="M402 949h352l-15 -68h-352zM400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5q0 50 13 111l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h112l-106 -498q-19 -87 -46 -148
t-70.5 -99.5t-107 -55.5t-156.5 -17z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="743" 
d="M309 756h330l-15 -73h-330zM279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h107l-119 -559h-74l-11 38q-53 -23 -110.5 -38.5t-117.5 -15.5z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="869" 
d="M560 842q-53 0 -88.5 8t-55.5 25.5t-23 36t-3 30.5q0 20 5 45h82q-3 -15 -3 -26q0 -4 1 -13t11 -17.5t30 -12.5t53 -4q65 0 92.5 16.5t35.5 56.5h81q-8 -39 -22 -66.5t-38.5 -45t-62.5 -25.5t-95 -8zM400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5q0 50 13 111
l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h112l-106 -498q-19 -87 -46 -148t-70.5 -99.5t-107 -55.5t-156.5 -17z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="743" 
d="M455 643q-96 0 -126.5 28t-30.5 72q0 20 5 44h82q-4 -17 -4 -30q0 -1 0.5 -9t9 -17t26 -13t46.5 -4q57 0 80.5 16.5t31.5 56.5h83q-8 -39 -21 -66.5t-36 -44.5t-58 -25t-88 -8zM279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106l-75 -357q-4 -18 -4 -34
q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h107l-119 -559h-74l-11 38q-53 -23 -110.5 -38.5t-117.5 -15.5z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="869" 
d="M400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5q0 50 13 111l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h112l-106 -498q-19 -87 -46 -148t-70.5 -99.5t-107 -55.5
t-156.5 -17z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="743" 
d="M449 617q-67 0 -97 22.5t-30 59.5q0 14 4 31q13 60 50.5 85t110.5 25q34 0 59.5 -6t42 -19.5t20.5 -29.5t4 -28q0 -14 -3 -30q-14 -60 -51.5 -85t-109.5 -25zM453 677q38 0 59.5 11t28.5 40q2 9 2 16q0 17 -12.5 26.5t-48.5 9.5q-38 0 -59 -11t-27 -40q-2 -10 -2 -18
q0 -16 12 -25t47 -9zM279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h107l-119 -559h-74l-11 38q-53 -23 -110.5 -38.5t-117.5 -15.5z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="869" 
d="M524 987h118l-145 -140h-101zM743 987h118l-144 -140h-102zM400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5q0 50 13 111l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h112
l-106 -498q-19 -87 -46 -148t-70.5 -99.5t-107 -55.5t-156.5 -17z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="743" 
d="M442 802h117l-170 -170h-96zM650 802h116l-170 -170h-96zM279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h107l-119 -559h-74l-11 38q-53 -23 -110.5 -38.5t-117.5 -15.5z
" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="869" 
d="M332 -102q-1 -7 -1 -13q0 -14 10.5 -23t42.5 -9q21 0 38.5 2.5t33.5 4.5l-14 -64q-19 -5 -39 -7.5t-46 -2.5q-63 0 -86.5 19.5t-23.5 50.5q0 11 3 25q6 29 25.5 52.5t61.5 52.5q-157 12 -202 75.5t-45 143.5q0 49 13 109l103 488h112l-103 -488q-11 -53 -11 -91
q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h112l-106 -498q-18 -85 -44 -145t-67 -98t-101 -56.5t-147 -20.5q-25 -15 -41 -26.5t-26 -21t-15 -19t-7 -19.5z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="743" 
d="M279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h107l-119 -559h-20q-34 -20 -56 -34t-35 -25.5t-19 -22t-9 -22.5q-1 -7 -1 -13q0 -14 10.5 -23t42.5 -9q21 0 38.5 2.5
t33.5 4.5l-14 -64q-19 -5 -39 -7.5t-46 -2.5q-63 0 -86.5 19.5t-23.5 50.5q0 11 3 25q4 18 13 34.5t25.5 33t42 33.5t61.5 38l-6 20q-53 -23 -110.5 -38.5t-117.5 -15.5z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="1269" 
d="M710 987h133l102 -140h-91l-81 97l-122 -97h-101zM147 802h114l33 -702h19l327 702h204l29 -702h20l330 702h122l-380 -802h-197l-33 702h-18l-332 -702h-198z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="1030" 
d="M559 802h116l89 -170h-92l-62 118l-113 -118h-100zM90 559h107l33 -479l255 479h171l49 -479l2 1l236 478h118l-280 -559h-168l-53 478h-2l-257 -478h-168z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="764" 
d="M468 987h133l102 -140h-91l-81 97l-122 -97h-101zM335 317l-209 485h119l159 -382l322 382h135l-414 -483l-68 -319h-112z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="672" 
d="M390 802h116l89 -170h-92l-62 118l-113 -118h-100zM83 559h108l91 -460h19l288 460h119l-390 -616q-27 -45 -52 -75t-54.5 -48t-65.5 -26t-85 -8q-23 0 -46 1.5t-46 5.5l19 92q17 -2 33 -3t32 -1q31 0 54.5 3.5t41.5 11.5t32 21.5t27 33.5l34 55h-48z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="764" 
d="M372 960h91l-18 -85h-91zM593 960h91l-18 -85h-91zM335 317l-209 485h119l159 -382l322 382h135l-414 -483l-68 -319h-112z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="744" 
d="M586 987h126l-157 -140h-110zM0 93l604 604h-450l24 105h612l-20 -94l-603 -603h503l-23 -105h-668z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="640" 
d="M517 796h124l-191 -170h-104zM16 74l430 394h-335l20 91h497l-15 -75l-433 -394h361l-19 -90h-522z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="744" 
d="M474 960h112l-18 -85h-112zM0 93l604 604h-450l24 105h612l-20 -94l-603 -603h503l-23 -105h-668z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="640" 
d="M372 768h112l-20 -98h-113zM16 74l430 394h-335l20 91h497l-15 -75l-433 -394h361l-19 -90h-522z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="744" 
d="M339 987h92l80 -97l123 97h101l-162 -140h-132zM0 93l604 604h-450l24 105h612l-20 -94l-603 -603h503l-23 -105h-668z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="640" 
d="M252 802h92l62 -118l113 118h100l-161 -170h-116zM16 74l430 394h-335l20 91h497l-15 -75l-433 -394h361l-19 -90h-522z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="406" 
d="M-121 -114q14 -2 32 -3t32 -1q49 0 76 20t38 74l134 633q11 53 29 92t46.5 65t70 39t98.5 13q29 0 54 -1.5t52 -5.5l-20 -92q-20 2 -39.5 3t-41.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-134 -633q-11 -51 -26.5 -88t-40 -60.5t-59.5 -35t-85 -11.5
q-25 0 -48.5 1t-45.5 5z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="700" 
d="M111 448h215l38 177q11 51 29 87.5t48.5 60t75.5 34.5t109 11q29 0 54 -1.5t52 -5.5l-20 -100q-23 4 -46.5 5t-46.5 1q-34 0 -59 -4.5t-43 -16t-29 -30t-17 -46.5l-37 -172h221l-19 -89h-220l-84 -393q-9 -44 -25 -77.5t-44.5 -56t-71.5 -34.5t-106 -12q-37 0 -62.5 1.5
t-51.5 5.5l21 100q23 -4 45.5 -5t45.5 -1q37 0 62 5t41 15.5t25 26.5t14 38l82 387h-216z" />
    <glyph glyph-name="Ohorn" unicode="&#x1a0;" horiz-adv-x="895" 
d="M402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q88 0 152 -18h78q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-17 -65 -55.5 -111t-104.5 -64q42 -54 46.5 -105t4.5 -72q0 -71 -20 -163
q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54
t125 -17z" />
    <glyph glyph-name="ohorn" unicode="&#x1a1;" horiz-adv-x="708" 
d="M308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5t125 -16h58q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-17 -63 -55 -110t-103 -65q22 -38 24.5 -70.5t2.5 -44.5q0 -45 -12 -102q-16 -76 -43 -129.5t-68 -87
t-97 -49t-131 -15.5zM317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5z" />
    <glyph glyph-name="Uhorn" unicode="&#x1af;" horiz-adv-x="869" 
d="M400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5q0 50 13 111l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h75q35 9 50.5 36.5t23.5 59.5h122q-19 -72 -61.5 -117.5
t-115.5 -62.5l-88 -414q-19 -87 -46 -148t-70.5 -99.5t-107 -55.5t-156.5 -17z" />
    <glyph glyph-name="uhorn" unicode="&#x1b0;" horiz-adv-x="743" 
d="M279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h63q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-19 -71 -62 -118t-117 -62l-102 -477h-74l-11 38q-53 -23 -110.5 -38.5
t-117.5 -15.5z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="828" 
d="M615 987h126l-157 -140h-110zM433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331l-57 379h-26l-216 -379h299z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="725" 
d="M611 1055h124l-191 -170h-104zM440 617q-67 0 -97 22.5t-30 59.5q0 14 4 31q13 60 50.5 85t110.5 25q34 0 59.5 -6t42 -19.5t20.5 -29.5t4 -28q0 -14 -3 -30q-14 -60 -51.5 -85t-109.5 -25zM444 677q38 0 59.5 11t28.5 40q2 9 2 16q0 17 -12.5 26.5t-48.5 9.5
q-38 0 -59 -11t-27 -40q-2 -10 -2 -18q0 -16 12 -25t47 -9zM290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285
q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="1242" 
d="M866 987h126l-157 -140h-110zM592 227h-331l-185 -227h-137l656 802h683l-21 -104h-453l-51 -242h418l-22 -105h-418l-55 -259h452l-21 -92h-563zM615 331l78 367h-48l-299 -367h269z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="1131" 
d="M750 787h124l-191 -170h-104zM254 -16q-56 0 -98.5 11.5t-69 37t-32.5 53.5t-6 47q0 27 7 59q17 76 72 108t163 32h227l8 38q3 17 3 31q0 9 -2 23t-19 28.5t-50.5 21.5t-90.5 7q-54 0 -105.5 -5t-105.5 -13l17 94q29 4 52 7.5t46.5 5.5t49.5 3.5t60 1.5q102 0 156 -23.5
t72 -68.5q39 48 96 70t137 22q141 0 186 -59.5t45 -137.5q0 -39 -9 -84l-9 -48h-449q-3 -23 -3 -42q0 -20 5 -44.5t24.5 -43.5t53 -27t80.5 -8q33 0 62 1.5t56 4t54 6.5t57 10l-15 -93q-50 -13 -103.5 -19.5t-122.5 -6.5q-78 0 -133.5 17.5t-87.5 58.5q-59 -37 -124.5 -56.5
t-153.5 -19.5zM833 498q-43 0 -76 -9t-58 -28.5t-43.5 -51.5t-31.5 -77h346q4 25 4 47q0 48 -26.5 83.5t-114.5 35.5zM270 68q71 0 125 15.5t106 44.5q-5 25 -6 43t-1 25q0 23 4 50h-204q-65 0 -97 -15.5t-40 -54.5q-3 -17 -3 -32q0 -35 25 -55.5t91 -20.5z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="895" 
d="M649 987h126l-157 -140h-110zM18 56l81 71q-22 54 -22 130q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q169 0 249 -68l76 68l60 -72l-81 -70q23 -54 23 -130q0 -72 -20 -163q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5q-85 0 -146 16t-102 51
l-76 -67zM205 401q-16 -74 -16 -129q0 -33 6 -59l511 457q-52 42 -172 42q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5zM414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 75 16 129q0 34 -6 60l-510 -460q55 -40 170 -40z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="708" 
d="M547 796h124l-191 -170h-104zM6 46l62 52q-15 36 -16 63.5t-1 33.5q0 44 12 99q16 76 43 129.5t68 87t97 49t130 15.5q63 0 111 -11.5t81 -37.5l62 49l50 -62l-63 -52q17 -39 17 -93q0 -46 -12 -103q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5q-62 0 -109.5 11.5
t-79.5 37.5l-62 -49zM169 279q-9 -45 -9 -77q0 -17 2 -30l342 281q-37 24 -111 24q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5zM317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q8 42 8 74q0 17 -2 32l-341 -281q37 -23 111 -23z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="789" 
d="M64 136q60 -17 139 -30t167 -13q53 0 90 6t62 18.5t39 33.5t21 51q6 25 6 44q0 7 -1.5 21t-20.5 31.5t-56.5 31t-100.5 25.5q-83 15 -137.5 37t-83.5 54t-32.5 60.5t-3.5 43.5q0 34 9 76q10 44 30 79.5t58 60.5t97 38.5t147 13.5t156 -9.5t131 -26.5l-23 -110
q-63 17 -130 27t-146 10q-54 0 -91.5 -5.5t-62 -17t-37.5 -29.5t-18 -42q-5 -25 -5 -44q0 -7 1.5 -21.5t19.5 -31t54.5 -28t96.5 -23.5q86 -16 142 -36.5t86 -51.5t34 -60t4 -45q0 -36 -10 -82q-12 -52 -34.5 -91t-61.5 -65t-98 -39t-143 -13q-97 0 -177 11.5t-140 29.5z
M275 -53h126l-176 -145h-105z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="639" 
d="M33 106q50 -12 114.5 -21t129.5 -9q44 0 74 2.5t50 9.5t31 19.5t15 31.5q3 17 3 29q0 6 -1.5 16t-16 20t-44 17t-78.5 15q-69 9 -113 21.5t-67 33.5t-26 43t-3 35q0 26 6 60q7 38 23 65.5t47 45.5t79.5 26.5t120.5 8.5q68 0 125.5 -7.5t110.5 -18.5l-20 -93
q-51 12 -102.5 19t-113.5 7q-46 0 -77 -3t-50.5 -10t-29 -18.5t-12.5 -29.5q-4 -16 -4 -27q0 -5 1.5 -14t16.5 -17.5t44.5 -14.5t79.5 -14q68 -10 112.5 -23t68 -35t27 -44.5t3.5 -35.5q0 -28 -8 -64q-8 -38 -26 -66t-49.5 -46t-80 -27t-117.5 -9q-73 0 -143 8t-119 22z
M196 -53h126l-176 -145h-105z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="755" 
d="M411 698h-286l20 104h687l-22 -104h-287l-149 -698h-111zM251 -53h126l-176 -145h-105z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="454" 
d="M263 -12q-61 0 -100 12t-59 36t-22 46t-2 31q0 30 8 68l61 287h-69l16 70l72 21l29 134h106l-28 -134h186l-20 -91h-186l-61 -289q-5 -22 -5 -37q0 -4 1 -15t11.5 -22.5t31.5 -16t54 -4.5q40 0 79 6l-21 -96q-20 -2 -39.5 -4t-42.5 -2zM160 -53h126l-176 -145h-105z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="290" 
d="M-151 -127q14 -2 33 -3t33 -1q48 0 74.5 23.5t39.5 83.5l123 583h107l-124 -583q-11 -51 -27 -87t-40 -59t-59 -33.5t-83 -10.5q-25 0 -49.5 1t-44.5 5z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="570" 
d="M340 802h116l89 -170h-92l-62 118l-113 -118h-100z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="570" 
d="M214 802h92l62 -118l113 118h100l-161 -170h-116z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="570" 
d="M369 643q-96 0 -126.5 28t-30.5 72q0 20 5 44h82q-4 -17 -4 -30q0 -1 0.5 -9t9 -17t26 -13t46.5 -4q57 0 80.5 16.5t31.5 56.5h83q-8 -39 -21 -66.5t-36 -44.5t-58 -25t-88 -8z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="570" 
d="M335 768h112l-20 -98h-113z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="570" 
d="M364 617q-67 0 -97 22.5t-30 59.5q0 14 4 31q13 60 50.5 85t110.5 25q34 0 59.5 -6t42 -19.5t20.5 -29.5t4 -28q0 -14 -3 -30q-14 -60 -51.5 -85t-109.5 -25zM368 677q38 0 59.5 11t28.5 40q2 9 2 16q0 17 -12.5 26.5t-48.5 9.5q-38 0 -59 -11t-27 -40q-2 -10 -2 -18
q0 -16 12 -25t47 -9z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="570" 
d="M180 -102q-1 -7 -1 -13q0 -14 10.5 -23t42.5 -9q21 0 38.5 2.5t33.5 4.5l-14 -64q-19 -5 -39 -7.5t-46 -2.5q-63 0 -86.5 19.5t-23.5 50.5q0 11 3 25q4 18 13 34.5t26 33t43.5 34.5t63.5 40l69 -13q-39 -22 -63.5 -37.5t-39 -28t-21 -23t-9.5 -23.5z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="570" 
d="M177 675q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="570" 
d="M292 802h117l-170 -170h-96zM500 802h116l-170 -170h-96z" />
    <glyph glyph-name="gravecomb" unicode="&#x300;" horiz-adv-x="0" 
d="M-90 802h115l99 -170h-95z" />
    <glyph glyph-name="acutecomb" unicode="&#x301;" horiz-adv-x="0" 
d="M195 802h124l-191 -170h-104z" />
    <glyph glyph-name="uni0302" unicode="&#x302;" horiz-adv-x="0" 
d="M55 802h116l89 -170h-92l-62 118l-113 -118h-100z" />
    <glyph glyph-name="tildecomb" unicode="&#x303;" horiz-adv-x="0" 
d="M-108 675q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="uni0304" unicode="&#x304;" horiz-adv-x="0" 
d="M-62 756h330l-15 -73h-330z" />
    <glyph glyph-name="uni0306" unicode="&#x306;" horiz-adv-x="0" 
d="M84 643q-96 0 -126.5 28t-30.5 71q0 20 5 45h82q-4 -17 -4 -30q0 -1 0.5 -9t9 -17t26 -13t46.5 -4q57 0 80.5 16.5t31.5 56.5h83q-8 -39 -21 -66.5t-36 -44.5t-58 -25t-88 -8z" />
    <glyph glyph-name="uni0307" unicode="&#x307;" horiz-adv-x="0" 
d="M49 768h112l-20 -98h-113z" />
    <glyph glyph-name="uni0308" unicode="&#x308;" horiz-adv-x="0" 
d="M-44 768h87l-20 -98h-87zM167 768h88l-21 -98h-87z" />
    <glyph glyph-name="uni030A" unicode="&#x30a;" horiz-adv-x="0" 
d="M78 617q-67 0 -97 22.5t-30 59.5q0 14 4 31q13 60 50.5 85t110.5 25q34 0 59.5 -6t42 -19.5t20.5 -29.5t4 -28q0 -14 -3 -30q-14 -60 -51.5 -85t-109.5 -25zM82 677q38 0 59.5 11t28.5 40q2 9 2 16q0 17 -12.5 26.5t-48.5 9.5q-38 0 -59 -11t-27 -40q-2 -10 -2 -18
q0 -16 12 -25t47 -9z" />
    <glyph glyph-name="uni030B" unicode="&#x30b;" horiz-adv-x="0" 
d="M71 802h117l-170 -170h-96zM279 802h116l-170 -170h-96z" />
    <glyph glyph-name="uni030C" unicode="&#x30c;" horiz-adv-x="0" 
d="M-71 802h92l62 -118l113 118h100l-161 -170h-116z" />
    <glyph glyph-name="uni0312" unicode="&#x312;" horiz-adv-x="0" 
d="M137 632h-123l189 170h104z" />
    <glyph glyph-name="uni0326" unicode="&#x326;" horiz-adv-x="0" 
d="M-127 -53h126l-176 -145h-105z" />
    <glyph glyph-name="uni0327" unicode="&#x327;" horiz-adv-x="0" 
d="M-83 2h59l-71 -60h12q55 0 77 -16t22 -43q0 -11 -3 -25q-4 -19 -12 -32.5t-23 -22.5t-39 -13t-59 -4q-26 0 -46.5 2.5t-37.5 6.5l11 48q20 -5 37 -7t38 -2q43 0 57.5 7t17.5 25q1 5 1 10q0 2 -0.5 6t-6.5 7.5t-18.5 5.5t-33.5 2q-19 0 -36.5 -1t-33.5 -4l10 44z" />
    <glyph glyph-name="uni0328" unicode="&#x328;" horiz-adv-x="0" 
d="M-142 -102q-1 -7 -1 -13q0 -14 10.5 -23t42.5 -9q21 0 38.5 2.5t33.5 4.5l-14 -64q-19 -5 -39 -7.5t-46 -2.5q-63 0 -86.5 19.5t-23.5 50.5q0 11 3 25q4 18 13 34.5t26 33t43 34.5t64 40l69 -13q-39 -22 -63.5 -37.5t-39 -28t-21 -23t-9.5 -23.5z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" 
d="M27 96h127q-56 51 -64 111t-8 93q0 69 18 155q21 97 51 165.5t74 112.5t104.5 64.5t142.5 20.5q89 0 148.5 -24t90.5 -73.5t33 -92.5t2 -55q0 -68 -18 -156q-26 -124 -74 -201.5t-117 -119.5h120l-21 -96h-275l16 78q50 12 88.5 40.5t67.5 71t49.5 99t35.5 125.5
q18 84 18.5 114.5t0.5 33.5q0 53 -17 88t-54 51t-95 16q-60 0 -103 -15.5t-74.5 -51.5t-53.5 -94t-40 -142q-14 -69 -16 -104t-2 -48q0 -40 9.5 -77.5t35.5 -66t71 -40.5l-16 -78h-276z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="754" 
d="M163 454h-73l23 105h649l-22 -105h-73l-97 -454h-111l97 454h-282l-97 -454h-110z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="1269" 
d="M607 987h115l80 -140h-98zM147 802h114l33 -702h19l327 702h204l29 -702h20l330 702h122l-380 -802h-197l-33 702h-18l-332 -702h-198z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="1030" 
d="M414 802h115l99 -170h-95zM90 559h107l33 -479l255 479h171l49 -479l2 1l236 478h118l-280 -559h-168l-53 478h-2l-257 -478h-168z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="1269" 
d="M826 987h126l-157 -140h-110zM147 802h114l33 -702h19l327 702h204l29 -702h20l330 702h122l-380 -802h-197l-33 702h-18l-332 -702h-198z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="1030" 
d="M698 796h124l-191 -170h-104zM90 559h107l33 -479l255 479h171l49 -479l2 1l236 478h118l-280 -559h-168l-53 478h-2l-257 -478h-168z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="1269" 
d="M614 960h91l-18 -85h-91zM835 960h91l-18 -85h-91zM147 802h114l33 -702h19l327 702h204l29 -702h20l330 702h122l-380 -802h-197l-33 702h-18l-332 -702h-198z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="1030" 
d="M460 768h87l-20 -98h-87zM671 768h88l-21 -98h-87zM90 559h107l33 -479l255 479h171l49 -479l2 1l236 478h118l-280 -559h-168l-53 478h-2l-257 -478h-168z" />
    <glyph glyph-name="uni1EA0" unicode="&#x1ea0;" horiz-adv-x="828" 
d="M433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331l-57 379h-26l-216 -379h299zM258 -102h130l-20 -115h-130z" />
    <glyph glyph-name="uni1EA1" unicode="&#x1ea1;" horiz-adv-x="725" 
d="M290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5
q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5zM191 -102h130l-20 -115h-130z" />
    <glyph glyph-name="uni1EA2" unicode="&#x1ea2;" horiz-adv-x="828" 
d="M533 939q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331
l-57 379h-26l-216 -379h299z" />
    <glyph glyph-name="uni1EA3" unicode="&#x1ea3;" horiz-adv-x="725" 
d="M410 698q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103
q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5
q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5z" />
    <glyph glyph-name="uni1EA4" unicode="&#x1ea4;" horiz-adv-x="828" 
d="M499 987h133l102 -140h-91l-81 97l-122 -97h-101zM433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331l-57 379h-26l-216 -379h299zM690 1194h124l-191 -170h-104z" />
    <glyph glyph-name="uni1EA5" unicode="&#x1ea5;" horiz-adv-x="725" 
d="M417 802h116l89 -170h-92l-62 118l-113 -118h-100zM290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285
q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5zM600 1009h124l-191 -170h-104z" />
    <glyph glyph-name="uni1EA6" unicode="&#x1ea6;" horiz-adv-x="828" 
d="M499 987h133l102 -140h-91l-81 97l-122 -97h-101zM433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331l-57 379h-26l-216 -379h299zM407 1200h115l99 -170h-95z" />
    <glyph glyph-name="uni1EA7" unicode="&#x1ea7;" horiz-adv-x="725" 
d="M417 802h116l89 -170h-92l-62 118l-113 -118h-100zM290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285
q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5zM317 1015h115l99 -170h-95z" />
    <glyph glyph-name="uni1EA8" unicode="&#x1ea8;" horiz-adv-x="828" 
d="M702 1016q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM499 987h133l102 -140h-91l-81 97l-122 -97h-101zM433 802h184
l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331l-57 379h-26l-216 -379h299z" />
    <glyph glyph-name="uni1EA9" unicode="&#x1ea9;" horiz-adv-x="725" 
d="M602 828q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM417 802h116l89 -170h-92l-62 118l-113 -118h-100zM290 -16
q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5
q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5z" />
    <glyph glyph-name="uni1EAA" unicode="&#x1eaa;" horiz-adv-x="828" 
d="M499 987h133l102 -140h-91l-81 97l-122 -97h-101zM433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331l-57 379h-26l-216 -379h299zM389 1073q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31
q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="uni1EAB" unicode="&#x1eab;" horiz-adv-x="725" 
d="M417 802h116l89 -170h-92l-62 118l-113 -118h-100zM290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285
q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5zM299 888q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5
l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="uni1EAC" unicode="&#x1eac;" horiz-adv-x="828" 
d="M433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331l-57 379h-26l-216 -379h299zM258 -102h130l-20 -115h-130zM520 1045h116l89 -170h-92l-62 118l-113 -118h-100z" />
    <glyph glyph-name="uni1EAD" unicode="&#x1ead;" horiz-adv-x="725" 
d="M290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5
q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5zM191 -102h130l-20 -115h-130zM417 802h116l89 -170h-92l-62 118l-113 -118h-100z" />
    <glyph glyph-name="uni1EAE" unicode="&#x1eae;" horiz-adv-x="828" 
d="M539 842q-53 0 -88.5 8t-55.5 25.5t-23 36t-3 30.5q0 20 5 45h82q-3 -15 -3 -26q0 -4 1 -13t11 -17.5t30 -12.5t53 -4q65 0 92.5 16.5t35.5 56.5h81q-8 -39 -22 -66.5t-38.5 -45t-62.5 -25.5t-95 -8zM433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331
l-57 379h-26l-216 -379h299zM690 1194h124l-191 -170h-104z" />
    <glyph glyph-name="uni1EAF" unicode="&#x1eaf;" horiz-adv-x="725" 
d="M446 643q-96 0 -126.5 28t-30.5 72q0 20 5 44h82q-4 -17 -4 -30q0 -1 0.5 -9t9 -17t26 -13t46.5 -4q57 0 80.5 16.5t31.5 56.5h83q-8 -39 -21 -66.5t-36 -44.5t-58 -25t-88 -8zM290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86
t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33
t-46 -59t-29.5 -89.5zM597 994h124l-191 -170h-104z" />
    <glyph glyph-name="uni1EB0" unicode="&#x1eb0;" horiz-adv-x="828" 
d="M539 842q-53 0 -88.5 8t-55.5 25.5t-23 36t-3 30.5q0 20 5 45h82q-3 -15 -3 -26q0 -4 1 -13t11 -17.5t30 -12.5t53 -4q65 0 92.5 16.5t35.5 56.5h81q-8 -39 -22 -66.5t-38.5 -45t-62.5 -25.5t-95 -8zM433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331
l-57 379h-26l-216 -379h299zM407 1200h115l99 -170h-95z" />
    <glyph glyph-name="uni1EB1" unicode="&#x1eb1;" horiz-adv-x="725" 
d="M446 643q-96 0 -126.5 28t-30.5 72q0 20 5 44h82q-4 -17 -4 -30q0 -1 0.5 -9t9 -17t26 -13t46.5 -4q57 0 80.5 16.5t31.5 56.5h83q-8 -39 -21 -66.5t-36 -44.5t-58 -25t-88 -8zM290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86
t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33
t-46 -59t-29.5 -89.5zM314 1000h115l99 -170h-95z" />
    <glyph glyph-name="uni1EB2" unicode="&#x1eb2;" horiz-adv-x="828" 
d="M541 1098q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM539 842q-53 0 -88.5 8t-55.5 25.5t-23 36t-3 30.5q0 20 5 45h82
q-3 -15 -3 -26q0 -4 1 -13t11 -17.5t30 -12.5t53 -4q65 0 92.5 16.5t35.5 56.5h81q-8 -39 -22 -66.5t-38.5 -45t-62.5 -25.5t-95 -8zM433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331l-57 379h-26l-216 -379h299z" />
    <glyph glyph-name="uni1EB3" unicode="&#x1eb3;" horiz-adv-x="725" 
d="M461 877q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM446 643q-96 0 -126.5 28t-30.5 72q0 20 5 44h82q-4 -17 -4 -30
q0 -1 0.5 -9t9 -17t26 -13t46.5 -4q57 0 80.5 16.5t31.5 56.5h83q-8 -39 -21 -66.5t-36 -44.5t-58 -25t-88 -8zM290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74
l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5z" />
    <glyph glyph-name="uni1EB4" unicode="&#x1eb4;" horiz-adv-x="828" 
d="M539 842q-53 0 -88.5 8t-55.5 25.5t-23 36t-3 30.5q0 20 5 45h82q-3 -15 -3 -26q0 -4 1 -13t11 -17.5t30 -12.5t53 -4q65 0 92.5 16.5t35.5 56.5h81q-8 -39 -22 -66.5t-38.5 -45t-62.5 -25.5t-95 -8zM433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331
l-57 379h-26l-216 -379h299zM389 1073q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="uni1EB5" unicode="&#x1eb5;" horiz-adv-x="725" 
d="M446 643q-96 0 -126.5 28t-30.5 72q0 20 5 44h82q-4 -17 -4 -30q0 -1 0.5 -9t9 -17t26 -13t46.5 -4q57 0 80.5 16.5t31.5 56.5h83q-8 -39 -21 -66.5t-36 -44.5t-58 -25t-88 -8zM290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86
t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33
t-46 -59t-29.5 -89.5zM296 873q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="uni1EB6" unicode="&#x1eb6;" horiz-adv-x="828" 
d="M433 802h184l125 -802h-112l-34 228h-373l-130 -228h-124zM581 331l-57 379h-26l-216 -379h299zM258 -102h130l-20 -115h-130zM549 886q-96 0 -126.5 28t-30.5 71q0 20 5 45h82q-4 -17 -4 -30q0 -1 0.5 -9t9 -17t26 -13t46.5 -4q57 0 80.5 16.5t31.5 56.5h83
q-8 -39 -21 -66.5t-36 -44.5t-58 -25t-88 -8z" />
    <glyph glyph-name="uni1EB7" unicode="&#x1eb7;" horiz-adv-x="725" 
d="M290 -16q-67 0 -117.5 18t-80.5 56t-35 76.5t-5 59.5q0 46 12 103q16 76 43.5 129t67 86t91.5 48t116 15q57 0 109.5 -15t101.5 -38l26 37h75l-119 -559h-74l-10 32q-50 -21 -100 -34.5t-101 -13.5zM170 285q-9 -43 -9 -75q0 -11 2.5 -35.5t22 -48.5t53 -34.5t81.5 -10.5
q46 0 91 12.5t84 29.5l66 314q-34 17 -74.5 28.5t-88.5 11.5q-49 0 -87 -10.5t-65.5 -33t-46 -59t-29.5 -89.5zM191 -102h130l-20 -115h-130zM446 643q-96 0 -126.5 28t-30.5 71q0 20 5 45h82q-4 -17 -4 -30q0 -1 0.5 -9t9 -17t26 -13t46.5 -4q57 0 80.5 16.5t31.5 56.5h83
q-8 -39 -21 -66.5t-36 -44.5t-58 -25t-88 -8z" />
    <glyph glyph-name="uni1EB8" unicode="&#x1eb8;" horiz-adv-x="746" 
d="M220 802h563l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247h452l-22 -104h-563zM226 -102h130l-20 -115h-130z" />
    <glyph glyph-name="uni1EB9" unicode="&#x1eb9;" horiz-adv-x="671" 
d="M394 575q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450q-3 -23 -3 -42q0 -21 5 -45t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93q-50 -12 -103.5 -19t-122.5 -7q-76 0 -130.5 16.5t-86.5 53.5t-37 76.5t-5 60.5q0 48 12 108
q17 74 44.5 126t68 85.5t94.5 49t124 15.5zM390 481q-84 0 -133 -33.5t-75 -113.5h345q3 19 3 35q0 47 -27 79.5t-113 32.5zM189 -102h130l-20 -115h-130z" />
    <glyph glyph-name="uni1EBA" unicode="&#x1eba;" horiz-adv-x="746" 
d="M533 942q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM220 802h563l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247
h452l-22 -104h-563z" />
    <glyph glyph-name="uni1EBB" unicode="&#x1ebb;" horiz-adv-x="671" 
d="M422 703q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM394 575q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450
q-3 -23 -3 -42q0 -21 5 -45t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93q-50 -12 -103.5 -19t-122.5 -7q-76 0 -130.5 16.5t-86.5 53.5t-37 76.5t-5 60.5q0 48 12 108q17 74 44.5 126t68 85.5t94.5 49t124 15.5zM390 481q-84 0 -133 -33.5
t-75 -113.5h345q3 19 3 35q0 47 -27 79.5t-113 32.5z" />
    <glyph glyph-name="uni1EBC" unicode="&#x1ebc;" horiz-adv-x="746" 
d="M220 802h563l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247h452l-22 -104h-563zM339 918q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5
t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="uni1EBD" unicode="&#x1ebd;" horiz-adv-x="671" 
d="M394 575q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450q-3 -23 -3 -42q0 -21 5 -45t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93q-50 -12 -103.5 -19t-122.5 -7q-76 0 -130.5 16.5t-86.5 53.5t-37 76.5t-5 60.5q0 48 12 108
q17 74 44.5 126t68 85.5t94.5 49t124 15.5zM390 481q-84 0 -133 -33.5t-75 -113.5h345q3 19 3 35q0 47 -27 79.5t-113 32.5zM240 675q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28
q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="uni1EBE" unicode="&#x1ebe;" horiz-adv-x="746" 
d="M481 987h133l102 -140h-91l-81 97l-122 -97h-101zM220 802h563l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247h452l-22 -104h-563zM672 1194h124l-191 -170h-104z" />
    <glyph glyph-name="uni1EBF" unicode="&#x1ebf;" horiz-adv-x="671" 
d="M402 802h116l89 -170h-92l-62 118l-113 -118h-100zM394 575q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450q-3 -23 -3 -42q0 -21 5 -45t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93q-50 -12 -103.5 -19t-122.5 -7q-76 0 -130.5 16.5
t-86.5 53.5t-37 76.5t-5 60.5q0 48 12 108q17 74 44.5 126t68 85.5t94.5 49t124 15.5zM390 481q-84 0 -133 -33.5t-75 -113.5h345q3 19 3 35q0 47 -27 79.5t-113 32.5zM585 1009h124l-191 -170h-104z" />
    <glyph glyph-name="uni1EC0" unicode="&#x1ec0;" horiz-adv-x="746" 
d="M481 987h133l102 -140h-91l-81 97l-122 -97h-101zM220 802h563l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247h452l-22 -104h-563zM389 1200h115l99 -170h-95z" />
    <glyph glyph-name="uni1EC1" unicode="&#x1ec1;" horiz-adv-x="671" 
d="M402 802h116l89 -170h-92l-62 118l-113 -118h-100zM394 575q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450q-3 -23 -3 -42q0 -21 5 -45t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93q-50 -12 -103.5 -19t-122.5 -7q-76 0 -130.5 16.5
t-86.5 53.5t-37 76.5t-5 60.5q0 48 12 108q17 74 44.5 126t68 85.5t94.5 49t124 15.5zM390 481q-84 0 -133 -33.5t-75 -113.5h345q3 19 3 35q0 47 -27 79.5t-113 32.5zM302 1015h115l99 -170h-95z" />
    <glyph glyph-name="uni1EC2" unicode="&#x1ec2;" horiz-adv-x="746" 
d="M683 1017q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM481 987h133l102 -140h-91l-81 97l-122 -97h-101zM220 802h563
l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247h452l-22 -104h-563z" />
    <glyph glyph-name="uni1EC3" unicode="&#x1ec3;" horiz-adv-x="671" 
d="M591 823q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM402 802h116l89 -170h-92l-62 118l-113 -118h-100zM394 575
q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450q-3 -23 -3 -42q0 -21 5 -45t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93q-50 -12 -103.5 -19t-122.5 -7q-76 0 -130.5 16.5t-86.5 53.5t-37 76.5t-5 60.5q0 48 12 108q17 74 44.5 126
t68 85.5t94.5 49t124 15.5zM390 481q-84 0 -133 -33.5t-75 -113.5h345q3 19 3 35q0 47 -27 79.5t-113 32.5z" />
    <glyph glyph-name="uni1EC4" unicode="&#x1ec4;" horiz-adv-x="746" 
d="M481 987h133l102 -140h-91l-81 97l-122 -97h-101zM220 802h563l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247h452l-22 -104h-563zM371 1073q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31
q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="uni1EC5" unicode="&#x1ec5;" horiz-adv-x="671" 
d="M402 802h116l89 -170h-92l-62 118l-113 -118h-100zM394 575q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450q-3 -23 -3 -42q0 -21 5 -45t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93q-50 -12 -103.5 -19t-122.5 -7q-76 0 -130.5 16.5
t-86.5 53.5t-37 76.5t-5 60.5q0 48 12 108q17 74 44.5 126t68 85.5t94.5 49t124 15.5zM390 481q-84 0 -133 -33.5t-75 -113.5h345q3 19 3 35q0 47 -27 79.5t-113 32.5zM284 888q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23
h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="uni1EC6" unicode="&#x1ec6;" horiz-adv-x="746" 
d="M220 802h563l-22 -104h-451l-52 -242h419l-21 -105h-420l-53 -247h452l-22 -104h-563zM226 -102h130l-20 -115h-130zM502 1045h116l89 -170h-92l-62 118l-113 -118h-100z" />
    <glyph glyph-name="uni1EC7" unicode="&#x1ec7;" horiz-adv-x="671" 
d="M394 575q141 0 189 -57.5t48 -136.5q0 -40 -10 -89l-9 -45h-450q-3 -23 -3 -42q0 -21 5 -45t24.5 -43.5t53 -27.5t80.5 -8q34 0 63 1.5t56.5 4t55.5 6.5t59 10l-20 -93q-50 -12 -103.5 -19t-122.5 -7q-76 0 -130.5 16.5t-86.5 53.5t-37 76.5t-5 60.5q0 48 12 108
q17 74 44.5 126t68 85.5t94.5 49t124 15.5zM390 481q-84 0 -133 -33.5t-75 -113.5h345q3 19 3 35q0 47 -27 79.5t-113 32.5zM189 -102h130l-20 -115h-130zM403 802h116l89 -170h-92l-62 118l-113 -118h-100z" />
    <glyph glyph-name="uni1EC8" unicode="&#x1ec8;" horiz-adv-x="329" 
d="M262 927q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM220 802h112l-171 -802h-111z" />
    <glyph glyph-name="uni1EC9" unicode="&#x1ec9;" horiz-adv-x="291" 
d="M204 767q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM153 559h106l-119 -559h-105z" />
    <glyph glyph-name="uni1ECA" unicode="&#x1eca;" horiz-adv-x="329" 
d="M220 802h112l-171 -802h-111zM20 -102h130l-20 -115h-130z" />
    <glyph glyph-name="uni1ECB" unicode="&#x1ecb;" horiz-adv-x="291" 
d="M192 752h112l-20 -98h-113zM153 559h106l-119 -559h-105zM-3 -102h130l-20 -115h-130z" />
    <glyph glyph-name="uni1ECC" unicode="&#x1ecc;" horiz-adv-x="895" 
d="M402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q104 0 174 -25t108 -78.5t41.5 -102.5t3.5 -66q0 -71 -20 -163q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54
t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17zM258 -102h130l-20 -115h-130z" />
    <glyph glyph-name="uni1ECD" unicode="&#x1ecd;" horiz-adv-x="708" 
d="M308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5q75 0 129.5 -17t86 -54.5t37 -76.5t5.5 -60q0 -46 -12 -102q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5zM317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5
q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5z" />
    <glyph glyph-name="uni1ECE" unicode="&#x1ece;" horiz-adv-x="895" 
d="M566 939q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5
q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q104 0 174 -25t108 -78.5t41.5 -102.5t3.5 -66q0 -71 -20 -163q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54
t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17z" />
    <glyph glyph-name="uni1ECF" unicode="&#x1ecf;" horiz-adv-x="708" 
d="M403 694q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101
q16 76 43 129.5t68 87t97 49t130 15.5q75 0 129.5 -17t86 -54.5t37 -76.5t5.5 -60q0 -46 -12 -102q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5zM317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5
t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5z" />
    <glyph glyph-name="uni1ED0" unicode="&#x1ed0;" horiz-adv-x="895" 
d="M533 987h133l102 -140h-91l-81 97l-122 -97h-101zM402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q104 0 174 -25t108 -78.5t41.5 -102.5t3.5 -66q0 -71 -20 -163q-22 -106 -58 -181.5t-90 -124
t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17zM724 1194h124l-191 -170
h-104z" />
    <glyph glyph-name="uni1ED1" unicode="&#x1ed1;" horiz-adv-x="708" 
d="M408 802h116l89 -170h-92l-62 118l-113 -118h-100zM308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5q75 0 129.5 -17t86 -54.5t37 -76.5t5.5 -60q0 -46 -12 -102q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5z
M317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5zM592 1009h124l-191 -170h-104z" />
    <glyph glyph-name="uni1ED2" unicode="&#x1ed2;" horiz-adv-x="895" 
d="M533 987h133l102 -140h-91l-81 97l-122 -97h-101zM402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q104 0 174 -25t108 -78.5t41.5 -102.5t3.5 -66q0 -71 -20 -163q-22 -106 -58 -181.5t-90 -124
t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17zM441 1200h115l99 -170h-95z
" />
    <glyph glyph-name="uni1ED3" unicode="&#x1ed3;" horiz-adv-x="708" 
d="M408 802h116l89 -170h-92l-62 118l-113 -118h-100zM308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5q75 0 129.5 -17t86 -54.5t37 -76.5t5.5 -60q0 -46 -12 -102q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5z
M317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5zM309 1015h115l99 -170h-95z" />
    <glyph glyph-name="uni1ED4" unicode="&#x1ed4;" horiz-adv-x="895" 
d="M740 1011q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM533 987h133l102 -140h-91l-81 97l-122 -97h-101zM402 -16
q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q104 0 174 -25t108 -78.5t41.5 -102.5t3.5 -66q0 -71 -20 -163q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54t65 96.5
t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17z" />
    <glyph glyph-name="uni1ED5" unicode="&#x1ed5;" horiz-adv-x="708" 
d="M599 830q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM408 802h116l89 -170h-92l-62 118l-113 -118h-100zM308 -16
q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5q75 0 129.5 -17t86 -54.5t37 -76.5t5.5 -60q0 -46 -12 -102q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5zM317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q9 42 9 75
q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5z" />
    <glyph glyph-name="uni1ED6" unicode="&#x1ed6;" horiz-adv-x="895" 
d="M533 987h133l102 -140h-91l-81 97l-122 -97h-101zM402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q104 0 174 -25t108 -78.5t41.5 -102.5t3.5 -66q0 -71 -20 -163q-22 -106 -58 -181.5t-90 -124
t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17zM423 1073q13 62 43.5 89.5
t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="uni1ED7" unicode="&#x1ed7;" horiz-adv-x="708" 
d="M408 802h116l89 -170h-92l-62 118l-113 -118h-100zM308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5q75 0 129.5 -17t86 -54.5t37 -76.5t5.5 -60q0 -46 -12 -102q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5z
M317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5zM291 888q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5
t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="uni1ED8" unicode="&#x1ed8;" horiz-adv-x="895" 
d="M402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q104 0 174 -25t108 -78.5t41.5 -102.5t3.5 -66q0 -71 -20 -163q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54
t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17zM258 -102h130l-20 -115h-130zM554 1045h116l89 -170h-92l-62 118l-113 -118h-100z
" />
    <glyph glyph-name="uni1ED9" unicode="&#x1ed9;" horiz-adv-x="708" 
d="M409 802h116l89 -170h-92l-62 118l-113 -118h-100zM308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5q75 0 129.5 -17t86 -54.5t37 -76.5t5.5 -60q0 -46 -12 -102q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5z
M317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5zM189 -102h130l-20 -115h-130z" />
    <glyph glyph-name="uni1EDA" unicode="&#x1eda;" horiz-adv-x="895" 
d="M402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q88 0 152 -18h78q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-17 -65 -55.5 -111t-104.5 -64q42 -54 46.5 -105t4.5 -72q0 -71 -20 -163
q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54
t125 -17zM692 1039h124l-191 -170h-104z" />
    <glyph glyph-name="uni1EDB" unicode="&#x1edb;" horiz-adv-x="708" 
d="M308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5t125 -16h58q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-17 -63 -55 -110t-103 -65q22 -38 24.5 -70.5t2.5 -44.5q0 -45 -12 -102q-16 -76 -43 -129.5t-68 -87
t-97 -49t-131 -15.5zM317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5zM547 796h124l-191 -170h-104z" />
    <glyph glyph-name="uni1EDC" unicode="&#x1edc;" horiz-adv-x="895" 
d="M402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q88 0 152 -18h78q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-17 -65 -55.5 -111t-104.5 -64q42 -54 46.5 -105t4.5 -72q0 -71 -20 -163
q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54
t125 -17zM409 1045h115l99 -170h-95z" />
    <glyph glyph-name="uni1EDD" unicode="&#x1edd;" horiz-adv-x="708" 
d="M308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5t125 -16h58q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-17 -63 -55 -110t-103 -65q22 -38 24.5 -70.5t2.5 -44.5q0 -45 -12 -102q-16 -76 -43 -129.5t-68 -87
t-97 -49t-131 -15.5zM317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5zM264 802h115l99 -170h-95z" />
    <glyph glyph-name="uni1EDE" unicode="&#x1ede;" horiz-adv-x="895" 
d="M566 939q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5
q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q88 0 152 -18h78q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-17 -65 -55.5 -111t-104.5 -64q42 -54 46.5 -105t4.5 -72q0 -71 -20 -163q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5zM414 90
q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54t125 -17z" />
    <glyph glyph-name="uni1EDF" unicode="&#x1edf;" horiz-adv-x="708" 
d="M424 695q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101
q16 76 43 129.5t68 87t97 49t130 15.5t125 -16h58q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-17 -63 -55 -110t-103 -65q22 -38 24.5 -70.5t2.5 -44.5q0 -45 -12 -102q-16 -76 -43 -129.5t-68 -87t-97 -49t-131 -15.5zM317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5
q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5z" />
    <glyph glyph-name="uni1EE0" unicode="&#x1ee0;" horiz-adv-x="895" 
d="M402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q88 0 152 -18h78q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-17 -65 -55.5 -111t-104.5 -64q42 -54 46.5 -105t4.5 -72q0 -71 -20 -163
q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54
t125 -17zM391 918q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="uni1EE1" unicode="&#x1ee1;" horiz-adv-x="708" 
d="M308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5t125 -16h58q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-17 -63 -55 -110t-103 -65q22 -38 24.5 -70.5t2.5 -44.5q0 -45 -12 -102q-16 -76 -43 -129.5t-68 -87
t-97 -49t-131 -15.5zM317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5zM246 675q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27
t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="uni1EE2" unicode="&#x1ee2;" horiz-adv-x="895" 
d="M402 -16q-103 0 -173 25t-107.5 78t-41 102.5t-3.5 67.5q0 71 19 162q23 105 58.5 180.5t89.5 124t128 71.5t174 23q88 0 152 -18h78q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-17 -65 -55.5 -111t-104.5 -64q42 -54 46.5 -105t4.5 -72q0 -71 -20 -163
q-22 -106 -58 -181.5t-90 -124t-128.5 -71t-174.5 -22.5zM414 90q74 0 128 17t93 54t65 96.5t44 143.5q16 76 16 131q0 7 -1 39.5t-26 69.5t-73.5 54t-125.5 17q-74 0 -128 -17t-92.5 -54t-64.5 -96.5t-44 -143.5q-16 -76 -16 -131q0 -7 1 -39.5t25.5 -69.5t73.5 -54
t125 -17zM283 -102h130l-20 -115h-130z" />
    <glyph glyph-name="uni1EE3" unicode="&#x1ee3;" horiz-adv-x="708" 
d="M308 -16q-74 0 -128.5 17.5t-86 55t-37 76t-5.5 60.5q0 45 12 101q16 76 43 129.5t68 87t97 49t130 15.5t125 -16h58q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-17 -63 -55 -110t-103 -65q22 -38 24.5 -70.5t2.5 -44.5q0 -45 -12 -102q-16 -76 -43 -129.5t-68 -87
t-97 -49t-131 -15.5zM317 81q50 0 87 11t63.5 35t44.5 61.5t29 90.5q9 42 9 75q0 50 -28.5 86.5t-128.5 36.5q-50 0 -87 -11t-63.5 -34.5t-44.5 -61t-29 -91.5q-9 -42 -9 -75q0 -50 28.5 -86.5t128.5 -36.5zM186 -102h130l-20 -115h-130z" />
    <glyph glyph-name="uni1EE4" unicode="&#x1ee4;" horiz-adv-x="869" 
d="M400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5q0 50 13 111l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h112l-106 -498q-19 -87 -46 -148t-70.5 -99.5t-107 -55.5
t-156.5 -17zM258 -102h130l-20 -115h-130z" />
    <glyph glyph-name="uni1EE5" unicode="&#x1ee5;" horiz-adv-x="743" 
d="M279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h107l-119 -559h-74l-11 38q-53 -23 -110.5 -38.5t-117.5 -15.5zM212 -102h130l-20 -115h-130z" />
    <glyph glyph-name="uni1EE6" unicode="&#x1ee6;" horiz-adv-x="869" 
d="M533 939q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5q0 50 13 111
l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h112l-106 -498q-19 -87 -46 -148t-70.5 -99.5t-107 -55.5t-156.5 -17z" />
    <glyph glyph-name="uni1EE7" unicode="&#x1ee7;" horiz-adv-x="743" 
d="M427 699q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106
l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h107l-119 -559h-74l-11 38q-53 -23 -110.5 -38.5t-117.5 -15.5z" />
    <glyph glyph-name="uni1EE8" unicode="&#x1ee8;" horiz-adv-x="869" 
d="M400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5q0 50 13 111l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h75q35 9 50.5 36.5t23.5 59.5h122q-19 -72 -61.5 -117.5
t-115.5 -62.5l-88 -414q-19 -87 -46 -148t-70.5 -99.5t-107 -55.5t-156.5 -17zM679 1039h124l-191 -170h-104z" />
    <glyph glyph-name="uni1EE9" unicode="&#x1ee9;" horiz-adv-x="743" 
d="M279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h63q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-19 -71 -62 -118t-117 -62l-102 -477h-74l-11 38q-53 -23 -110.5 -38.5
t-117.5 -15.5zM564 796h124l-191 -170h-104z" />
    <glyph glyph-name="uni1EEA" unicode="&#x1eea;" horiz-adv-x="869" 
d="M400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5q0 50 13 111l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h75q35 9 50.5 36.5t23.5 59.5h122q-19 -72 -61.5 -117.5
t-115.5 -62.5l-88 -414q-19 -87 -46 -148t-70.5 -99.5t-107 -55.5t-156.5 -17zM396 1045h115l99 -170h-95z" />
    <glyph glyph-name="uni1EEB" unicode="&#x1eeb;" horiz-adv-x="743" 
d="M279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h63q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-19 -71 -62 -118t-117 -62l-102 -477h-74l-11 38q-53 -23 -110.5 -38.5
t-117.5 -15.5zM281 802h115l99 -170h-95z" />
    <glyph glyph-name="uni1EEC" unicode="&#x1eec;" horiz-adv-x="869" 
d="M566 939q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5q0 50 13 111
l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h75q35 9 50.5 36.5t23.5 59.5h122q-19 -72 -61.5 -117.5t-115.5 -62.5l-88 -414q-19 -87 -46 -148t-70.5 -99.5t-107 -55.5t-156.5 -17z
" />
    <glyph glyph-name="uni1EED" unicode="&#x1eed;" horiz-adv-x="743" 
d="M430 706q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106
l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h63q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-19 -71 -62 -118t-117 -62l-102 -477h-74l-11 38q-53 -23 -110.5 -38.5t-117.5 -15.5z" />
    <glyph glyph-name="uni1EEE" unicode="&#x1eee;" horiz-adv-x="869" 
d="M400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5q0 50 13 111l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h75q35 9 50.5 36.5t23.5 59.5h122q-19 -72 -61.5 -117.5
t-115.5 -62.5l-88 -414q-19 -87 -46 -148t-70.5 -99.5t-107 -55.5t-156.5 -17zM378 918q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12
q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="uni1EEF" unicode="&#x1eef;" horiz-adv-x="743" 
d="M279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h63q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-19 -71 -62 -118t-117 -62l-102 -477h-74l-11 38q-53 -23 -110.5 -38.5
t-117.5 -15.5zM263 675q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="uni1EF0" unicode="&#x1ef0;" horiz-adv-x="869" 
d="M400 -16q-97 0 -162.5 19t-101.5 59t-41 80.5t-5 60.5q0 50 13 111l103 488h112l-103 -488q-11 -53 -11 -91q0 -8 1.5 -32.5t25 -50t68.5 -36.5t115 -11q63 0 105.5 11t71.5 35.5t47 65t30 99.5l106 498h75q35 9 50.5 36.5t23.5 59.5h122q-19 -72 -61.5 -117.5
t-115.5 -62.5l-88 -414q-19 -87 -46 -148t-70.5 -99.5t-107 -55.5t-156.5 -17zM258 -102h130l-20 -115h-130z" />
    <glyph glyph-name="uni1EF1" unicode="&#x1ef1;" horiz-adv-x="743" 
d="M279 -16q-126 0 -168.5 50t-42.5 112q0 26 6 56l76 357h106l-75 -357q-4 -18 -4 -34q0 -33 23 -58.5t105 -25.5q56 0 109 15.5t101 36.5l89 423h63q20 3 34 12t23.5 22.5t16 30t10.5 33.5h122q-19 -71 -62 -118t-117 -62l-102 -477h-74l-11 38q-53 -23 -110.5 -38.5
t-117.5 -15.5zM197 -102h130l-20 -115h-130z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="764" 
d="M365 987h115l80 -140h-98zM335 317l-209 485h119l159 -382l322 382h135l-414 -483l-68 -319h-112z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="672" 
d="M245 802h115l99 -170h-95zM83 559h108l91 -460h19l288 460h119l-390 -616q-27 -45 -52 -75t-54.5 -48t-65.5 -26t-85 -8q-23 0 -46 1.5t-46 5.5l19 92q17 -2 33 -3t32 -1q31 0 54.5 3.5t41.5 11.5t32 21.5t27 33.5l34 55h-48z" />
    <glyph glyph-name="uni1EF4" unicode="&#x1ef4;" horiz-adv-x="764" 
d="M335 317l-209 485h119l159 -382l322 382h135l-414 -483l-68 -319h-112zM239 -102h130l-20 -115h-130z" />
    <glyph glyph-name="uni1EF5" unicode="&#x1ef5;" horiz-adv-x="672" 
d="M83 559h108l91 -460h19l288 460h119l-390 -616q-27 -45 -52 -75t-54.5 -48t-65.5 -26t-85 -8q-23 0 -46 1.5t-46 5.5l19 92q17 -2 33 -3t32 -1q31 0 54.5 3.5t41.5 11.5t32 21.5t27 33.5l34 55h-48zM344 -102h130l-20 -115h-130z" />
    <glyph glyph-name="uni1EF6" unicode="&#x1ef6;" horiz-adv-x="764" 
d="M469 931q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM335 317l-209 485h119l159 -382l322 382h135l-414 -483l-68 -319
h-112z" />
    <glyph glyph-name="uni1EF7" unicode="&#x1ef7;" horiz-adv-x="672" 
d="M365 693q27 1 46.5 10t22.5 24v2q0 7 -5 13t-16 6t-26.5 -3t-36.5 -21l-39 44q28 26 57 36.5t58 10.5h5q45 -1 64 -21.5t19 -45.5q0 -8 -2 -17v-3q-7 -34 -31.5 -55t-54.5 -29q-1 -7 -3 -12t-3 -11h-61zM83 559h108l91 -460h19l288 460h119l-390 -616q-27 -45 -52 -75
t-54.5 -48t-65.5 -26t-85 -8q-23 0 -46 1.5t-46 5.5l19 92q17 -2 33 -3t32 -1q31 0 54.5 3.5t41.5 11.5t32 21.5t27 33.5l34 55h-48z" />
    <glyph glyph-name="uni1EF8" unicode="&#x1ef8;" horiz-adv-x="764" 
d="M335 317l-209 485h119l159 -382l322 382h135l-414 -483l-68 -319h-112zM326 918q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12
q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="uni1EF9" unicode="&#x1ef9;" horiz-adv-x="672" 
d="M83 559h108l91 -460h19l288 460h119l-390 -616q-27 -45 -52 -75t-54.5 -48t-65.5 -26t-85 -8q-23 0 -46 1.5t-46 5.5l19 92q17 -2 33 -3t32 -1q31 0 54.5 3.5t41.5 11.5t32 21.5t27 33.5l34 55h-48zM227 675q13 62 43.5 89.5t75.5 27.5q31 0 54 -12.5t42.5 -27t37 -27
t37.5 -12.5t31 13.5t16 37.5l5 23h70l-6 -31q-12 -60 -42.5 -88t-74.5 -28q-31 0 -54.5 12t-43 26.5t-37 26.5t-37.5 12q-22 0 -32.5 -12t-15.5 -37l-5 -24h-70z" />
    <glyph glyph-name="uni2000" unicode="&#x2000;" 
 />
    <glyph glyph-name="uni2001" unicode="&#x2001;" horiz-adv-x="1520" 
 />
    <glyph glyph-name="uni2002" unicode="&#x2002;" 
 />
    <glyph glyph-name="uni2003" unicode="&#x2003;" horiz-adv-x="1520" 
 />
    <glyph glyph-name="uni2004" unicode="&#x2004;" horiz-adv-x="507" 
 />
    <glyph glyph-name="uni2005" unicode="&#x2005;" horiz-adv-x="380" 
 />
    <glyph glyph-name="uni2006" unicode="&#x2006;" horiz-adv-x="253" 
 />
    <glyph glyph-name="uni2007" unicode="&#x2007;" horiz-adv-x="700" 
 />
    <glyph glyph-name="uni2008" unicode="&#x2008;" horiz-adv-x="380" 
 />
    <glyph glyph-name="uni2009" unicode="&#x2009;" horiz-adv-x="190" 
 />
    <glyph glyph-name="uni200A" unicode="&#x200a;" horiz-adv-x="152" 
 />
    <glyph glyph-name="uni200B" unicode="&#x200b;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni200C" unicode="&#x200c;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni200D" unicode="&#x200d;" horiz-adv-x="0" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" 
d="M-10 230l22 100h760l-22 -100h-760z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1520" 
d="M-10 230l22 100h1520l-22 -100h-1520z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="380" 
d="M255 507h-118l165 295h100z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="380" 
d="M284 802h119l-166 -295h-100z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="380" 
d="M150 123h118l-167 -294h-99z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="581" 
d="M255 507h-118l165 295h100zM475 507h-118l165 295h100z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="580" 
d="M284 802h119l-166 -295h-100zM504 802h119l-166 -295h-100z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="580" 
d="M150 123h118l-167 -294h-99zM370 123h118l-167 -294h-99z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" 
d="M346 365l19 89h-229l23 105h228l51 243h106l-51 -243h229l-22 -105h-229l-19 -89l-120 -511h-84z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" 
d="M84 202h227l53 252h-228l23 105h227l52 243h106l-51 -243h229l-22 -105h-229l-54 -252h229l-22 -105h-229l-51 -243h-107l52 243h-229z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="449" 
d="M209 148q-67 0 -92 27.5t-25 67.5q0 22 6 48q14 65 50 92.5t96 27.5q32 0 56.5 -8t38.5 -25t16.5 -35t2.5 -28q0 -21 -5 -46q-14 -66 -50 -93.5t-94 -27.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="811" 
d="M97 116h120l-24 -116h-120zM322 116h120l-24 -116h-120zM547 116h120l-24 -116h-120z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1273" 
d="M261 474q-94 0 -126.5 34.5t-32.5 87.5q0 27 7 62q19 87 66 123.5t138 36.5q47 0 80.5 -10t53 -32t23 -45t3.5 -35q0 -28 -8 -62q-19 -87 -66 -123.5t-138 -36.5zM155 0h-105l717 802h107zM269 546q47 0 70 23t34 77q5 24 5 42q0 23 -11.5 40t-60.5 17q-48 0 -71 -22.5
t-34 -76.5q-5 -24 -5 -41q0 -24 11.5 -41.5t61.5 -17.5zM611 -16q-94 0 -126.5 34.5t-32.5 87.5q0 27 7 62q19 87 66 123.5t138 36.5q47 0 80.5 -10t53 -32t23 -45t3.5 -35q0 -28 -8 -62q-19 -87 -66 -123.5t-138 -36.5zM1014 -16q-93 0 -125.5 34.5t-32.5 87.5q0 27 7 62
q9 44 25 74t40.5 49.5t58.5 28t79 8.5q47 0 80.5 -10t53 -32t23 -45t3.5 -35q0 -28 -8 -62q-9 -43 -25 -73.5t-40.5 -50t-58.5 -28t-80 -8.5zM619 56q47 0 70 23t34 77q5 24 5 42q0 23 -11.5 40t-60.5 17q-48 0 -71 -22.5t-34 -76.5q-5 -24 -5 -41q0 -24 11.5 -41.5
t61.5 -17.5zM1022 56q47 0 70.5 23t34.5 77q5 23 5 41q0 23 -12 40.5t-61 17.5q-48 0 -70.5 -22.5t-33.5 -76.5q-5 -24 -5 -42q0 -23 11.5 -40.5t60.5 -17.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="333" 
d="M241 512h111l-207 -234l107 -230h-109l-109 232z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="333" 
d="M94 48h-111l207 233l-106 231h108l110 -232z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="208" 
d="M436 802h107l-719 -802h-105z" />
    <glyph glyph-name="uni2060" unicode="&#x2060;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni2070" unicode="&#x2070;" horiz-adv-x="405" 
d="M350 1009q50 0 83.5 -14t51 -44.5t18.5 -57t1 -30.5q0 -46 -13 -107q-12 -57 -28.5 -99.5t-42 -70t-62 -41.5t-88.5 -14q-50 0 -83.5 14t-51 44.5t-18.5 57.5t-1 33q0 45 12 104q12 57 28.5 99.5t42 70t62.5 41.5t89 14zM278 610q26 0 44.5 8t32 26.5t24 49t20.5 76.5
q9 46 10.5 66t1.5 27q0 20 -5.5 36t-21 23.5t-42.5 7.5q-26 0 -44.5 -8t-32 -26.5t-24 -49t-20.5 -76.5t-11 -65t-1 -26q0 -21 5.5 -37.5t21 -24t42.5 -7.5z" />
    <glyph glyph-name="uni2074" unicode="&#x2074;" horiz-adv-x="405" 
d="M486 802l-55 -262h-93l20 93h-263l15 72l219 295h106l-218 -292h156l20 94h93z" />
    <glyph glyph-name="uni2075" unicode="&#x2075;" horiz-adv-x="405" 
d="M110 628q66 -17 147 -17q30 0 50 3t33 10.5t20 19.5t10 30q2 12 2 22q0 23 -15.5 36t-64.5 13h-142l54 255h306l-16 -77h-212l-22 -102h51q97 0 127.5 -31.5t30.5 -77.5q0 -22 -5 -48q-8 -41 -25 -67t-43.5 -40.5t-63.5 -20t-87 -5.5q-48 0 -88 5.5t-64 11.5z" />
    <glyph glyph-name="uni2076" unicode="&#x2076;" horiz-adv-x="405" 
d="M282 531q-55 0 -91 14t-54 44t-19 56t-1 30q0 45 14 105q14 63 32 106.5t45.5 71t65.5 39.5t90 12q45 0 80 -5t58 -13l-16 -79q-27 7 -56.5 12t-64.5 5q-29 0 -50 -5.5t-36 -18.5t-25.5 -35.5t-19.5 -56.5q20 8 43.5 12.5t51.5 4.5q82 0 113 -33.5t31 -81.5q0 -21 -5 -47
q-16 -76 -60.5 -106.5t-125.5 -30.5zM305 753q-26 0 -47 -4.5t-42 -12.5q-8 -37 -8 -63q0 -24 11.5 -45t66.5 -21q39 0 59 15.5t28 57.5q4 15 4 27q0 21 -15 33.5t-57 12.5z" />
    <glyph glyph-name="uni2077" unicode="&#x2077;" horiz-adv-x="405" 
d="M180 1000h359l-16 -72l-292 -388h-117l300 383h-251z" />
    <glyph glyph-name="uni2078" unicode="&#x2078;" horiz-adv-x="405" 
d="M271 531q-46 0 -81 7t-57 23.5t-27.5 36t-5.5 35.5q0 17 4 37q10 45 34.5 70.5t63.5 36.5q-29 12 -39 32t-10 41q0 18 5 41q13 62 57.5 90t134.5 28q44 0 78 -7t55 -23t26 -34t5 -32q0 -16 -4 -35q-9 -47 -33 -72.5t-59 -37.5q34 -11 45.5 -32t11.5 -44q0 -18 -5 -40
q-13 -63 -59.5 -92t-139.5 -29zM320 808q45 0 65.5 13t27.5 48q2 12 2 22q0 3 -0.5 10.5t-8.5 15t-23 11t-40 3.5q-47 0 -67 -15t-28 -47q-2 -11 -2 -20q0 -19 13 -30t61 -11zM277 608q50 0 72 15t30 50q2 12 2 21q0 21 -15 33.5t-63 12.5t-71.5 -14.5t-31.5 -52.5
q-3 -11 -3 -21q0 -19 14 -31.5t66 -12.5z" />
    <glyph glyph-name="uni2079" unicode="&#x2079;" horiz-adv-x="405" 
d="M334 1009q110 0 137.5 -45t27.5 -100q0 -44 -13 -104q-13 -63 -31.5 -106.5t-46 -71t-65.5 -39.5t-91 -12q-45 0 -79.5 5t-57.5 13l16 79q26 -7 55.5 -12t64.5 -5q30 0 51 5t36.5 18.5t26 36t18.5 56.5q-20 -8 -43 -12.5t-52 -4.5q-83 0 -113.5 33.5t-30.5 80.5
q0 22 5 48q16 76 60 106.5t125 30.5zM243 860q-3 -14 -3 -25q0 -22 14.5 -35t56.5 -13q26 0 47 4t42 12q7 37 7 62t-7.5 40t-24.5 21.5t-45 6.5q-38 0 -58.5 -15.5t-28.5 -57.5z" />
    <glyph glyph-name="uni2080" unicode="&#x2080;" horiz-adv-x="408" 
d="M194 271q50 0 83.5 -14t51 -44.5t18.5 -57t1 -30.5q0 -46 -13 -107q-12 -57 -28.5 -99.5t-42 -70t-62 -41.5t-88.5 -14q-50 0 -83.5 14t-51 44.5t-18.5 57.5t-1 33q0 44 12 104q12 57 28.5 99.5t42 70t62.5 41.5t89 14zM122 -128q26 0 44.5 8t32 26.5t24 49t20.5 76.5
q9 46 10.5 66t1.5 27q0 20 -5.5 36t-21 23.5t-42.5 7.5q-26 0 -44.5 -8t-32 -26.5t-24 -49t-20.5 -76.5t-11 -65t-1 -26q0 -21 5.5 -37.5t21 -24t42.5 -7.5z" />
    <glyph glyph-name="uni2081" unicode="&#x2081;" horiz-adv-x="336" 
d="M180 262h92l-98 -460h-94l76 357l-136 -62l-28 79z" />
    <glyph glyph-name="uni2082" unicode="&#x2082;" horiz-adv-x="408" 
d="M192 271q94 0 127 -28t33 -70q0 -17 -4 -36q-5 -23 -12 -38.5t-20 -28.5t-33 -25.5t-51 -28.5l-138 -72q-23 -12 -30.5 -21.5t-10.5 -27.5l-3 -16h243l-16 -77h-339l22 105q4 19 11 33t17.5 26t26 22.5t37.5 21.5l143 74q18 9 28.5 15t16.5 12t9 12t5 15q2 8 2 15
q0 17 -16 27.5t-67 10.5q-35 0 -73 -5t-68 -10l16 80q31 7 64 11t80 4z" />
    <glyph glyph-name="uni2083" unicode="&#x2083;" horiz-adv-x="408" 
d="M-46 -113q33 -8 65.5 -12t72.5 -4q35 0 57 3t35.5 9.5t20 18t9.5 28.5q3 13 3 24q0 19 -12.5 31t-57.5 12h-119l15 77h119q40 0 59.5 10t26.5 41q2 12 2 21q0 22 -15 35t-72 13q-44 0 -75 -3.5t-65 -11.5l16 77q26 6 62 10.5t81 4.5q103 0 135.5 -29t32.5 -73
q0 -19 -5 -42q-9 -44 -32.5 -65.5t-59.5 -30.5q35 -13 46.5 -37t11.5 -47q0 -17 -5 -38q-7 -32 -21 -54t-38.5 -36t-62.5 -20t-94 -6q-51 0 -89 5t-64 12z" />
    <glyph glyph-name="uni2084" unicode="&#x2084;" horiz-adv-x="408" 
d="M330 64l-55 -262h-93l20 93h-263l15 72l219 295h106l-218 -292h156l20 94h93z" />
    <glyph glyph-name="uni2085" unicode="&#x2085;" horiz-adv-x="408" 
d="M-46 -110q66 -17 147 -17q30 0 50 3t33 10.5t20 19.5t10 30q2 12 2 22q0 23 -15.5 36t-64.5 13h-142l54 255h306l-16 -77h-212l-22 -102h51q97 0 127.5 -31.5t30.5 -77.5q0 -22 -5 -48q-8 -41 -25 -67t-43.5 -40.5t-63.5 -20t-87 -5.5q-48 0 -88 5.5t-64 11.5z" />
    <glyph glyph-name="uni2086" unicode="&#x2086;" horiz-adv-x="408" 
d="M126 -207q-55 0 -91 14t-54 44t-19 56t-1 30q0 45 14 105q13 63 31.5 106.5t46 71t65.5 39.5t90 12q45 0 80 -5t58 -13l-16 -79q-27 7 -56.5 12t-64.5 5q-29 0 -50 -5.5t-36 -18.5t-25.5 -35.5t-19.5 -56.5q20 8 43.5 12.5t51.5 4.5q82 0 113 -33.5t31 -81.5
q0 -21 -5 -47q-16 -76 -60.5 -106.5t-125.5 -30.5zM149 15q-26 0 -47 -4.5t-42 -12.5q-8 -37 -8 -63q0 -24 11.5 -45t66.5 -21q39 0 59 15.5t28 57.5q4 15 4 27q0 21 -15 33.5t-57 12.5z" />
    <glyph glyph-name="uni2087" unicode="&#x2087;" horiz-adv-x="408" 
d="M24 262h359l-16 -72l-292 -388h-117l300 383h-251z" />
    <glyph glyph-name="uni2088" unicode="&#x2088;" horiz-adv-x="408" 
d="M115 -207q-46 0 -81.5 7t-57 23.5t-27 36t-5.5 35.5q0 17 4 37q10 45 34.5 70.5t63.5 36.5q-29 12 -39 32t-10 41q0 18 5 41q13 62 57.5 90t134.5 28q44 0 78 -7t55 -23t26 -34t5 -32q0 -16 -4 -35q-9 -47 -33 -72.5t-59 -37.5q34 -11 45.5 -32t11.5 -44q0 -18 -5 -40
q-13 -63 -59.5 -92t-139.5 -29zM164 70q45 0 65.5 13t27.5 48q2 12 2 22q0 3 -0.5 10.5t-8.5 15t-23 11t-40 3.5q-47 0 -67 -15t-28 -47q-2 -11 -2 -20q0 -19 13 -30t61 -11zM121 -130q50 0 72 15t30 50q2 12 2 21q0 21 -15 33.5t-63 12.5t-71.5 -14.5t-31.5 -52.5
q-3 -11 -3 -21q0 -19 14 -31.5t66 -12.5z" />
    <glyph glyph-name="uni2089" unicode="&#x2089;" horiz-adv-x="408" 
d="M178 271q110 0 137.5 -45t27.5 -100q0 -44 -13 -104q-13 -63 -31.5 -106.5t-46 -71t-65.5 -39.5t-91 -12q-45 0 -79.5 5t-57.5 13l16 79q26 -7 55.5 -12t64.5 -5q30 0 51 5t36.5 18.5t26 35.5t18.5 57q-20 -8 -43 -12.5t-52 -4.5q-83 0 -113.5 33.5t-30.5 80.5q0 22 5 48
q16 76 60 106.5t125 30.5zM87 122q-3 -14 -3 -25q0 -22 14.5 -35t56.5 -13q26 0 47 4t42 12q7 37 7 62t-7.5 40t-24.5 21.5t-45 6.5q-38 0 -58.5 -15.5t-28.5 -57.5z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="700" 
d="M50 342h91q5 30 10.5 60t12.5 60h-88l18 89h94q23 72 54 122.5t74.5 83t101.5 47t135 14.5q67 0 113.5 -7.5t82.5 -16.5l-20 -100q-42 11 -84 17.5t-101 6.5q-51 0 -89 -8.5t-67 -28.5t-50.5 -51.5t-38.5 -78.5h352l-18 -89h-360q-8 -29 -14 -59.5t-11 -60.5h359l-20 -89
h-350v-19q0 -35 7 -64t27.5 -48.5t54.5 -28t82 -8.5q59 0 104 6.5t91 16.5l-21 -100q-35 -9 -80.5 -16.5t-106.5 -7.5q-144 0 -203.5 61.5t-59.5 186.5v21h-102z" />
    <glyph glyph-name="uni2113" unicode="&#x2113;" 
d="M378 -16q-43 0 -77.5 11t-57.5 35t-31 55.5t-8 63.5q0 19 2 40q-21 -13 -42.5 -26t-44.5 -26l-44 79l153 93l66 313q12 55 29 92.5t42.5 60.5t60.5 33t83 10q42 0 75 -10.5t53 -33.5t24 -47.5t4 -38.5q0 -27 -8 -62q-20 -87 -111 -179t-234 -189l-3 -16q-9 -43 -9 -74
q0 -32 14.5 -61t72.5 -29q50 0 90 30t79 81l66 -61q-54 -70 -110.5 -107t-133.5 -37zM339 383q86 60 146.5 122t74.5 126q4 21 4 37q0 24 -13 40t-54 16q-46 0 -71 -24t-36 -78z" />
    <glyph glyph-name="uni2116" unicode="&#x2116;" horiz-adv-x="1423" 
d="M219 802h136l248 -609l129 609h119l-170 -802h-139l-245 606l-128 -606h-120zM885 453q14 68 38.5 113.5t59.5 74t80.5 40.5t101.5 12q69 0 116.5 -15t73.5 -47.5t29.5 -65t3.5 -48.5q0 -41 -11 -94q-14 -67 -38.5 -113.5t-59.5 -74.5t-80.5 -40.5t-101.5 -12.5
q-137 0 -179.5 51.5t-42.5 126.5q0 41 10 93zM1273 437q7 35 7 62q0 6 -1 23.5t-14.5 36.5t-39 27.5t-64.5 8.5q-75 0 -114.5 -36.5t-57.5 -120.5q-7 -34 -7 -60q0 -40 20.5 -69t99.5 -29q74 0 113.5 36.5t57.5 120.5zM836 104h450l-22 -104h-450z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="895" 
d="M234 721h-102l12 81h293l-12 -81h-100l-68 -320h-91zM506 802h109l56 -227l151 227h118l-85 -401h-90l54 256l-120 -177h-90l-46 180l-55 -259h-87z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="895" 
d="M229 362l-44 -206q25 -48 77.5 -69t134.5 -21q53 0 97 6.5t80 21.5t64.5 39.5t50.5 59.5h104q-30 -57 -68.5 -96.5t-87.5 -64.5t-110 -36.5t-135 -11.5q-102 0 -171.5 24.5t-108 77t-42.5 102t-4 68.5q0 70 19 160q23 106 61 182t95.5 125t135 72t178.5 23
q105 0 175 -24.5t107.5 -76.5t41.5 -101.5t4 -69.5q0 -68 -17 -154l-6 -30h-631zM551 736q-85 0 -150 -23.5t-113 -74.5l-42 -198h476l44 214q-26 44 -78 63t-137 19z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" 
d="M67 280l304 304l66 -65l-187 -188h510v-101h-513l190 -190l-66 -65z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" 
d="M381 628l303 -304l-65 -66l-189 188v-446h-100v447l-189 -189l-66 66z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" 
d="M334 29l210 209h-544v85h542l-208 208l54 53l304 -304l-304 -304z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" 
d="M130 290l208 -208v477h84v-477l209 208l53 -53l-304 -304l-304 304z" />
    <glyph glyph-name="uni2196" unicode="&#x2196;" 
d="M119 532h430v-75h-295l385 -384l-60 -60l-385 384v-295h-75v430z" />
    <glyph glyph-name="uni2197" unicode="&#x2197;" 
d="M566 129v294l-384 -383l-60 60l385 384h-295v75h429v-430h-75z" />
    <glyph glyph-name="uni2198" unicode="&#x2198;" 
d="M641 0h-429v75h294l-384 384l60 60l384 -384v294h75v-429z" />
    <glyph glyph-name="uni2199" unicode="&#x2199;" 
d="M194 429v-294l384 384l60 -60l-383 -384h294v-75h-430v429h75z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="735" 
d="M661 286q-18 -82 -45.5 -139.5t-67.5 -93.5t-94 -52.5t-124 -16.5q-77 0 -132.5 16.5t-87.5 54t-37 76.5t-5 60q0 48 13 110q28 135 106 198.5t216 63.5q40 0 78.5 -7.5t73.5 -20.5q-23 77 -80 140.5t-152 126.5h132q75 -49 123.5 -103.5t72.5 -117t25 -102.5t1 -48
q0 -67 -16 -145zM556 298q14 72 14 130v8q-34 15 -73 25t-84 10q-48 0 -85.5 -10.5t-65.5 -33.5t-47.5 -60.5t-30.5 -91.5q-9 -42 -9 -75q0 -49 28 -86t131 -37q48 0 84 12t62 38.5t44.5 68.5t31.5 102z" />
    <glyph glyph-name="uni2206" unicode="&#x2206;" 
d="M394 802h193l90 -802h-716zM546 104l-60 614h-18l-323 -614h401z" />
    <glyph glyph-name="product" unicode="&#x220f;" 
d="M218 697h-72l21 105h648l-21 -105h-74l-170 -806h-110l170 806h-281l-172 -806h-110z" />
    <glyph glyph-name="summation" unicode="&#x2211;" 
d="M20 -33l383 394l-218 366l15 75h587l-22 -104h-436l209 -352l-340 -351h417l-21 -104h-590z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M131 323h521l-20 -87h-521z" />
    <glyph glyph-name="radical" unicode="&#x221a;" 
d="M118 559h214l18 -420l404 848h112l-473 -987h-140l-20 462h-135z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" 
d="M417 461l9 12q41 54 88.5 80.5t99.5 26.5q34 0 62.5 -10t46 -35t20.5 -51.5t3 -39.5q0 -35 -10 -82q-12 -56 -31 -91.5t-44 -56t-54.5 -28t-62.5 -7.5q-55 0 -93 28.5t-60 79.5l-5 11l-7 -10q-41 -54 -89 -81.5t-100 -27.5q-34 0 -62.5 10.5t-45.5 35.5t-20 51.5t-3 38.5
q0 35 10 82q12 58 31.5 94t45 55.5t55 26.5t60.5 7q55 0 92 -28t59 -79zM326 440q-11 26 -27.5 42.5t-45.5 16.5q-35 0 -63 -26t-42 -93q-6 -30 -6 -51q0 -27 12 -48t48 -21q30 0 58.5 16t50.5 45l42 55zM478 321q11 -27 28 -44t45 -17q37 0 64.5 27t41.5 93q6 29 6 51
q0 26 -12 47t-48 21q-64 0 -109 -60l-43 -56z" />
    <glyph glyph-name="integral" unicode="&#x222b;" 
d="M339 -19q-15 -49 -33 -86t-44.5 -61.5t-65.5 -37t-96 -12.5q-31 0 -55.5 1.5t-51.5 6.5l20 99q41 -7 85 -7q33 0 55 4.5t37 15.5t24.5 29t17.5 44l200 644q15 50 32.5 86.5t44.5 61.5t66 37t96 12q31 0 55.5 -1.5t51.5 -5.5l-19 -95q-22 2 -42.5 3t-42.5 1
q-33 0 -55.5 -4.5t-37.5 -15.5t-24.5 -29t-17.5 -45z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M140 366q14 71 48.5 103.5t98.5 32.5q39 0 72.5 -16t62.5 -35t56 -35t53 -16q31 0 45.5 16t21.5 49l7 29h83l-9 -39q-14 -71 -48.5 -104t-100.5 -33q-39 0 -72 16t-62 35.5t-55.5 35.5t-52.5 16q-32 0 -46 -16t-22 -48l-6 -31h-83zM86 114q14 71 48.5 103.5t99.5 32.5
q39 0 72.5 -16t62.5 -35t56 -35t53 -16q31 0 45 16t21 49l7 29h83l-8 -39q-14 -71 -49 -104t-100 -33q-39 0 -72 16t-62.5 35.5t-56 35.5t-52.5 16q-32 0 -46 -16t-21 -48l-7 -31h-83z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M99 177h158l142 207h-256l19 88h298l60 87l106 -1l-59 -86h115l-18 -88h-158l-143 -207h257l-20 -88h-298l-61 -89h-106l61 89h-116z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M153 431l553 151l-19 -91l-465 -125l-3 -15l411 -124l-17 -85l-489 150zM80 88h520l-18 -88h-521z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M644 292l-552 -150l20 91l463 124l3 14l-411 125l18 86l490 -151zM80 88h520l-18 -88h-521z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" 
d="M129 401l309 401h107l138 -401l-308 -401h-107zM339 102l234 300l-100 299l-234 -302z" />
    <glyph glyph-name="uni25CC" unicode="&#x25cc;" horiz-adv-x="659" 
d="M329 -10q-60 0 -112.5 23t-91.5 62t-62 91.5t-23 112.5t23 112.5t62 92t91.5 62.5t112.5 23t112.5 -23t92 -62.5t62.5 -92t23 -112.5t-23 -112.5t-62.5 -91.5t-92 -62t-112.5 -23zM329 559q-57 0 -108 -22t-89 -60t-60 -89t-22 -109q0 -57 22 -108t60 -89t89 -60t108 -22
q58 0 109 22t89 60t60 89t22 108q0 58 -22 109t-60 89t-89 60t-109 22z" />
    <glyph glyph-name="uniFEFF" unicode="&#xfeff;" horiz-adv-x="0" 
 />
    <glyph glyph-name="glyph1" horiz-adv-x="0" 
 />
    <glyph glyph-name="ampersand.1" horiz-adv-x="833" 
d="M529 124q-28 -33 -55 -59t-58.5 -44t-69 -27.5t-87.5 -9.5q-61 0 -105.5 14t-71 44.5t-31 62.5t-4.5 50q0 36 9 81q20 92 77 146t162 62q-16 23 -29 46t-20.5 48t-8 40.5t-0.5 17.5q0 27 7 61q9 39 25 69t47 50.5t79 31t120 10.5q68 0 117.5 -8t81.5 -16l-20 -96
q-37 8 -80.5 14.5t-96.5 6.5q-45 0 -75.5 -5t-49 -16t-28 -27t-14.5 -39q-5 -21 -5 -39q0 -1 0.5 -11.5t6.5 -28t16 -34.5t25 -37l149 -204l176 206h117l-239 -281l148 -203h-124zM162 217q-7 -31 -7 -55q0 -37 20.5 -64.5t97.5 -27.5q28 0 50.5 5.5t42.5 17.5t40.5 31
t43.5 46l25 29l-131 178q-43 -1 -74 -11t-52 -29.5t-34.5 -49t-21.5 -70.5z" />
    <glyph glyph-name="f.1" horiz-adv-x="406" 
d="M-121 -114q14 -2 32 -3t32 -1q49 0 76 20t38 74l104 492h-69l16 70l72 21l11 50q11 53 29 92t46.5 65t69.5 39t99 13q29 0 54 -1.5t52 -5.5l-19 -92q-20 2 -40 3t-42 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h187l-20 -91h-186l-103 -487
q-11 -51 -26.5 -88t-40 -60.5t-59.5 -35t-85 -11.5q-25 0 -48.5 1t-45.5 5z" />
    <glyph glyph-name="commaaccentbelow" horiz-adv-x="570" 
d="M215 -53h126l-176 -145h-105z" />
    <glyph glyph-name="commaturnedabove" horiz-adv-x="570" 
d="M356 632h-123l189 170h104z" />
    <glyph glyph-name="gravecomb.case" horiz-adv-x="0" 
d="M-17 987h115l80 -140h-98z" />
    <glyph glyph-name="acutecomb.case" horiz-adv-x="0" 
d="M202 987h126l-157 -140h-110z" />
    <glyph glyph-name="uni030B.case" horiz-adv-x="0" 
d="M90 987h118l-145 -140h-101zM309 987h118l-144 -140h-102z" />
    <glyph glyph-name="uni0302.case" horiz-adv-x="0" 
d="M86 987h133l102 -140h-91l-81 97l-122 -97h-101z" />
    <glyph glyph-name="uni030C.case" horiz-adv-x="0" 
d="M-45 987h92l80 -97l123 97h101l-162 -140h-132z" />
    <glyph glyph-name="uni0306.case" horiz-adv-x="0" 
d="M126 842q-106 0 -138 28.5t-32 72.5q0 20 5 44h82q-3 -15 -3 -26q0 -4 1 -13t11 -17.5t30 -12.5t53 -4q65 0 92.5 16.5t35.5 56.5h81q-8 -39 -22 -66.5t-38.5 -45t-62.5 -25.5t-95 -8z" />
    <glyph glyph-name="uni030A.case" horiz-adv-x="0" 
d="M121 832q-32 0 -57 6t-41 19t-20 28t-4 26q0 14 4 31q12 56 48.5 80t110.5 24q32 0 57.5 -6t41.5 -19t20 -28.5t4 -27.5q0 -13 -3 -29q-13 -56 -49.5 -80t-111.5 -24zM128 892q39 0 61 10t27 37q2 8 2 14q0 16 -13 24.5t-49 8.5q-39 0 -61 -10.5t-27 -36.5q-2 -8 -2 -14
q0 -16 13 -24.5t49 -8.5z" />
    <glyph glyph-name="tildecomb.case" horiz-adv-x="0" 
d="M-63 879q12 52 38 76t72 24q30 0 56 -10.5t48.5 -22.5t41.5 -22.5t34 -10.5q20 0 30 11t14 29l4 21h69l-5 -27q-19 -99 -111 -99q-30 0 -56 10.5t-48.5 23t-41 23t-34.5 10.5q-20 0 -29.5 -11t-14.5 -31l-4 -21h-69z" />
    <glyph glyph-name="uni0307.case" horiz-adv-x="0" 
d="M90 960h112l-18 -85h-112z" />
    <glyph glyph-name="uni0308.case" horiz-adv-x="0" 
d="M-10 960h91l-18 -85h-91zM211 960h91l-18 -85h-91z" />
    <glyph glyph-name="uni0304.case" horiz-adv-x="0" 
d="M-32 949h352l-15 -68h-352z" />
    <glyph glyph-name="uni030C.alt" horiz-adv-x="0" 
d="M235 802h96l-155 -255h-57z" />
    <glyph glyph-name="uni0327.1" horiz-adv-x="0" 
d="M-164 -198h-98l167 201h95z" />
    <glyph glyph-name="Ccedilla.1" horiz-adv-x="835" 
d="M337 -14q-85 7 -142 35t-86.5 80.5t-31 95.5t-1.5 51q0 73 20 168q23 105 57.5 181t86.5 125t124 72.5t169 23.5q154 0 230 -65.5t81 -186.5h-116q-8 74 -57 110t-148 36q-71 0 -122.5 -17t-89 -54t-63 -96.5t-43.5 -143.5q-17 -78 -17 -135q0 -4 1 -36t24.5 -69t71 -54
t121.5 -17q47 0 84.5 8t68 25.5t54.5 45.5t44 68h118q-28 -69 -63.5 -117t-81.5 -78t-104 -44t-132 -14l-50 -42h12q55 0 77 -16t22 -43q0 -11 -3 -25q-4 -19 -12 -32.5t-23 -22.5t-39 -13t-59 -4q-26 0 -46.5 2.5t-37.5 6.5l11 48q20 -5 37 -7t38 -2q22 0 36 1.5t22 5.5
t12 10t5 15q1 5 1 10q0 2 -0.5 6t-6.5 7.5t-18.5 5.5t-33.5 2q-19 0 -36.5 -1t-33.5 -4l10 44z" />
    <glyph glyph-name="germandbls.cap" horiz-adv-x="1575" 
d="M64 136q60 -17 139 -30t167 -13q53 0 90 6t62 18.5t39 33.5t21 51q6 25 6 44q0 7 -1.5 21t-20.5 31.5t-56.5 31t-100.5 25.5q-83 15 -137.5 37t-83.5 54t-32.5 60.5t-3.5 43.5q0 34 9 76q10 44 30 79.5t58 60.5t97 38.5t147 13.5t156 -9.5t131 -26.5l-23 -110
q-63 17 -130 27t-146 10q-54 0 -91.5 -5.5t-62 -17t-37.5 -29.5t-18 -42q-5 -25 -5 -44q0 -7 1.5 -21.5t19.5 -31t54.5 -28t96.5 -23.5q86 -16 142 -36.5t86 -51.5t34 -60t4 -45q0 -36 -10 -82q-12 -52 -34.5 -91t-61.5 -65t-98 -39t-143 -13q-97 0 -177 11.5t-140 29.5z
M852 136q60 -17 139 -30t167 -13q53 0 90 6t62 18.5t39 33.5t21 51q6 25 6 44q0 7 -1.5 21t-20.5 31.5t-56.5 31t-100.5 25.5q-83 15 -137.5 37t-83.5 54t-32.5 60.5t-3.5 43.5q0 34 9 76q10 44 30 79.5t58 60.5t97 38.5t147 13.5t156 -9.5t131 -26.5l-23 -110
q-63 17 -130 27t-146 10q-54 0 -91.5 -5.5t-62 -17t-37.5 -29.5t-18 -42q-5 -25 -5 -44q0 -7 1.5 -21.5t19.5 -31t54.5 -28t96.5 -23.5q86 -16 142 -36.5t86 -51.5t34 -60t4 -45q0 -36 -10 -82q-12 -52 -34.5 -91t-61.5 -65t-98 -39t-143 -13q-97 0 -177 11.5t-140 29.5z
" />
    <glyph glyph-name="ccedilla.1" horiz-adv-x="618" 
d="M253 -13q-60 6 -103 26t-67.5 57.5t-27 72t-2.5 47.5q0 48 12 109q16 74 43.5 126t69 85.5t97 49t128.5 15.5q63 0 115 -7.5t97 -18.5l-17 -94q-45 9 -93 15t-105 6q-51 0 -89 -10.5t-65.5 -33t-45.5 -59t-29 -88.5q-9 -44 -9 -77q0 -10 2 -34t21 -47.5t53.5 -33.5
t83.5 -10q57 0 105 5.5t100 14.5l-17 -93q-49 -14 -99.5 -20t-101.5 -6l-50 -42h12q55 0 77 -16t22 -43q0 -11 -3 -25q-4 -19 -12 -32.5t-23 -22.5t-39 -13t-59 -4q-26 0 -46.5 2.5t-37.5 6.5l11 48q20 -5 37 -7t38 -2q22 0 36 1.5t22 5.5t12 10t5 15q1 5 1 10q0 2 -0.5 6
t-6.5 7.5t-18.5 5.5t-33.5 2q-19 0 -36.5 -1t-33.5 -4l10 44z" />
    <glyph glyph-name="i.trk" horiz-adv-x="291" 
d="M193 768h112l-20 -98h-113zM153 559h106l-119 -559h-105z" />
    <glyph glyph-name="f_b.1" horiz-adv-x="1105" 
d="M-129 -114q14 -2 32 -3t32 -1q49 0 76 20t38 74l104 492h-69l16 70l72 21l11 50q11 53 29 92t46.5 65t69.5 39t99 13q23 0 43 -2t41 -5l-18 -92q-15 2 -30 3t-31 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h165l-20 -91h-164l-103 -487
q-11 -51 -26.5 -88t-40 -60.5t-60 -35t-84.5 -11.5q-25 0 -48.5 1t-45.5 5zM584 802h106l-57 -269q45 19 91.5 30.5t92.5 11.5q68 0 118 -18t80 -56t35 -77t5 -59q0 -47 -12 -104q-16 -75 -43 -128t-66.5 -86t-91 -48t-116.5 -15q-57 0 -110 15t-102 37l-26 -36h-74z
M788 477q-47 0 -92 -12t-84 -29l-66 -314q34 -17 74.5 -28.5t88.5 -11.5q99 0 152.5 44t75.5 147q9 43 9 75q0 11 -2.5 35.5t-21.5 48.5t-53 34.5t-81 10.5z" />
    <glyph glyph-name="f_f.1" horiz-adv-x="752" 
d="M-121 -114q14 -2 32 -3t32 -1q49 0 76 20t38 74l104 492h-69l16 70l72 21l11 50q11 53 29 92t46.5 65t69.5 39t99 13q25 0 45 -2t43 -5l-18 -92q-17 2 -32.5 3t-32.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h240l11 50q11 53 29 92t46.5 65t69.5 39
t99 13q29 0 54.5 -1.5t51.5 -5.5l-19 -92q-20 2 -40 3t-42 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h187l-20 -91h-186l-103 -487q-11 -51 -26.5 -88t-40 -60.5t-59.5 -35t-85 -11.5q-25 0 -48.5 1t-45.5 5l20 94q14 -2 32.5 -3t32.5 -1q48 0 75 20
t38 74l104 492h-240l-103 -487q-11 -51 -26.5 -88t-40 -60.5t-59.5 -35t-85 -11.5q-25 0 -48.5 1t-45.5 5z" />
    <glyph glyph-name="f_h.1" horiz-adv-x="1122" 
d="M-129 -114q14 -2 32 -3t32 -1q49 0 76 20t38 74l104 492h-69l16 70l72 21l11 50q11 53 29 92t46.5 65t69.5 39t99 13q23 0 43 -2t41 -5l-18 -92q-15 2 -30 3t-31 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h165l-20 -91h-164l-103 -487
q-11 -51 -26.5 -88t-40 -60.5t-60 -35t-84.5 -11.5q-25 0 -48.5 1t-45.5 5zM584 802h106l-58 -275q50 21 104 34.5t110 13.5q126 0 168 -50t42 -113q0 -26 -6 -56l-76 -356h-105l75 356q4 18 4 33q0 34 -23.5 59.5t-105.5 25.5q-54 0 -107 -10.5t-100 -28.5l-92 -435h-106z
" />
    <glyph glyph-name="fi.1" horiz-adv-x="657" 
d="M-129 -114q14 -2 32 -3t32 -1q49 0 76 20t38 74l104 492h-69l16 70l72 21l11 50q11 53 29 92t46.5 65t69.5 39t98 13q18 0 34.5 -2t30.5 -5l-19 -92q-11 2 -20.5 3t-20.5 1q-33 0 -56.5 -5.5t-40 -18t-27 -33.5t-17.5 -52l-12 -55h346l-119 -559h-105l99 468h-240
l-103 -487q-11 -51 -26.5 -88t-40 -60.5t-60 -35t-84.5 -11.5q-25 0 -48.5 1t-45.5 5zM564 788h112l-20 -98h-113z" />
    <glyph glyph-name="f_j.1" horiz-adv-x="656" 
d="M-129 -114q14 -2 32 -3t32 -1q49 0 76 20t38 74l104 492h-69l16 70l72 21l11 50q11 53 29 92t46.5 65t69.5 39t98 13q18 0 33.5 -2t31.5 -5l-19 -92q-11 2 -20.5 3t-20.5 1q-33 0 -56.5 -5.5t-40 -18t-27 -33.5t-17.5 -52l-12 -55h346l-124 -583q-11 -51 -27 -87t-40 -59
t-59 -33.5t-83 -10.5q-25 0 -49.5 1t-44.5 5l17 81q14 -2 33 -3t33 -1q48 0 74.5 23.5t39.5 83.5l104 492h-239l-103 -487q-11 -51 -26.5 -88t-40 -60.5t-60 -35t-84.5 -11.5q-25 0 -48.5 1t-45.5 5zM563 788h112l-19 -98h-113z" />
    <glyph glyph-name="f_k.1" horiz-adv-x="1046" 
d="M-129 -114q14 -2 32 -3t32 -1q49 0 76 20t38 74l104 492h-69l16 70l72 21l11 50q11 53 29 92t46.5 65t69.5 39t99 13q23 0 43 -2t41 -5l-18 -92q-15 2 -30 3t-31 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h165l-20 -91h-164l-103 -487
q-11 -51 -26.5 -88t-40 -60.5t-60 -35t-84.5 -11.5q-25 0 -48.5 1t-45.5 5zM584 802h107l-98 -463l328 220h147l-364 -248l258 -311h-131l-244 310l-66 -310h-107z" />
    <glyph glyph-name="fl.1" horiz-adv-x="670" 
d="M-129 -114q14 -2 32 -3t32 -1q49 0 76 20t38 74l104 492h-69l16 70l72 21l11 50q11 53 29 92t46.5 65t69.5 39t99 13q23 0 43 -2t41 -5l-18 -92q-15 2 -30 3t-31 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h165l-20 -91h-164l-103 -487
q-11 -51 -26.5 -88t-40 -60.5t-60 -35t-84.5 -11.5q-25 0 -48.5 1t-45.5 5zM584 802h106l-170 -802h-106z" />
    <glyph glyph-name="f_f_b.1" horiz-adv-x="1451" 
d="M-129 -114q14 -2 32 -3t32 -1q49 0 76 20t38 74l104 492h-68l15 70l72 21l11 50q11 53 29 92t46.5 65t69.5 39t99 13q25 0 45 -2t43 -5l-18 -92q-17 2 -32.5 3t-32.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h240l11 50q11 53 29 92t46.5 65t69.5 39
t99 13q23 0 43 -2t41 -5l-18 -92q-15 2 -30 3t-31 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h165l-20 -91h-164l-103 -487q-11 -51 -26.5 -88t-40 -60.5t-59.5 -35t-85 -11.5q-25 0 -48.5 1t-45.5 5l20 94q14 -2 32.5 -3t32.5 -1q48 0 75 20t38 74
l104 492h-240l-103 -487q-11 -51 -26.5 -88t-40 -60.5t-60 -35t-84.5 -11.5q-25 0 -48.5 1t-45.5 5zM929 802h106l-57 -269q46 19 92.5 30.5t92.5 11.5q68 0 118 -18t79.5 -56t34.5 -77t5 -59q0 -47 -12 -104q-16 -75 -43 -128t-66.5 -86t-91 -48t-116.5 -15q-57 0 -110 15
t-102 37l-26 -36h-74zM1133 477q-47 0 -92 -12t-84 -29l-66 -314q34 -17 74.5 -28.5t88.5 -11.5q50 0 87.5 10.5t65.5 33.5t46.5 59t29.5 88q9 41 9 73q0 12 -3 37t-22 49t-52.5 34.5t-81.5 10.5z" />
    <glyph glyph-name="f_f_h.1" horiz-adv-x="1469" 
d="M-129 -114q14 -2 32 -3t32 -1q49 0 76 20t38 74l104 492h-68l15 70l72 21l11 50q11 53 29 92t46.5 65t69.5 39t99 13q25 0 45 -2t43 -5l-18 -92q-17 2 -32.5 3t-32.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h240l11 50q11 53 29 92t46.5 65t69.5 39
t99 13q29 0 52.5 -1.5t49.5 -5.5l-19 -92q-19 2 -38 3t-40 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h183l-20 -91h-182l-103 -487q-11 -51 -26.5 -88t-40 -60.5t-59.5 -35t-85 -11.5q-25 0 -48.5 1t-45.5 5l20 94q14 -2 32.5 -3t32.5 -1q48 0 75 20
t38 74l104 492h-240l-103 -487q-11 -51 -26.5 -88t-40 -60.5t-60 -35t-84.5 -11.5q-25 0 -48.5 1t-45.5 5zM929 802h106l-58 -275q50 21 104 34.5t110 13.5q126 0 168.5 -50.5t42.5 -112.5q0 -26 -6 -56l-76 -356h-106l75 356q4 19 4 34q0 34 -23 59t-105 25
q-54 0 -107 -10.5t-101 -28.5l-92 -435h-106z" />
    <glyph glyph-name="f_f_i.1" horiz-adv-x="1002" 
d="M-129 -114q14 -2 32 -3t32 -1q49 0 76 20t38 74l104 492h-69l16 70l72 21l11 50q11 53 29 92t46.5 65t69.5 39t99 13q25 0 45 -2t43 -5l-18 -92q-17 2 -32.5 3t-32.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h240l11 50q11 53 29 92t46.5 65t69.5 39
t98 13q18 0 33.5 -2t30.5 -5l-18 -92q-10 2 -20 3t-21 1q-32 0 -55.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h346l-119 -559h-106l99 468h-239l-103 -487q-11 -51 -26.5 -88t-40 -60.5t-59.5 -35t-85 -11.5q-25 0 -48.5 1t-45.5 5l20 94q14 -2 32.5 -3t32.5 -1
q48 0 75 20t38 74l104 492h-240l-103 -487q-11 -51 -26.5 -88t-40 -60.5t-60 -35t-84.5 -11.5q-25 0 -48.5 1t-45.5 5zM910 788h111l-20 -98h-111z" />
    <glyph glyph-name="f_f_j.1" horiz-adv-x="1002" 
d="M-129 -114q14 -2 32 -3t32 -1q49 0 76 20t38 74l104 492h-69l16 70l72 21l11 50q11 53 29 92t46.5 65t69.5 39t99 13q25 0 45 -2t43 -5l-18 -92q-17 2 -32.5 3t-32.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h240l11 50q11 53 29 92t46.5 65t69.5 39
t98 13q18 0 34.5 -2t30.5 -5l-18 -92q-11 2 -21 3t-21 1q-32 0 -55.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h346l-124 -583q-10 -51 -26 -87t-40.5 -59t-59.5 -33.5t-83 -10.5q-26 0 -49.5 1t-43.5 5l19 94q14 -2 32 -3t32 -1q48 0 75 20t38 74l104 492h-239
l-103 -487q-11 -51 -26.5 -88t-40 -60.5t-59.5 -35t-85 -11.5q-25 0 -48.5 1t-45.5 5l20 94q14 -2 32.5 -3t32.5 -1q48 0 75 20t38 74l104 492h-240l-103 -487q-11 -51 -26.5 -88t-40 -60.5t-60 -35t-84.5 -11.5q-25 0 -48.5 1t-45.5 5zM909 788h112l-20 -98h-112z" />
    <glyph glyph-name="f_f_k.1" horiz-adv-x="1392" 
d="M-129 -114q14 -2 32 -3t32 -1q49 0 76 20t38 74l104 492h-68l15 70l72 21l11 50q11 53 29 92t46.5 65t69.5 39t99 13q25 0 45 -2t43 -5l-18 -92q-17 2 -32.5 3t-32.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h240l11 50q11 53 29 92t46.5 65t69.5 39
t99 13q45 0 83 -7l-18 -92q-15 2 -29.5 3t-30.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h165l-20 -91h-164l-103 -487q-11 -51 -26.5 -88t-40 -60.5t-59.5 -35t-85 -11.5q-25 0 -48.5 1t-45.5 5l20 94q14 -2 32.5 -3t32.5 -1q48 0 75 20t38 74l104 492
h-240l-103 -487q-11 -51 -26.5 -88t-40 -60.5t-60 -35t-84.5 -11.5q-25 0 -48.5 1t-45.5 5zM929 802h106l-98 -464l329 221h147l-365 -248l258 -311h-131l-244 311l-66 -311h-106z" />
    <glyph glyph-name="f_f_l.1" horiz-adv-x="1016" 
d="M-129 -114q14 -2 32 -3t32 -1q49 0 76 20t38 74l104 492h-68l15 70l72 21l11 50q11 53 29 92t46.5 65t69.5 39t99 13q25 0 45 -2t43 -5l-18 -92q-17 2 -32.5 3t-32.5 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h240l11 50q11 53 29 92t46.5 65t69.5 39
t99 13q23 0 43 -2t41 -5l-18 -92q-15 2 -30 3t-31 1q-33 0 -56.5 -5.5t-40.5 -18t-27.5 -33.5t-17.5 -52l-12 -55h165l-20 -91h-164l-103 -487q-11 -51 -26.5 -88t-40 -60.5t-59.5 -35t-85 -11.5q-25 0 -48.5 1t-45.5 5l20 94q14 -2 32.5 -3t32.5 -1q48 0 75 20t38 74
l104 492h-240l-103 -487q-11 -51 -26.5 -88t-40 -60.5t-60 -35t-84.5 -11.5q-25 0 -48.5 1t-45.5 5zM930 802h105l-170 -802h-106z" />
    <glyph glyph-name="exclamdown.case" horiz-adv-x="380" 
d="M329 686h-121l25 116h120zM217 190l-40 -190h-107l40 190l88 363h85z" />
    <glyph glyph-name="questiondown.case" horiz-adv-x="627" 
d="M543 694h-119l24 116h119zM481 424l-14 -70h-115q-55 0 -92.5 -6.5t-62 -21t-38 -37.5t-20.5 -56q-5 -25 -5 -46q0 -10 2.5 -27.5t22.5 -34.5t59 -24.5t100 -7.5q60 0 120.5 7.5t114.5 19.5l-18 -101q-50 -11 -110 -18.5t-125 -7.5q-163 0 -218.5 49.5t-55.5 125.5
q0 30 7 66q11 56 33.5 95.5t59 64t88.5 35.5t122 11h40l37 121h85z" />
    <glyph glyph-name="hyphen.case" horiz-adv-x="380" 
d="M57 352l20 100h302l-21 -100h-301z" />
    <glyph glyph-name="guilsinglleft.case" horiz-adv-x="333" 
d="M266 633h111l-207 -234l107 -230h-109l-109 232z" />
    <glyph glyph-name="guilsinglright.case" horiz-adv-x="333" 
d="M119 170h-111l207 233l-106 231h108l110 -232z" />
    <glyph glyph-name="guillemotleft.case" horiz-adv-x="572" 
d="M266 633h111l-207 -234l107 -230h-109l-109 232zM505 633h111l-207 -234l107 -230h-109l-109 232z" />
    <glyph glyph-name="guillemotright.case" horiz-adv-x="572" 
d="M119 170h-111l207 233l-106 231h108l110 -232zM358 170h-111l207 233l-106 231h108l110 -232z" />
    <glyph glyph-name="parenleft.case" horiz-adv-x="368" 
d="M170 -73q-26 41 -46 91.5t-29 106.5t-9 115q0 11 1 53.5t19 129.5q29 138 94 251t160 201h109q-46 -42 -87 -94.5t-74.5 -113t-59 -128t-40.5 -138.5q-21 -96 -21 -181q0 -46 12 -127.5t69 -165.5h-98z" />
    <glyph glyph-name="parenright.case" horiz-adv-x="368" 
d="M250 875q26 -41 46 -91.5t29 -106.5t9 -115q0 -11 -1 -53.5t-19 -129.5q-29 -138 -94 -251t-160 -201h-110q46 42 87 94.5t74.5 113t59 128t40.5 138.5q21 96 21 181q0 46 -12 127.5t-69 165.5h99z" />
    <glyph glyph-name="braceleft.case" horiz-adv-x="368" 
d="M268 -73h-37q-49 0 -84 9t-55 30.5t-23 45t-3 35.5q0 30 8 68l33 155q5 24 5.5 32.5t0.5 9.5q0 15 -6.5 25t-21 14.5t-39.5 4.5h-25l19 90h26q24 0 40 4.5t27 14.5t17.5 26t11.5 40l42 199q9 41 22.5 68.5t35.5 44.5t54 24t76 7h77l-18 -90h-52q-47 0 -67.5 -13.5
t-28.5 -50.5l-38 -178q-13 -62 -35.5 -97.5t-66.5 -50.5q48 -21 48 -83q0 -23 -6 -51l-33 -153q-6 -25 -6 -42q0 -1 0.5 -10.5t9 -19t26 -13t45.5 -3.5h40z" />
    <glyph glyph-name="braceright.case" horiz-adv-x="368" 
d="M217 71q-9 -41 -23 -69t-36 -44.5t-53.5 -23.5t-75.5 -7h-77l18 90h51q47 0 67.5 14t28.5 51l38 178q13 61 35.5 96t65.5 50q-47 24 -47 87q0 22 6 49l34 152q6 26 6 43q0 1 -0.5 10.5t-9.5 19.5t-26.5 13.5t-45.5 3.5h-40l19 90h38q49 0 84 -9t55 -30.5t23 -44.5t3 -35
q0 -30 -9 -69l-32 -154q-5 -24 -5.5 -32.5t-0.5 -9.5q0 -15 6.5 -25t21 -14.5t38.5 -4.5h26l-20 -90h-26q-24 0 -40 -4.5t-27 -14.5t-17.5 -26t-11.5 -40z" />
    <glyph glyph-name="bracketleft.case" horiz-adv-x="368" 
d="M208 875h261l-18 -90h-161l-163 -768h160l-19 -90h-261z" />
    <glyph glyph-name="bracketright.case" horiz-adv-x="368" 
d="M211 -73h-260l19 90h159l163 768h-159l19 90h260z" />
    <glyph glyph-name="endash.case" 
d="M15 352l22 100h760l-22 -100h-760z" />
    <glyph glyph-name="emdash.case" horiz-adv-x="1520" 
d="M15 352l22 100h1520l-22 -100h-1520z" />
    <glyph glyph-name="bullet.case" horiz-adv-x="449" 
d="M234 270q-67 0 -92 27.5t-25 67.5q0 22 6 48q14 65 50 92.5t96 27.5q32 0 56.5 -8t38.5 -25t16.5 -35t2.5 -28q0 -21 -5 -46q-14 -66 -50 -93.5t-94 -27.5z" />
    <glyph glyph-name="periodcentered.case" horiz-adv-x="380" 
d="M170 459h120l-24 -116h-120z" />
    <glyph glyph-name="at.case" horiz-adv-x="930" 
d="M416 -16q-94 0 -165 24t-113 79.5t-48.5 112.5t-6.5 84q0 74 20 170q20 92 51 160t83 113t129 67.5t188 22.5q104 0 178 -24t116.5 -74.5t49.5 -102.5t7 -80q0 -60 -16 -136l-36 -167q-6 -30 -16.5 -54.5t-27.5 -41.5t-41.5 -26t-60.5 -9q-79 0 -114.5 28.5t-35.5 74.5
q0 6 1 12q-28 -14 -63 -23.5t-81 -9.5q-34 0 -63 8t-47.5 25.5t-24 38t-5.5 35.5q0 19 5 41q13 60 49 87t105 27h187l7 26q3 11 3 19q0 6 -2 14.5t-15.5 16.5t-40.5 11.5t-72 3.5q-41 0 -77.5 -4t-74.5 -9l20 83q19 3 35 5.5t33 4.5t36 3t43 1q64 0 105.5 -9.5t64 -28.5
t26.5 -39.5t4 -32.5q0 -21 -6 -46l-47 -223q-3 -14 -3 -25t4.5 -24t38.5 -13q32 0 43.5 15t16.5 36l36 169q16 77 16 133q0 15 -3.5 54t-35 76.5t-89 53t-141.5 15.5q-85 0 -144.5 -16.5t-100 -52t-65.5 -89.5t-41 -129q-18 -86 -18 -150q0 -16 3.5 -60t35.5 -87t89.5 -60.5
t138.5 -17.5q16 0 37 1t37 3l-22 -85q-15 -3 -37 -4t-42 -1zM437 261q42 0 77.5 11t62.5 25l22 76h-135q-41 0 -61 -11t-27 -39q-2 -10 -2 -18q0 -20 14 -32t49 -12z" />
    <glyph glyph-name="dollar.weight" horiz-adv-x="700" 
d="M59 169q51 -15 119.5 -28.5t145.5 -13.5q53 0 88.5 5t57.5 16.5t33.5 30.5t17.5 46q5 25 5 42q0 8 -2 22t-19 28.5t-51 23t-89 17.5q-73 11 -122.5 26t-76.5 41t-31.5 53t-4.5 43q0 32 9 73q9 43 28 75t51.5 53.5t80 33t113.5 13.5l20 92h89l-20 -94q54 -4 101.5 -12.5
t85.5 -20.5l-17 -100q-53 14 -114 24.5t-126 10.5q-50 0 -84.5 -4.5t-57 -14.5t-34.5 -26.5t-17 -40.5q-5 -23 -5 -40q0 -7 2 -20t19.5 -25.5t51.5 -21t89 -17.5q75 -12 125 -28t77 -43t30.5 -53.5t3.5 -40.5q0 -33 -10 -78q-10 -47 -28.5 -81t-50 -56.5t-79.5 -33.5
t-118 -13l-20 -92h-89l20 94q-68 5 -122 15.5t-94 24.5z" />
    <glyph glyph-name="cent.weight" horiz-adv-x="700" 
d="M547 802l-23 -106q48 -2 91 -8.5t84 -14.5l-21 -101q-60 11 -115 16.5t-117 5.5q-56 0 -96 -9t-67.5 -30t-44 -55.5t-27.5 -85.5q-10 -47 -10 -83q0 -5 1 -27.5t20.5 -47t58 -35.5t99.5 -11q63 0 119 5t115 15l-21 -98q-92 -22 -194 -23l-23 -109h-88l23 110
q-68 5 -115.5 25.5t-74 59t-29.5 74.5t-3 51q0 48 12 108q16 75 43.5 125.5t68 82t96 46t126.5 16.5l23 104h89z" />
    <glyph glyph-name="zeroslash" horiz-adv-x="700" 
d="M448 818q81 0 137 -23t85 -74.5t30.5 -97.5t1.5 -55q0 -78 -22 -185q-22 -103 -51.5 -178.5t-73 -124.5t-104 -72.5t-146.5 -23.5q-81 0 -137 23t-85 74.5t-30.5 97.5t-1.5 55q0 78 22 185q22 104 51.5 179t73 124t104.5 72.5t146 23.5zM181 401q-22 -103 -22 -170
l404 443q-19 22 -49.5 31.5t-74.5 9.5q-55 0 -95.5 -16.5t-70.5 -53.5t-52 -97t-40 -147zM314 87q56 0 96 16.5t70 53.5t52 97t40 147q21 100 21 165v6l-403 -444q19 -22 49.5 -31.5t74.5 -9.5z" />
    <glyph glyph-name="dollar.lt" 
d="M89 169q42 -13 98 -24.5t119 -15.5l50 235q-62 11 -103.5 27t-64 41.5t-25.5 50.5t-3 37q0 32 9 74q9 43 28 75t51.5 53.5t80 33t113.5 13.5l20 92h89l-20 -94q54 -4 101.5 -12.5t85.5 -20.5l-17 -100q-43 11 -91.5 20.5t-99.5 12.5l-46 -215q63 -12 105 -28.5t64.5 -43
t25 -51t2.5 -35.5q0 -34 -10 -78q-10 -47 -28.5 -81t-50 -56.5t-79.5 -33.5t-118 -13l-20 -92h-89l20 94q-68 5 -122 15.5t-94 24.5zM268 583q-5 -23 -5 -40q0 -4 1 -15.5t13.5 -24t37 -20.5t63.5 -16l43 201q-77 -3 -110.5 -22.5t-42.5 -62.5zM551 225q5 24 5 42
q0 4 -1 16.5t-13 26.5t-36.5 23.5t-63.5 16.5l-47 -222q41 2 68.5 8t45.5 18t27.5 29.5t14.5 41.5z" />
    <glyph glyph-name="dollar.lt.weight" 
d="M89 169q51 -15 119.5 -28.5t145.5 -13.5q53 0 88.5 5t57.5 16.5t33.5 30.5t17.5 46q5 25 5 42q0 8 -2 22t-19 28.5t-51 23t-89 17.5q-73 11 -122.5 26t-76.5 41t-31.5 53t-4.5 43q0 32 9 73q9 43 28 75t51.5 53.5t80 33t113.5 13.5l20 92h89l-20 -94q54 -4 101.5 -12.5
t85.5 -20.5l-17 -100q-53 14 -114 24.5t-126 10.5q-50 0 -84.5 -4.5t-57 -14.5t-34.5 -26.5t-17 -40.5q-5 -23 -5 -40q0 -7 2 -20t19.5 -25.5t51.5 -21t89 -17.5q75 -12 125 -28t77 -43t30.5 -53.5t3.5 -40.5q0 -33 -10 -78q-10 -47 -28.5 -81t-50 -56.5t-79.5 -33.5
t-118 -13l-20 -92h-89l20 94q-68 5 -122 15.5t-94 24.5z" />
    <glyph glyph-name="Euro.lt" 
d="M80 342h91q5 30 10.5 60t12.5 60h-88l18 89h94q23 72 54 122.5t74.5 83t101.5 47t135 14.5q67 0 113.5 -7.5t82.5 -16.5l-20 -100q-42 11 -84 17.5t-101 6.5q-51 0 -89 -8.5t-67 -28.5t-50.5 -51.5t-38.5 -78.5h352l-18 -89h-360q-8 -29 -14 -59.5t-11 -60.5h359l-20 -89
h-350v-19q0 -35 7 -64t27.5 -48.5t54.5 -28t82 -8.5q59 0 104 6.5t91 16.5l-21 -100q-35 -9 -80.5 -16.5t-106.5 -7.5q-144 0 -203.5 61.5t-59.5 186.5v21h-102z" />
    <glyph glyph-name="cent.lt" 
d="M577 802l-23 -106q50 -2 95.5 -8.5t87.5 -14.5l-21 -101q-48 9 -92 14t-92 7l-81 -383q53 2 101.5 6.5t99.5 13.5l-21 -98q-95 -21 -202 -24l-23 -108h-88l23 110q-71 5 -120 25.5t-76 59t-30.5 75t-3.5 52.5q0 47 12 106q16 75 44 126t70 82.5t99 45.5t130 16l22 104h89
zM233 414q-10 -48 -10 -83q0 -2 0.5 -21t15 -43.5t45 -37.5t79.5 -17l81 381q-49 -2 -84.5 -13t-60.5 -32t-40.5 -54t-25.5 -80z" />
    <glyph glyph-name="cent.lt.weight" 
d="M577 802l-23 -106q48 -2 91 -8.5t84 -14.5l-21 -101q-60 11 -115 16.5t-117 5.5q-56 0 -96 -9t-67.5 -30t-44 -55.5t-27.5 -85.5q-10 -47 -10 -83q0 -5 1 -27.5t20.5 -47t58 -35.5t99.5 -11q63 0 119 5t115 15l-21 -98q-92 -22 -194 -23l-23 -109h-88l23 110
q-68 5 -115.5 25.5t-74 59t-29.5 74.5t-3 51q0 48 12 108q16 75 43.5 125.5t68 82t96 46t126.5 16.5l23 104h89z" />
    <glyph glyph-name="sterling.lt" 
d="M97 96q51 0 74.5 21t33.5 67l37 175h-143l19 89h143l34 160q11 53 30 92.5t51.5 65.5t80 39t116.5 13q68 0 119.5 -8t89.5 -18l-21 -98q-40 11 -84.5 17t-102.5 6q-85 0 -124 -28t-51 -84l-33 -157h308l-19 -89h-308l-35 -165q-8 -33 -22 -57t-37 -41h403l-21 -96h-612
l20 96h54z" />
    <glyph glyph-name="yen.lt" 
d="M321 253h-224l19 89h223l26 120h-224l19 89h158l-149 251h117l143 -248l264 248h122l-261 -251h163l-19 -89h-224l-26 -120h225l-19 -89h-224l-54 -253h-109z" />
    <glyph glyph-name="florin.lt" 
d="M141 448h215l38 177q11 51 29 87.5t48.5 60t75.5 34.5t109 11q29 0 54 -1.5t52 -5.5l-20 -100q-23 4 -46.5 5t-46.5 1q-34 0 -59 -4.5t-43 -16t-29 -30t-17 -46.5l-37 -172h221l-19 -89h-220l-84 -393q-9 -44 -25 -77.5t-44.5 -56t-71.5 -34.5t-106 -12q-37 0 -62.5 1.5
t-51.5 5.5l21 100q23 -4 45.5 -5t45.5 -1q37 0 62 5t41 15.5t25 26.5t14 38l82 387h-216z" />
    <glyph glyph-name="zeroslash.lt" 
d="M478 818q81 0 137 -23t85 -74.5t30.5 -97.5t1.5 -55q0 -78 -22 -185q-22 -103 -51.5 -178.5t-73 -124.5t-104 -72.5t-146.5 -23.5q-81 0 -137 23t-85 74.5t-30.5 97.5t-1.5 55q0 78 22 185q22 104 51.5 179t73 124t104.5 72.5t146 23.5zM211 401q-22 -103 -22 -170
l404 443q-19 22 -49.5 31.5t-74.5 9.5q-55 0 -95.5 -16.5t-70.5 -53.5t-52 -97t-40 -147zM344 87q56 0 96 16.5t70 53.5t52 97t40 147q21 100 21 165v6l-403 -444q19 -22 49.5 -31.5t74.5 -9.5z" />
    <glyph glyph-name="zero.lt" 
d="M478 818q81 0 137 -23t85 -74.5t30.5 -97.5t1.5 -55q0 -78 -22 -185q-22 -103 -51.5 -178.5t-73 -124.5t-104 -72.5t-146.5 -23.5q-81 0 -137 23t-85 74.5t-30.5 97.5t-1.5 55q0 78 22 185q22 104 51.5 179t73 124t104.5 72.5t146 23.5zM344 87q56 0 96 16.5t70 53.5
t52 97t40 147q19 88 20.5 122t1.5 42q0 49 -15 83.5t-49.5 50.5t-90.5 16q-55 0 -95.5 -16.5t-70.5 -53.5t-52 -97t-40 -147q-19 -88 -20.5 -122t-1.5 -42q0 -49 15 -83.5t49.5 -50.5t90.5 -16z" />
    <glyph glyph-name="one.lt" 
d="M82 100h244l124 578l-253 -114l-32 96l315 142h104l-149 -702h219l-21 -100h-571z" />
    <glyph glyph-name="two.lt" 
d="M478 818q158 0 209 -47t51 -116q0 -27 -6 -58q-9 -38 -20 -64t-31 -48t-52 -42.5t-82 -47.5l-258 -138q-27 -15 -44.5 -26.5t-28.5 -23.5t-17 -27t-11 -36l-9 -45h452l-21 -99h-561l34 159q8 35 18.5 61t28 47t44 39.5t64.5 38.5l268 143q34 18 55 30.5t33.5 24t18.5 25
t10 33.5q3 18 3 32q0 11 -3 26.5t-22.5 29t-54.5 20t-89 6.5q-61 0 -124 -7.5t-114 -18.5l21 103q51 11 106.5 18.5t131.5 7.5z" />
    <glyph glyph-name="three.lt" 
d="M61 102q55 -13 113 -20t126 -7t111.5 7.5t70.5 23.5t41 41.5t22 61.5q7 32 7 55q0 8 -2 25.5t-19 34.5t-50 24.5t-84 7.5h-190l19 101h188q44 0 77 5t56.5 17t37.5 33.5t21 54.5q6 30 6 55q0 9 -2 28.5t-21.5 39t-56 29t-93.5 9.5q-71 0 -125.5 -6.5t-108.5 -18.5l18 89
q46 11 104.5 18.5t130.5 7.5q165 0 217.5 -51t52.5 -125q0 -34 -9 -74q-16 -77 -58 -115t-105 -52q64 -19 84.5 -59t20.5 -82q0 -33 -9 -74q-12 -54 -34.5 -92.5t-63 -62.5t-102 -35.5t-152.5 -11.5q-86 0 -148 8t-110 20z" />
    <glyph glyph-name="four.lt" 
d="M686 456l-97 -456h-106l38 179h-477l18 85l400 538h126l-387 -522h341l37 176h107z" />
    <glyph glyph-name="five.lt" 
d="M63 114q107 -27 252 -27q60 0 100 8t65.5 24.5t40 42t21.5 61.5q6 26 6 48q0 48 -34.5 76t-138.5 28h-226l91 427h498l-21 -102h-390l-48 -223h118q155 0 209.5 -52t54.5 -132q0 -37 -10 -82q-13 -64 -38.5 -108t-67 -70t-101.5 -37.5t-143 -11.5q-84 0 -149.5 8.5
t-110.5 19.5z" />
    <glyph glyph-name="six.lt" 
d="M353 -16q-89 0 -147.5 23.5t-88 75.5t-30 95.5t-0.5 46.5q0 82 24 196q24 110 55.5 186t77.5 123t111 67.5t155 20.5q71 0 124.5 -8.5t94.5 -20.5l-21 -101q-43 11 -92.5 19t-109.5 8q-63 0 -107 -12t-75.5 -42t-54 -81.5t-40.5 -129.5q42 17 89.5 28.5t110.5 11.5
q65 0 115 -16t81 -49t38.5 -69t7.5 -60q0 -34 -9 -75q-13 -66 -38 -111.5t-62.5 -73t-89 -40t-119.5 -12.5zM404 390q-60 0 -105.5 -10.5t-90.5 -27.5q-15 -76 -15 -127.5t17.5 -83t54.5 -45.5t96 -14q41 0 73 7.5t55.5 25t39.5 47t25 73.5q5 26 5 48q0 14 -4 34.5
t-23.5 38.5t-52 26t-75.5 8z" />
    <glyph glyph-name="seven.lt" 
d="M194 802h596l-18 -84l-543 -718h-138l545 700h-465z" />
    <glyph glyph-name="eight.lt" 
d="M330 -16q-157 0 -216 47t-59 121q0 30 8 65q19 88 63.5 133t116.5 62q-53 18 -72 53t-19 74q0 30 8 68q11 54 34 93.5t61 65.5t94 39t134 13q151 0 206 -46t55 -117q0 -28 -7 -62q-17 -85 -58.5 -129t-106.5 -62q30 -8 53 -23.5t36 -40t14.5 -45t1.5 -28.5q0 -29 -8 -67
q-12 -55 -35.5 -95t-63.5 -66.5t-99 -39.5t-141 -13zM417 457q50 0 85.5 6t60 20t39.5 39t23 62q5 24 5 44q0 7 -1.5 23t-18 32t-49.5 24t-87 8q-52 0 -88.5 -8t-60.5 -24.5t-38 -41t-21 -57.5q-5 -25 -5 -44q0 -10 2.5 -26.5t20.5 -30.5t51 -20t82 -6zM339 87q55 0 94 8
t65 24.5t41.5 42t22.5 59.5q5 24 5 43q0 12 -3.5 30t-23 33.5t-55 21.5t-87.5 6q-51 0 -89 -6t-65 -21.5t-43.5 -41.5t-25.5 -65q-5 -24 -5 -43q0 -9 2 -25.5t20.5 -33t54 -24.5t92.5 -8z" />
    <glyph glyph-name="nine.lt" 
d="M455 818q89 0 147.5 -23.5t88 -75.5t30.5 -96t1 -49q0 -80 -25 -193q-24 -110 -55.5 -186t-77.5 -123t-111 -67.5t-155 -20.5q-71 0 -124.5 8.5t-94.5 20.5l21 101q43 -11 92.5 -19t109.5 -8q63 0 107 12t75.5 42t54 81.5t40.5 129.5q-42 -17 -89.5 -28.5t-110.5 -11.5
q-65 0 -115 16t-81 49t-38.5 69t-7.5 60q0 34 9 75q14 66 38.5 111.5t62 73t89 40t119.5 12.5zM254 567q-5 -26 -5 -47q0 -14 4 -35t23.5 -39t52 -26t75.5 -8q60 0 105.5 10.5t90.5 27.5q15 76 15 127.5t-17.5 83t-54.5 45.5t-96 14q-41 0 -73 -7.5t-55.5 -25t-39.5 -47
t-25 -73.5z" />
    <glyph glyph-name="dollar.ot" 
d="M93 178q42 -13 97.5 -24t117.5 -15l39 183q-64 11 -106 25.5t-65 37t-26 44.5t-3 33q0 27 8 62q8 39 26 67t49.5 46t79 27.5t113.5 10.5l18 86h89l-19 -88q54 -4 101 -13t86 -20l-20 -99q-42 11 -89.5 20t-98.5 13l-35 -164q63 -11 105 -26.5t65 -39t26.5 -46.5t3.5 -35
q0 -28 -8 -62q-9 -46 -28 -76.5t-50.5 -49t-79 -27t-113.5 -10.5l-21 -98h-89l21 100q-64 4 -120 14t-96 24zM255 512q-3 -15 -3 -26q0 -4 1 -12.5t13 -18t36.5 -16.5t66.5 -15l32 152q-39 -1 -65.5 -5t-43.5 -11.5t-25.5 -19t-11.5 -28.5zM547 213q4 15 4 27q0 5 -1.5 14.5
t-13.5 20t-36.5 18.5t-66.5 15l-36 -171q38 1 64.5 5t44 12.5t27.5 23t14 35.5z" />
    <glyph glyph-name="dollar.ot.weight" 
d="M93 178q51 -16 121 -28.5t148 -12.5q48 0 81 3t54.5 11.5t33.5 23t16 38.5q3 15 3 27q0 7 -2 17.5t-19 22t-51.5 19.5t-91.5 17q-76 12 -126 26t-77 37t-31.5 46.5t-4.5 37.5q0 27 8 61q8 39 26 67t49.5 46t79 27.5t113.5 10.5l18 86h89l-19 -88q54 -4 101 -13t86 -20
l-20 -99q-53 14 -116 24.5t-129 10.5q-48 0 -81 -3t-53 -10.5t-30.5 -20t-13.5 -30.5q-3 -15 -3 -27q0 -6 1.5 -15.5t18.5 -19t51.5 -17t91.5 -16.5q75 -12 125 -27t77.5 -39t32.5 -48.5t5 -40.5q0 -27 -8 -61q-9 -46 -28 -76.5t-51 -49t-79 -27t-113 -10.5l-21 -98h-89
l21 100q-64 4 -120 14t-96 24z" />
    <glyph glyph-name="Euro.ot" 
d="M71 300h91q5 35 12 72l2 8l3 16l2 8h-89l20 89h95q22 60 52 102.5t73 69.5t101 40t134 13q66 0 110.5 -8t80.5 -18l-22 -100q-40 11 -82 18.5t-102 7.5q-48 0 -84.5 -7t-64.5 -22t-48 -38.5t-35 -57.5h349l-20 -89h-358q-8 -25 -13 -51.5t-9 -52.5h359l-18 -88h-349
q1 -36 11.5 -60.5t32 -39t56 -21t82.5 -6.5q59 0 104 7t87 18l-22 -99q-34 -11 -81 -19t-106 -8q-139 0 -203.5 55t-65.5 173h-103z" />
    <glyph glyph-name="Euro.ot.weight" 
d="M91 399h89q20 86 49 146.5t74 98.5t109.5 56t154.5 18q66 0 110.5 -8t80.5 -18l-22 -100q-40 11 -82 18.5t-102 7.5q-62 0 -105.5 -11.5t-73.5 -37.5t-49.5 -68t-34.5 -102h359l-18 -89h-359q-9 -55 -9 -95q0 -7 1 -32t20.5 -51t58.5 -36.5t101 -10.5q59 0 104 7t87 18
l-22 -99q-34 -11 -81 -19t-106 -8q-82 0 -138 19t-87.5 58.5t-37.5 83.5t-6 73q0 42 7 92h-90z" />
    <glyph glyph-name="cent.ot" 
d="M556 702l-17 -80q103 -4 183 -24l-22 -101q-45 9 -89 14t-94 7l-71 -331q103 3 201 19l-21 -100q-95 -21 -203 -24l-17 -82h-88l18 83q-66 4 -115.5 22t-78.5 53t-35 71.5t-6 58.5q0 39 10 86q15 71 42.5 118t69 75.5t98 41t129.5 14.5l17 79h89zM222 361q-7 -32 -7 -58
q0 -45 25.5 -77.5t117.5 -38.5l70 331q-48 -2 -82.5 -11.5t-59.5 -28.5t-40 -47.5t-24 -69.5z" />
    <glyph glyph-name="cent.ot.weight" 
d="M556 702l-18 -80q104 -4 184 -24l-22 -101q-56 11 -112.5 16.5t-123.5 5.5q-57 0 -98.5 -8t-70.5 -26.5t-46.5 -48.5t-26.5 -75q-7 -34 -7 -60q0 -12 3 -34t24 -42.5t60 -29.5t97 -9q68 0 127 5t121 15l-21 -100q-93 -21 -202 -24l-18 -82h-88l17 83q-66 4 -115 22
t-78 53t-35 71.5t-6 58.5q0 39 10 86q15 71 42.5 118t69 75.5t98 41t129.5 14.5l17 79h89z" />
    <glyph glyph-name="sterling.ot" 
d="M95 89q51 0 75.5 23.5t35.5 75.5l26 122h-144l19 89h144l30 142q11 51 30.5 88.5t53.5 62t84 37t121 12.5q65 0 113 -8.5t83 -17.5l-21 -100q-38 11 -79 18.5t-95 7.5q-47 0 -80 -7t-55 -20.5t-34 -33t-17 -43.5l-30 -138h308l-19 -89h-308l-24 -113q-15 -72 -64 -108
h407l-20 -89h-612l18 89h54z" />
    <glyph glyph-name="yen.ot" 
d="M107 300h223l22 104h-223l19 89h152l-153 209h113l158 -209h1l256 209h119l-243 -209h154l-19 -89h-225l-22 -104h225l-19 -88h-224l-45 -212h-109l45 212h-224z" />
    <glyph glyph-name="florin.ot" 
d="M130 399h216l26 124q12 55 31 94.5t49.5 65t74 37.5t104.5 12q36 0 61 -1.5t52 -5.5l-21 -99q-46 6 -92 6q-35 0 -60.5 -5.5t-43.5 -19t-29.5 -35.5t-18.5 -55l-25 -118h221l-20 -89h-220l-68 -316q-11 -54 -29.5 -93t-47.5 -64.5t-70 -38t-97 -12.5q-43 0 -69.5 2
t-52.5 5l21 100q45 -6 91 -6q35 0 60 5.5t42 19t28 35.5t18 54l66 309h-217z" />
    <glyph glyph-name="zeroslash.ot" 
d="M455 718q167 0 214 -72t47 -162q0 -63 -17 -144q-20 -98 -50 -166t-73 -110t-102 -61t-137 -19q-165 0 -213.5 72t-48.5 164q0 62 17 142q20 98 50.5 166t74 110t102.5 61t136 19zM201 351q-9 -42 -13 -71t-4 -50q0 -9 1 -18l387 369q-37 36 -130 36q-52 0 -90.5 -14
t-67 -45.5t-48.5 -82t-35 -124.5zM349 85q52 0 90.5 14t67 45.5t48.5 82t35 124.5q9 42 13 71t4 50q0 9 -1 18l-387 -369q37 -36 130 -36z" />
    <glyph glyph-name="zero.ot" 
d="M455 718q167 0 214 -72t47 -162q0 -63 -17 -144q-20 -98 -50 -166t-73 -110t-102 -61t-137 -19q-165 0 -213.5 72t-48.5 164q0 62 17 142q20 98 50.5 166t74 110t102.5 61t136 19zM349 85q52 0 90.5 14t67 45.5t48.5 82t35 124.5q16 75 16.5 101t0.5 27q0 49 -17.5 80
t-54 44.5t-93.5 13.5q-52 0 -90.5 -14t-67 -45.5t-48.5 -82t-35 -124.5q-16 -75 -16.5 -101t-0.5 -27q0 -49 17.5 -80t54 -44.5t93.5 -13.5z" />
    <glyph glyph-name="one.ot" 
d="M80 89h244l105 494l-253 -114l-30 93l314 140h103l-130 -613h219l-19 -89h-571z" />
    <glyph glyph-name="two.ot" 
d="M453 718q69 0 121 -10.5t84 -34.5t41 -52.5t9 -51.5q0 -24 -6 -52q-6 -33 -17 -58t-29.5 -45.5t-47 -38t-70.5 -35.5l-262 -114q-27 -12 -43.5 -22t-26.5 -21t-15 -25t-10 -35l-7 -34h445l-19 -89h-554l24 117q8 39 19.5 68t30.5 52.5t48.5 42.5t72.5 38l266 117
q25 11 40.5 21t24.5 20t13.5 21.5t7.5 25.5q2 12 2 23q0 36 -33 54t-128 18q-69 0 -135.5 -7.5t-119.5 -18.5l21 100q54 11 114.5 18.5t138.5 7.5z" />
    <glyph glyph-name="three.ot" 
d="M40 2q55 -13 113 -20t126 -7t111.5 7.5t70.5 23.5t41 41.5t22 61.5q7 32 7 55q0 8 -2 25.5t-19 34.5t-50 24.5t-84 7.5h-190l19 101h188q44 0 77 5t56.5 17t37.5 33.5t21 54.5q6 30 6 55q0 9 -2 28.5t-21.5 39t-56 29t-93.5 9.5q-71 0 -125.5 -6.5t-108.5 -18.5l18 89
q46 11 104.5 18.5t130.5 7.5q165 0 217.5 -51t52.5 -125q0 -34 -9 -74q-16 -77 -58 -115t-105 -52q64 -19 84.5 -59t20.5 -82q0 -33 -9 -74q-12 -54 -34.5 -92.5t-63 -62.5t-102 -35.5t-152.5 -11.5q-86 0 -148 8t-110 20z" />
    <glyph glyph-name="four.ot" 
d="M665 356l-97 -456h-106l38 179h-477l18 85l400 538h126l-387 -522h341l37 176h107z" />
    <glyph glyph-name="five.ot" 
d="M42 14q107 -27 252 -27q60 0 100 8t65.5 24.5t40 42t21.5 61.5q6 26 6 48q0 48 -34.5 76t-138.5 28h-226l91 427h498l-21 -102h-390l-48 -223h118q155 0 209.5 -52t54.5 -132q0 -37 -10 -82q-13 -64 -38.5 -108t-67 -70t-101.5 -37.5t-143 -11.5q-84 0 -149.5 8.5
t-110.5 19.5z" />
    <glyph glyph-name="six.ot" 
d="M353 -16q-89 0 -147.5 23.5t-88 75.5t-30 95.5t-0.5 46.5q0 82 24 196q24 110 55.5 186t77.5 123t111 67.5t155 20.5q71 0 124.5 -8.5t94.5 -20.5l-21 -101q-43 11 -92.5 19t-109.5 8q-63 0 -107 -12t-75.5 -42t-54 -81.5t-40.5 -129.5q42 17 89.5 28.5t110.5 11.5
q65 0 115 -16t81 -49t38.5 -69t7.5 -60q0 -34 -9 -75q-13 -66 -38 -111.5t-62.5 -73t-89 -40t-119.5 -12.5zM404 390q-60 0 -105.5 -10.5t-90.5 -27.5q-15 -76 -15 -127.5t17.5 -83t54.5 -45.5t96 -14q41 0 73 7.5t55.5 25t39.5 47t25 73.5q5 26 5 48q0 14 -4 34.5
t-23.5 38.5t-52 26t-75.5 8z" />
    <glyph glyph-name="seven.ot" 
d="M173 702h596l-18 -84l-543 -718h-138l545 700h-465z" />
    <glyph glyph-name="eight.ot" 
d="M330 -16q-157 0 -216 47t-59 121q0 30 8 65q19 88 63.5 133t116.5 62q-53 18 -72 53t-19 74q0 30 8 68q11 54 34 93.5t61 65.5t94 39t134 13q151 0 206 -46t55 -117q0 -28 -7 -62q-17 -85 -58.5 -129t-106.5 -62q30 -8 53 -23.5t36 -40t14.5 -45t1.5 -28.5q0 -29 -8 -67
q-12 -55 -35.5 -95t-63.5 -66.5t-99 -39.5t-141 -13zM417 457q50 0 85.5 6t60 20t39.5 39t23 62q5 24 5 44q0 7 -1.5 23t-18 32t-49.5 24t-87 8q-52 0 -88.5 -8t-60.5 -24.5t-38 -41t-21 -57.5q-5 -25 -5 -44q0 -10 2.5 -26.5t20.5 -30.5t51 -20t82 -6zM339 87q55 0 94 8
t65 24.5t41.5 42t22.5 59.5q5 24 5 43q0 12 -3.5 30t-23 33.5t-55 21.5t-87.5 6q-51 0 -89 -6t-65 -21.5t-43.5 -41.5t-25.5 -65q-5 -24 -5 -43q0 -9 2 -25.5t20.5 -33t54 -24.5t92.5 -8z" />
    <glyph glyph-name="nine.ot" 
d="M434 718q89 0 147.5 -23.5t88 -75.5t30.5 -96t1 -49q0 -80 -25 -193q-24 -110 -55.5 -186t-77.5 -123t-111 -67.5t-155 -20.5q-71 0 -124.5 8.5t-94.5 20.5l21 101q43 -11 92.5 -19t109.5 -8q63 0 107 12t75.5 42t54 81.5t40.5 129.5q-42 -17 -89.5 -28.5t-110.5 -11.5
q-65 0 -115 16t-81 49t-38.5 69t-7.5 60q0 34 9 75q14 66 38.5 111.5t62 73t89 40t119.5 12.5zM233 467q-5 -26 -5 -47q0 -14 4 -35t23.5 -39t52 -26t75.5 -8q60 0 105.5 10.5t90.5 27.5q15 76 15 127.5t-17.5 83t-54.5 45.5t-96 14q-41 0 -73 -7.5t-55.5 -25t-39.5 -47
t-25 -73.5z" />
    <glyph glyph-name="dollar.op" horiz-adv-x="700" 
d="M63 178q42 -13 97.5 -24t117.5 -15l39 183q-64 11 -106 25.5t-65 37t-26 44.5t-3 33q0 27 8 62q8 39 26 67t49.5 46t79 27.5t113.5 10.5l18 86h89l-19 -88q54 -4 101 -13t86 -20l-20 -99q-42 11 -89.5 20t-98.5 13l-35 -164q63 -11 105 -26.5t65 -39t26.5 -46.5t3.5 -35
q0 -28 -8 -62q-9 -46 -28 -76.5t-50.5 -49t-79 -27t-113.5 -10.5l-21 -98h-89l21 100q-64 4 -120 14t-96 24zM225 512q-3 -15 -3 -26q0 -4 1 -12.5t13 -18t36.5 -16.5t66.5 -15l32 152q-39 -1 -65.5 -5t-43.5 -11.5t-25.5 -19t-11.5 -28.5zM517 213q4 15 4 27q0 5 -1.5 14.5
t-13.5 20t-36.5 18.5t-66.5 15l-36 -171q38 1 64.5 5t44 12.5t27.5 23t14 35.5z" />
    <glyph glyph-name="dollar.op.weight" horiz-adv-x="700" 
d="M63 178q51 -16 121 -28.5t148 -12.5q48 0 81 3t54.5 11.5t33.5 23t16 38.5q3 15 3 27q0 7 -2 17.5t-19 22t-51.5 19.5t-91.5 17q-76 12 -126 26t-77 37t-31.5 46.5t-4.5 37.5q0 27 8 61q8 39 26 67t49.5 46t79 27.5t113.5 10.5l18 86h89l-19 -88q54 -4 101 -13t86 -20
l-20 -99q-53 14 -116 24.5t-129 10.5q-48 0 -81 -3t-53 -10.5t-30.5 -20t-13.5 -30.5q-3 -15 -3 -27q0 -6 1.5 -15.5t18.5 -19t51.5 -17t91.5 -16.5q75 -12 125 -27t77.5 -39t32.5 -48.5t5 -40.5q0 -27 -8 -61q-9 -46 -28 -76.5t-51 -49t-79 -27t-113 -10.5l-21 -98h-89
l21 100q-64 4 -120 14t-96 24z" />
    <glyph glyph-name="Euro.op" horiz-adv-x="700" 
d="M41 300h91q5 35 12 72l2 8l3 16l2 8h-89l20 89h95q22 60 52 102.5t73 69.5t101 40t134 13q66 0 110.5 -8t80.5 -18l-22 -100q-40 11 -82 18.5t-102 7.5q-48 0 -84.5 -7t-64.5 -22t-48 -38.5t-35 -57.5h349l-20 -89h-358q-8 -25 -13 -51.5t-9 -52.5h359l-18 -88h-349
q1 -36 11.5 -60.5t32 -39t56 -21t82.5 -6.5q59 0 104 7t87 18l-22 -99q-34 -11 -81 -19t-106 -8q-139 0 -203.5 55t-65.5 173h-103z" />
    <glyph glyph-name="Euro.op.weight" horiz-adv-x="700" 
d="M61 399h89q20 86 49 146.5t74 98.5t109.5 56t154.5 18q66 0 110.5 -8t80.5 -18l-22 -100q-40 11 -82 18.5t-102 7.5q-62 0 -105.5 -11.5t-73.5 -37.5t-49.5 -68t-34.5 -102h359l-18 -89h-359q-9 -55 -9 -95q0 -7 1 -32t20.5 -51t58.5 -36.5t101 -10.5q59 0 104 7t87 18
l-22 -99q-34 -11 -81 -19t-106 -8q-82 0 -138 19t-87.5 58.5t-37.5 83.5t-6 73q0 42 7 92h-90z" />
    <glyph glyph-name="cent.op" horiz-adv-x="700" 
d="M526 702l-17 -80q103 -4 183 -24l-22 -101q-45 9 -89 14t-94 7l-71 -331q103 3 201 19l-21 -100q-95 -21 -203 -24l-17 -82h-88l18 83q-66 4 -115.5 22t-78.5 53t-35 71.5t-6 58.5q0 39 10 86q15 71 42.5 118t69 75.5t98 41t129.5 14.5l17 79h89zM192 361q-7 -32 -7 -58
q0 -45 25.5 -77.5t117.5 -38.5l70 331q-48 -2 -82.5 -11.5t-59.5 -28.5t-40 -47.5t-24 -69.5z" />
    <glyph glyph-name="cent.op.weight" horiz-adv-x="700" 
d="M526 702l-18 -80q104 -4 184 -24l-22 -101q-56 11 -112.5 16.5t-123.5 5.5q-57 0 -98.5 -8t-70.5 -26.5t-46.5 -48.5t-26.5 -75q-7 -34 -7 -60q0 -12 3 -34t24 -42.5t60 -29.5t97 -9q68 0 127 5t121 15l-21 -100q-93 -21 -202 -24l-18 -82h-88l17 83q-66 4 -115 22
t-78 53t-35 71.5t-6 58.5q0 39 10 86q15 71 42.5 118t69 75.5t98 41t129.5 14.5l17 79h89z" />
    <glyph glyph-name="sterling.op" horiz-adv-x="700" 
d="M65 89q51 0 75.5 23.5t35.5 75.5l26 122h-144l19 89h144l30 142q11 51 30.5 88.5t53.5 62t84 37t121 12.5q65 0 113 -8.5t83 -17.5l-21 -100q-38 11 -79 18.5t-95 7.5q-47 0 -80 -7t-55 -20.5t-34 -33t-17 -43.5l-30 -138h308l-19 -89h-308l-24 -113q-15 -72 -64 -108
h407l-20 -89h-612l18 89h54z" />
    <glyph glyph-name="yen.op" horiz-adv-x="700" 
d="M77 300h223l22 104h-223l19 89h152l-153 209h113l158 -209h1l256 209h119l-243 -209h154l-19 -89h-225l-22 -104h225l-19 -88h-224l-45 -212h-109l45 212h-224z" />
    <glyph glyph-name="florin.op" horiz-adv-x="700" 
d="M100 399h216l26 124q12 55 31 94.5t49.5 65t74 37.5t104.5 12q36 0 61 -1.5t52 -5.5l-21 -99q-46 6 -92 6q-35 0 -60.5 -5.5t-43.5 -19t-29.5 -35.5t-18.5 -55l-25 -118h221l-20 -89h-220l-68 -316q-11 -54 -29.5 -93t-47.5 -64.5t-70 -38t-97 -12.5q-43 0 -69.5 2
t-52.5 5l21 100q45 -6 91 -6q35 0 60 5.5t42 19t28 35.5t18 54l66 309h-217z" />
    <glyph glyph-name="zeroslash.op" horiz-adv-x="700" 
d="M425 718q167 0 214 -72t47 -162q0 -63 -17 -144q-20 -98 -50 -166t-73 -110t-102 -61t-137 -19q-165 0 -213.5 72t-48.5 164q0 62 17 142q20 98 50.5 166t74 110t102.5 61t136 19zM171 351q-9 -42 -13 -71t-4 -50q0 -9 1 -18l387 369q-37 36 -130 36q-52 0 -90.5 -14
t-67 -45.5t-48.5 -82t-35 -124.5zM319 85q52 0 90.5 14t67 45.5t48.5 82t35 124.5q9 42 13 71t4 50q0 9 -1 18l-387 -369q37 -36 130 -36z" />
    <glyph glyph-name="zero.op" horiz-adv-x="700" 
d="M425 718q167 0 214 -72t47 -162q0 -63 -17 -144q-20 -98 -50 -166t-73 -110t-102 -61t-137 -19q-165 0 -213.5 72t-48.5 164q0 62 17 142q20 98 50.5 166t74 110t102.5 61t136 19zM319 85q52 0 90.5 14t67 45.5t48.5 82t35 124.5q16 75 16.5 101t0.5 27q0 49 -17.5 80
t-54 44.5t-93.5 13.5q-52 0 -90.5 -14t-67 -45.5t-48.5 -82t-35 -124.5q-16 -75 -16.5 -101t-0.5 -27q0 -49 17.5 -80t54 -44.5t93.5 -13.5z" />
    <glyph glyph-name="one.op" horiz-adv-x="547" 
d="M413 702h105l-150 -702h-108l124 583l-263 -119l-35 91z" />
    <glyph glyph-name="two.op" horiz-adv-x="700" 
d="M423 718q69 0 121 -10.5t84 -34.5t41 -52.5t9 -51.5q0 -24 -6 -52q-6 -33 -17 -58t-29.5 -45.5t-47 -38t-70.5 -35.5l-262 -114q-27 -12 -43.5 -22t-26.5 -21t-15 -25t-10 -35l-7 -34h445l-19 -89h-554l24 117q8 39 19.5 68t30.5 52.5t48.5 42.5t72.5 38l266 117
q25 11 40.5 21t24.5 20t13.5 21.5t7.5 25.5q2 12 2 23q0 36 -33 54t-128 18q-69 0 -135.5 -7.5t-119.5 -18.5l21 100q54 11 114.5 18.5t138.5 7.5z" />
    <glyph glyph-name="three.op" horiz-adv-x="700" 
d="M10 2q55 -13 113 -20t126 -7t111.5 7.5t70.5 23.5t41 41.5t22 61.5q7 32 7 55q0 8 -2 25.5t-19 34.5t-50 24.5t-84 7.5h-190l19 101h188q44 0 77 5t56.5 17t37.5 33.5t21 54.5q6 30 6 55q0 9 -2 28.5t-21.5 39t-56 29t-93.5 9.5q-71 0 -125.5 -6.5t-108.5 -18.5l18 89
q46 11 104.5 18.5t130.5 7.5q165 0 217.5 -51t52.5 -125q0 -34 -9 -74q-16 -77 -58 -115t-105 -52q64 -19 84.5 -59t20.5 -82q0 -33 -9 -74q-12 -54 -34.5 -92.5t-63 -62.5t-102 -35.5t-152.5 -11.5q-86 0 -148 8t-110 20z" />
    <glyph glyph-name="four.op" horiz-adv-x="700" 
d="M635 356l-97 -456h-106l38 179h-477l18 85l400 538h126l-387 -522h341l37 176h107z" />
    <glyph glyph-name="five.op" horiz-adv-x="700" 
d="M12 14q107 -27 252 -27q60 0 100 8t65.5 24.5t40 42t21.5 61.5q6 26 6 48q0 48 -34.5 76t-138.5 28h-226l91 427h498l-21 -102h-390l-48 -223h118q155 0 209.5 -52t54.5 -132q0 -37 -10 -82q-13 -64 -38.5 -108t-67 -70t-101.5 -37.5t-143 -11.5q-84 0 -149.5 8.5
t-110.5 19.5z" />
    <glyph glyph-name="six.op" horiz-adv-x="700" 
d="M323 -16q-89 0 -147.5 23.5t-88 75.5t-30 95.5t-0.5 46.5q0 82 24 196q24 110 55.5 186t77.5 123t111 67.5t155 20.5q71 0 124.5 -8.5t94.5 -20.5l-21 -101q-43 11 -92.5 19t-109.5 8q-63 0 -107 -12t-75.5 -42t-54 -81.5t-40.5 -129.5q42 17 89.5 28.5t110.5 11.5
q65 0 115 -16t81 -49t38.5 -69t7.5 -60q0 -34 -9 -75q-13 -66 -38 -111.5t-62.5 -73t-89 -40t-119.5 -12.5zM374 390q-60 0 -105.5 -10.5t-90.5 -27.5q-15 -76 -15 -127.5t17.5 -83t54.5 -45.5t96 -14q41 0 73 7.5t55.5 25t39.5 47t25 73.5q5 26 5 48q0 14 -4 34.5
t-23.5 38.5t-52 26t-75.5 8z" />
    <glyph glyph-name="seven.op" horiz-adv-x="700" 
d="M143 702h596l-18 -84l-543 -718h-138l545 700h-465z" />
    <glyph glyph-name="eight.op" horiz-adv-x="700" 
d="M300 -16q-157 0 -216 47t-59 121q0 30 8 65q19 88 63.5 133t116.5 62q-53 18 -72 53t-19 74q0 30 8 68q11 54 34 93.5t61 65.5t94 39t134 13q151 0 206 -46t55 -117q0 -28 -7 -62q-17 -85 -58.5 -129t-106.5 -62q30 -8 53 -23.5t36 -40t14.5 -45t1.5 -28.5q0 -29 -8 -67
q-12 -55 -35.5 -95t-63.5 -66.5t-99 -39.5t-141 -13zM387 457q50 0 85.5 6t60 20t39.5 39t23 62q5 24 5 44q0 7 -1.5 23t-18 32t-49.5 24t-87 8q-52 0 -88.5 -8t-60.5 -24.5t-38 -41t-21 -57.5q-5 -25 -5 -44q0 -10 2.5 -26.5t20.5 -30.5t51 -20t82 -6zM309 87q55 0 94 8
t65 24.5t41.5 42t22.5 59.5q5 24 5 43q0 12 -3.5 30t-23 33.5t-55 21.5t-87.5 6q-51 0 -89 -6t-65 -21.5t-43.5 -41.5t-25.5 -65q-5 -24 -5 -43q0 -9 2 -25.5t20.5 -33t54 -24.5t92.5 -8z" />
    <glyph glyph-name="nine.op" horiz-adv-x="700" 
d="M404 718q89 0 147.5 -23.5t88 -75.5t30.5 -96t1 -49q0 -80 -25 -193q-24 -110 -55.5 -186t-77.5 -123t-111 -67.5t-155 -20.5q-71 0 -124.5 8.5t-94.5 20.5l21 101q43 -11 92.5 -19t109.5 -8q63 0 107 12t75.5 42t54 81.5t40.5 129.5q-42 -17 -89.5 -28.5t-110.5 -11.5
q-65 0 -115 16t-81 49t-38.5 69t-7.5 60q0 34 9 75q14 66 38.5 111.5t62 73t89 40t119.5 12.5zM203 467q-5 -26 -5 -47q0 -14 4 -35t23.5 -39t52 -26t75.5 -8q60 0 105.5 10.5t90.5 27.5q15 76 15 127.5t-17.5 83t-54.5 45.5t-96 14q-41 0 -73 -7.5t-55.5 -25t-39.5 -47
t-25 -73.5z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="408" 
d="M308 811q50 0 83.5 -14t51 -44.5t18.5 -57t1 -30.5q0 -46 -13 -107q-12 -57 -28.5 -99.5t-42 -70t-62 -41.5t-88.5 -14q-50 0 -83.5 14t-51 44.5t-18.5 57.5t-1 33q0 45 12 104q12 57 28.5 99.5t42 70t62.5 41.5t89 14zM236 412q26 0 44.5 8t32 26.5t24 49t20.5 76.5
q9 46 10.5 66t1.5 27q0 20 -5.5 36t-21 23.5t-42.5 7.5q-26 0 -44.5 -8t-32 -26.5t-24 -49t-20.5 -76.5t-11 -65t-1 -26q0 -21 5.5 -37.5t21 -24t42.5 -7.5z" />
    <glyph glyph-name="one.numr" horiz-adv-x="336" 
d="M294 802h92l-98 -460h-94l76 357l-136 -62l-28 79z" />
    <glyph glyph-name="two.numr" horiz-adv-x="408" 
d="M306 811q94 0 127 -28t33 -70q0 -17 -4 -36q-5 -23 -12 -38.5t-20 -28.5t-33 -25.5t-51 -28.5l-138 -72q-23 -12 -30.5 -21.5t-10.5 -27.5l-3 -16h243l-16 -77h-339l22 105q4 19 11 33t17.5 26t26 22.5t37.5 21.5l143 74q18 9 28.5 15t16.5 12t9 12t5 15q2 8 2 15
q0 17 -16 27.5t-67 10.5q-35 0 -73 -5t-68 -10l16 80q31 7 64 11t80 4z" />
    <glyph glyph-name="three.numr" horiz-adv-x="408" 
d="M68 427q33 -8 65.5 -12t72.5 -4q35 0 57 3t35.5 9.5t20 18t9.5 28.5q3 13 3 24q0 19 -12.5 31t-57.5 12h-119l15 77h119q40 0 59.5 10t26.5 41q2 12 2 21q0 22 -15 35t-72 13q-44 0 -75 -3.5t-65 -11.5l16 77q26 6 62 10.5t81 4.5q103 0 135.5 -29t32.5 -73q0 -19 -5 -42
q-9 -44 -32.5 -65.5t-59.5 -30.5q35 -13 46.5 -37t11.5 -47q0 -17 -5 -38q-7 -32 -21 -54t-38.5 -36t-62.5 -20t-94 -6q-51 0 -89 5t-64 12z" />
    <glyph glyph-name="four.numr" horiz-adv-x="408" 
d="M444 604l-55 -262h-93l20 93h-263l15 72l219 295h106l-218 -292h156l20 94h93z" />
    <glyph glyph-name="five.numr" horiz-adv-x="408" 
d="M68 430q66 -17 147 -17q30 0 50 3t33 10.5t20 19.5t10 30q2 12 2 22q0 23 -15.5 36t-64.5 13h-142l54 255h306l-16 -77h-212l-22 -102h51q97 0 127.5 -31.5t30.5 -77.5q0 -22 -5 -48q-8 -41 -25 -67t-43.5 -40.5t-63.5 -20t-87 -5.5q-48 0 -88 5.5t-64 11.5z" />
    <glyph glyph-name="six.numr" horiz-adv-x="408" 
d="M240 333q-55 0 -91 14t-54 44t-19 56t-1 30q0 45 14 105q14 63 32 106.5t45.5 71t65.5 39.5t90 12q45 0 80 -5t58 -13l-16 -79q-27 7 -56.5 12t-64.5 5q-29 0 -50 -5.5t-36 -18.5t-25.5 -35.5t-19.5 -56.5q20 8 43.5 12.5t51.5 4.5q82 0 113 -33.5t31 -81.5q0 -21 -5 -47
q-16 -76 -60.5 -106.5t-125.5 -30.5zM263 555q-26 0 -47 -4.5t-42 -12.5q-8 -37 -8 -63q0 -24 11.5 -45t66.5 -21q39 0 59 15.5t28 57.5q4 15 4 27q0 21 -15 33.5t-57 12.5z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="408" 
d="M138 802h359l-16 -72l-292 -388h-117l300 383h-251z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="408" 
d="M229 333q-46 0 -81 7t-57 23.5t-27.5 36t-5.5 35.5q0 17 4 37q10 45 34.5 70.5t63.5 36.5q-29 12 -39 32t-10 41q0 18 5 41q13 62 57.5 90t134.5 28q44 0 78 -7t55 -23t26 -34t5 -32q0 -16 -4 -35q-9 -47 -33 -72.5t-59 -37.5q34 -11 45.5 -32t11.5 -44q0 -18 -5 -40
q-13 -63 -59.5 -92t-139.5 -29zM278 610q45 0 65.5 13t27.5 48q2 12 2 22q0 3 -0.5 10.5t-8.5 15t-23 11t-40 3.5q-47 0 -67 -15t-28 -47q-2 -11 -2 -20q0 -19 13 -30t61 -11zM235 410q50 0 72 15t30 50q2 12 2 21q0 21 -15 33.5t-63 12.5t-71.5 -14.5t-31.5 -52.5
q-3 -11 -3 -21q0 -19 14 -31.5t66 -12.5z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="408" 
d="M292 811q110 0 137.5 -45t27.5 -100q0 -44 -13 -104q-13 -63 -31.5 -106.5t-46 -71t-65.5 -39.5t-91 -12q-45 0 -79.5 5t-57.5 13l16 79q26 -7 55.5 -12t64.5 -5q30 0 51 5t36.5 18.5t26 36t18.5 56.5q-20 -8 -43 -12.5t-52 -4.5q-83 0 -113.5 33.5t-30.5 80.5q0 22 5 48
q16 76 60 106.5t125 30.5zM201 662q-3 -14 -3 -25q0 -22 14.5 -35t56.5 -13q26 0 47 4t42 12q7 37 7 62t-7.5 40t-24.5 21.5t-45 6.5q-38 0 -58.5 -15.5t-28.5 -57.5z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="408" 
d="M236 469q50 0 83.5 -14t51 -44.5t18.5 -57t1 -30.5q0 -46 -13 -107q-12 -57 -28.5 -99.5t-42 -70t-62 -41.5t-88.5 -14q-50 0 -83.5 14t-51 44.5t-18.5 57.5t-1 33q0 45 12 104q12 57 28.5 99.5t42 70t62.5 41.5t89 14zM164 70q26 0 44.5 8t32 26.5t24 49t20.5 76.5
q9 46 10.5 66t1.5 27q0 20 -5.5 36t-21 23.5t-42.5 7.5q-26 0 -44.5 -8t-32 -26.5t-24 -49t-20.5 -76.5t-11 -65t-1 -26q0 -21 5.5 -37.5t21 -24t42.5 -7.5z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="336" 
d="M222 460h92l-98 -460h-94l76 357l-136 -62l-28 79z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="408" 
d="M234 469q94 0 127 -28t33 -70q0 -17 -4 -36q-5 -23 -12 -38.5t-20 -28.5t-33 -25.5t-51 -28.5l-138 -72q-23 -12 -30.5 -21.5t-10.5 -27.5l-3 -16h243l-16 -77h-339l22 105q4 19 11 33t17.5 26t26 22.5t37.5 21.5l143 74q18 9 28.5 15t16.5 12t9 12t5 15q2 8 2 15
q0 17 -16 27.5t-67 10.5q-35 0 -73 -5t-68 -10l16 80q31 7 64 11t80 4z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="408" 
d="M-4 85q33 -8 65.5 -12t72.5 -4q35 0 57 3t35.5 9.5t20 18t9.5 28.5q3 13 3 24q0 19 -12.5 31t-57.5 12h-119l15 77h119q40 0 59.5 10t26.5 41q2 12 2 21q0 22 -15 35t-72 13q-44 0 -75 -3.5t-65 -11.5l16 77q26 6 62 10.5t81 4.5q103 0 135.5 -29t32.5 -73q0 -19 -5 -42
q-9 -44 -32.5 -65.5t-59.5 -30.5q35 -13 46.5 -37t11.5 -47q0 -17 -5 -38q-7 -32 -21 -54t-38.5 -36t-62.5 -20t-94 -6q-51 0 -89 5t-64 12z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="408" 
d="M372 262l-55 -262h-93l20 93h-263l15 72l219 295h106l-218 -292h156l20 94h93z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="408" 
d="M-4 88q66 -17 147 -17q30 0 50 3t33 10.5t20 19.5t10 30q2 12 2 22q0 23 -15.5 36t-64.5 13h-142l54 255h306l-16 -77h-212l-22 -102h51q97 0 127.5 -31.5t30.5 -77.5q0 -22 -5 -48q-8 -41 -25 -67t-43.5 -40.5t-63.5 -20t-87 -5.5q-48 0 -88 5.5t-64 11.5z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="408" 
d="M168 -9q-55 0 -91 14t-54 44t-19 56t-1 30q0 45 14 105q14 63 32 106.5t45.5 71t65.5 39.5t90 12q45 0 80 -5t58 -13l-16 -79q-27 7 -56.5 12t-64.5 5q-29 0 -50 -5.5t-36 -18.5t-25.5 -35.5t-19.5 -56.5q20 8 43.5 12.5t51.5 4.5q82 0 113 -33.5t31 -81.5q0 -21 -5 -47
q-16 -76 -60.5 -106.5t-125.5 -30.5zM191 213q-26 0 -47 -4.5t-42 -12.5q-8 -37 -8 -63q0 -24 11.5 -45t66.5 -21q39 0 59 15.5t28 57.5q4 15 4 27q0 21 -15 33.5t-57 12.5z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="408" 
d="M66 460h359l-16 -72l-292 -388h-117l300 383h-251z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="408" 
d="M157 -9q-46 0 -81 7t-57 23.5t-27.5 36t-5.5 35.5q0 17 4 37q10 45 34.5 70.5t63.5 36.5q-29 12 -39 32t-10 41q0 18 5 41q13 62 57.5 90t134.5 28q44 0 78 -7t55 -23t26 -34t5 -32q0 -16 -4 -35q-9 -47 -33 -72.5t-59 -37.5q34 -11 45.5 -32t11.5 -44q0 -18 -5 -40
q-13 -63 -59.5 -92t-139.5 -29zM206 268q45 0 65.5 13t27.5 48q2 12 2 22q0 3 -0.5 10.5t-8.5 15t-23 11t-40 3.5q-47 0 -67 -15t-28 -47q-2 -11 -2 -20q0 -19 13 -30t61 -11zM163 68q50 0 72 15t30 50q2 12 2 21q0 21 -15 33.5t-63 12.5t-71.5 -14.5t-31.5 -52.5
q-3 -11 -3 -21q0 -19 14 -31.5t66 -12.5z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="408" 
d="M220 469q110 0 137.5 -45t27.5 -100q0 -44 -13 -104q-13 -63 -31.5 -106.5t-46 -71t-65.5 -39.5t-91 -12q-45 0 -79.5 5t-57.5 13l16 79q26 -7 55.5 -12t64.5 -5q30 0 51 5t36.5 18.5t26 36t18.5 56.5q-20 -8 -43 -12.5t-52 -4.5q-83 0 -113.5 33.5t-30.5 80.5q0 22 5 48
q16 76 60 106.5t125 30.5zM129 320q-3 -14 -3 -25q0 -22 14.5 -35t56.5 -13q26 0 47 4t42 12q7 37 7 62t-7.5 40t-24.5 21.5t-45 6.5q-38 0 -58.5 -15.5t-28.5 -57.5z" />
    <hkern u1="&#x23;" g2="nine.op" k="10" />
    <hkern u1="&#x23;" g2="four.op" k="56" />
    <hkern u1="&#x23;" u2="&#x37;" k="-12" />
    <hkern u1="&#x23;" u2="&#x34;" k="42" />
    <hkern u1="&#x28;" u2="&#x237;" k="-50" />
    <hkern u1="&#x28;" u2="j" k="-50" />
    <hkern u1="&#x2a;" u2="X" k="38" />
    <hkern u1="&#x2a;" u2="&#x2f;" k="110" />
    <hkern u1="&#x2f;" u2="&#xdf;" k="20" />
    <hkern u1="&#x2f;" u2="x" k="20" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="120" />
    <hkern u1="&#x31;" u2="&#x20ac;" k="10" />
    <hkern u1="&#x31;" u2="&#x192;" k="20" />
    <hkern u1="&#x31;" u2="&#xb0;" k="8" />
    <hkern u1="&#x31;" u2="&#x33;" k="20" />
    <hkern u1="&#x31;" u2="&#x31;" k="60" />
    <hkern u1="&#x32;" u2="&#x192;" k="60" />
    <hkern u1="&#x32;" u2="&#xa5;" k="14" />
    <hkern u1="&#x32;" u2="&#x3c;" k="36" />
    <hkern u1="&#x32;" u2="&#x37;" k="10" />
    <hkern u1="&#x32;" u2="&#x34;" k="16" />
    <hkern u1="&#x32;" u2="&#x32;" k="10" />
    <hkern u1="&#x32;" u2="&#x31;" k="10" />
    <hkern u1="&#x33;" u2="&#x3e;" k="12" />
    <hkern u1="&#x33;" u2="&#x39;" k="4" />
    <hkern u1="&#x33;" u2="&#x37;" k="10" />
    <hkern u1="&#x33;" u2="&#x34;" k="-4" />
    <hkern u1="&#x33;" u2="&#x33;" k="20" />
    <hkern u1="&#x33;" u2="&#x32;" k="4" />
    <hkern u1="&#x33;" u2="&#x31;" k="20" />
    <hkern u1="&#x34;" u2="&#x39;" k="2" />
    <hkern u1="&#x34;" u2="&#x31;" k="110" />
    <hkern u1="&#x35;" u2="&#x192;" k="6" />
    <hkern u1="&#x35;" u2="&#xb0;" k="24" />
    <hkern u1="&#x35;" u2="&#x39;" k="16" />
    <hkern u1="&#x35;" u2="&#x37;" k="30" />
    <hkern u1="&#x35;" u2="&#x35;" k="10" />
    <hkern u1="&#x35;" u2="&#x33;" k="30" />
    <hkern u1="&#x35;" u2="&#x32;" k="14" />
    <hkern u1="&#x35;" u2="&#x31;" k="70" />
    <hkern u1="&#x37;" u2="&#x192;" k="70" />
    <hkern u1="&#x37;" u2="&#xb0;" k="-20" />
    <hkern u1="&#x37;" u2="&#xa3;" k="50" />
    <hkern u1="&#x37;" u2="&#x3e;" k="90" />
    <hkern u1="&#x37;" u2="&#x3c;" k="140" />
    <hkern u1="&#x37;" u2="&#x35;" k="20" />
    <hkern u1="&#x37;" u2="&#x34;" k="84" />
    <hkern u1="&#x37;" u2="&#x33;" k="10" />
    <hkern u1="&#x37;" u2="&#x32;" k="40" />
    <hkern u1="&#x37;" u2="&#x23;" k="90" />
    <hkern u1="&#x3c;" g2="seven.op" k="16" />
    <hkern u1="&#x3c;" g2="three.op" k="12" />
    <hkern u1="&#x3c;" u2="&#x37;" k="40" />
    <hkern u1="&#x3c;" u2="&#x31;" k="90" />
    <hkern u1="&#x3e;" g2="nine.op" k="6" />
    <hkern u1="&#x3e;" g2="seven.op" k="110" />
    <hkern u1="&#x3e;" g2="five.op" k="6" />
    <hkern u1="&#x3e;" g2="three.op" k="18" />
    <hkern u1="&#x3e;" g2="one.op" k="36" />
    <hkern u1="&#x3e;" u2="&#x37;" k="100" />
    <hkern u1="&#x3e;" u2="&#x33;" k="12" />
    <hkern u1="&#x3e;" u2="&#x31;" k="72" />
    <hkern u1="&#x3f;" u2="X" k="30" />
    <hkern u1="&#x40;" u2="v" k="20" />
    <hkern u1="&#x40;" u2="X" k="20" />
    <hkern u1="&#x40;" u2="V" k="70" />
    <hkern u1="B" g2="bullet.case" k="4" />
    <hkern u1="B" u2="&#x2122;" k="40" />
    <hkern u1="B" u2="&#xae;" k="30" />
    <hkern u1="B" u2="v" k="20" />
    <hkern u1="B" u2="X" k="20" />
    <hkern u1="B" u2="V" k="32" />
    <hkern u1="F" g2="at.case" k="12" />
    <hkern u1="F" u2="&#xbf;" k="176" />
    <hkern u1="F" u2="&#xb0;" k="-8" />
    <hkern u1="F" u2="x" k="20" />
    <hkern u1="F" u2="v" k="10" />
    <hkern u1="F" u2="X" k="12" />
    <hkern u1="F" u2="&#x40;" k="12" />
    <hkern u1="F" u2="&#x3f;" k="-16" />
    <hkern u1="F" u2="&#x2f;" k="108" />
    <hkern u1="P" g2="at.case" k="-8" />
    <hkern u1="P" g2="bullet.case" k="-8" />
    <hkern u1="P" u2="v" k="-8" />
    <hkern u1="P" u2="X" k="54" />
    <hkern u1="P" u2="V" k="12" />
    <hkern u1="P" u2="&#x2f;" k="68" />
    <hkern u1="P" u2="&#x2a;" k="-16" />
    <hkern u1="V" g2="at.case" k="14" />
    <hkern u1="V" g2="bullet.case" k="50" />
    <hkern u1="V" g2="questiondown.case" k="76" />
    <hkern u1="V" u2="&#x2022;" k="70" />
    <hkern u1="V" u2="&#xdf;" k="30" />
    <hkern u1="V" u2="&#xbf;" k="128" />
    <hkern u1="V" u2="&#xb0;" k="-8" />
    <hkern u1="V" u2="&#xa9;" k="30" />
    <hkern u1="V" u2="&#xa1;" k="50" />
    <hkern u1="V" u2="x" k="40" />
    <hkern u1="V" u2="v" k="34" />
    <hkern u1="V" u2="V" k="-8" />
    <hkern u1="V" u2="&#x40;" k="50" />
    <hkern u1="V" u2="&#x2f;" k="60" />
    <hkern u1="V" u2="&#x23;" k="76" />
    <hkern u1="X" g2="at.case" k="80" />
    <hkern u1="X" g2="bullet.case" k="120" />
    <hkern u1="X" u2="&#x2022;" k="40" />
    <hkern u1="X" u2="&#xae;" k="20" />
    <hkern u1="X" u2="&#xa9;" k="40" />
    <hkern u1="X" u2="x" k="-4" />
    <hkern u1="X" u2="v" k="50" />
    <hkern u1="X" u2="&#x40;" k="20" />
    <hkern u1="X" u2="&#x2a;" k="26" />
    <hkern u1="Y" u2="&#xf0;" k="80" />
    <hkern u1="\" u2="&#xdf;" k="20" />
    <hkern u1="\" u2="v" k="60" />
    <hkern u1="\" u2="V" k="100" />
    <hkern u1="v" u2="&#x2022;" k="20" />
    <hkern u1="v" u2="v" k="-10" />
    <hkern u1="v" u2="X" k="50" />
    <hkern u1="v" u2="V" k="40" />
    <hkern u1="v" u2="&#x2f;" k="60" />
    <hkern u1="x" u2="&#x2122;" k="40" />
    <hkern u1="x" u2="&#x2022;" k="40" />
    <hkern u1="x" u2="x" k="-8" />
    <hkern u1="x" u2="\" k="20" />
    <hkern u1="x" u2="X" k="-4" />
    <hkern u1="x" u2="V" k="50" />
    <hkern u1="&#xa1;" u2="V" k="80" />
    <hkern u1="&#xa3;" u2="&#x37;" k="10" />
    <hkern u1="&#xa3;" u2="&#x31;" k="30" />
    <hkern u1="&#xa5;" u2="&#x34;" k="8" />
    <hkern u1="&#xa5;" u2="&#x32;" k="24" />
    <hkern u1="&#xa5;" u2="&#x31;" k="30" />
    <hkern u1="&#xa9;" u2="X" k="40" />
    <hkern u1="&#xa9;" u2="V" k="50" />
    <hkern u1="&#xae;" u2="X" k="20" />
    <hkern u1="&#xb0;" g2="seven.op" k="-20" />
    <hkern u1="&#xb0;" g2="four.op" k="104" />
    <hkern u1="&#xb0;" u2="&#x37;" k="-10" />
    <hkern u1="&#xb0;" u2="&#x34;" k="80" />
    <hkern u1="&#xb0;" u2="&#x32;" k="20" />
    <hkern u1="&#xbf;" u2="&#xdf;" k="50" />
    <hkern u1="&#xbf;" u2="x" k="50" />
    <hkern u1="&#xbf;" u2="v" k="70" />
    <hkern u1="&#xbf;" u2="X" k="40" />
    <hkern u1="&#xbf;" u2="V" k="130" />
    <hkern u1="&#xbf;" u2="&#x237;" k="-100" />
    <hkern u1="&#xbf;" u2="j" k="-90" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="80" />
    <hkern u1="&#xde;" g2="at.case" k="-12" />
    <hkern u1="&#xde;" g2="bullet.case" k="-8" />
    <hkern u1="&#xde;" g2="parenleft.case" k="-30" />
    <hkern u1="&#xde;" u2="&#xbf;" k="30" />
    <hkern u1="&#xde;" u2="v" k="-8" />
    <hkern u1="&#xde;" u2="X" k="62" />
    <hkern u1="&#xde;" u2="V" k="44" />
    <hkern u1="&#xde;" u2="&#x40;" k="-4" />
    <hkern u1="&#xde;" u2="&#x2f;" k="38" />
    <hkern u1="&#xde;" u2="&#x2a;" k="-4" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="30" />
    <hkern u1="&#xdf;" u2="&#xb0;" k="10" />
    <hkern u1="&#xdf;" u2="&#xae;" k="20" />
    <hkern u1="&#xdf;" u2="x" k="10" />
    <hkern u1="&#xdf;" u2="v" k="10" />
    <hkern u1="&#xdf;" u2="\" k="20" />
    <hkern u1="&#xdf;" u2="X" k="12" />
    <hkern u1="&#xdf;" u2="V" k="40" />
    <hkern u1="&#xdf;" u2="&#x2f;" k="20" />
    <hkern u1="&#xdf;" u2="&#x23;" k="-8" />
    <hkern u1="&#xf0;" u2="&#x2122;" k="30" />
    <hkern u1="&#xf0;" u2="&#xb0;" k="30" />
    <hkern u1="&#xf0;" u2="&#xae;" k="20" />
    <hkern u1="&#xf0;" u2="x" k="28" />
    <hkern u1="&#xf0;" u2="v" k="10" />
    <hkern u1="&#xf0;" u2="\" k="40" />
    <hkern u1="&#xf0;" u2="X" k="28" />
    <hkern u1="&#xf0;" u2="V" k="72" />
    <hkern u1="&#xf0;" u2="&#x2a;" k="10" />
    <hkern u1="&#x13d;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x13d;" u2="&#x21a;" k="80" />
    <hkern u1="&#x13d;" u2="&#x17d;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x17b;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x179;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x178;" k="80" />
    <hkern u1="&#x13d;" u2="&#x176;" k="80" />
    <hkern u1="&#x13d;" u2="&#x166;" k="80" />
    <hkern u1="&#x13d;" u2="&#x164;" k="80" />
    <hkern u1="&#x13d;" u2="&#x162;" k="80" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="80" />
    <hkern u1="&#x13d;" u2="Z" k="-20" />
    <hkern u1="&#x13d;" u2="Y" k="80" />
    <hkern u1="&#x13d;" u2="V" k="60" />
    <hkern u1="&#x13d;" u2="T" k="80" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-40" />
    <hkern u1="&#x165;" u2="]" k="-40" />
    <hkern u1="&#x165;" u2="&#x29;" k="-40" />
    <hkern u1="&#x176;" u2="&#xf0;" k="80" />
    <hkern u1="&#x178;" u2="&#xf0;" k="80" />
    <hkern u1="&#x192;" u2="&#x37;" k="20" />
    <hkern u1="&#x192;" u2="&#x34;" k="38" />
    <hkern u1="&#x192;" u2="&#x33;" k="6" />
    <hkern u1="&#x192;" u2="&#x32;" k="18" />
    <hkern u1="&#x192;" u2="&#x31;" k="40" />
    <hkern u1="&#x1ef2;" u2="&#xf0;" k="80" />
    <hkern u1="&#x2022;" u2="x" k="40" />
    <hkern u1="&#x2022;" u2="v" k="20" />
    <hkern u1="&#x2022;" u2="X" k="40" />
    <hkern u1="&#x2022;" u2="V" k="70" />
    <hkern u1="&#x2044;" g2="seven.dnom" k="-40" />
    <hkern u1="&#x2044;" g2="four.dnom" k="20" />
    <hkern u1="&#x20ac;" u2="&#x31;" k="10" />
    <hkern u1="&#x2116;" u2="&#x37;" k="30" />
    <hkern g1="questiondown.case" u2="V" k="80" />
    <hkern g1="bullet.case" u2="X" k="110" />
    <hkern g1="bullet.case" u2="V" k="70" />
    <hkern g1="at.case" u2="X" k="60" />
    <hkern g1="at.case" u2="V" k="45" />
    <hkern g1="sterling.op" g2="nine.op" k="10" />
    <hkern g1="sterling.op" g2="three.op" k="20" />
    <hkern g1="sterling.op" g2="one.op" k="20" />
    <hkern g1="yen.op" g2="nine.op" k="10" />
    <hkern g1="yen.op" g2="four.op" k="8" />
    <hkern g1="florin.op" g2="nine.op" k="10" />
    <hkern g1="florin.op" g2="seven.op" k="30" />
    <hkern g1="florin.op" g2="five.op" k="24" />
    <hkern g1="florin.op" g2="four.op" k="34" />
    <hkern g1="florin.op" g2="three.op" k="20" />
    <hkern g1="florin.op" g2="two.op" k="20" />
    <hkern g1="florin.op" g2="one.op" k="30" />
    <hkern g1="one.op" g2="three.op" k="30" />
    <hkern g1="one.op" g2="one.op" k="30" />
    <hkern g1="two.op" g2="florin.op" k="18" />
    <hkern g1="two.op" u2="&#x3c;" k="36" />
    <hkern g1="three.op" g2="nine.op" k="5" />
    <hkern g1="three.op" g2="one.op" k="20" />
    <hkern g1="three.op" g2="florin.op" k="20" />
    <hkern g1="three.op" g2="yen.op" k="20" />
    <hkern g1="four.op" g2="nine.op" k="10" />
    <hkern g1="four.op" g2="seven.op" k="10" />
    <hkern g1="four.op" g2="one.op" k="110" />
    <hkern g1="four.op" u2="&#xb0;" k="70" />
    <hkern g1="five.op" g2="nine.op" k="20" />
    <hkern g1="five.op" g2="seven.op" k="40" />
    <hkern g1="five.op" g2="one.op" k="50" />
    <hkern g1="five.op" g2="florin.op" k="16" />
    <hkern g1="five.op" u2="&#xb0;" k="10" />
    <hkern g1="five.op" u2="&#x3c;" k="6" />
    <hkern g1="five.op" u2="&#x23;" k="-30" />
    <hkern g1="seven.op" g2="five.op" k="20" />
    <hkern g1="seven.op" g2="four.op" k="80" />
    <hkern g1="seven.op" g2="florin.op" k="70" />
    <hkern g1="seven.op" u2="&#xb0;" k="-20" />
    <hkern g1="seven.op" u2="&#x3e;" k="30" />
    <hkern g1="seven.op" u2="&#x3c;" k="70" />
    <hkern g1="nine.op" g2="nine.op" k="20" />
    <hkern g1="nine.op" g2="seven.op" k="50" />
    <hkern g1="nine.op" g2="five.op" k="20" />
    <hkern g1="nine.op" g2="three.op" k="15" />
    <hkern g1="nine.op" g2="one.op" k="20" />
    <hkern g1="nine.op" u2="&#xb0;" k="20" />
    <hkern g1="two.numr" u2="&#x2044;" k="-40" />
    <hkern g1="four.numr" u2="&#x2044;" k="-30" />
    <hkern g1="seven.numr" u2="&#x2044;" k="60" />
    <hkern g1="germandbls"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="germandbls"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="germandbls"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="germandbls"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="germandbls"
	g2="comma,quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="germandbls"
	g2="hyphen,periodcentered,endash,emdash"
	k="-4" />
    <hkern g1="germandbls"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="4" />
    <hkern g1="germandbls"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="50" />
    <hkern g1="germandbls"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="germandbls"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="germandbls"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="germandbls"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="germandbls"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="2" />
    <hkern g1="germandbls"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="8" />
    <hkern g1="germandbls"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="2" />
    <hkern g1="germandbls"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="2" />
    <hkern g1="six,six.op"
	g2="quotedbl,quotesingle"
	k="58" />
    <hkern g1="six,six.op"
	g2="period,ellipsis"
	k="34" />
    <hkern g1="six,six.op"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="six,six.op"
	g2="three.op"
	k="35" />
    <hkern g1="six,six.op"
	g2="five"
	k="20" />
    <hkern g1="six,six.op"
	g2="three"
	k="30" />
    <hkern g1="six,six.op"
	g2="nine.op"
	k="50" />
    <hkern g1="six,six.op"
	g2="five.op"
	k="20" />
    <hkern g1="six,six.op"
	g2="two"
	k="14" />
    <hkern g1="six,six.op"
	g2="ordfeminine,ordmasculine"
	k="34" />
    <hkern g1="six,six.op"
	g2="cent,cent.weight"
	k="8" />
    <hkern g1="six,six.op"
	g2="one"
	k="70" />
    <hkern g1="six,six.op"
	g2="Euro"
	k="4" />
    <hkern g1="six,six.op"
	g2="one.op"
	k="50" />
    <hkern g1="six,six.op"
	g2="dollar,dollar.weight"
	k="24" />
    <hkern g1="six,six.op"
	g2="degree"
	k="34" />
    <hkern g1="six,six.op"
	g2="seven.op"
	k="55" />
    <hkern g1="six,six.op"
	g2="florin"
	k="20" />
    <hkern g1="six,six.op"
	g2="nine"
	k="34" />
    <hkern g1="six,six.op"
	g2="florin.op"
	k="12" />
    <hkern g1="six,six.op"
	g2="yen"
	k="38" />
    <hkern g1="six,six.op"
	g2="seven"
	k="64" />
    <hkern g1="six,six.op"
	g2="zeroslash.op,zero.op"
	k="5" />
    <hkern g1="six,six.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="70" />
    <hkern g1="six,six.op"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="six,six.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="six,six.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="twosuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="twosuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="two.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="two.op"
	g2="plus,divide,minus"
	k="30" />
    <hkern g1="two.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="18" />
    <hkern g1="four"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="four"
	g2="ordfeminine,ordmasculine"
	k="40" />
    <hkern g1="four"
	g2="cent,cent.weight"
	k="20" />
    <hkern g1="four"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="quotedbl,quotesingle"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="quotedbl,quotesingle"
	g2="three.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="five"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="three"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="nine.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="five.op"
	k="30" />
    <hkern g1="quotedbl,quotesingle"
	g2="two"
	k="30" />
    <hkern g1="quotedbl,quotesingle"
	g2="two.op"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="slash"
	k="180" />
    <hkern g1="quotedbl,quotesingle"
	g2="four.op"
	k="130" />
    <hkern g1="quotedbl,quotesingle"
	g2="four"
	k="70" />
    <hkern g1="quotedbl,quotesingle"
	g2="x"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="zeroslash.op,zero.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="quotedbl,quotesingle"
	g2="eight,eight.op"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="60" />
    <hkern g1="quotedbl,quotesingle"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="V"
	k="60" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="X"
	k="100" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="18" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="76" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="two"
	g2="cent,cent.weight"
	k="18" />
    <hkern g1="two"
	g2="dollar,dollar.weight"
	k="10" />
    <hkern g1="two"
	g2="plus,divide,minus"
	k="78" />
    <hkern g1="two"
	g2="zero,six,zeroslash,six.op"
	k="10" />
    <hkern g1="two"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="10" />
    <hkern g1="threesuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="threesuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="Euro.op,Euro.op.weight"
	g2="three.op"
	k="6" />
    <hkern g1="nine.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="nine.op"
	g2="period,ellipsis"
	k="34" />
    <hkern g1="nine.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="nine.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-16" />
    <hkern g1="eth"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="eth"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="eth"
	g2="ae,aeacute"
	k="5" />
    <hkern g1="eth"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="eth"
	g2="z,zacute,zdotaccent,zcaron"
	k="8" />
    <hkern g1="eth"
	g2="hyphen,periodcentered,endash,emdash"
	k="-8" />
    <hkern g1="eth"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="eth"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="56" />
    <hkern g1="eth"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="eth"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="30" />
    <hkern g1="eth"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="28" />
    <hkern g1="eth"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="eth"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="6" />
    <hkern g1="eth"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="2" />
    <hkern g1="eth"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="12" />
    <hkern g1="eth"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-2" />
    <hkern g1="eth"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="2" />
    <hkern g1="uni2075"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2075"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="one"
	g2="quotedbl,quotesingle"
	k="16" />
    <hkern g1="one"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="one"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="one"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="14" />
    <hkern g1="five.op"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="five.op"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="five.op"
	g2="ordfeminine,ordmasculine"
	k="30" />
    <hkern g1="five.op"
	g2="cent.op,cent.op.weight"
	k="30" />
    <hkern g1="five.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="five.op"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="five.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="five.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="30" />
    <hkern g1="uni2077"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2077"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="zeroslash.op,zero.op"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="zeroslash.op,zero.op"
	g2="period,ellipsis"
	k="34" />
    <hkern g1="zeroslash.op,zero.op"
	g2="three.op"
	k="40" />
    <hkern g1="zeroslash.op,zero.op"
	g2="nine.op"
	k="5" />
    <hkern g1="zeroslash.op,zero.op"
	g2="five.op"
	k="20" />
    <hkern g1="zeroslash.op,zero.op"
	g2="one.op"
	k="10" />
    <hkern g1="zeroslash.op,zero.op"
	g2="seven.op"
	k="40" />
    <hkern g1="zeroslash.op,zero.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="64" />
    <hkern g1="zeroslash.op,zero.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="three"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="three"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="three"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="three"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="three.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="three.op"
	g2="cent.op,cent.op.weight"
	k="20" />
    <hkern g1="three.op"
	g2="Euro.op,Euro.op.weight"
	k="-6" />
    <hkern g1="three.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="three.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="uni2078"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2078"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="v"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="v"
	g2="ae,aeacute"
	k="15" />
    <hkern g1="v"
	g2="guillemotleft,guilsinglleft"
	k="14" />
    <hkern g1="v"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="v"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="v"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="v"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="68" />
    <hkern g1="v"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-2" />
    <hkern g1="v"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-10" />
    <hkern g1="v"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="66" />
    <hkern g1="v"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="v"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-8" />
    <hkern g1="v"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-2" />
    <hkern g1="v"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="v"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="18" />
    <hkern g1="v"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-6" />
    <hkern g1="v"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="v"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="2" />
    <hkern g1="eight,eight.op"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="eight,eight.op"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="eight,eight.op"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="eight,eight.op"
	g2="nine.op"
	k="5" />
    <hkern g1="eight,eight.op"
	g2="one"
	k="10" />
    <hkern g1="eight,eight.op"
	g2="Euro"
	k="-6" />
    <hkern g1="eight,eight.op"
	g2="cent.op,cent.op.weight"
	k="10" />
    <hkern g1="eight,eight.op"
	g2="numbersign"
	k="-10" />
    <hkern g1="eight,eight.op"
	g2="seven"
	k="10" />
    <hkern g1="eight,eight.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="eight,eight.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="Thorn"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="Thorn"
	g2="period,ellipsis"
	k="60" />
    <hkern g1="Thorn"
	g2="guilsinglleft.case,guillemotleft.case"
	k="-12" />
    <hkern g1="Thorn"
	g2="guilsinglright.case,guillemotright.case"
	k="-8" />
    <hkern g1="Thorn"
	g2="AE,AEacute"
	k="48" />
    <hkern g1="Thorn"
	g2="J,Jcircumflex"
	k="18" />
    <hkern g1="Thorn"
	g2="comma,quotesinglbase,quotedblbase"
	k="100" />
    <hkern g1="Thorn"
	g2="hyphen,periodcentered,endash,emdash"
	k="-12" />
    <hkern g1="Thorn"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="Thorn"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-36" />
    <hkern g1="Thorn"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="76" />
    <hkern g1="Thorn"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-12" />
    <hkern g1="Thorn"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-8" />
    <hkern g1="Thorn"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="64" />
    <hkern g1="Thorn"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="Thorn"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-8" />
    <hkern g1="Thorn"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-8" />
    <hkern g1="Thorn"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="24" />
    <hkern g1="Thorn"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-4" />
    <hkern g1="Thorn"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-8" />
    <hkern g1="Thorn"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-8" />
    <hkern g1="Thorn"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-8" />
    <hkern g1="V"
	g2="period,ellipsis"
	k="140" />
    <hkern g1="V"
	g2="ae,aeacute"
	k="70" />
    <hkern g1="V"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="V"
	g2="guillemotleft,guilsinglleft"
	k="64" />
    <hkern g1="V"
	g2="guilsinglleft.case,guillemotleft.case"
	k="48" />
    <hkern g1="V"
	g2="guilsinglright.case,guillemotright.case"
	k="22" />
    <hkern g1="V"
	g2="AE,AEacute"
	k="86" />
    <hkern g1="V"
	g2="J,Jcircumflex"
	k="38" />
    <hkern g1="V"
	g2="guillemotright,guilsinglright"
	k="44" />
    <hkern g1="V"
	g2="comma,quotesinglbase,quotedblbase"
	k="160" />
    <hkern g1="V"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="V"
	g2="hyphen,periodcentered,endash,emdash"
	k="70" />
    <hkern g1="V"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="60" />
    <hkern g1="V"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-4" />
    <hkern g1="V"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="V"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="V"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-4" />
    <hkern g1="V"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-4" />
    <hkern g1="V"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="34" />
    <hkern g1="V"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="34" />
    <hkern g1="V"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="74" />
    <hkern g1="V"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="24" />
    <hkern g1="V"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="60" />
    <hkern g1="V"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="50" />
    <hkern g1="V"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="44" />
    <hkern g1="V"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="42" />
    <hkern g1="V"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="V"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="62" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="V"
	k="44" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="88" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-12" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="74" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="4" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="x"
	k="38" />
    <hkern g1="guillemotright,guilsinglright"
	g2="V"
	k="70" />
    <hkern g1="guillemotright,guilsinglright"
	g2="X"
	k="18" />
    <hkern g1="guillemotright,guilsinglright"
	g2="v"
	k="14" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="100" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="94" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="guillemotright,guilsinglright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="14" />
    <hkern g1="asterisk"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="asterisk"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="asterisk"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="8" />
    <hkern g1="asterisk"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="asterisk"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="asterisk"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="48" />
    <hkern g1="asterisk"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="asterisk"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="F"
	g2="period,ellipsis"
	k="130" />
    <hkern g1="F"
	g2="ae,aeacute"
	k="30" />
    <hkern g1="F"
	g2="AE,AEacute"
	k="104" />
    <hkern g1="F"
	g2="J,Jcircumflex"
	k="60" />
    <hkern g1="F"
	g2="comma,quotesinglbase,quotedblbase"
	k="130" />
    <hkern g1="F"
	g2="z,zacute,zdotaccent,zcaron"
	k="26" />
    <hkern g1="F"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="F"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-4" />
    <hkern g1="F"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-16" />
    <hkern g1="F"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="F"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="22" />
    <hkern g1="F"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="68" />
    <hkern g1="F"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-2" />
    <hkern g1="F"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="10" />
    <hkern g1="onesuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="onesuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="two"
	k="20" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="four.op"
	k="110" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="four"
	k="80" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="seven"
	k="-10" />
    <hkern g1="dollar.op,dollar.op.weight"
	g2="three.op"
	k="18" />
    <hkern g1="dollar.op,dollar.op.weight"
	g2="nine.op"
	k="10" />
    <hkern g1="dollar.op,dollar.op.weight"
	g2="one.op"
	k="70" />
    <hkern g1="seven"
	g2="period,ellipsis"
	k="160" />
    <hkern g1="seven"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="seven"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="seven"
	g2="cent,cent.weight"
	k="24" />
    <hkern g1="seven"
	g2="dollar,dollar.weight"
	k="24" />
    <hkern g1="seven"
	g2="comma,quotesinglbase,quotedblbase"
	k="180" />
    <hkern g1="seven"
	g2="plus,divide,minus"
	k="160" />
    <hkern g1="seven"
	g2="hyphen,periodcentered,endash,emdash"
	k="60" />
    <hkern g1="seven"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="106" />
    <hkern g1="x"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="x"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="x"
	g2="guillemotleft,guilsinglleft"
	k="38" />
    <hkern g1="x"
	g2="z,zacute,zdotaccent,zcaron"
	k="-4" />
    <hkern g1="x"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="x"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-4" />
    <hkern g1="x"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="74" />
    <hkern g1="x"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="4" />
    <hkern g1="x"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="46" />
    <hkern g1="x"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="x"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-2" />
    <hkern g1="x"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="2" />
    <hkern g1="x"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-8" />
    <hkern g1="x"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="x"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="12" />
    <hkern g1="x"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="15" />
    <hkern g1="x"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="26" />
    <hkern g1="X"
	g2="ae,aeacute"
	k="30" />
    <hkern g1="X"
	g2="guillemotleft,guilsinglleft"
	k="24" />
    <hkern g1="X"
	g2="guilsinglleft.case,guillemotleft.case"
	k="94" />
    <hkern g1="X"
	g2="guilsinglright.case,guillemotright.case"
	k="58" />
    <hkern g1="X"
	g2="AE,AEacute"
	k="-4" />
    <hkern g1="X"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="X"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="104" />
    <hkern g1="X"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="40" />
    <hkern g1="X"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="64" />
    <hkern g1="X"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="64" />
    <hkern g1="X"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="X"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="X"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="16" />
    <hkern g1="X"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-4" />
    <hkern g1="X"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="X"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="50" />
    <hkern g1="X"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="X"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="44" />
    <hkern g1="X"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="60" />
    <hkern g1="B"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="B"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="B"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="48" />
    <hkern g1="B"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="26" />
    <hkern g1="B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="16" />
    <hkern g1="B"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="B"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="8" />
    <hkern g1="B"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="B"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="five"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="five"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="five"
	g2="ordfeminine,ordmasculine"
	k="30" />
    <hkern g1="five"
	g2="dollar,dollar.weight"
	k="14" />
    <hkern g1="five"
	g2="comma,quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="five"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="K,uni0136"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="K,uni0136"
	g2="ae,aeacute"
	k="60" />
    <hkern g1="K,uni0136"
	g2="degree"
	k="30" />
    <hkern g1="K,uni0136"
	g2="guillemotleft,guilsinglleft"
	k="44" />
    <hkern g1="K,uni0136"
	g2="guilsinglleft.case,guillemotleft.case"
	k="128" />
    <hkern g1="K,uni0136"
	g2="guilsinglright.case,guillemotright.case"
	k="78" />
    <hkern g1="K,uni0136"
	g2="v"
	k="100" />
    <hkern g1="K,uni0136"
	g2="at.case"
	k="90" />
    <hkern g1="K,uni0136"
	g2="bullet"
	k="100" />
    <hkern g1="K,uni0136"
	g2="bullet.case"
	k="120" />
    <hkern g1="K,uni0136"
	g2="copyright"
	k="70" />
    <hkern g1="K,uni0136"
	g2="registered"
	k="30" />
    <hkern g1="K,uni0136"
	g2="at"
	k="20" />
    <hkern g1="K,uni0136"
	g2="questiondown.case"
	k="48" />
    <hkern g1="K,uni0136"
	g2="germandbls"
	k="14" />
    <hkern g1="K,uni0136"
	g2="asterisk"
	k="30" />
    <hkern g1="K,uni0136"
	g2="hyphen,periodcentered,endash,emdash"
	k="104" />
    <hkern g1="K,uni0136"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="140" />
    <hkern g1="K,uni0136"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="60" />
    <hkern g1="K,uni0136"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="46" />
    <hkern g1="K,uni0136"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="K,uni0136"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="100" />
    <hkern g1="K,uni0136"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="70" />
    <hkern g1="K,uni0136"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="K,uni0136"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="34" />
    <hkern g1="K,uni0136"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="34" />
    <hkern g1="K,uni0136"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="75" />
    <hkern g1="K,uni0136"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="50" />
    <hkern g1="K,uni0136"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="74" />
    <hkern g1="K,uni0136"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="20" />
    <hkern g1="K,uni0136"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="70" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="V"
	k="28" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="X"
	k="70" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="66" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="period,ellipsis"
	g2="quotedbl,quotesingle"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="three.op"
	k="10" />
    <hkern g1="period,ellipsis"
	g2="nine.op"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="five.op"
	k="30" />
    <hkern g1="period,ellipsis"
	g2="one"
	k="214" />
    <hkern g1="period,ellipsis"
	g2="four"
	k="28" />
    <hkern g1="period,ellipsis"
	g2="one.op"
	k="210" />
    <hkern g1="period,ellipsis"
	g2="V"
	k="140" />
    <hkern g1="period,ellipsis"
	g2="seven.op"
	k="120" />
    <hkern g1="period,ellipsis"
	g2="seven"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="v"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="asterisk"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="zeroslash.op,zero.op"
	k="34" />
    <hkern g1="period,ellipsis"
	g2="eight,eight.op"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="period,ellipsis"
	g2="zero,six,zeroslash,six.op"
	k="65" />
    <hkern g1="period,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="144" />
    <hkern g1="period,ellipsis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="150" />
    <hkern g1="period,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="period,ellipsis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="50" />
    <hkern g1="period,ellipsis"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="29" />
    <hkern g1="slash"
	g2="period,ellipsis"
	k="130" />
    <hkern g1="slash"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="slash"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="slash"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="slash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-8" />
    <hkern g1="slash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-8" />
    <hkern g1="slash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="slash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="24" />
    <hkern g1="slash"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="slash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="slash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="60" />
    <hkern g1="slash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="slash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="24" />
    <hkern g1="slash"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="seven.op"
	g2="period,ellipsis"
	k="180" />
    <hkern g1="seven.op"
	g2="zeroslash.op,zero.op"
	k="10" />
    <hkern g1="seven.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="190" />
    <hkern g1="seven.op"
	g2="plus,divide,minus"
	k="96" />
    <hkern g1="seven.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="30" />
    <hkern g1="seven.op"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="30" />
    <hkern g1="P"
	g2="period,ellipsis"
	k="100" />
    <hkern g1="P"
	g2="ae,aeacute"
	k="5" />
    <hkern g1="P"
	g2="guilsinglright.case,guillemotright.case"
	k="-4" />
    <hkern g1="P"
	g2="AE,AEacute"
	k="102" />
    <hkern g1="P"
	g2="J,Jcircumflex"
	k="34" />
    <hkern g1="P"
	g2="guillemotright,guilsinglright"
	k="-8" />
    <hkern g1="P"
	g2="comma,quotesinglbase,quotedblbase"
	k="170" />
    <hkern g1="P"
	g2="hyphen,periodcentered,endash,emdash"
	k="-4" />
    <hkern g1="P"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="P"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-12" />
    <hkern g1="P"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="28" />
    <hkern g1="P"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-16" />
    <hkern g1="P"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-16" />
    <hkern g1="P"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="12" />
    <hkern g1="P"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-8" />
    <hkern g1="P"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-8" />
    <hkern g1="P"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="48" />
    <hkern g1="P"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-4" />
    <hkern g1="P"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-12" />
    <hkern g1="P"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-8" />
    <hkern g1="P"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-4" />
    <hkern g1="cent,cent.weight"
	g2="three"
	k="12" />
    <hkern g1="cent,cent.weight"
	g2="one"
	k="10" />
    <hkern g1="cent,cent.weight"
	g2="seven"
	k="10" />
    <hkern g1="dcaron,lcaron"
	g2="x"
	k="-50" />
    <hkern g1="dcaron,lcaron"
	g2="V"
	k="-146" />
    <hkern g1="dcaron,lcaron"
	g2="X"
	k="-120" />
    <hkern g1="dcaron,lcaron"
	g2="AE,AEacute"
	k="34" />
    <hkern g1="dcaron,lcaron"
	g2="J,Jcircumflex"
	k="-6" />
    <hkern g1="dcaron,lcaron"
	g2="v"
	k="-30" />
    <hkern g1="dcaron,lcaron"
	g2="germandbls"
	k="-64" />
    <hkern g1="dcaron,lcaron"
	g2="asterisk"
	k="-106" />
    <hkern g1="dcaron,lcaron"
	g2="parenright"
	k="-120" />
    <hkern g1="dcaron,lcaron"
	g2="bracketright,braceright"
	k="-140" />
    <hkern g1="dcaron,lcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="-26" />
    <hkern g1="dcaron,lcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-124" />
    <hkern g1="dcaron,lcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-140" />
    <hkern g1="dcaron,lcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-50" />
    <hkern g1="dcaron,lcaron"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-100" />
    <hkern g1="dcaron,lcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-138" />
    <hkern g1="dcaron,lcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-140" />
    <hkern g1="dcaron,lcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-30" />
    <hkern g1="dcaron,lcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-26" />
    <hkern g1="dcaron,lcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="8" />
    <hkern g1="dcaron,lcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="-70" />
    <hkern g1="dcaron,lcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="dcaron,lcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-90" />
    <hkern g1="dcaron,lcaron"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-40" />
    <hkern g1="dcaron,lcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-64" />
    <hkern g1="dcaron,lcaron"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="-76" />
    <hkern g1="dcaron,lcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="dcaron,lcaron"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,uni2116"
	k="-74" />
    <hkern g1="four.op"
	g2="quotedbl,quotesingle"
	k="60" />
    <hkern g1="four.op"
	g2="ordfeminine,ordmasculine"
	k="50" />
    <hkern g1="four.op"
	g2="plus,divide,minus"
	k="40" />
    <hkern g1="four.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="four.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="uni2074"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2074"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="cent.op,cent.op.weight"
	g2="three.op"
	k="20" />
    <hkern g1="cent.op,cent.op.weight"
	g2="one.op"
	k="10" />
    <hkern g1="uni2076"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2076"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="colon,semicolon"
	g2="V"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="seven"
	k="20" />
    <hkern g1="colon,semicolon"
	g2="eight,eight.op"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="100" />
    <hkern g1="colon,semicolon"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="quotedbl,quotesingle"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="three.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="nine.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="five.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="one"
	k="154" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="one.op"
	k="160" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="V"
	k="120" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="X"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="seven.op"
	k="90" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="seven"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="v"
	k="40" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="asterisk"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="zero,six,zeroslash,six.op"
	k="22" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="144" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="130" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="70" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="60" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="2" />
    <hkern g1="uni2070,uni2079"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2070,uni2079"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="zero,nine,zeroslash"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="period,ellipsis"
	k="25" />
    <hkern g1="zero,nine,zeroslash"
	g2="three"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="two"
	k="10" />
    <hkern g1="zero,nine,zeroslash"
	g2="one"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="degree"
	k="4" />
    <hkern g1="zero,nine,zeroslash"
	g2="seven"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="zero,nine,zeroslash"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="hyphen,periodcentered,endash,emdash"
	k="-30" />
    <hkern g1="zero,nine,zeroslash"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="zero,nine,zeroslash"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="20" />
    <hkern g1="plus,divide,minus"
	g2="three.op"
	k="50" />
    <hkern g1="plus,divide,minus"
	g2="five"
	k="12" />
    <hkern g1="plus,divide,minus"
	g2="three"
	k="50" />
    <hkern g1="plus,divide,minus"
	g2="nine.op"
	k="6" />
    <hkern g1="plus,divide,minus"
	g2="five.op"
	k="30" />
    <hkern g1="plus,divide,minus"
	g2="two"
	k="20" />
    <hkern g1="plus,divide,minus"
	g2="one"
	k="180" />
    <hkern g1="plus,divide,minus"
	g2="four.op"
	k="30" />
    <hkern g1="plus,divide,minus"
	g2="one.op"
	k="100" />
    <hkern g1="plus,divide,minus"
	g2="seven.op"
	k="140" />
    <hkern g1="plus,divide,minus"
	g2="seven"
	k="104" />
    <hkern g1="plus,divide,minus"
	g2="zero,six,zeroslash,six.op"
	k="14" />
    <hkern g1="registered"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="registered"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-12" />
    <hkern g1="registered"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-8" />
    <hkern g1="registered"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-4" />
    <hkern g1="registered"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="registered"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="90" />
    <hkern g1="registered"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="three.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="nine.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="five.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="two"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="two.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="slash"
	k="150" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="four.op"
	k="160" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="four"
	k="110" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="one.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="V"
	k="-8" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="v"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="zeroslash.op,zero.op"
	k="70" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="eight,eight.op"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="z,zacute,zdotaccent,zcaron"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="zero,six,zeroslash,six.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-8" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="70" />
    <hkern g1="questiondown"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="questiondown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="134" />
    <hkern g1="questiondown"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="questiondown"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="128" />
    <hkern g1="questiondown"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="110" />
    <hkern g1="questiondown"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="40" />
    <hkern g1="questiondown"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="70" />
    <hkern g1="questiondown"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="60" />
    <hkern g1="questiondown"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="30" />
    <hkern g1="questiondown"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="questiondown"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="70" />
    <hkern g1="questiondown"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="54" />
    <hkern g1="questiondown"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="60" />
    <hkern g1="questiondown"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="60" />
    <hkern g1="questiondown"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="50" />
    <hkern g1="questiondown"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="60" />
    <hkern g1="questiondown"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,uni2116"
	k="60" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="period,ellipsis"
	k="114" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="slash"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="V"
	k="34" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="X"
	k="54" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="degree"
	k="-8" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="v"
	k="-4" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="bullet"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="registered"
	k="-4" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="germandbls"
	k="4" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="asterisk"
	k="-8" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="134" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="4" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="trademark"
	k="40" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="78" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-8" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="48" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="26" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-4" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="6" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="38" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-8" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-2" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="12" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="V"
	k="30" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="X"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="J,Jcircumflex"
	k="8" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="bullet"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="bullet.case"
	k="-4" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="registered"
	k="8" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-4" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="trademark"
	k="34" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="backslash"
	k="30" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="42" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="28" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="6" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="2" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="8" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-4" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="Euro"
	g2="zero,six,zeroslash,six.op"
	k="10" />
    <hkern g1="degree"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-4" />
    <hkern g1="degree"
	g2="zero,six,zeroslash,six.op"
	k="-16" />
    <hkern g1="degree"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="8" />
    <hkern g1="degree"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-12" />
    <hkern g1="degree"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-8" />
    <hkern g1="degree"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-4" />
    <hkern g1="degree"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="degree"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="90" />
    <hkern g1="degree"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="4" />
    <hkern g1="degree"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="ae,aeacute"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="V"
	k="70" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="numbersign"
	k="-20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="bullet"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-4" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="trademark"
	k="50" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="backslash"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="124" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="72" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="2" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="bullet.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="54" />
    <hkern g1="bullet.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="102" />
    <hkern g1="bullet.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="94" />
    <hkern g1="bullet.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="bullet.case"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="bullet.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="questiondown.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="questiondown.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="70" />
    <hkern g1="questiondown.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="questiondown.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="questiondown.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-20" />
    <hkern g1="questiondown.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="questiondown.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="period,ellipsis"
	k="-8" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="x"
	k="-4" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="numbersign"
	k="-8" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="J,Jcircumflex"
	k="-14" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="v"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="bullet.case"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="-4" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="60" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="question"
	k="-12" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-4" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="-4" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="f,longs,f_f,f.1"
	g2="quotedbl,quotesingle"
	k="-16" />
    <hkern g1="f,longs,f_f,f.1"
	g2="period,ellipsis"
	k="94" />
    <hkern g1="f,longs,f_f,f.1"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="f,longs,f_f,f.1"
	g2="slash"
	k="40" />
    <hkern g1="f,longs,f_f,f.1"
	g2="x"
	k="-4" />
    <hkern g1="f,longs,f_f,f.1"
	g2="V"
	k="-34" />
    <hkern g1="f,longs,f_f,f.1"
	g2="degree"
	k="-42" />
    <hkern g1="f,longs,f_f,f.1"
	g2="guillemotleft,guilsinglleft"
	k="18" />
    <hkern g1="f,longs,f_f,f.1"
	g2="numbersign"
	k="20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="v"
	k="-8" />
    <hkern g1="f,longs,f_f,f.1"
	g2="bullet"
	k="20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="registered"
	k="-44" />
    <hkern g1="f,longs,f_f,f.1"
	g2="asterisk"
	k="-38" />
    <hkern g1="f,longs,f_f,f.1"
	g2="comma,quotesinglbase,quotedblbase"
	k="94" />
    <hkern g1="f,longs,f_f,f.1"
	g2="z,zacute,zdotaccent,zcaron"
	k="-2" />
    <hkern g1="f,longs,f_f,f.1"
	g2="hyphen,periodcentered,endash,emdash"
	k="20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="trademark"
	k="-42" />
    <hkern g1="f,longs,f_f,f.1"
	g2="backslash"
	k="-72" />
    <hkern g1="f,longs,f_f,f.1"
	g2="question"
	k="-38" />
    <hkern g1="f,longs,f_f,f.1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-36" />
    <hkern g1="f,longs,f_f,f.1"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-22" />
    <hkern g1="f,longs,f_f,f.1"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-32" />
    <hkern g1="f,longs,f_f,f.1"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-50" />
    <hkern g1="f,longs,f_f,f.1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-28" />
    <hkern g1="f,longs,f_f,f.1"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-8" />
    <hkern g1="f,longs,f_f,f.1"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-6" />
    <hkern g1="f,longs,f_f,f.1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="38" />
    <hkern g1="f,longs,f_f,f.1"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-22" />
    <hkern g1="f,longs,f_f,f.1"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="2" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="three.op"
	k="60" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="three"
	k="18" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="nine.op"
	k="-10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="one"
	k="170" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="four"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="x"
	k="50" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="one.op"
	k="26" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="V"
	k="70" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="X"
	k="50" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="seven.op"
	k="100" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="seven"
	k="70" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="v"
	k="10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="zeroslash.op,zero.op"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="eight,eight.op"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="zero,six,zeroslash,six.op"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="106" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="90" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="6" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-4" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-8" />
    <hkern g1="numbersign"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="numbersign"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-4" />
    <hkern g1="numbersign"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-8" />
    <hkern g1="numbersign"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-4" />
    <hkern g1="numbersign"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="40" />
    <hkern g1="numbersign"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="92" />
    <hkern g1="numbersign"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="numbersign"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="34" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="V"
	k="70" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="X"
	k="86" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="122" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="100" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="26" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-4" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-8" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="ae,aeacute"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="slash"
	k="40" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="x"
	k="-2" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="V"
	k="34" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="X"
	k="24" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="degree"
	k="-8" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="guillemotleft,guilsinglleft"
	k="14" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="numbersign"
	k="12" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="v"
	k="-8" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="bullet"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="at"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="100" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="trademark"
	k="24" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="backslash"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="68" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-2" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-4" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-4" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="22" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="18" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-6" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="2" />
    <hkern g1="at.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="90" />
    <hkern g1="at.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="40" />
    <hkern g1="at.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="at.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="at.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-4" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="period,ellipsis"
	k="138" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="ae,aeacute"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="slash"
	k="140" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="x"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="V"
	k="-4" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="X"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="degree"
	k="-12" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guillemotleft,guilsinglleft"
	k="82" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="numbersign"
	k="76" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guilsinglleft.case,guillemotleft.case"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="AE,AEacute"
	k="150" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="J,Jcircumflex"
	k="44" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guillemotright,guilsinglright"
	k="74" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="v"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="at.case"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bullet"
	k="130" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bullet.case"
	k="90" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="copyright"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="registered"
	k="-8" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="at"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="questiondown.case"
	k="24" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="germandbls"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="comma,quotesinglbase,quotedblbase"
	k="144" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bracketright,braceright"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="z,zacute,zdotaccent,zcaron"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="hyphen,periodcentered,endash,emdash"
	k="90" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-4" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="trademark"
	k="-8" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="backslash"
	k="-8" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="question"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-12" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-4" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="questiondown"
	k="142" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="exclamdown"
	k="52" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="84" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="74" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="40" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="32" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="80" />
    <hkern g1="question"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-12" />
    <hkern g1="question"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="62" />
    <hkern g1="exclamdown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="98" />
    <hkern g1="exclamdown"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="70" />
    <hkern g1="exclamdown"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="24" />
    <hkern g1="exclamdown"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="exclamdown"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="ae,aeacute"
	k="40" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="x"
	k="-4" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="V"
	k="70" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="bullet"
	k="50" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="copyright"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="hyphen,periodcentered,endash,emdash"
	k="70" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="trademark"
	k="30" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="backslash"
	k="40" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="114" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="68" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="12" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="22" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-4" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="28" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="18" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="22" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="42" />
    <hkern g1="exclamdown.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="period,ellipsis"
	k="132" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ae,aeacute"
	k="130" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="colon,semicolon"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="slash"
	k="102" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="x"
	k="68" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="V"
	k="-4" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotleft,guilsinglleft"
	k="94" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="numbersign"
	k="92" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guilsinglleft.case,guillemotleft.case"
	k="76" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guilsinglright.case,guillemotright.case"
	k="36" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="AE,AEacute"
	k="130" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J,Jcircumflex"
	k="42" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotright,guilsinglright"
	k="76" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="v"
	k="56" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at.case"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="bullet"
	k="130" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="bullet.case"
	k="66" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="copyright"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at"
	k="94" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="germandbls"
	k="24" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="asterisk"
	k="8" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="132" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="102" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="94" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="104" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="backslash"
	k="-8" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-16" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="66" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="26" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="56" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="56" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="questiondown"
	k="160" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="exclamdown"
	k="98" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="110" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="36" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="76" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="92" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="84" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="38" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="8" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="98" />
    <hkern g1="bullet"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="146" />
    <hkern g1="bullet"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="130" />
    <hkern g1="bullet"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="bullet"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="bullet"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="bullet"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="bullet"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="quotedbl,quotesingle"
	k="170" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="period,ellipsis"
	k="-8" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="slash"
	k="-8" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="V"
	k="96" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="X"
	k="-12" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="degree"
	k="130" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="numbersign"
	k="-16" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="guilsinglleft.case,guillemotleft.case"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="AE,AEacute"
	k="-16" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="J,Jcircumflex"
	k="-18" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="v"
	k="50" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="at.case"
	k="34" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="bullet"
	k="60" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="bullet.case"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="copyright"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="registered"
	k="150" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="asterisk"
	k="158" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="comma,quotesinglbase,quotedblbase"
	k="-12" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="z,zacute,zdotaccent,zcaron"
	k="-10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="hyphen,periodcentered,endash,emdash"
	k="24" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-12" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="112" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="trademark"
	k="180" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="backslash"
	k="110" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="question"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="144" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="100" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="176" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="58" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="45" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="-8" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-8" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="36" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="slash"
	k="44" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="x"
	k="2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="V"
	k="24" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="X"
	k="34" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="J,Jcircumflex"
	k="6" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="guillemotright,guilsinglright"
	k="-4" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="at.case"
	k="-4" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="bullet"
	k="-4" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="asterisk"
	k="-8" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="comma,quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="hyphen,periodcentered,endash,emdash"
	k="-8" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="4" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-8" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="trademark"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="backslash"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="52" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-8" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="32" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-4" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="questiondown"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="8" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-6" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-4" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ae,aeacute"
	k="45" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="colon,semicolon"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="slash"
	k="48" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="V"
	k="-4" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="numbersign"
	k="48" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guilsinglleft.case,guillemotleft.case"
	k="34" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guilsinglright.case,guillemotright.case"
	k="6" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="AE,AEacute"
	k="84" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex"
	k="16" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotright,guilsinglright"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="at.case"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet.case"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="copyright"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="registered"
	k="-4" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="at"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="germandbls"
	k="8" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="90" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,periodcentered,endash,emdash"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="26" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="trademark"
	k="-8" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-4" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="questiondown"
	k="80" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="exclamdown"
	k="6" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="24" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="34" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="2" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="backslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="130" />
    <hkern g1="backslash"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="40" />
    <hkern g1="backslash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="140" />
    <hkern g1="backslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="74" />
    <hkern g1="backslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="28" />
    <hkern g1="backslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="backslash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="backslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="backslash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="backslash"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="40" />
    <hkern g1="backslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="70" />
    <hkern g1="backslash"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="34" />
    <hkern g1="backslash"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="ae,aeacute"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="slash"
	k="16" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="V"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="X"
	k="14" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="degree"
	k="-4" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="v"
	k="-2" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="bullet"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="4" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="backslash"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="74" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-2" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="50" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-4" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="2" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-14" />
    <hkern g1="copyright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="90" />
    <hkern g1="copyright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="copyright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="copyright"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="copyright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="34" />
    <hkern g1="at"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="118" />
    <hkern g1="at"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="at"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="at"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="period,ellipsis"
	k="-4" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="ae,aeacute"
	k="5" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="V"
	k="50" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="numbersign"
	k="-8" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="guillemotright,guilsinglright"
	k="-4" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="bullet"
	k="22" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="hyphen,periodcentered,endash,emdash"
	k="18" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="backslash"
	k="20" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="90" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-2" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="58" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="12" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-4" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="2" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="8" />
    <hkern g1="bracketleft,braceleft"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="ae,aeacute"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="x"
	k="6" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="V"
	k="69" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="X"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="degree"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="v"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="germandbls"
	k="2" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="asterisk"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="hyphen,periodcentered,endash,emdash"
	k="-4" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="trademark"
	k="50" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="backslash"
	k="50" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="112" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="4" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="22" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="12" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="4" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="2" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="4" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="three.op"
	k="30" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="one"
	k="110" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="one.op"
	k="36" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="seven.op"
	k="50" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="seven"
	k="130" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="zero,six,zeroslash,six.op"
	k="20" />
    <hkern g1="trademark"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="trademark"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="slash"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="x"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="V"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="X"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="J,Jcircumflex"
	k="4" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="v"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="bullet.case"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="registered"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="asterisk"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="hyphen,periodcentered,endash,emdash"
	k="-4" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="4" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="trademark"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="backslash"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="50" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="40" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="14" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="12" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="slash"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="V"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="X"
	k="32" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="degree"
	k="-8" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="guillemotleft,guilsinglleft"
	k="-12" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="numbersign"
	k="-8" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="registered"
	k="-8" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="asterisk"
	k="-8" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="comma,quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="hyphen,periodcentered,endash,emdash"
	k="-16" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="trademark"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="backslash"
	k="24" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="42" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-4" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="28" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="2" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="slash"
	k="-4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="V"
	k="54" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="numbersign"
	k="-8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="registered"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="z,zacute,zdotaccent,zcaron"
	k="-2" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="trademark"
	k="20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="88" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="42" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="32" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="-2" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="2" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-2" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="quotedbl,quotesingle"
	k="80" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="x"
	k="-8" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="V"
	k="76" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="X"
	k="-4" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="degree"
	k="120" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="numbersign"
	k="-8" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guilsinglleft.case,guillemotleft.case"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guilsinglright.case,guillemotright.case"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="AE,AEacute"
	k="-12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="J,Jcircumflex"
	k="-10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="v"
	k="52" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="at.case"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="bullet"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="bullet.case"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="copyright"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="registered"
	k="110" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="asterisk"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="62" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="trademark"
	k="120" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="backslash"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="question"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="110" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="108" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="44" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="46" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="22" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-8" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="32" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="22" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="35" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="x"
	k="-2" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="numbersign"
	k="-8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="AE,AEacute"
	k="-8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="J,Jcircumflex"
	k="-4" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="v"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="at.case"
	k="12" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="-8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="2" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="quotedbl,quotesingle"
	k="60" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="slash"
	k="-4" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="x"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="V"
	k="80" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="X"
	k="-4" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="degree"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="numbersign"
	k="-12" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="guillemotright,guilsinglright"
	k="-4" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="v"
	k="4" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="registered"
	k="60" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="asterisk"
	k="30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="hyphen,periodcentered,endash,emdash"
	k="-8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="trademark"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="backslash"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="114" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="76" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="4" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="4" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-2" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-4" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="V"
	k="58" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="degree"
	k="30" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="v"
	k="12" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="registered"
	k="50" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="asterisk"
	k="40" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-4" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="trademark"
	k="80" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="backslash"
	k="40" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="116" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="82" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="24" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="12" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="period,ellipsis"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="slash"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="x"
	k="22" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="X"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="J,Jcircumflex"
	k="8" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="guillemotright,guilsinglright"
	k="4" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="hyphen,periodcentered,endash,emdash"
	k="-4" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-4" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-2" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="2" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="2" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-2" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="2" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="period,ellipsis"
	k="50" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="slash"
	k="64" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="x"
	k="26" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="V"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="X"
	k="44" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="degree"
	k="4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="J,Jcircumflex"
	k="4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="v"
	k="2" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="at.case"
	k="-4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="asterisk"
	k="-10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="2" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-8" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="trademark"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="backslash"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="56" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-2" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="52" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="2" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="2" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-6" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-2" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="x"
	k="22" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="V"
	k="74" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="X"
	k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="degree"
	k="80" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="numbersign"
	k="-8" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="v"
	k="22" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="registered"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="asterisk"
	k="50" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="comma,quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="hyphen,periodcentered,endash,emdash"
	k="-8" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="4" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="trademark"
	k="70" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="backslash"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="114" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="22" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="2" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="2" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-2" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-2" />
    <hkern g1="d,i,j,l,igrave,iacute,icircumflex,idieresis,dcroat,itilde,imacron,ibreve,iogonek,ij,jcircumflex,lacute,uni013C,lslash,f_f_i,f_f_l,i.trk,f_j,fl,f_f_j"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="8" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="V"
	k="40" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-4" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="trademark"
	k="30" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="backslash"
	k="10" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="98" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
  </font>
</defs></svg>

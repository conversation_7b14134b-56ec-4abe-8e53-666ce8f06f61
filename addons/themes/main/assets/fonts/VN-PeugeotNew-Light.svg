<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Wed Jun 23 16:32:39 2021
 By <PERSON><PERSON>sey,,,
Copyright (c) 2019 by Peugeot. All rights reserved.
</metadata>
<defs>
<font id="VN-PeugeotNew-Light" horiz-adv-x="760" >
  <font-face 
    font-family="VN-Peugeot New"
    font-weight="300"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 0 0 0 0 0 0 0 0"
    ascent="802"
    descent="-198"
    x-height="550"
    cap-height="802"
    bbox="-205 -266 1520 1209"
    underline-thickness="55"
    underline-position="-222"
    unicode-range="U+0020-FEFF"
  />
<missing-glyph horiz-adv-x="615" 
d="M0 802h615v-1000h-615v1000zM339 195l15 85q105 2 149.5 41t44.5 115q0 38 -15 68t-48.5 50t-88 30.5t-132.5 10.5q-23 0 -48.5 -1.5t-51 -4t-49 -6t-43.5 -7.5v-124q48 9 96.5 14.5t93.5 5.5q42 0 67.5 -2t39 -7.5t17.5 -14.5t4 -23q0 -12 -2.5 -21t-11.5 -14
t-25.5 -7.5t-44.5 -2.5h-114v-70l21 -115h126zM357 0v153h-162v-153h162z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="726" 
d="M126 502h-63v36l63 12v76q0 101 43 146.5t140 45.5q24 0 45 -1.5t44 -3.5v-50q-21 3 -42 4t-42 1q-70 0 -100.5 -31.5t-30.5 -108.5v-78h282v76q0 50 10.5 86.5t33.5 60t60.5 34.5t91.5 11q26 0 49.5 -1.5t48.5 -3.5v-50q-23 2 -46.5 3.5t-46.5 1.5q-80 0 -112 -31.5
t-32 -108.5v-78h214v-48h-214v-502h-57v502h-282v-502h-57v502z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="965" 
d="M123 502h-64v36l64 12v76q0 101 42.5 146.5t139.5 45.5q24 0 45.5 -1.5t44.5 -3.5v-50q-21 2 -42 3.5t-42 1.5q-70 0 -100.5 -31.5t-30.5 -108.5v-78h281v76q0 101 40.5 146.5t132.5 45.5q20 0 38.5 -1.5t38.5 -3.5v-50q-19 2 -36.5 3.5t-35.5 1.5q-65 0 -93 -31.5
t-28 -108.5v-78h338v-550h-56v502h-282v-502h-57v502h-281v-502h-57v502zM795 782h66v-69h-66v69z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="977" 
d="M123 502h-63l-1 36l64 12v76q0 101 42.5 146.5t139.5 45.5q24 0 45.5 -1.5t44.5 -3.5v-50q-21 2 -42 3.5t-42 1.5q-70 0 -100.5 -31.5t-30.5 -108.5v-78h282v76q0 50 10 86.5t31.5 60t57 34.5t86.5 11q23 0 44 -1.5t44 -3.5v-50q-22 2 -42.5 3.5t-41.5 1.5
q-73 0 -103 -31.5t-30 -108.5v-78h194v-48h-194v-502h-56v502h-282v-502h-57v502zM812 802h56v-802h-56v802z" />
    <glyph glyph-name="f_b" unicode="fb" horiz-adv-x="1099" 
d="M123 502h-64v36l64 12v76q0 50 10 86.5t32 60t58 34.5t87 11q23 0 43 -1.5t43 -3.5v-50q-21 2 -41 3.5t-41 1.5q-73 0 -103.5 -31.5t-30.5 -108.5v-78h193v-48h-193v-502h-57v502zM473 802h57v-288q48 21 107 36.5t118 15.5q66 0 116.5 -16.5t85 -51.5t52.5 -90.5
t18 -132.5t-18 -132t-52.5 -90.5t-85 -52t-116.5 -16.5q-60 0 -120 16.5t-109 37.5l-11 -38h-42v802zM745 514q-63 0 -117.5 -15.5t-97.5 -35.5v-375q43 -20 97.5 -36t117.5 -16q57 0 98.5 13t69.5 41.5t41.5 74.5t13.5 111q0 129 -55 183.5t-168 54.5z" />
    <glyph glyph-name="f_h" unicode="fh" horiz-adv-x="1120" 
d="M123 502h-64v36l64 12v76q0 50 10 86.5t32 60t58 34.5t87 11q23 0 43 -1.5t43 -3.5v-50q-21 2 -41 3.5t-41 1.5q-73 0 -103.5 -31.5t-30.5 -108.5v-78h193v-48h-193v-502h-57v502zM473 802h57v-291q51 23 116.5 39t134.5 16q59 0 103 -13.5t73 -37.5t43.5 -58t14.5 -75
v-382h-56v367q0 69 -43.5 107.5t-141.5 38.5q-73 0 -134.5 -17t-109.5 -38v-458h-57v802z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="627" 
d="M123 502h-64v36l64 12v76q0 101 40.5 146.5t133.5 45.5q20 0 38 -1.5t38 -3.5v-50q-18 2 -35.5 3.5t-35.5 1.5q-65 0 -93.5 -31.5t-28.5 -108.5v-78h338v-550h-57v502h-281v-502h-57v502zM457 782h65v-69h-65v69z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="627" 
d="M123 502h-64v36l64 12v76q0 101 40.5 146.5t133.5 45.5q20 0 38 -1.5t38 -3.5v-50q-18 2 -35.5 3.5t-35.5 1.5q-65 0 -93.5 -31.5t-28.5 -108.5v-78h338v-590q0 -93 -37 -133.5t-126 -40.5q-19 0 -42.5 1t-40.5 3v51q15 -2 38 -3t39 -1q59 0 85.5 27.5t26.5 95.5v542
h-281v-502h-57v502zM457 782h66v-69h-66v69z" />
    <glyph glyph-name="f_k" unicode="fk" horiz-adv-x="1025" 
d="M123 502h-64v36l64 12v76q0 50 10 86.5t32 60t58 34.5t87 11q23 0 43 -1.5t43 -3.5v-50q-21 2 -41 3.5t-41 1.5q-73 0 -103.5 -31.5t-30.5 -108.5v-78h193v-48h-193v-502h-57v502zM473 802h57v-471l346 219h85l-365 -232l386 -318h-83l-369 308v-308h-57v802z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="639" 
d="M123 502h-64v36l64 12v76q0 50 10 86.5t32 60t58 34.5t87 11q23 0 43.5 -1.5t42.5 -3.5v-50q-20 2 -41 3.5t-41 1.5q-74 0 -104 -31.5t-30 -108.5v-78h193v-48h-193v-502h-57v502zM473 802h57v-802h-57v802z" />
    <glyph glyph-name="f_f_b" unicode="ffb" horiz-adv-x="1438" 
d="M123 502h-63l-1 36l64 12v76q0 101 42.5 146.5t139.5 45.5q24 0 45.5 -1.5t44.5 -3.5v-50q-21 2 -42 3.5t-42 1.5q-70 0 -100.5 -31.5t-30.5 -108.5v-78h282v76q0 50 10 86.5t31.5 60t57 34.5t86.5 11q23 0 44 -1.5t44 -3.5v-50q-22 2 -42.5 3.5t-41.5 1.5
q-73 0 -103 -31.5t-30 -108.5v-78h194v-48h-194v-502h-56v502h-282v-502h-57v502zM812 802h56v-288q48 21 107 36.5t119 15.5q66 0 116.5 -16.5t85 -51.5t52.5 -90.5t18 -132.5t-18 -132t-52.5 -90.5t-85 -52t-116.5 -16.5q-60 0 -120.5 16.5t-108.5 37.5l-11 -38h-42v802z
M1084 514q-64 0 -119 -15.5t-97 -35.5v-375q43 -20 97.5 -36t118.5 -16q57 0 98.5 13t69.5 41.5t41.5 74.5t13.5 111q0 129 -55 183.5t-168 54.5z" />
    <glyph glyph-name="f_f_h" unicode="ffh" horiz-adv-x="1459" 
d="M123 502h-63l-1 36l64 12v76q0 101 42.5 146.5t139.5 45.5q24 0 45.5 -1.5t44.5 -3.5v-50q-21 2 -42 3.5t-42 1.5q-70 0 -100.5 -31.5t-30.5 -108.5v-78h282v76q0 50 10 86.5t31.5 60t57 34.5t86.5 11q23 0 44 -1.5t44 -3.5v-50q-22 2 -42.5 3.5t-41.5 1.5
q-73 0 -103 -31.5t-30 -108.5v-78h194v-48h-194v-502h-56v502h-282v-502h-57v502zM812 802h56v-291q52 23 117 39t135 16q118 0 176 -51t58 -133v-382h-57v367q0 69 -43 107.5t-141 38.5q-73 0 -135 -17t-110 -38v-458h-56v802z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="965" 
d="M123 502h-64v36l64 12v76q0 101 42.5 146.5t139.5 45.5q24 0 45.5 -1.5t44.5 -3.5v-50q-21 2 -42 3.5t-42 1.5q-70 0 -100.5 -31.5t-30.5 -108.5v-78h281v76q0 101 40.5 146.5t132.5 45.5q21 0 39.5 -1.5t38.5 -3.5v-50q-19 2 -36.5 3.5t-36.5 1.5q-65 0 -93 -31.5
t-28 -108.5v-78h338v-590q0 -93 -37 -133.5t-126 -40.5q-19 0 -42.5 1t-40.5 3v51q16 -2 38.5 -3t38.5 -1q59 0 86 27.5t27 95.5v542h-282v-502h-57v502h-281v-502h-57v502zM796 782h65v-69h-65v69z" />
    <glyph glyph-name="f_f_k" unicode="ffk" horiz-adv-x="1360" 
d="M123 502h-63l-1 36l64 12v76q0 101 42.5 146.5t139.5 45.5q24 0 45.5 -1.5t44.5 -3.5v-50q-21 2 -42 3.5t-42 1.5q-70 0 -100.5 -31.5t-30.5 -108.5v-78h282v76q0 50 10 86.5t31.5 60t57 34.5t86.5 11q23 0 44 -1.5t44 -3.5v-50q-22 2 -42.5 3.5t-41.5 1.5
q-73 0 -103 -31.5t-30 -108.5v-78h194v-48h-194v-502h-56v502h-282v-502h-57v502zM812 802h56v-472l346 220h86l-366 -232l387 -318h-83l-370 309v-309h-56v802z" />
    <glyph glyph-name=".notdef" horiz-adv-x="615" 
d="M0 802h615v-1000h-615v1000zM339 195l15 85q105 2 149.5 41t44.5 115q0 38 -15 68t-48.5 50t-88 30.5t-132.5 10.5q-23 0 -48.5 -1.5t-51 -4t-49 -6t-43.5 -7.5v-124q48 9 96.5 14.5t93.5 5.5q42 0 67.5 -2t39 -7.5t17.5 -14.5t4 -23q0 -12 -2.5 -21t-11.5 -14
t-25.5 -7.5t-44.5 -2.5h-114v-70l21 -115h126zM357 0v153h-162v-153h162z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni000D" horiz-adv-x="338" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="300" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="380" 
d="M160 632v170h59v-170l-6 -394h-46zM151 75h78v-75h-78v75z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="577" 
d="M159 802h63l-7 -267h-49zM355 802h63l-7 -267h-49z" />
    <glyph glyph-name="numbersign" unicode="#" 
d="M162 240h-129l17 52h130l79 218h-129l17 52h130l86 240h54l-86 -240h213l86 240h54l-86 -240h129l-17 -52h-130l-79 -218h129l-17 -52h-130l-86 -240h-54l86 240h-214l-86 -240h-53zM447 292l79 218h-214l-78 -218h213z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="726" 
d="M89 124q45 -13 109.5 -25t139.5 -14v302q-76 11 -124 25t-75.5 35.5t-37.5 54t-10 80.5q0 45 12.5 79t41 57t76 35.5t117.5 14.5v92h51v-92q69 -2 122 -11.5t96 -22.5v-54q-43 13 -97.5 22.5t-120.5 11.5v-277q78 -12 127.5 -27t78 -38.5t39 -58t10.5 -85.5t-14.5 -87
t-45.5 -59t-79 -35t-116 -14v-93l-51 -1v94q-79 2 -141.5 12.5t-107.5 25.5v53zM149 587q0 -36 7 -59t27.5 -38.5t57.5 -26t97 -19.5v270q-55 -1 -91.5 -9t-58 -23t-30.5 -38.5t-9 -56.5zM586 227q0 38 -7.5 62.5t-29.5 41.5t-60 28t-100 21v-295q54 2 92 10.5t61 25
t33.5 43t10.5 63.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="889" 
d="M211 481q-88 0 -130.5 39t-42.5 129q0 91 42.5 130t130.5 39t131 -39t43 -130q0 -90 -43 -129t-131 -39zM209 0h-59l532 802h59zM211 521q64 0 93 29.5t29 98.5t-29 99t-93 30q-63 0 -92 -30t-29 -99t29 -98.5t92 -29.5zM678 -16q-87 0 -130.5 39t-43.5 129t43.5 129.5
t130.5 39.5t130 -39.5t43 -129.5t-43 -129t-130 -39zM678 24q63 0 92.5 29.5t29.5 98.5t-29.5 98.5t-92.5 29.5q-64 0 -93 -29.5t-29 -98.5t29 -98.5t93 -29.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="791" 
d="M737 37l-39 -36l-478 384q-34 -10 -55 -25.5t-33 -36.5t-16.5 -47.5t-4.5 -59.5q0 -40 11 -73q13 -33 41.5 -51.5t63 -27.5t71.5 -11t67 -2q23 0 48 1t50 5t48.5 11t43.5 19l44 -37q-24 -17 -53.5 -27t-60 -15t-61.5 -6.5t-60 -1.5q-60 0 -116 7t-99 29.5t-69 64
t-26 111.5q0 30 4.5 59t16 54.5t30.5 46t48 32.5q8 3 17 4.5t17 3.5q-16 13 -32 25.5t-30.5 26t-26.5 30t-18 36.5q-5 17 -8 35.5t-3 41.5q0 33 7 61t26 55q21 26 49 41t59 23.5t63.5 11t63.5 3.5q29 -1 60 -4.5t60.5 -12t55.5 -23.5t45 -39q19 -27 26 -55t7 -61
q0 -25 -2 -43t-8.5 -33t-18 -28t-30.5 -28q-15 -11 -33.5 -22t-43.5 -21t-57.5 -19t-75.5 -16l-36 31q33 6 72 15t74 23.5t61 33.5t33 44q3 13 5 26.5t2 30.5q0 33 -10.5 63.5t-39.5 49.5q-14 10 -32 17t-41 12q-17 3 -32.5 5t-40.5 3q-26 -1 -47.5 -3t-39.5 -5
q-23 -5 -41 -12t-32 -17q-29 -19 -39.5 -49.5t-10.5 -63.5q0 -29 8.5 -56t30.5 -48q11 -11 24 -20.5t25 -19.5l390 -314q2 4 11 24.5t17.5 54t11 63.5t2.5 45q0 34 -6 72h50q7 -34 7 -74v-10q-1 -40 -11 -94t-38 -117z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="380" 
d="M159 802h63l-7 -267h-49z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="358" 
d="M269 -146q-87 88 -132 206.5t-45 267.5q0 152 47 270.5t130 203.5h63q-87 -82 -134 -203.5t-47 -270.5q0 -148 46.5 -268.5t134.5 -205.5h-63z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="358" 
d="M89 802q87 -88 132 -206.5t45 -267.5q0 -152 -47 -270.5t-130 -203.5h-63q87 82 134 203.5t47 270.5q0 148 -46.5 268.5t-134.5 205.5h63z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="404" 
d="M295 440l-93 141l-93 -141l-40 29l106 132l-163 45l15 47l158 -60l-8 169h50l-9 -169l159 60l15 -47l-163 -45l106 -132z" />
    <glyph glyph-name="plus" unicode="+" 
d="M114 300h241v250h50v-250h242v-49h-242v-251h-50v251h-241v49z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="380" 
d="M201 85h63l-94 -269h-53z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="380" 
d="M37 250v52h306v-52h-306z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="380" 
d="M151 75h78v-75h-78v75z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="365" 
d="M297 802h58l-288 -948h-57z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="726" 
d="M363 -16q-82 0 -141 23.5t-96 73.5t-54.5 129t-17.5 191t17.5 191t54.5 129.5t96 73.5t141 23q83 0 141.5 -23t95.5 -73.5t54.5 -129.5t17.5 -191t-17.5 -191t-54.5 -129t-95.5 -73.5t-141.5 -23.5zM363 40q68 0 115 19.5t76.5 62.5t43 111.5t13.5 167.5t-13.5 167.5
t-43 111.5t-76.5 62.5t-115 19.5q-67 0 -114.5 -19.5t-77 -62.5t-43 -111.5t-13.5 -167.5t13.5 -167.5t43 -111.5t77 -62.5t114.5 -19.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="547" 
d="M347 802h52v-802h-58v741l-281 -130l-24 48z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="726" 
d="M343 818q84 0 140 -12t89 -36t47 -60t14 -85q0 -41 -7.5 -69.5t-26 -52t-49.5 -45t-77 -48.5l-230 -135q-33 -19 -52.5 -33.5t-30 -30.5t-14 -35.5t-3.5 -48.5v-74h504v-53h-562v127q0 38 5.5 65t20 48.5t39 41t63.5 41.5l233 138q38 23 63 39t39 32.5t19.5 37t5.5 52.5
q0 36 -9.5 62.5t-35 44t-71 26t-117.5 8.5q-65 0 -124.5 -7t-107.5 -18v55q48 11 103.5 18t131.5 7z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="726" 
d="M80 59q52 -11 113.5 -18t134.5 -7q77 0 127.5 11t80 33t41 54t11.5 74q0 51 -12.5 84.5t-40.5 53t-71.5 27.5t-105.5 8h-178v53h178q57 0 97.5 7t66 25.5t37.5 50.5t12 82q0 43 -10.5 75t-36.5 53.5t-70.5 32t-113.5 10.5q-71 0 -130 -6.5t-109 -18.5v50q48 11 105.5 18
t134.5 7q81 0 137 -13t90 -40t49 -67.5t15 -96.5q0 -92 -35.5 -137t-98.5 -59q72 -13 111.5 -59t39.5 -143q0 -55 -16.5 -96t-54 -68.5t-98.5 -41t-150 -13.5q-80 0 -140 7t-110 18v50z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="726" 
d="M618 456v-456h-58v195h-521v47l315 560h62l-311 -554h455v208h58z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="726" 
d="M78 64q54 -12 116.5 -18.5t137.5 -6.5q72 0 119.5 11.5t75.5 34.5t39.5 57.5t11.5 79.5q0 49 -12.5 83t-40 55.5t-71 31t-105.5 9.5h-233v401h490v-53h-432v-295h182q75 0 129 -13t88 -41t50 -72t16 -106q0 -61 -16 -105.5t-52 -74t-94.5 -44t-143.5 -14.5
q-81 0 -142.5 7t-112.5 18v55z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="726" 
d="M377 -16q-89 0 -148.5 21t-95 69.5t-50.5 128.5t-15 198q0 113 18.5 192.5t60 129.5t107.5 72.5t161 22.5q60 0 102.5 -7t80.5 -18v-55q-40 11 -83 18t-99 7q-81 0 -135.5 -18.5t-88.5 -59.5t-48.5 -107t-14.5 -161q30 11 59 20.5t60.5 16.5t67 11t78.5 4
q140 0 201.5 -57t61.5 -183q0 -69 -19.5 -116t-56 -75.5t-88 -41t-116.5 -12.5zM385 416q-41 0 -74.5 -3.5t-63.5 -10t-58.5 -15t-59.5 -19.5q2 -96 14 -159.5t40.5 -101.5t76 -54t118.5 -16q51 0 91.5 9t69 31t43.5 58.5t15 91.5q0 51 -10 87t-34.5 58.5t-65 33
t-102.5 10.5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="726" 
d="M64 802h598v-46l-438 -756h-70l442 749h-532v53z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="726" 
d="M363 -16q-84 0 -142 13.5t-93.5 41t-51.5 69t-16 97.5q0 95 40.5 140.5t114.5 59.5q-64 15 -101 59.5t-37 134.5q0 55 15 96t49 68.5t88.5 41t133.5 13.5t133.5 -13.5t88.5 -41t49 -68.5t15 -96q0 -90 -36.5 -134.5t-101.5 -59.5q74 -14 114.5 -59.5t40.5 -140.5
q0 -56 -16 -97.5t-51.5 -69t-93.5 -41t-142 -13.5zM363 432q61 0 104 7t70 25.5t39 49.5t12 79q0 42 -10.5 74t-36 53t-69 32t-109.5 11t-109.5 -11t-69 -32t-36 -53t-10.5 -74q0 -48 12 -79t39 -49.5t70 -25.5t104 -7zM363 40q71 0 118 10.5t74.5 32t38.5 53.5t11 74
q0 50 -13.5 82.5t-42.5 51.5t-75 26.5t-111 7.5q-64 0 -110 -7.5t-75.5 -26.5t-43 -51.5t-13.5 -82.5q0 -42 11 -74t39 -53.5t74.5 -32t117.5 -10.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="726" 
d="M349 818q89 0 148.5 -21t95.5 -69.5t51 -128.5t15 -198q0 -113 -19 -192.5t-60.5 -129.5t-107.5 -72.5t-161 -22.5q-60 0 -102.5 7t-80.5 18v55q41 -11 83.5 -18t98.5 -7q81 0 136 18.5t89 59.5t48.5 107t14.5 161q-30 -11 -58.5 -20.5t-60 -16.5t-67.5 -11t-79 -4
q-141 0 -202.5 57t-61.5 183q0 69 19.5 116t56 75.5t88.5 41t116 12.5zM130 575q0 -51 10 -87t34 -58.5t65 -33t103 -10.5q41 0 74.5 3.5t63.5 10.5t58.5 16t59.5 19q-1 95 -13.5 158.5t-41 101.5t-76 54t-119.5 16q-51 0 -91.5 -9t-68.5 -31t-43 -58.5t-15 -91.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="380" 
d="M151 549h78v-75h-78v75zM151 75h78v-75h-78v75z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="380" 
d="M155 544h78v-75h-78v75zM181 85h63l-94 -269h-53z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M114 309l533 210v-53l-482 -190l482 -190v-53l-533 210v66z" />
    <glyph glyph-name="equal" unicode="=" 
d="M114 457h533v-50h-533v50zM114 146h533v-50h-533v50z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M114 86l482 190l-482 190v53l533 -210v-66l-533 -210v53z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="632" 
d="M201 376v42h101q59 0 99.5 11.5t66 33.5t36.5 55.5t11 76.5q0 41 -10 72.5t-35.5 52.5t-69 32t-111.5 11t-130 -7t-113 -19v54q50 11 109 18t134 7q80 0 134.5 -14t88 -42t48 -68.5t14.5 -93.5q0 -113 -62 -170t-202 -57h-51l-5 -133h-47zM192 75h78v-75h-78v75z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="980" 
d="M461 68q-83 0 -128 30.5t-45 104.5q0 67 39.5 94t117.5 27h192v40q0 27 -7 46.5t-23.5 32.5t-44.5 19t-70 6q-48 0 -89 -4t-81 -10v48q21 4 40 6.5t39 4t42.5 2t50.5 0.5q56 0 93.5 -8.5t60.5 -26.5t33 -45.5t10 -66.5v-212q0 -25 2.5 -46t11 -35t24 -22t40.5 -8
q26 0 41 8t22.5 22t10 35t2.5 46v132q0 100 -21.5 170t-65.5 114.5t-110 64.5t-156 20t-157 -20t-111 -64.5t-65.5 -114.5t-21.5 -170t22 -170.5t66 -114t110.5 -63.5t156.5 -20q17 0 40 1t39 3v-48q-16 -3 -39 -4t-40 -1q-104 0 -181 23t-127.5 73.5t-75 129.5t-24.5 191
q0 111 25 190.5t76 129.5t127.5 73.5t179.5 23.5q102 0 178 -23.5t126 -73.5t75 -129.5t25 -190.5v-131q0 -37 -6 -66t-20.5 -48.5t-39 -29.5t-62.5 -10q-63 0 -91.5 27t-36.5 76q-32 -14 -78 -26t-101 -12zM473 112q50 0 92 10.5t72 23.5v136h-178q-63 0 -89.5 -17.5
t-26.5 -62.5q0 -48 31 -69t99 -21z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="819" 
d="M355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436h368z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="834" 
d="M130 802h354q64 0 109.5 -10.5t75 -34.5t43.5 -63t14 -98q0 -46 -8 -79t-24 -56t-38 -36.5t-51 -19.5q68 -11 106.5 -55.5t38.5 -139.5q0 -61 -15 -101t-47 -64t-81 -34.5t-118 -10.5h-359v802zM190 747v-315h300q47 0 80 7t54 25t30.5 48.5t9.5 77.5t-10 77t-32 48
t-56.5 25t-83.5 7h-292zM190 377v-322h299q54 0 91.5 7.5t61.5 25.5t35 49t11 79t-11 79t-34.5 49.5t-60 25.5t-88.5 7h-304z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="868" 
d="M449 -16q-93 0 -161.5 23.5t-114.5 73.5t-68.5 129t-22.5 191q0 111 22.5 190.5t68.5 129.5t114.5 73.5t161.5 23.5q143 0 228.5 -56t119.5 -182h-64q-29 97 -99 139t-185 42q-79 0 -137 -19.5t-95 -63t-55 -111.5t-18 -166q0 -97 18 -165.5t55 -112t95 -63t137 -19.5
q116 0 185.5 42.5t98.5 139.5h64q-33 -126 -119 -182.5t-229 -56.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="896" 
d="M130 802h313q95 0 164.5 -22.5t116 -71t69 -124.5t22.5 -183q0 -108 -22.5 -184t-69 -124t-116 -70.5t-164.5 -22.5h-313v802zM190 747v-692h253q81 0 139.5 19t96 60.5t55.5 107t18 159.5q0 93 -18 159t-55.5 107.5t-96 60.5t-139.5 19h-253z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="764" 
d="M130 802h542v-55h-482v-316h451v-55h-451v-321h482v-55h-542v802z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="726" 
d="M130 802h532v-55h-472v-321h441v-55h-441v-371h-60v802z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="896" 
d="M463 -16q-98 0 -169.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 22.5 190.5t68.5 129.5t116 73.5t164 23.5q146 0 232 -56t119 -182h-63q-29 97 -99.5 139t-188.5 42q-81 0 -139 -19.5t-96 -63t-56 -111.5t-18 -166q0 -97 18 -165.5t56.5 -112t99 -63t145.5 -19.5
q75 0 130 14t91 45t53 79.5t17 117.5v53h-287v55h347v-110q0 -160 -84.5 -235.5t-266.5 -75.5z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="909" 
d="M130 802h60v-370h529v370h60v-802h-60v377h-529v-377h-60v802z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="321" 
d="M130 802h60v-802h-60v802z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="381" 
d="M58 -14q-21 0 -40 4v53q11 -2 18.5 -2h16.5q42 0 68.5 8.5t42.5 26.5t22 46t6 66v614h60v-614q0 -51 -9 -89t-31.5 -63t-60 -37.5t-93.5 -12.5z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="761" 
d="M130 802h59v-361l450 361h81l-468 -376l474 -426h-83l-454 411v-411h-59v802z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="684" 
d="M130 802h60v-747h455v-55h-515v802z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1037" 
d="M129 802h90l291 -544h17l291 544h90v-802h-60v728l-287 -534h-85l-287 534v-728h-60v802z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="920" 
d="M129 802h80l522 -728v728h60v-802h-81l-521 725v-725h-60v802z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="924" 
d="M462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q96 0 167.5 -23.5t119 -73.5t70.5 -129.5t23 -190.5q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5zM462 41q83 0 143 19.5t98.5 62.5t57 111.5
t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="815" 
d="M130 802h337q78 0 134 -13t91.5 -44t52.5 -82t17 -126q0 -77 -17 -128.5t-52.5 -82.5t-91.5 -44t-134 -13h-277v-269h-60v802zM190 747v-423h277q62 0 106 9.5t72 33.5t41 65t13 104q0 62 -13 103t-41 65t-72 33.5t-106 9.5h-277z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="924" 
d="M462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q96 0 167.5 -23.5t119 -73.5t70.5 -129.5t23 -190.5q0 -120 -26 -201.5t-80 -131.5l80 -101l-44 -35l-82 102q-87 -50 -228 -50zM462 41q120 0 190 41l-118 147
l45 36l118 -148q42 43 62 112.5t20 171.5q0 98 -18.5 166t-57 111.5t-98.5 63t-143 19.5t-143 -19.5t-99 -63t-57.5 -111.5t-18.5 -166q0 -97 18.5 -165.5t57.5 -112t99 -63t143 -19.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="845" 
d="M130 802h337q78 0 134 -13t91.5 -43t52.5 -79t17 -122q0 -60 -11 -104t-34.5 -74.5t-59.5 -48t-86 -25.5l180 -293h-72l-172 287q-10 -1 -19 -1h-298v-286h-60v802zM190 747v-406h277q62 0 106 9t72 32t41 62.5t13 100.5q0 60 -13 98.5t-41 61.5t-72 32.5t-106 9.5h-277z
" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="804" 
d="M112 83q51 -15 123.5 -28t158.5 -13q72 0 121 8.5t79 27.5t43 49.5t13 75.5t-9.5 74t-36.5 48.5t-75.5 32t-126.5 24.5q-93 13 -151.5 28t-91.5 40t-45 62t-12 94q0 54 15.5 94t51 66t94 38.5t144.5 12.5q87 0 149 -10t113 -25v-59q-51 15 -116.5 25.5t-144.5 10.5
q-73 0 -120 -8.5t-74.5 -27t-38.5 -47t-11 -68.5q0 -41 9 -68t35.5 -45.5t74.5 -30.5t127 -23q93 -12 151.5 -28.5t92 -43t46 -66t12.5 -98.5q0 -61 -18 -103.5t-56.5 -68.5t-99 -37.5t-144.5 -11.5q-92 0 -161.5 11.5t-120.5 27.5v60z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="764" 
d="M352 747h-314v55h688v-55h-314v-747h-60v747z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="886" 
d="M443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132v527h60v-527q0 -124 62.5 -178.5t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h60v-527q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="821" 
d="M36 802h63l300 -749h25l295 749h66l-319 -802h-109z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1276" 
d="M46 802h63l222 -749h18l231 749h115l231 -749h18l223 749h63l-237 -802h-113l-233 749h-18l-233 -749h-113z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="831" 
d="M374 411l-305 391h74l273 -351l271 351h73l-303 -391l320 -411h-75l-287 370l-287 -370h-74z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="751" 
d="M345 316l-327 486h72l286 -427l285 427h71l-327 -486v-316h-60v316z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="754" 
d="M40 55l562 691h-526v56h602v-55l-562 -691h598v-56h-674v55z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="358" 
d="M99 802h236v-49h-181v-850h181v-49h-236v948z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="365" 
d="M10 802h58l287 -948h-58z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="358" 
d="M259 -146h-236v49h181v850h-181v49h236v-948z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M347 802h66l245 -510h-54l-224 464l-224 -464h-54z" />
    <glyph glyph-name="underscore" unicode="_" 
d="M0 -198v55h760v-55h-760z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="600" 
d="M198 802h68l136 -170h-55z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="719" 
d="M301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379h-41l-13 40
q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="735" 
d="M109 802h57v-288q48 21 107 36.5t118 15.5q66 0 116.5 -16.5t85 -51.5t52.5 -90.5t18 -132.5t-18 -132t-52.5 -90.5t-85 -52t-116.5 -16.5q-60 0 -120 16.5t-109 37.5l-11 -38h-42v802zM381 514q-63 0 -117.5 -15.5t-97.5 -35.5v-375q43 -20 97.5 -36t117.5 -16
q57 0 98.5 13t69.5 41.5t41.5 74.5t13.5 111q0 129 -55 183.5t-168 54.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="630" 
d="M364 -16q-76 0 -131 16.5t-91 51.5t-53.5 90.5t-17.5 133.5t17.5 133t53.5 90t91 51t131 16q60 0 110.5 -7.5t88.5 -16.5v-50q-38 8 -87.5 15t-109.5 7q-64 0 -109 -13.5t-73 -42t-40.5 -73.5t-12.5 -109t12.5 -109t40.5 -74t73 -42.5t109 -13.5q62 0 114.5 6t92.5 16
v-50q-41 -11 -93.5 -18t-115.5 -7z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="735" 
d="M344 -16q-66 0 -116.5 16.5t-85.5 51.5t-53 90.5t-18 133.5q0 154 71 222t202 68q59 0 118 -16t107 -36v288h57v-802h-41l-12 38q-49 -21 -109 -37.5t-120 -16.5zM131 276q0 -65 13.5 -111t41.5 -74.5t69.5 -41.5t98.5 -13q63 0 117.5 15.5t97.5 35.5v376q-42 20 -97 36
t-118 16q-113 0 -168 -55t-55 -184z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="687" 
d="M376 -16q-85 0 -143.5 16.5t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7t100 16v-50q-45 -11 -97.5 -18t-121.5 -7zM353 516
q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h431q0 55 -11.5 93t-36.5 62t-65 35t-97 11z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="387" 
d="M126 502h-63v36l63 12v76q0 50 10.5 86.5t33.5 60t61 34.5t92 11q26 0 49.5 -1.5t48.5 -3.5v-50q-23 2 -47 3.5t-47 1.5q-79 0 -111.5 -31.5t-32.5 -108.5v-78h214v-48h-214v-502h-57v502z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="733" 
d="M344 -12q-131 0 -202 68t-71 222q0 153 71 220.5t202 67.5q60 0 120 -16.5t108 -37.5l11 38h41v-526q0 -63 -15 -106.5t-47 -71t-83 -40t-122 -12.5q-33 0 -61.5 1t-55 3.5t-53 7t-57.5 10.5v51q33 -6 60.5 -10t54 -7t53 -4t57.5 -1q61 0 102 9.5t65.5 30.5t35 55
t10.5 82v19q-48 -21 -106.5 -37t-117.5 -16zM131 278q0 -128 55 -182.5t168 -54.5q63 0 117 15t97 35v372q-42 20 -96.5 35.5t-117.5 15.5q-113 0 -168 -54t-55 -182z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="756" 
d="M109 802h57v-291q51 23 116.5 39t134.5 16q118 0 176 -51t58 -133v-382h-57v367q0 69 -43 107.5t-141 38.5q-73 0 -134.5 -17t-109.5 -38v-458h-57v802z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="275" 
d="M105 710h65v-69h-65v69zM109 550h57v-550h-57v550z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="275" 
d="M105 782h65v-69h-65v69zM-81 -159q16 -2 38.5 -3t39.5 -1q59 0 85.5 27.5t26.5 95.5v590h57v-590q0 -93 -37.5 -133.5t-126.5 -40.5q-19 0 -42 1t-41 3v51z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="660" 
d="M109 802h57v-471l345 219h86l-366 -232l387 -318h-83l-369 308v-308h-57v802z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="275" 
d="M109 802h57v-802h-57v802z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1199" 
d="M109 550h42l12 -40q50 23 113 39.5t130 16.5q68 0 114.5 -18.5t72.5 -51.5q56 28 128 49t149 21q113 0 168 -51t55 -133v-382h-56v367q0 69 -40.5 107.5t-132.5 38.5q-74 0 -135 -17t-112 -42q12 -33 12 -72v-382h-56v367q0 69 -40.5 107.5t-132.5 38.5q-70 0 -129 -16
t-105 -38v-459h-57v550z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="756" 
d="M163 510q51 23 117.5 39.5t136.5 16.5q118 0 176 -51t58 -133v-382h-57v367q0 69 -43 107.5t-141 38.5q-73 0 -134.5 -16t-109.5 -39v-458h-57v550h42z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="726" 
d="M363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5t218 -67.5t73 -222.5q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5t176 -56.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="735" 
d="M109 550h42l11 -38q49 21 109 37.5t120 16.5q66 0 116.5 -16.5t85 -51.5t52.5 -90t18 -133q0 -154 -70.5 -222.5t-201.5 -68.5q-59 0 -118 16t-107 36v-234h-57v748zM381 514q-63 0 -117.5 -15.5t-97.5 -35.5v-375q43 -20 97.5 -36t117.5 -16q57 0 98.5 13t69.5 42
t41.5 74.5t13.5 110.5q0 129 -55 183.5t-168 54.5z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="735" 
d="M344 -16q-66 0 -116.5 16.5t-85.5 52t-53 90.5t-18 133q0 77 18 132t53 90t85.5 51.5t116.5 16.5q60 0 120 -16.5t109 -37.5l12 38h41v-748h-57v234q-48 -20 -107 -36t-118 -16zM131 276q0 -65 13.5 -110.5t41.5 -74.5t69.5 -42t98.5 -13q63 0 118 16t97 36v375
q-42 20 -97 35.5t-118 15.5q-113 0 -168 -54.5t-55 -183.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="407" 
d="M109 550h42l12 -40q42 21 94.5 34.5t111.5 13.5v-54q-60 -1 -110.5 -13t-92.5 -29v-462h-57v550z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="644" 
d="M81 61q44 -9 105.5 -18t141.5 -9q52 0 87.5 4t58 15.5t32.5 31t10 50.5t-7 51t-27.5 33.5t-57.5 22t-98 15.5q-74 8 -120 19t-72 29t-35.5 44.5t-9.5 66.5q0 42 12 70.5t40 46.5t75 25.5t116 7.5q65 0 120 -7.5t100 -18.5v-49q-44 10 -95.5 17.5t-122.5 7.5
q-56 0 -92.5 -4.5t-58 -15.5t-30 -30t-8.5 -48q0 -27 6.5 -44.5t27 -29.5t57 -20t96.5 -15q74 -8 121 -20t73.5 -31t36 -48t9.5 -72q0 -45 -13.5 -74.5t-42.5 -47t-75 -25t-111 -7.5q-74 0 -139 8t-110 21v48z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="455" 
d="M318 -11q-55 0 -93 10t-60.5 31t-32.5 54t-10 78v340h-64v36l64 12v129h56v-129h215v-48h-215v-334q0 -37 7.5 -61.5t24.5 -39t44.5 -20.5t67.5 -6q39 0 78 6v-52q-40 -6 -82 -6z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="756" 
d="M339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367q0 -69 43 -107.5t141 -38.5q73 0 134.5 17t109.5 38v458h57v-550h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="681" 
d="M34 550h63l244 -506l244 506h63l-264 -550h-88z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1027" 
d="M36 550h61l177 -508l196 508h89l195 -507l174 508l64 -1l-193 -550h-88l-197 506l-197 -506h-88zM514 512l2 1h-4zM752 36l5 1h-1zM276 36l-2 1l-2 -1h4z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="659" 
d="M292 284l-237 266h70l205 -229l204 229h69l-235 -266l251 -284h-70l-219 247l-222 -247h-69z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="678" 
d="M61 -163q15 -1 29.5 -1h30.5q66 0 102.5 21.5t61.5 72.5l35 73h-28l-258 547h59l236 -498h15l241 498h59l-305 -629q-18 -37 -37.5 -63t-44 -42t-56 -23t-72.5 -7q-18 0 -34.5 0.5t-33.5 2.5v48z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="657" 
d="M71 47l425 455h-412v48h488v-47l-426 -455h439v-48h-514v47z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="358" 
d="M335 -144h-35q-92 0 -131.5 33t-39.5 112v188q0 32 -4 54t-15.5 35.5t-32 19.5t-52.5 6h-23v49h23q32 0 52.5 6t32 19.5t15.5 36t4 54.5v188q0 78 39.5 111.5t131.5 33.5h35v-49h-26q-72 0 -98 -23t-26 -77v-181q0 -64 -18.5 -97.5t-59.5 -45.5q41 -12 59.5 -45.5
t18.5 -97.5v-181q0 -54 26 -77.5t98 -23.5h26v-48z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="321" 
d="M133 802h55v-948h-55v948z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="358" 
d="M228 1q0 -79 -39.5 -112t-131.5 -33h-34v48h24q36 0 60.5 5.5t39 18t20.5 31.5t6 46v181q0 64 18.5 97.5t58.5 45.5q-40 12 -58.5 45.5t-18.5 97.5v181q0 54 -26.5 77t-98.5 23h-25v49h34q92 0 131.5 -33.5t39.5 -111.5v-188q0 -32 4 -54.5t15.5 -36t32 -19.5t53.5 -6h23
v-49h-23q-33 0 -53.5 -6t-32 -19.5t-15.5 -35.5t-4 -54v-188z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M72 217q0 99 37 143.5t110 44.5q35 0 64.5 -13.5t56 -34t50.5 -44.5t47.5 -44.5t48.5 -34t54 -13.5q52 0 75.5 34t23.5 109v33h48v-37q0 -188 -144 -188q-36 0 -65.5 13.5t-56 34t-50 44.5t-47.5 44.5t-49 34t-53 13.5q-54 0 -78 -32.5t-24 -110.5v-33h-48v37z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="300" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="380" 
d="M229 550v-75h-78v75h78zM213 312l6 -394v-170h-59v170l7 394h46z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="726" 
d="M418 802v-108q64 -2 118 -8t96 -17v-53q-42 10 -96.5 15t-117.5 7v-471q66 1 122.5 6t101.5 16v-53q-45 -11 -101 -17t-123 -8v-111h-50v111q-77 2 -133 20t-92 53t-53.5 89.5t-17.5 129.5t17.5 129t53.5 89.5t92 53t133 19.5v108h50zM133 403q0 -60 12.5 -104
t40.5 -72.5t73 -43t109 -16.5v471q-64 -2 -109 -16.5t-73 -43t-40.5 -72t-12.5 -103.5z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="726" 
d="M115 51q62 0 84.5 26.5t22.5 84.5v218h-161v48h161v185q0 51 12.5 89.5t42 64t77 38.5t117.5 13q64 0 111 -8t87 -17v-53q-41 9 -88 16.5t-104 7.5q-58 0 -96 -9.5t-60.5 -28.5t-32 -48t-9.5 -67v-183h347v-48h-347v-219q0 -78 -44 -110h458v-51h-632v51h54z" />
    <glyph glyph-name="currency" unicode="&#xa4;" 
d="M118 61l111 110q-30 42 -30 120q0 77 28 118l-109 109l34 33l108 -108q44 28 119 28q76 0 120 -28l109 108l34 -33l-109 -110q28 -41 28 -117t-30 -120l111 -111l-34 -34l-112 112q-22 -14 -51 -20.5t-66 -6.5q-36 0 -65 6.5t-51 20.5l-111 -111zM379 154q69 0 100 32
t31 105t-31 104.5t-100 31.5q-68 0 -99 -31.5t-31 -104.5q0 -74 31 -105.5t99 -31.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="726" 
d="M80 328h254v147h-254v48h212l-256 280h70l257 -279l258 278h69l-256 -279h211v-48h-253v-147h253v-49h-253v-279h-58v279h-254v49z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="321" 
d="M133 432v370h55v-370h-55zM133 224h55v-370h-55v370z" />
    <glyph glyph-name="section" unicode="&#xa7;" 
d="M121 -119q44 -14 107 -27t137 -13q58 0 98.5 7.5t65.5 25t36 45.5t11 69q0 39 -7.5 64.5t-29 43t-61 28.5t-103.5 20q-86 11 -140 24.5t-85 34.5t-42 52.5t-11 79.5q0 45 21 86.5t55 67.5q-26 23 -36 58t-10 87q0 48 13 82.5t43.5 57t81.5 33t126 10.5q71 0 121 -11
t94 -24v-56q-43 13 -96 24.5t-119 11.5q-61 0 -101 -7t-63.5 -23t-33 -40t-9.5 -59q0 -38 7 -63.5t28.5 -42.5t60.5 -27.5t103 -19.5q86 -12 140.5 -26t85.5 -35.5t42.5 -54t11.5 -80.5q0 -46 -21 -84t-55 -67q26 -23 36 -58t10 -87q0 -56 -15 -94.5t-47.5 -62.5
t-83.5 -34.5t-122 -10.5q-80 0 -139.5 12.5t-104.5 26.5v56zM383 198q60 -8 102 -18t71 -25q26 23 40 52t14 64q0 38 -9 63t-34.5 41.5t-71.5 28t-120 22.5q-60 8 -101.5 18t-70.5 25q-26 -24 -40 -56t-14 -67q0 -37 8.5 -60.5t33.5 -39.5t71 -26.5t121 -21.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="600" 
d="M162 754h56v-68h-56v68zM382 754h56v-68h-56v68z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="924" 
d="M462 -16q-101 0 -175.5 23.5t-123.5 73.5t-73 129.5t-24 190.5t24 190t73 129.5t123.5 74t175.5 23.5t175 -23.5t123.5 -74t73.5 -129.5t24 -190t-24 -190.5t-73.5 -129.5t-123.5 -73.5t-175 -23.5zM462 31q89 0 153.5 20t106.5 64.5t62.5 115t20.5 170.5
q0 101 -20.5 171t-62.5 114.5t-106.5 64.5t-153.5 20t-153.5 -20t-106.5 -64.5t-62.5 -114.5t-20.5 -171q0 -100 20.5 -170.5t62.5 -115t106.5 -64.5t153.5 -20zM462 143q-58 0 -101 14.5t-71.5 45.5t-42.5 80t-14 118q0 138 56.5 198t172.5 60q89 0 142.5 -35t74.5 -116
h-58q-18 54 -56.5 77.5t-102.5 23.5q-45 0 -77.5 -11.5t-53.5 -36.5t-31 -64.5t-10 -95.5q0 -112 41.5 -160t130.5 -48q65 0 103 23.5t56 77.5h59q-22 -80 -75 -115.5t-143 -35.5z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="416" 
d="M174 481q-63 0 -95 24t-32 81q0 51 29 72t92 21h143v27q0 19 -5 32.5t-17.5 22.5t-33.5 13t-54 4q-35 0 -67 -2.5t-64 -7.5v40q35 5 63 7.5t72 2.5t74 -6.5t48 -20.5t26 -34.5t8 -49.5v-217h-35l-10 22q-25 -12 -61 -21.5t-81 -9.5zM177 517q42 0 76 8t58 19v101h-136
q-42 0 -60 -13.5t-18 -47.5q0 -35 17.5 -51t62.5 -16z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="540" 
d="M218 509h62l-177 -232l177 -232h-62l-177 232zM437 509h62l-177 -232l177 -232h-62l-177 232z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M114 457h533v-361h-49v311h-484v50z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="462" 
d="M231 401q-101 0 -149.5 48.5t-48.5 160.5q0 111 49 159.5t149 48.5q101 0 149.5 -48.5t48.5 -159.5q0 -112 -48.5 -160.5t-149.5 -48.5zM231 430q43 0 74.5 9.5t51.5 30.5t30 55.5t10 84.5t-10 84t-30 55t-51.5 30.5t-74.5 9.5t-74.5 -9.5t-51.5 -30.5t-30 -55t-10 -84
t10 -84.5t30.5 -55.5t51.5 -30.5t74 -9.5zM142 732h104q49 0 70.5 -17.5t21.5 -63.5q0 -38 -13.5 -55.5t-42.5 -22.5l53 -86h-38l-49 83h-72v-83h-34v245zM176 702v-103h70q32 0 45 10.5t13 41.5q0 30 -13 40.5t-45 10.5h-70z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="600" 
d="M151 741h298v-41h-298v41z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="421" 
d="M211 481q-88 0 -130.5 39t-42.5 129q0 91 42.5 130t130.5 39t131 -39t43 -130q0 -90 -43 -129t-131 -39zM211 521q64 0 93 29.5t29 98.5t-29 99t-93 30q-63 0 -92 -30t-29 -99t29 -98.5t92 -29.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M114 369h241v181h50v-181h242v-49h-242v-182h-50v182h-241v49zM114 50h533v-50h-533v50z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="419" 
d="M199 1009q94 0 133.5 -27.5t39.5 -87.5q0 -24 -4.5 -41t-15.5 -30.5t-29 -25.5t-45 -28l-130 -74q-16 -10 -26 -17.5t-15.5 -15.5t-7 -18t-1.5 -25v-37h279v-42h-331v79q0 22 3.5 37.5t12 28t23.5 23.5t38 24l132 77q19 11 31.5 19t19.5 16.5t10 19.5t3 27q0 38 -25.5 56
t-97.5 18q-37 0 -72.5 -4.5t-63.5 -10.5v45q29 6 61.5 10t77.5 4z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="419" 
d="M43 589q32 -8 66.5 -11.5t77.5 -3.5q42 0 68.5 5.5t42.5 16t22 27.5t6 39q0 27 -6.5 44t-20.5 27.5t-36.5 14.5t-54.5 4h-109v42h109q59 0 84.5 16.5t25.5 68.5q0 23 -5.5 39t-20 27t-38.5 16t-61 5q-42 0 -75.5 -3.5t-63.5 -11.5v44q26 5 61 9.5t80 4.5
q97 0 136.5 -29.5t39.5 -94.5q0 -51 -19.5 -77t-54.5 -36q40 -8 61.5 -34.5t21.5 -80.5q0 -66 -43 -96t-148 -30q-47 0 -82.5 4t-63.5 10v44z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="600" 
d="M335 802h67l-149 -170h-54z" />
    <glyph glyph-name="uni00B5" unicode="&#xb5;" 
d="M116 550h56v-360q0 -73 43 -113.5t140 -40.5q70 0 128.5 15.5t104.5 37.5v461h56v-550h-41l-11 39q-48 -23 -109.5 -39t-126.5 -16q-68 0 -113.5 21t-70.5 59v-262h-56v748z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" 
d="M310 339h-1q-62 0 -107 11.5t-73.5 38.5t-42 71.5t-13.5 111.5q0 66 13.5 110t42.5 70.5t74.5 38t108.5 11.5h301v-948h-56v898h-191v-898h-56v485z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="380" 
d="M151 314h78v-75h-78v75z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="600" 
d="M287 1h41l-70 -70q7 1 13.5 1h14.5q62 0 85.5 -16.5t23.5 -55.5q0 -40 -24 -57t-88 -17q-49 0 -79 9v32q34 -8 78 -8q24 0 39.5 2t23.5 7t11 12.5t3 19.5q0 10 -3 18t-11 13.5t-23.5 8t-39.5 2.5q-20 0 -38.5 -1.5t-32.5 -3.5v29z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="320" 
d="M196 1000h49v-460h-51v409l-154 -73l-22 41z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="423" 
d="M211 481q-88 0 -130.5 39t-42.5 129q0 91 42.5 130t130.5 39t131 -39t43 -130q0 -90 -43 -129t-131 -39zM211 521q64 0 93 29.5t29 98.5t-29 99t-93 30q-63 0 -92 -30t-29 -99t29 -98.5t92 -29.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="540" 
d="M103 44h-62l177 232l-177 232h61l178 -232zM322 44h-62l177 232l-177 232h61l178 -232z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="921" 
d="M196 802h49v-460h-51v409l-154 -73l-22 41zM174 0h-59l533 802h59zM866 262v-262h-51v106h-293v40l176 314h55l-176 -312h238v114h51z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="921" 
d="M196 802h49v-460h-51v409l-154 -73l-22 41zM174 0h-59l533 802h59zM700 469q94 0 133.5 -27.5t39.5 -87.5q0 -24 -4.5 -41t-15.5 -30.5t-29 -25.5t-45 -28l-130 -74q-16 -10 -26 -17.5t-15.5 -15.5t-7 -18t-1.5 -25v-37h279v-42h-331v79q0 22 3.5 37.5t12 28t23.5 23.5
t38 24l132 77q19 11 31.5 19t19.5 16.5t10 19.5t3 27q0 38 -25.5 56t-97.5 18q-37 0 -72.5 -4.5t-63.5 -10.5v45q29 6 61.5 10t77.5 4z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1021" 
d="M43 391q32 -8 66.5 -11.5t77.5 -3.5q42 0 68.5 5.5t42.5 16t22 27.5t6 39q0 27 -6.5 44t-20.5 27.5t-36.5 14.5t-54.5 4h-109v42h109q59 0 84.5 16.5t25.5 68.5q0 23 -5.5 39t-20 27t-38.5 16t-61 5q-42 0 -75.5 -3.5t-63.5 -11.5v44q26 5 61 9.5t80 4.5
q97 0 136.5 -29.5t39.5 -94.5q0 -51 -19.5 -77t-54.5 -36q40 -8 61.5 -34.5t21.5 -80.5q0 -66 -43 -96t-148 -30q-47 0 -82.5 4t-63.5 10v44zM273 0h-59l533 802h59zM967 262v-262h-51v106h-293v40l176 314h55l-176 -312h238v114h51z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="632" 
d="M440 475h-78v75h78v-75zM431 174v-42h-101q-59 0 -99.5 -11.5t-66 -33.5t-36.5 -55.5t-11 -76.5q0 -41 10 -72.5t35.5 -52.5t69.5 -32t111 -11q68 0 130 7t113 19v-54q-50 -11 -109 -18t-134 -7q-80 0 -134.5 14t-88 42t-48 68.5t-14.5 93.5q0 113 62 170t202 57h51
l5 133h47z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="819" 
d="M259 987h68l115 -140h-57zM355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436h368z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="819" 
d="M494 987h68l-126 -140h-57zM355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436h368z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="819" 
d="M373 987h74l142 -140h-55l-124 113l-124 -113h-55zM355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436h368z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="819" 
d="M224 872q0 54 24 78t70 24q32 0 56 -12.5t44.5 -27.5t40 -27.5t42.5 -12.5q29 0 42 14.5t13 49.5v11h41v-13q0 -103 -94 -103q-32 0 -56 12.5t-44.5 28t-40 28t-43.5 12.5q-29 0 -41.5 -14.5t-12.5 -50.5v-11h-41v14zM355 802h107l322 -802h-63l-105 261h-412l-103 -261
h-65zM594 316l-174 436h-23l-171 -436h368z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="819" 
d="M269 949h57v-63h-57v63zM494 949h57v-63h-57v63zM355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436h368z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="819" 
d="M355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436h368z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1258" 
d="M624 261h-378l-176 -261h-71l539 802h628v-55h-482v-316h450v-55h-450v-321h482v-55h-542v261zM624 316v431h-50l-291 -431h341z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="868" 
d="M797 223q-31 -118 -108 -175t-205 -63l-114 -183h-56l115 182q-88 3 -153 27.5t-108 75t-64.5 128t-21.5 186.5q0 111 22.5 190.5t68.5 129.5t114.5 73.5t161.5 23.5q143 0 228.5 -56t119.5 -182h-64q-29 97 -99 139t-185 42q-79 0 -137 -19.5t-95 -63t-55 -111.5
t-18 -166q0 -97 18 -165.5t55 -112t95 -63t137 -19.5q116 0 185.5 42.5t98.5 139.5h64z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="764" 
d="M258 987h68l115 -140h-57zM130 802h542v-55h-482v-316h451v-55h-451v-321h482v-55h-542v802z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="764" 
d="M493 987h68l-126 -140h-57zM130 802h542v-55h-482v-316h451v-55h-451v-321h482v-55h-542v802z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="764" 
d="M372 987h74l142 -140h-55l-124 113l-124 -113h-55zM130 802h542v-55h-482v-316h451v-55h-451v-321h482v-55h-542v802z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="764" 
d="M268 949h57v-63h-57v63zM493 949h57v-63h-57v63zM130 802h542v-55h-482v-316h451v-55h-451v-321h482v-55h-542v802z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="321" 
d="M9 987h68l115 -140h-57zM130 802h60v-802h-60v802z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="321" 
d="M244 987h68l-126 -140h-57zM130 802h60v-802h-60v802z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="321" 
d="M123 987h74l142 -140h-55l-124 113l-124 -113h-55zM130 802h60v-802h-60v802z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="321" 
d="M19 949h57v-63h-57v63zM244 949h57v-63h-57v63zM130 802h60v-802h-60v802z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="896" 
d="M130 376h-130v55h130v371h313q95 0 164.5 -22.5t116 -71t69 -124.5t22.5 -183q0 -108 -22.5 -184t-69 -124t-116 -70.5t-164.5 -22.5h-313v376zM190 376v-321h253q81 0 139.5 19t96 60.5t55.5 107t18 159.5q0 93 -18 159t-55.5 107.5t-96 60.5t-139.5 19h-253v-316h238
v-55h-238z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="920" 
d="M274 872q0 54 24 78t70 24q32 0 56 -12.5t44.5 -27.5t40 -27.5t42.5 -12.5q29 0 42 14.5t13 49.5v11h41v-13q0 -103 -94 -103q-32 0 -56 12.5t-44.5 28t-40 28t-43.5 12.5q-29 0 -41.5 -14.5t-12.5 -50.5v-11h-41v14zM129 802h80l522 -728v728h60v-802h-81l-521 725v-725
h-60v802z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="924" 
d="M311 987h68l115 -140h-57zM462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q96 0 167.5 -23.5t119 -73.5t70.5 -129.5t23 -190.5q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5zM462 41q83 0 143 19.5
t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="924" 
d="M546 987h68l-126 -140h-57zM462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q96 0 167.5 -23.5t119 -73.5t70.5 -129.5t23 -190.5q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5zM462 41q83 0 143 19.5
t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="924" 
d="M425 987h74l142 -140h-55l-124 113l-124 -113h-55zM462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q96 0 167.5 -23.5t119 -73.5t70.5 -129.5t23 -190.5q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5z
M462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="924" 
d="M276 872q0 54 24 78t70 24q32 0 56 -12.5t44.5 -27.5t40 -27.5t42.5 -12.5q29 0 42 14.5t13 49.5v11h41v-13q0 -103 -94 -103q-32 0 -56 12.5t-44.5 28t-40 28t-43.5 12.5q-29 0 -41.5 -14.5t-12.5 -50.5v-11h-41v14zM462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129
t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q96 0 167.5 -23.5t119 -73.5t70.5 -129.5t23 -190.5q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5zM462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5
t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="924" 
d="M321 949h57v-63h-57v63zM546 949h57v-63h-57v63zM462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q96 0 167.5 -23.5t119 -73.5t70.5 -129.5t23 -190.5q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5z
M462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M126 56l219 219l-219 220l35 35l219 -220l220 220l35 -35l-220 -220l220 -220l-35 -35l-220 220l-219 -219z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="924" 
d="M76 15l80 89q-74 99 -74 297q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q88 0 154.5 -19t114.5 -61l75 80l39 -31l-79 -87q76 -100 76 -299q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5q-89 0 -155.5 19t-113.5 61l-75 -80zM144 401q0 -165 55 -251
l490 543q-39 35 -95 51.5t-132 16.5q-83 0 -143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5zM462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5q0 84 -13 145.5t-41 104.5l-491 -542q39 -35 95.5 -51.5t132.5 -16.5z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="886" 
d="M292 987h68l115 -140h-57zM443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132v527h60v-527q0 -124 62.5 -178.5t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h60v-527q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="886" 
d="M527 987h68l-126 -140h-57zM443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132v527h60v-527q0 -124 62.5 -178.5t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h60v-527q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="886" 
d="M406 987h74l142 -140h-55l-124 113l-124 -113h-55zM443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132v527h60v-527q0 -124 62.5 -178.5t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h60v-527q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="886" 
d="M302 949h57v-63h-57v63zM527 949h57v-63h-57v63zM443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132v527h60v-527q0 -124 62.5 -178.5t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h60v-527q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="751" 
d="M460 987h68l-126 -140h-57zM345 316l-327 486h72l286 -427l285 427h71l-327 -486v-316h-60v316z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="815" 
d="M190 802v-133h277q78 0 134 -13.5t91.5 -44t52.5 -81t17 -125.5q0 -76 -17 -127.5t-52.5 -82t-91.5 -43.5t-134 -13h-277v-139h-60v802h60zM190 614v-420h277q62 0 106 9.5t72 33t41 64.5t13 104q0 62 -13 102t-41 64t-72 33.5t-106 9.5h-277z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="733" 
d="M425 -3q-40 0 -67.5 2t-56.5 8v51q16 -2 29 -4t26 -3.5t27.5 -2t33.5 -0.5q40 1 74 8.5t58.5 27t38.5 54t14 88.5q0 47 -11.5 80t-36.5 53.5t-64.5 29.5t-94.5 9h-94v48h94q98 0 141 35t43 122q0 42 -11 73t-35.5 51.5t-64 30t-95.5 9.5q-112 0 -159.5 -39t-47.5 -122
v-606h-57v606q0 51 14 90.5t45 66.5t81.5 41t123.5 14q140 0 202 -51.5t62 -157.5q0 -85 -34.5 -129t-97.5 -58q74 -13 115 -60t41 -140q0 -64 -17 -107t-48 -69t-74.5 -37.5t-96.5 -11.5z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="719" 
d="M183 802h68l136 -170h-55zM301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379
h-41l-13 40q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="719" 
d="M467 794h67l-149 -170h-54zM301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379
h-41l-13 40q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="719" 
d="M326 802h66l133 -170h-53l-113 138l-113 -138h-53zM301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11
t79 -34t43 -58t13 -84v-379h-41l-13 40q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="719" 
d="M173 672q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-41.5 33t-46.5 15q-27 0 -39 -16t-12 -56v-20h-41v23zM301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5
t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379h-41l-13 40q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257
q-82 0 -117.5 -25.5t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="719" 
d="M221 754h56v-68h-56v68zM441 754h56v-68h-56v68zM301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34
t43 -58t13 -84v-379h-41l-13 40q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="719" 
d="M359 617q-63 0 -99.5 24.5t-36.5 82.5t36.5 82t99.5 24t99.5 -24t36.5 -82t-36.5 -82.5t-99.5 -24.5zM359 654q45 0 71 15.5t26 54.5q0 38 -26 53.5t-71 15.5q-46 0 -71 -15.5t-25 -53.5q0 -39 25 -54.5t71 -15.5zM301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5
t38.5 49.5t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379h-41l-13 40q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191
h-257q-82 0 -117.5 -25.5t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1172" 
d="M315 -16q-108 0 -167 40t-59 137q0 87 51 122t154 35h263v59q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54 8.5t51.5 5.5t55.5 3t65 1q113 0 169 -30.5t71 -92.5q29 63 88.5 93t155.5 30q68 0 118.5 -14t83.5 -45t49.5 -80.5t16.5 -120.5
v-34h-492q0 -64 11.5 -108.5t39 -73t75.5 -41.5t121 -13q63 0 117.5 7t99.5 16v-49q-45 -11 -97.5 -18.5t-121.5 -7.5q-97 0 -158.5 21t-95.5 66q-48 -34 -123.5 -60.5t-168.5 -26.5zM838 516q-55 0 -95.5 -11t-67 -35t-40.5 -61.5t-18 -90.5h435q-2 54 -14.5 91.5t-38 61
t-65.5 34.5t-96 11zM318 31q82 0 150.5 23t116.5 55q-15 32 -21.5 72.5t-6.5 90.5h-257q-82 0 -117.5 -25.5t-35.5 -88.5q0 -69 40.5 -98t130.5 -29z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="630" 
d="M573 9q-37 -10 -83.5 -16.5t-102.5 -8.5l-114 -182h-56l115 183q-135 7 -198 75t-63 216q0 78 17.5 133t53.5 90t91 51t131 16q60 0 110.5 -7.5t88.5 -16.5v-50q-38 8 -87.5 15t-109.5 7q-64 0 -109 -13.5t-73 -42t-40.5 -73.5t-12.5 -109t12.5 -109t40.5 -74t73 -42.5
t109 -13.5q62 0 114.5 6t92.5 16v-50z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="687" 
d="M182 802h68l136 -170h-55zM376 -16q-85 0 -143.5 16.5t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7t100 16v-50q-45 -11 -97.5 -18
t-121.5 -7zM353 516q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h431q0 55 -11.5 93t-36.5 62t-65 35t-97 11z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="687" 
d="M466 794h67l-149 -170h-54zM376 -16q-85 0 -143.5 16.5t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7t100 16v-50
q-45 -11 -97.5 -18t-121.5 -7zM353 516q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h431q0 55 -11.5 93t-36.5 62t-65 35t-97 11z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="687" 
d="M325 802h66l133 -170h-53l-113 138l-113 -138h-53zM376 -16q-85 0 -143.5 16.5t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7
t100 16v-50q-45 -11 -97.5 -18t-121.5 -7zM353 516q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h431q0 55 -11.5 93t-36.5 62t-65 35t-97 11z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="687" 
d="M220 754h56v-68h-56v68zM440 754h56v-68h-56v68zM376 -16q-85 0 -143.5 16.5t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7t100 16
v-50q-45 -11 -97.5 -18t-121.5 -7zM353 516q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h431q0 55 -11.5 93t-36.5 62t-65 35t-97 11z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="275" 
d="M-38 802h68l136 -170h-55zM109 550h57v-550h-57v550z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="275" 
d="M246 794h67l-149 -170h-54zM109 550h57v-550h-57v550z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="275" 
d="M105 802h66l133 -170h-53l-113 138l-113 -138h-53zM109 550h57v-550h-57v550z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="275" 
d="M0 754h56v-68h-56v68zM220 754h56v-68h-56v68zM109 550h57v-550h-57v550z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="717" 
d="M245 654l143 61q-60 43 -149 87h88q63 -31 111 -66l161 68l14 -38l-137 -58q48 -39 80.5 -82t52.5 -93t28.5 -107t8.5 -125q0 -87 -18 -147t-53 -98t-88 -55t-124 -17q-145 0 -218.5 68t-73.5 222q0 77 18 132.5t53 91t85.5 52t116.5 16.5q53 0 106.5 -13t100.5 -30
q-19 45 -49 85t-77 78l-165 -70zM586 301q0 47 -4 88t-13 79q-44 19 -98 33t-117 14q-113 0 -168 -55.5t-55 -185.5q0 -126 56 -182t176 -56q58 0 100 14.5t69.5 46t40.5 82t13 122.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="756" 
d="M192 672q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-41.5 33t-46.5 15q-27 0 -39 -16t-12 -56v-20h-41v23zM163 510q51 23 117.5 39.5t136.5 16.5q118 0 176 -51t58 -133v-382
h-57v367q0 69 -43 107.5t-141 38.5q-73 0 -134.5 -16t-109.5 -39v-458h-57v550h42z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="726" 
d="M187 802h68l136 -170h-55zM363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5t218 -67.5t73 -222.5q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5t176 -56.5z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="726" 
d="M471 794h67l-149 -170h-54zM363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5t218 -67.5t73 -222.5q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5t176 -56.5z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="726" 
d="M330 802h66l133 -170h-53l-113 138l-113 -138h-53zM363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5t218 -67.5t73 -222.5q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5t176 -56.5z
" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="726" 
d="M177 672q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-41.5 33t-46.5 15q-27 0 -39 -16t-12 -56v-20h-41v23zM363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5
t218 -67.5t73 -222.5q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5t176 -56.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="726" 
d="M225 754h56v-68h-56v68zM445 754h56v-68h-56v68zM363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5t218 -67.5t73 -222.5q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5t176 -56.5z
" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M346 508h68v-66h-68v66zM114 300h533v-49h-533v49zM346 109h68v-67h-68v67z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="726" 
d="M69 19l56 55q-54 71 -54 202q0 155 73.5 222.5t218.5 67.5q65 0 115 -12.5t86 -40.5l56 53l36 -35l-55 -55q53 -70 53 -200q0 -156 -73 -224t-218 -68q-130 0 -201 53l-56 -53zM131 276q0 -105 37 -161l356 356v1h-3l1 1q-54 41 -159 41q-120 0 -176 -55.5t-56 -182.5z
M363 36q120 0 176 56.5t56 183.5q0 104 -37 160l-357 -357l2 -1h1q54 -42 159 -42z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="756" 
d="M202 802h68l136 -170h-55zM339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367q0 -69 43 -107.5t141 -38.5q73 0 134.5 17t109.5 38v458h57v-550h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="756" 
d="M486 794h67l-149 -170h-54zM339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367q0 -69 43 -107.5t141 -38.5q73 0 134.5 17t109.5 38v458h57v-550h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="756" 
d="M345 802h66l133 -170h-53l-113 138l-113 -138h-53zM339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367q0 -69 43 -107.5t141 -38.5q73 0 134.5 17t109.5 38v458h57v-550h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="756" 
d="M240 754h56v-68h-56v68zM460 754h56v-68h-56v68zM339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367q0 -69 43 -107.5t141 -38.5q73 0 134.5 17t109.5 38v458h57v-550h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="678" 
d="M447 794h67l-149 -170h-54zM61 -163q15 -1 29.5 -1h30.5q66 0 102.5 21.5t61.5 72.5l35 73h-28l-258 547h59l236 -498h15l241 498h59l-305 -629q-18 -37 -37.5 -63t-44 -42t-56 -23t-72.5 -7q-18 0 -34.5 0.5t-33.5 2.5v48z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="735" 
d="M109 802h57v-288q48 20 107 36t118 16q66 0 116.5 -16.5t85 -51.5t52.5 -90t18 -133q0 -77 -18 -132t-52.5 -90.5t-85 -52t-116.5 -16.5q-59 0 -118 16.5t-107 36.5v-235h-57v1000zM381 513q-63 0 -118 -15.5t-97 -35.5v-373q42 -20 97 -36t118 -16q113 0 168 55t55 184
t-55 183t-168 54z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="678" 
d="M201 754h56v-68h-56v68zM421 754h56v-68h-56v68zM61 -163q15 -1 29.5 -1h30.5q66 0 102.5 21.5t61.5 72.5l35 73h-28l-258 547h59l236 -498h15l241 498h59l-305 -629q-18 -37 -37.5 -63t-44 -42t-56 -23t-72.5 -7q-18 0 -34.5 0.5t-33.5 2.5v48z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="819" 
d="M244 933h332v-39h-332v39zM355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436h368z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="719" 
d="M210 741h298v-41h-298v41zM301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379
h-41l-13 40q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="819" 
d="M410 842q-50 0 -83.5 8t-53.5 25.5t-28 45t-8 66.5h45q0 -28 5.5 -47.5t20 -32t39.5 -18t63 -5.5t63 5.5t39.5 18t20 32t5.5 47.5h46q0 -39 -8.5 -66.5t-28.5 -45t-53.5 -25.5t-83.5 -8zM355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23
l-171 -436h368z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="719" 
d="M359 649q-47 0 -78 7.5t-50 23t-26.5 40.5t-7.5 61h46q0 -25 5 -42.5t18 -28t35.5 -15t57.5 -4.5t57.5 4.5t35.5 15t18 28t5 42.5h46q0 -36 -7.5 -61t-26.5 -40.5t-50 -23t-78 -7.5zM301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56
q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379h-41l-13 40q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5
t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="819" 
d="M355 802h107l322 -802h-27q-28 -22 -47.5 -38t-31.5 -30.5t-17 -27.5t-5 -29q0 -25 18.5 -37t51.5 -12q23 0 40 2t30 5v-38q-15 -4 -33 -6.5t-44 -2.5q-109 0 -109 81q0 35 23.5 66.5t83.5 75.5l-101 252h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436h368z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="719" 
d="M301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379h-15q-28 -22 -47.5 -38
t-31.5 -30.5t-17 -27.5t-5 -29q0 -25 18.5 -37t51.5 -12q23 0 40 2t30 5v-38q-15 -4 -33 -6.5t-44 -2.5q-109 0 -109 81q0 18 6 35t19.5 34t35.5 36.5t55 42.5l-8 25q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5
t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="868" 
d="M533 987h68l-126 -140h-57zM449 -16q-93 0 -161.5 23.5t-114.5 73.5t-68.5 129t-22.5 191q0 111 22.5 190.5t68.5 129.5t114.5 73.5t161.5 23.5q143 0 228.5 -56t119.5 -182h-64q-29 97 -99 139t-185 42q-79 0 -137 -19.5t-95 -63t-55 -111.5t-18 -166q0 -97 18 -165.5
t55 -112t95 -63t137 -19.5q116 0 185.5 42.5t98.5 139.5h64q-33 -126 -119 -182.5t-229 -56.5z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="630" 
d="M465 794h67l-149 -170h-54zM364 -16q-76 0 -131 16.5t-91 51.5t-53.5 90.5t-17.5 133.5t17.5 133t53.5 90t91 51t131 16q60 0 110.5 -7.5t88.5 -16.5v-50q-38 8 -87.5 15t-109.5 7q-64 0 -109 -13.5t-73 -42t-40.5 -73.5t-12.5 -109t12.5 -109t40.5 -74t73 -42.5
t109 -13.5q62 0 114.5 6t92.5 16v-50q-41 -11 -93.5 -18t-115.5 -7z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="868" 
d="M412 987h74l142 -140h-55l-124 113l-124 -113h-55zM449 -16q-93 0 -161.5 23.5t-114.5 73.5t-68.5 129t-22.5 191q0 111 22.5 190.5t68.5 129.5t114.5 73.5t161.5 23.5q143 0 228.5 -56t119.5 -182h-64q-29 97 -99 139t-185 42q-79 0 -137 -19.5t-95 -63t-55 -111.5
t-18 -166q0 -97 18 -165.5t55 -112t95 -63t137 -19.5q116 0 185.5 42.5t98.5 139.5h64q-33 -126 -119 -182.5t-229 -56.5z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="630" 
d="M324 802h66l133 -170h-53l-113 138l-113 -138h-53zM364 -16q-76 0 -131 16.5t-91 51.5t-53.5 90.5t-17.5 133.5t17.5 133t53.5 90t91 51t131 16q60 0 110.5 -7.5t88.5 -16.5v-50q-38 8 -87.5 15t-109.5 7q-64 0 -109 -13.5t-73 -42t-40.5 -73.5t-12.5 -109t12.5 -109
t40.5 -74t73 -42.5t109 -13.5q62 0 114.5 6t92.5 16v-50q-41 -11 -93.5 -18t-115.5 -7z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="868" 
d="M416 949h66v-63h-66v63zM449 -16q-93 0 -161.5 23.5t-114.5 73.5t-68.5 129t-22.5 191q0 111 22.5 190.5t68.5 129.5t114.5 73.5t161.5 23.5q143 0 228.5 -56t119.5 -182h-64q-29 97 -99 139t-185 42q-79 0 -137 -19.5t-95 -63t-55 -111.5t-18 -166q0 -97 18 -165.5
t55 -112t95 -63t137 -19.5q116 0 185.5 42.5t98.5 139.5h64q-33 -126 -119 -182.5t-229 -56.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="630" 
d="M324 754h66v-68h-66v68zM364 -16q-76 0 -131 16.5t-91 51.5t-53.5 90.5t-17.5 133.5t17.5 133t53.5 90t91 51t131 16q60 0 110.5 -7.5t88.5 -16.5v-50q-38 8 -87.5 15t-109.5 7q-64 0 -109 -13.5t-73 -42t-40.5 -73.5t-12.5 -109t12.5 -109t40.5 -74t73 -42.5t109 -13.5
q62 0 114.5 6t92.5 16v-50q-41 -11 -93.5 -18t-115.5 -7z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="868" 
d="M270 987h55l124 -113l124 113h55l-142 -140h-74zM449 -16q-93 0 -161.5 23.5t-114.5 73.5t-68.5 129t-22.5 191q0 111 22.5 190.5t68.5 129.5t114.5 73.5t161.5 23.5q143 0 228.5 -56t119.5 -182h-64q-29 97 -99 139t-185 42q-79 0 -137 -19.5t-95 -63t-55 -111.5
t-18 -166q0 -97 18 -165.5t55 -112t95 -63t137 -19.5q116 0 185.5 42.5t98.5 139.5h64q-33 -126 -119 -182.5t-229 -56.5z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="630" 
d="M191 802h53l113 -138l113 138h53l-133 -170h-66zM364 -16q-76 0 -131 16.5t-91 51.5t-53.5 90.5t-17.5 133.5t17.5 133t53.5 90t91 51t131 16q60 0 110.5 -7.5t88.5 -16.5v-50q-38 8 -87.5 15t-109.5 7q-64 0 -109 -13.5t-73 -42t-40.5 -73.5t-12.5 -109t12.5 -109
t40.5 -74t73 -42.5t109 -13.5q62 0 114.5 6t92.5 16v-50q-41 -11 -93.5 -18t-115.5 -7z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="896" 
d="M269 987h55l124 -113l124 113h55l-142 -140h-74zM130 802h313q95 0 164.5 -22.5t116 -71t69 -124.5t22.5 -183q0 -108 -22.5 -184t-69 -124t-116 -70.5t-164.5 -22.5h-313v802zM190 747v-692h253q81 0 139.5 19t96 60.5t55.5 107t18 159.5q0 93 -18 159t-55.5 107.5
t-96 60.5t-139.5 19h-253z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="735" 
d="M344 -16q-66 0 -116.5 16.5t-85.5 51.5t-53 90.5t-18 133.5q0 154 71 222t202 68q59 0 118 -16t107 -36v288h57v-802h-41l-12 38q-49 -21 -109 -37.5t-120 -16.5zM777 802h58l-96 -256h-39zM131 276q0 -65 13.5 -111t41.5 -74.5t69.5 -41.5t98.5 -13q63 0 117.5 15.5
t97.5 35.5v376q-42 20 -97 36t-118 16q-113 0 -168 -55t-55 -184z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="896" 
d="M130 376h-130v55h130v371h313q95 0 164.5 -22.5t116 -71t69 -124.5t22.5 -183q0 -108 -22.5 -184t-69 -124t-116 -70.5t-164.5 -22.5h-313v376zM190 376v-321h253q81 0 139.5 19t96 60.5t55.5 107t18 159.5q0 93 -18 159t-55.5 107.5t-96 60.5t-139.5 19h-253v-316h238
v-55h-238z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="735" 
d="M344 -16q-66 0 -116.5 16.5t-85.5 51.5t-53 90.5t-18 133.5q0 154 71 222t202 68q59 0 118 -16t107 -36v136h-237v47h237v105h57v-105h109v-47h-109v-650h-41l-12 38q-49 -21 -109 -37.5t-120 -16.5zM131 276q0 -65 13.5 -111t41.5 -74.5t69.5 -41.5t98.5 -13
q63 0 117.5 15.5t97.5 35.5v376q-42 20 -97 36t-118 16q-113 0 -168 -55t-55 -184z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="764" 
d="M243 933h332v-39h-332v39zM130 802h542v-55h-482v-316h451v-55h-451v-321h482v-55h-542v802z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="687" 
d="M209 741h298v-41h-298v41zM376 -16q-85 0 -143.5 16.5t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7t100 16v-50q-45 -11 -97.5 -18
t-121.5 -7zM353 516q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h431q0 55 -11.5 93t-36.5 62t-65 35t-97 11z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="764" 
d="M409 842q-50 0 -83.5 8t-53.5 25.5t-28 45t-8 66.5h45q0 -28 5.5 -47.5t20 -32t39.5 -18t63 -5.5t63 5.5t39.5 18t20 32t5.5 47.5h46q0 -39 -8.5 -66.5t-28.5 -45t-53.5 -25.5t-83.5 -8zM130 802h542v-55h-482v-316h451v-55h-451v-321h482v-55h-542v802z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="687" 
d="M358 649q-47 0 -78 7.5t-50 23t-26.5 40.5t-7.5 61h46q0 -25 5 -42.5t18 -28t35.5 -15t57.5 -4.5t57.5 4.5t35.5 15t18 28t5 42.5h46q0 -36 -7.5 -61t-26.5 -40.5t-50 -23t-78 -7.5zM376 -16q-85 0 -143.5 16.5t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51
t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7t100 16v-50q-45 -11 -97.5 -18t-121.5 -7zM353 516q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h431q0 55 -11.5 93t-36.5 62t-65 35t-97 11z
" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="764" 
d="M376 949h66v-63h-66v63zM130 802h542v-55h-482v-316h451v-55h-451v-321h482v-55h-542v802z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="687" 
d="M325 754h66v-68h-66v68zM376 -16q-85 0 -143.5 16.5t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7t100 16v-50q-45 -11 -97.5 -18
t-121.5 -7zM353 516q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h431q0 55 -11.5 93t-36.5 62t-65 35t-97 11z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="764" 
d="M130 802h542v-55h-482v-316h451v-55h-451v-321h482v-55h-39q-28 -22 -47.5 -38t-31.5 -30.5t-17 -27.5t-5 -29q0 -25 18.5 -37t51.5 -12q23 0 40 2t30 5v-38q-15 -4 -33 -6.5t-44 -2.5q-109 0 -109 81q0 34 21 63t74 70h-451v802z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="687" 
d="M376 -16q-85 0 -143.5 16.5t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7t100 16v-50q-9 -2 -18.5 -4t-19.5 -4
q-28 -22 -47.5 -38.5t-31.5 -30.5t-17.5 -27.5t-5.5 -29.5q0 -25 18.5 -37t51.5 -12q23 0 40 2t30 5v-38q-15 -4 -33 -6.5t-44 -2.5q-109 0 -109 81q0 32 18 59.5t65 64.5q-26 -4 -55 -5.5t-61 -1.5zM353 516q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h431
q0 55 -11.5 93t-36.5 62t-65 35t-97 11z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="764" 
d="M230 987h55l124 -113l124 113h55l-142 -140h-74zM130 802h542v-55h-482v-316h451v-55h-451v-321h482v-55h-542v802z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="687" 
d="M192 802h53l113 -138l113 138h53l-133 -170h-66zM376 -16q-85 0 -143.5 16.5t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7t100 16
v-50q-45 -11 -97.5 -18t-121.5 -7zM353 516q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h431q0 55 -11.5 93t-36.5 62t-65 35t-97 11z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="896" 
d="M417 987h74l142 -140h-55l-124 113l-124 -113h-55zM463 -16q-98 0 -169.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 22.5 190.5t68.5 129.5t116 73.5t164 23.5q146 0 232 -56t119 -182h-63q-29 97 -99.5 139t-188.5 42q-81 0 -139 -19.5t-96 -63t-56 -111.5t-18 -166
q0 -97 18 -165.5t56.5 -112t99 -63t145.5 -19.5q75 0 130 14t91 45t53 79.5t17 117.5v53h-287v55h347v-110q0 -160 -84.5 -235.5t-266.5 -75.5z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="733" 
d="M344 802h66l133 -170h-53l-113 138l-113 -138h-53zM344 -12q-131 0 -202 68t-71 222q0 153 71 220.5t202 67.5q60 0 120 -16.5t108 -37.5l11 38h41v-526q0 -63 -15 -106.5t-47 -71t-83 -40t-122 -12.5q-33 0 -61.5 1t-55 3.5t-53 7t-57.5 10.5v51q33 -6 60.5 -10t54 -7
t53 -4t57.5 -1q61 0 102 9.5t65.5 30.5t35 55t10.5 82v19q-48 -21 -106.5 -37t-117.5 -16zM131 278q0 -128 55 -182.5t168 -54.5q63 0 117 15t97 35v372q-42 20 -96.5 35.5t-117.5 15.5q-113 0 -168 -54t-55 -182z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="896" 
d="M454 842q-50 0 -83.5 8t-53.5 25.5t-28 45t-8 66.5h45q0 -28 5.5 -47.5t20 -32t39.5 -18t63 -5.5t63 5.5t39.5 18t20 32t5.5 47.5h46q0 -39 -8.5 -66.5t-28.5 -45t-53.5 -25.5t-83.5 -8zM463 -16q-98 0 -169.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 22.5 190.5
t68.5 129.5t116 73.5t164 23.5q146 0 232 -56t119 -182h-63q-29 97 -99.5 139t-188.5 42q-81 0 -139 -19.5t-96 -63t-56 -111.5t-18 -166q0 -97 18 -165.5t56.5 -112t99 -63t145.5 -19.5q75 0 130 14t91 45t53 79.5t17 117.5v53h-287v55h347v-110q0 -160 -84.5 -235.5
t-266.5 -75.5z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="733" 
d="M377 649q-47 0 -78 7.5t-50 23t-26.5 40.5t-7.5 61h46q0 -25 5 -42.5t18 -28t35.5 -15t57.5 -4.5t57.5 4.5t35.5 15t18 28t5 42.5h46q0 -36 -7.5 -61t-26.5 -40.5t-50 -23t-78 -7.5zM344 -12q-131 0 -202 68t-71 222q0 153 71 220.5t202 67.5q60 0 120 -16.5t108 -37.5
l11 38h41v-526q0 -63 -15 -106.5t-47 -71t-83 -40t-122 -12.5q-33 0 -61.5 1t-55 3.5t-53 7t-57.5 10.5v51q33 -6 60.5 -10t54 -7t53 -4t57.5 -1q61 0 102 9.5t65.5 30.5t35 55t10.5 82v19q-48 -21 -106.5 -37t-117.5 -16zM131 278q0 -128 55 -182.5t168 -54.5q63 0 117 15
t97 35v372q-42 20 -96.5 35.5t-117.5 15.5q-113 0 -168 -54t-55 -182z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="896" 
d="M421 949h66v-63h-66v63zM463 -16q-98 0 -169.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 22.5 190.5t68.5 129.5t116 73.5t164 23.5q146 0 232 -56t119 -182h-63q-29 97 -99.5 139t-188.5 42q-81 0 -139 -19.5t-96 -63t-56 -111.5t-18 -166q0 -97 18 -165.5t56.5 -112
t99 -63t145.5 -19.5q75 0 130 14t91 45t53 79.5t17 117.5v53h-287v55h347v-110q0 -160 -84.5 -235.5t-266.5 -75.5z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="733" 
d="M344 754h66v-68h-66v68zM344 -12q-131 0 -202 68t-71 222q0 153 71 220.5t202 67.5q60 0 120 -16.5t108 -37.5l11 38h41v-526q0 -63 -15 -106.5t-47 -71t-83 -40t-122 -12.5q-33 0 -61.5 1t-55 3.5t-53 7t-57.5 10.5v51q33 -6 60.5 -10t54 -7t53 -4t57.5 -1q61 0 102 9.5
t65.5 30.5t35 55t10.5 82v19q-48 -21 -106.5 -37t-117.5 -16zM131 278q0 -128 55 -182.5t168 -54.5q63 0 117 15t97 35v372q-42 20 -96.5 35.5t-117.5 15.5q-113 0 -168 -54t-55 -182z" />
    <glyph glyph-name="uni0122" unicode="&#x122;" horiz-adv-x="896" 
d="M463 -16q-98 0 -169.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 22.5 190.5t68.5 129.5t116 73.5t164 23.5q146 0 232 -56t119 -182h-63q-29 97 -99.5 139t-188.5 42q-81 0 -139 -19.5t-96 -63t-56 -111.5t-18 -166q0 -97 18 -165.5t56.5 -112t99 -63t145.5 -19.5
q75 0 130 14t91 45t53 79.5t17 117.5v53h-287v55h347v-110q0 -160 -84.5 -235.5t-266.5 -75.5zM427 -53h72l-137 -145h-55z" />
    <glyph glyph-name="uni0123" unicode="&#x123;" horiz-adv-x="733" 
d="M410 632h-67l148 170h55zM344 -12q-131 0 -202 68t-71 222q0 153 71 220.5t202 67.5q60 0 120 -16.5t108 -37.5l11 38h41v-526q0 -63 -15 -106.5t-47 -71t-83 -40t-122 -12.5q-33 0 -61.5 1t-55 3.5t-53 7t-57.5 10.5v51q33 -6 60.5 -10t54 -7t53 -4t57.5 -1
q61 0 102 9.5t65.5 30.5t35 55t10.5 82v19q-48 -21 -106.5 -37t-117.5 -16zM131 278q0 -128 55 -182.5t168 -54.5q63 0 117 15t97 35v372q-42 20 -96.5 35.5t-117.5 15.5q-113 0 -168 -54t-55 -182z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="909" 
d="M418 987h74l142 -140h-55l-124 113l-124 -113h-55zM130 802h60v-370h529v370h60v-802h-60v377h-529v-377h-60v802z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="756" 
d="M341 987h74l142 -140h-55l-124 113l-124 -113h-55zM109 802h57v-291q51 23 116.5 39t134.5 16q118 0 176 -51t58 -133v-382h-57v367q0 69 -43 107.5t-141 38.5q-73 0 -134.5 -17t-109.5 -38v-458h-57v802z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="909" 
d="M130 592h-130v55h130v155h60v-155h529v155h60v-155h130v-55h-130v-592h-60v377h-529v-377h-60v592zM719 432v160h-529v-160h529z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="756" 
d="M109 650h-109v47h109v105h57v-105h237v-47h-237v-139q51 23 116.5 39t134.5 16q118 0 176 -51t58 -133v-382h-57v367q0 69 -43 107.5t-141 38.5q-73 0 -134.5 -17t-109.5 -38v-458h-57v650z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="321" 
d="M-26 872q0 54 24 78t70 24q32 0 56 -12.5t44.5 -27.5t40 -27.5t42.5 -12.5q29 0 42 14.5t13 49.5v11h41v-13q0 -103 -94 -103q-32 0 -56 12.5t-44.5 28t-40 28t-43.5 12.5q-29 0 -41.5 -14.5t-12.5 -50.5v-11h-41v14zM130 802h60v-802h-60v802z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="275" 
d="M-48 672q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-41.5 33t-46.5 15q-27 0 -39 -16t-12 -56v-20h-41v23zM109 550h57v-550h-57v550z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="321" 
d="M-6 933h332v-39h-332v39zM130 802h60v-802h-60v802z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="275" 
d="M-11 741h298v-41h-298v41zM109 550h57v-550h-57v550z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="321" 
d="M160 842q-50 0 -83.5 8t-53.5 25.5t-28 45t-8 66.5h45q0 -28 5.5 -47.5t20 -32t39.5 -18t63 -5.5t63 5.5t39.5 18t20 32t5.5 47.5h46q0 -39 -8.5 -66.5t-28.5 -45t-53.5 -25.5t-83.5 -8zM130 802h60v-802h-60v802z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="275" 
d="M138 649q-47 0 -78 7.5t-50 23t-26.5 40.5t-7.5 61h46q0 -25 5 -42.5t18 -28t35.5 -15t57.5 -4.5t57.5 4.5t35.5 15t18 28t5 42.5h46q0 -36 -7.5 -61t-26.5 -40.5t-50 -23t-78 -7.5zM109 550h57v-550h-57v550z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="321" 
d="M75 -125q0 -25 18.5 -37t51.5 -12q23 0 40 2t30 5v-38q-15 -4 -33 -6.5t-44 -2.5q-109 0 -109 81q0 34 22 64.5t79 72.5v798h60v-802h-14q-28 -22 -47.5 -38t-31.5 -30.5t-17 -27.5t-5 -29z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="275" 
d="M105 782h65v-69h-65v69zM50 -125q0 -25 18.5 -37t51.5 -12q23 0 40 2t30 5v-38q-15 -4 -33 -6.5t-44 -2.5q-109 0 -109 81q0 35 23 66t82 74v543h57v-550h-15q-28 -22 -47.5 -38t-31.5 -30.5t-17 -27.5t-5 -29z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="321" 
d="M127 949h66v-63h-66v63zM130 802h60v-802h-60v802z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="275" 
d="M109 550h57v-550h-57v550z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="702" 
d="M130 802h60v-802h-60v802zM379 -14q-21 0 -40 4v53q11 -2 18.5 -2h16.5q42 0 68.5 8.5t42.5 26.5t22 46t6 66v614h60v-614q0 -51 -9 -89t-31.5 -63t-60 -37.5t-93.5 -12.5z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="549" 
d="M105 782h65v-69h-65v69zM380 782h65v-69h-65v69zM109 550h57v-550h-57v550zM194 -159q16 -2 38.5 -3t39.5 -1q59 0 85.5 27.5t26.5 95.5v590h57v-590q0 -93 -37.5 -133.5t-126.5 -40.5q-19 0 -42 1t-41 3v51z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="381" 
d="M184 987h74l142 -140h-55l-124 113l-124 -113h-55zM58 -14q-21 0 -40 4v53q11 -2 18.5 -2h16.5q42 0 68.5 8.5t42.5 26.5t22 46t6 66v614h60v-614q0 -51 -9 -89t-31.5 -63t-60 -37.5t-93.5 -12.5z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="275" 
d="M104 802h66l133 -170h-53l-113 138l-113 -138h-53zM-81 -164q16 -2 38.5 -3t39.5 -1q59 0 85.5 27.5t26.5 98.5v592h57v-590q0 -93 -37.5 -133.5t-126.5 -40.5q-19 0 -42 1t-41 3v46z" />
    <glyph glyph-name="uni0136" unicode="&#x136;" horiz-adv-x="761" 
d="M130 802h59v-361l450 361h81l-468 -376l474 -426h-83l-454 411v-411h-59v802zM353 -53h72l-137 -145h-55z" />
    <glyph glyph-name="uni0137" unicode="&#x137;" horiz-adv-x="660" 
d="M109 802h57v-471l345 219h86l-366 -232l387 -318h-83l-369 308v-308h-57v802zM295 -53h72l-137 -145h-55z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="658" 
d="M109 550h57v-219l345 219h86l-366 -232l387 -318h-83l-369 308v-308h-57v550z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="684" 
d="M475 987h68l-126 -140h-57zM130 802h60v-747h455v-55h-515v802z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="275" 
d="M221 987h68l-126 -140h-57zM109 802h57v-802h-57v802z" />
    <glyph glyph-name="uni013B" unicode="&#x13b;" horiz-adv-x="684" 
d="M130 802h60v-747h455v-55h-515v802zM364 -53h72l-137 -145h-55z" />
    <glyph glyph-name="uni013C" unicode="&#x13c;" horiz-adv-x="275" 
d="M109 802h57v-802h-57v802zM104 -53h72l-137 -145h-55z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="684" 
d="M130 802h60v-747h455v-55h-515v802zM359 802h58l-96 -256h-39z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="275" 
d="M109 802h57v-802h-57v802zM317 802h58l-96 -256h-39z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="684" 
d="M130 802h60v-747h455v-55h-515v802zM499 466h78v-75h-78v75z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="352" 
d="M109 802h57v-802h-57v802zM310 314h78v-75h-78v75z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="684" 
d="M130 345l-130 -62v62l130 61v396h60v-367l235 111v-61l-235 -112v-318h455v-55h-515v345z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="275" 
d="M109 281l-109 -52v53l109 52v468h57v-441l109 52v-54l-109 -52v-307h-57v281z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="920" 
d="M544 987h68l-126 -140h-57zM129 802h80l522 -728v728h60v-802h-81l-521 725v-725h-60v802z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="756" 
d="M486 794h67l-149 -170h-54zM163 510q51 23 117.5 39.5t136.5 16.5q118 0 176 -51t58 -133v-382h-57v367q0 69 -43 107.5t-141 38.5q-73 0 -134.5 -16t-109.5 -39v-458h-57v550h42z" />
    <glyph glyph-name="uni0145" unicode="&#x145;" horiz-adv-x="920" 
d="M129 802h80l522 -728v728h60v-802h-81l-521 725v-725h-60v802zM410 -53h72l-137 -145h-55z" />
    <glyph glyph-name="uni0146" unicode="&#x146;" horiz-adv-x="756" 
d="M163 510q51 23 117.5 39.5t136.5 16.5q118 0 176 -51t58 -133v-382h-57v367q0 69 -43 107.5t-141 38.5q-73 0 -134.5 -16t-109.5 -39v-458h-57v550h42zM345 -53h72l-137 -145h-55z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="920" 
d="M281 987h55l124 -113l124 113h55l-142 -140h-74zM129 802h80l522 -728v728h60v-802h-81l-521 725v-725h-60v802z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="756" 
d="M212 802h53l113 -138l113 138h53l-133 -170h-66zM163 510q51 23 117.5 39.5t136.5 16.5q118 0 176 -51t58 -133v-382h-57v367q0 69 -43 107.5t-141 38.5q-73 0 -134.5 -16t-109.5 -39v-458h-57v550h42z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="875" 
d="M201 802h63l-94 -269h-53zM282 510q51 23 117.5 39.5t136.5 16.5q118 0 176 -51t58 -133v-382h-57v367q0 69 -43 107.5t-141 38.5q-73 0 -134.5 -16t-109.5 -39v-458h-57v550h42z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="920" 
d="M129 802h78l524 -705v705h60v-814q0 -51 -9 -89t-31.5 -63t-60 -37.5t-93.5 -12.5q-21 0 -40 4v49q10 -1 18 -1h17q42 0 69 8t42.5 25.5t21.5 45t6 66.5v17l-542 729v-729h-60v802z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="756" 
d="M405 -160q15 -2 38 -3t39 -1q60 0 86 27.5t26 96.5v407q0 72 -43 111.5t-141 39.5q-72 0 -134 -16.5t-110 -37.5v-464h-57v550h42l12 -40q51 23 117.5 39.5t136.5 16.5q118 0 176 -51t58 -133v-422q0 -47 -9 -80t-28.5 -54t-50.5 -30.5t-75 -9.5q-19 0 -42.5 1t-40.5 3
v50z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="924" 
d="M296 933h332v-39h-332v39zM462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q96 0 167.5 -23.5t119 -73.5t70.5 -129.5t23 -190.5q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5zM462 41q83 0 143 19.5
t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="726" 
d="M214 741h298v-41h-298v41zM363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5t218 -67.5t73 -222.5q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5t176 -56.5z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="924" 
d="M462 842q-50 0 -83.5 8t-53.5 25.5t-28 45t-8 66.5h45q0 -28 5.5 -47.5t20 -32t39.5 -18t63 -5.5t63 5.5t39.5 18t20 32t5.5 47.5h46q0 -39 -8.5 -66.5t-28.5 -45t-53.5 -25.5t-83.5 -8zM462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5
t118.5 73.5t168.5 23.5q96 0 167.5 -23.5t119 -73.5t70.5 -129.5t23 -190.5q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5zM462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5
t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="726" 
d="M363 649q-47 0 -78 7.5t-50 23t-26.5 40.5t-7.5 61h46q0 -25 5 -42.5t18 -28t35.5 -15t57.5 -4.5t57.5 4.5t35.5 15t18 28t5 42.5h46q0 -36 -7.5 -61t-26.5 -40.5t-50 -23t-78 -7.5zM363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5t218 -67.5
t73 -222.5q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5t176 -56.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="924" 
d="M446 987h65l-121 -140h-54zM644 987h65l-121 -140h-54zM462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q96 0 167.5 -23.5t119 -73.5t70.5 -129.5t23 -190.5q0 -112 -23 -191t-70.5 -129t-119 -73.5
t-167.5 -23.5zM462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="726" 
d="M370 802h64l-140 -170h-52zM565 802h64l-141 -170h-52zM363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5t218 -67.5t73 -222.5q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5
t176 -56.5z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1340" 
d="M82 401q0 108 22.5 184t68.5 124.5t116 70.5t164 22h795v-55h-482v-316h450v-55h-450v-321h482v-55h-795q-94 0 -164 22.5t-116 71.5t-68.5 125t-22.5 182zM706 55v692h-253q-81 0 -139.5 -19t-96 -60.5t-55.5 -107.5t-18 -159q0 -94 18 -159.5t55.5 -107t96 -60.5
t139.5 -19h253z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1210" 
d="M361 -16q-144 0 -217 68t-73 224q0 155 73 222.5t217 67.5q104 0 169.5 -36t93.5 -115q26 79 88.5 115t166.5 36q68 0 118 -14t83 -45t49.5 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117.5 6.5t99.5 16.5v-49q-45 -11 -97 -18.5
t-120 -7.5q-121 0 -187 36t-91 115q-29 -78 -94 -114.5t-168 -36.5zM876 516q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h435q-1 55 -13.5 93t-38 62t-65.5 35t-97 11zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5t176 -56.5
z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="845" 
d="M507 987h68l-126 -140h-57zM130 802h337q78 0 134 -13t91.5 -43t52.5 -79t17 -122q0 -60 -11 -104t-34.5 -74.5t-59.5 -48t-86 -25.5l180 -293h-72l-172 287q-10 -1 -19 -1h-298v-286h-60v802zM190 747v-406h277q62 0 106 9t72 32t41 62.5t13 100.5q0 60 -13 98.5
t-41 61.5t-72 32.5t-106 9.5h-277z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="407" 
d="M356 794h67l-149 -170h-54zM109 550h42l12 -40q42 21 94.5 34.5t111.5 13.5v-54q-60 -1 -110.5 -13t-92.5 -29v-462h-57v550z" />
    <glyph glyph-name="uni0156" unicode="&#x156;" horiz-adv-x="845" 
d="M130 802h337q78 0 134 -13t91.5 -43t52.5 -79t17 -122q0 -60 -11 -104t-34.5 -74.5t-59.5 -48t-86 -25.5l180 -293h-72l-172 287q-10 -1 -19 -1h-298v-286h-60v802zM190 747v-406h277q62 0 106 9t72 32t41 62.5t13 100.5q0 60 -13 98.5t-41 61.5t-72 32.5t-106 9.5h-277z
M387 -53h72l-137 -145h-55z" />
    <glyph glyph-name="uni0157" unicode="&#x157;" horiz-adv-x="407" 
d="M109 550h42l12 -40q42 21 94.5 34.5t111.5 13.5v-54q-60 -1 -110.5 -13t-92.5 -29v-462h-57v550zM103 -53h72l-137 -145h-55z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="845" 
d="M244 987h55l124 -113l124 113h55l-142 -140h-74zM130 802h337q78 0 134 -13t91.5 -43t52.5 -79t17 -122q0 -60 -11 -104t-34.5 -74.5t-59.5 -48t-86 -25.5l180 -293h-72l-172 287q-10 -1 -19 -1h-298v-286h-60v802zM190 747v-406h277q62 0 106 9t72 32t41 62.5t13 100.5
q0 60 -13 98.5t-41 61.5t-72 32.5t-106 9.5h-277z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="407" 
d="M82 802h53l113 -138l113 138h53l-133 -170h-66zM109 550h42l12 -40q42 21 94.5 34.5t111.5 13.5v-54q-60 -1 -110.5 -13t-92.5 -29v-462h-57v550z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="804" 
d="M488 987h68l-126 -140h-57zM112 83q51 -15 123.5 -28t158.5 -13q72 0 121 8.5t79 27.5t43 49.5t13 75.5t-9.5 74t-36.5 48.5t-75.5 32t-126.5 24.5q-93 13 -151.5 28t-91.5 40t-45 62t-12 94q0 54 15.5 94t51 66t94 38.5t144.5 12.5q87 0 149 -10t113 -25v-59
q-51 15 -116.5 25.5t-144.5 10.5q-73 0 -120 -8.5t-74.5 -27t-38.5 -47t-11 -68.5q0 -41 9 -68t35.5 -45.5t74.5 -30.5t127 -23q93 -12 151.5 -28.5t92 -43t46 -66t12.5 -98.5q0 -61 -18 -103.5t-56.5 -68.5t-99 -37.5t-144.5 -11.5q-92 0 -161.5 11.5t-120.5 27.5v60z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="644" 
d="M430 794h67l-149 -170h-54zM81 61q44 -9 105.5 -18t141.5 -9q52 0 87.5 4t58 15.5t32.5 31t10 50.5t-7 51t-27.5 33.5t-57.5 22t-98 15.5q-74 8 -120 19t-72 29t-35.5 44.5t-9.5 66.5q0 42 12 70.5t40 46.5t75 25.5t116 7.5q65 0 120 -7.5t100 -18.5v-49
q-44 10 -95.5 17.5t-122.5 7.5q-56 0 -92.5 -4.5t-58 -15.5t-30 -30t-8.5 -48q0 -27 6.5 -44.5t27 -29.5t57 -20t96.5 -15q74 -8 121 -20t73.5 -31t36 -48t9.5 -72q0 -45 -13.5 -74.5t-42.5 -47t-75 -25t-111 -7.5q-74 0 -139 8t-110 21v48z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="804" 
d="M367 987h74l142 -140h-55l-124 113l-124 -113h-55zM112 83q51 -15 123.5 -28t158.5 -13q72 0 121 8.5t79 27.5t43 49.5t13 75.5t-9.5 74t-36.5 48.5t-75.5 32t-126.5 24.5q-93 13 -151.5 28t-91.5 40t-45 62t-12 94q0 54 15.5 94t51 66t94 38.5t144.5 12.5q87 0 149 -10
t113 -25v-59q-51 15 -116.5 25.5t-144.5 10.5q-73 0 -120 -8.5t-74.5 -27t-38.5 -47t-11 -68.5q0 -41 9 -68t35.5 -45.5t74.5 -30.5t127 -23q93 -12 151.5 -28.5t92 -43t46 -66t12.5 -98.5q0 -61 -18 -103.5t-56.5 -68.5t-99 -37.5t-144.5 -11.5q-92 0 -161.5 11.5
t-120.5 27.5v60z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="644" 
d="M289 802h66l133 -170h-53l-113 138l-113 -138h-53zM81 61q44 -9 105.5 -18t141.5 -9q52 0 87.5 4t58 15.5t32.5 31t10 50.5t-7 51t-27.5 33.5t-57.5 22t-98 15.5q-74 8 -120 19t-72 29t-35.5 44.5t-9.5 66.5q0 42 12 70.5t40 46.5t75 25.5t116 7.5q65 0 120 -7.5
t100 -18.5v-49q-44 10 -95.5 17.5t-122.5 7.5q-56 0 -92.5 -4.5t-58 -15.5t-30 -30t-8.5 -48q0 -27 6.5 -44.5t27 -29.5t57 -20t96.5 -15q74 -8 121 -20t73.5 -31t36 -48t9.5 -72q0 -45 -13.5 -74.5t-42.5 -47t-75 -25t-111 -7.5q-74 0 -139 8t-110 21v48z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="804" 
d="M112 83q51 -15 123.5 -28t158.5 -13q72 0 121 8.5t79 27.5t43 49.5t13 75.5t-9.5 74t-36.5 48.5t-75.5 32t-126.5 24.5q-93 13 -151.5 28t-91.5 40t-45 62t-12 94q0 54 15.5 94t51 66t94 38.5t144.5 12.5q87 0 149 -10t113 -25v-59q-51 15 -116.5 25.5t-144.5 10.5
q-73 0 -120 -8.5t-74.5 -27t-38.5 -47t-11 -68.5q0 -41 9 -68t35.5 -45.5t74.5 -30.5t127 -23q93 -12 151.5 -28.5t92 -43t46 -66t12.5 -98.5q0 -60 -17 -101.5t-53.5 -68t-93.5 -38.5t-137 -13l-53 -53q7 1 13.5 1h14.5q62 0 85.5 -16.5t23.5 -55.5q0 -40 -24 -57t-88 -17
q-49 0 -79 9v32q34 -8 78 -8q24 0 39.5 2t23.5 7t11 12.5t3 19.5q0 10 -3 18t-11 13.5t-23.5 8t-39.5 2.5q-20 0 -38.5 -1.5t-32.5 -3.5v29l59 58q-83 2 -146.5 13t-111.5 26v60z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="644" 
d="M81 61q44 -9 105.5 -18t141.5 -9q52 0 87.5 4t58 15.5t32.5 31t10 50.5t-7 51t-27.5 33.5t-57.5 22t-98 15.5q-74 8 -120 19t-72 29t-35.5 44.5t-9.5 66.5q0 42 12 70.5t40 46.5t75 25.5t116 7.5q65 0 120 -7.5t100 -18.5v-49q-44 10 -95.5 17.5t-122.5 7.5
q-56 0 -92.5 -4.5t-58 -15.5t-30 -30t-8.5 -48q0 -27 6.5 -44.5t27 -29.5t57 -20t96.5 -15q74 -8 121 -20t73.5 -31t36 -48t9.5 -72q0 -45 -13.5 -74.5t-42.5 -47t-75 -25t-111 -7.5h-1l-53 -53q7 1 13.5 1h14.5q62 0 85.5 -16.5t23.5 -55.5q0 -40 -24 -57t-88 -17
q-49 0 -79 9v32q34 -8 78 -8q24 0 39.5 2t23.5 7t11 12.5t3 19.5q0 10 -3 18t-11 13.5t-23.5 8t-39.5 2.5q-20 0 -38.5 -1.5t-32.5 -3.5v29l60 59q-62 2 -116 9.5t-92 18.5v48z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="804" 
d="M225 987h55l124 -113l124 113h55l-142 -140h-74zM112 83q51 -15 123.5 -28t158.5 -13q72 0 121 8.5t79 27.5t43 49.5t13 75.5t-9.5 74t-36.5 48.5t-75.5 32t-126.5 24.5q-93 13 -151.5 28t-91.5 40t-45 62t-12 94q0 54 15.5 94t51 66t94 38.5t144.5 12.5q87 0 149 -10
t113 -25v-59q-51 15 -116.5 25.5t-144.5 10.5q-73 0 -120 -8.5t-74.5 -27t-38.5 -47t-11 -68.5q0 -41 9 -68t35.5 -45.5t74.5 -30.5t127 -23q93 -12 151.5 -28.5t92 -43t46 -66t12.5 -98.5q0 -61 -18 -103.5t-56.5 -68.5t-99 -37.5t-144.5 -11.5q-92 0 -161.5 11.5
t-120.5 27.5v60z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="644" 
d="M156 802h53l113 -138l113 138h53l-133 -170h-66zM81 61q44 -9 105.5 -18t141.5 -9q52 0 87.5 4t58 15.5t32.5 31t10 50.5t-7 51t-27.5 33.5t-57.5 22t-98 15.5q-74 8 -120 19t-72 29t-35.5 44.5t-9.5 66.5q0 42 12 70.5t40 46.5t75 25.5t116 7.5q65 0 120 -7.5t100 -18.5
v-49q-44 10 -95.5 17.5t-122.5 7.5q-56 0 -92.5 -4.5t-58 -15.5t-30 -30t-8.5 -48q0 -27 6.5 -44.5t27 -29.5t57 -20t96.5 -15q74 -8 121 -20t73.5 -31t36 -48t9.5 -72q0 -45 -13.5 -74.5t-42.5 -47t-75 -25t-111 -7.5q-74 0 -139 8t-110 21v48z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="764" 
d="M365 0h-13v747h-314v55h688v-55h-314v-747h-6l-69 -69q7 1 13.5 1h14.5q62 0 85.5 -16.5t23.5 -55.5q0 -40 -24 -57t-88 -17q-49 0 -79 9v32q34 -8 78 -8q24 0 39.5 2t23.5 7t11 12.5t3 19.5q0 10 -3 18t-11 13.5t-23.5 8t-39.5 2.5q-20 0 -38.5 -1.5t-32.5 -3.5v29z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="455" 
d="M259 -8q-76 11 -106.5 52t-30.5 118v340h-64v36l64 12v129h56v-129h215v-48h-215v-334q0 -37 7.5 -61.5t24.5 -39t44.5 -20.5t67.5 -6q39 0 78 6v-52q-26 -4 -51.5 -5t-51.5 -1l-58 -58q7 1 13.5 1h14.5q62 0 85.5 -16.5t23.5 -55.5q0 -40 -24 -57t-88 -17q-49 0 -79 9
v32q34 -8 78 -8q24 0 39.5 2t23.5 7t11 12.5t3 19.5q0 10 -3 18t-11 13.5t-23.5 8t-39.5 2.5q-20 0 -38.5 -1.5t-32.5 -3.5v29z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="764" 
d="M203 987h55l124 -113l124 113h55l-142 -140h-74zM352 747h-314v55h688v-55h-314v-747h-60v747z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="455" 
d="M329 855h58l-96 -256h-39zM318 -11q-55 0 -93 10t-60.5 31t-32.5 54t-10 78v340h-64v36l64 12v129h56v-129h215v-48h-215v-334q0 -37 7.5 -61.5t24.5 -39t44.5 -20.5t67.5 -6q39 0 78 6v-52q-40 -6 -82 -6z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="764" 
d="M352 366h-207v54h207v327h-314v55h688v-55h-314v-327h207v-54h-207v-366h-60v366z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="455" 
d="M59 305h63v197h-64v36l64 12v129h56v-129h215v-48h-215v-197h180v-47h-180v-90q0 -37 7.5 -61.5t24.5 -39t44.5 -20.5t67.5 -6q39 0 78 6v-52q-40 -6 -82 -6q-55 0 -93 10t-60.5 31t-32.5 54t-10 78v96h-63v47z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="886" 
d="M257 872q0 54 24 78t70 24q32 0 56 -12.5t44.5 -27.5t40 -27.5t42.5 -12.5q29 0 42 14.5t13 49.5v11h41v-13q0 -103 -94 -103q-32 0 -56 12.5t-44.5 28t-40 28t-43.5 12.5q-29 0 -41.5 -14.5t-12.5 -50.5v-11h-41v14zM443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132
v527h60v-527q0 -124 62.5 -178.5t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h60v-527q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="756" 
d="M192 672q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-41.5 33t-46.5 15q-27 0 -39 -16t-12 -56v-20h-41v23zM339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367
q0 -69 43 -107.5t141 -38.5q73 0 134.5 17t109.5 38v458h57v-550h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="886" 
d="M277 933h332v-39h-332v39zM443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132v527h60v-527q0 -124 62.5 -178.5t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h60v-527q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="756" 
d="M229 741h298v-41h-298v41zM339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367q0 -69 43 -107.5t141 -38.5q73 0 134.5 17t109.5 38v458h57v-550h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="886" 
d="M443 842q-50 0 -83.5 8t-53.5 25.5t-28 45t-8 66.5h45q0 -28 5.5 -47.5t20 -32t39.5 -18t63 -5.5t63 5.5t39.5 18t20 32t5.5 47.5h46q0 -39 -8.5 -66.5t-28.5 -45t-53.5 -25.5t-83.5 -8zM443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132v527h60v-527
q0 -124 62.5 -178.5t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h60v-527q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="756" 
d="M378 649q-47 0 -78 7.5t-50 23t-26.5 40.5t-7.5 61h46q0 -25 5 -42.5t18 -28t35.5 -15t57.5 -4.5t57.5 4.5t35.5 15t18 28t5 42.5h46q0 -36 -7.5 -61t-26.5 -40.5t-50 -23t-78 -7.5zM339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367q0 -69 43 -107.5
t141 -38.5q73 0 134.5 17t109.5 38v458h57v-550h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="886" 
d="M443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132v527h60v-527q0 -124 62.5 -178.5t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h60v-527q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="756" 
d="M378 617q-63 0 -99.5 24.5t-36.5 82.5t36.5 82t99.5 24t99.5 -24t36.5 -82t-36.5 -82.5t-99.5 -24.5zM378 654q45 0 71 15.5t26 54.5q0 38 -26 53.5t-71 15.5q-46 0 -71 -15.5t-25 -53.5q0 -39 25 -54.5t71 -15.5zM339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75
v381h57v-367q0 -69 43 -107.5t141 -38.5q73 0 134.5 17t109.5 38v458h57v-550h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="886" 
d="M427 987h65l-121 -140h-54zM625 987h65l-121 -140h-54zM443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132v527h60v-527q0 -124 62.5 -178.5t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h60v-527q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="756" 
d="M385 802h64l-140 -170h-52zM580 802h64l-141 -170h-52zM339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367q0 -69 43 -107.5t141 -38.5q73 0 134.5 17t109.5 38v458h57v-550h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="886" 
d="M401 -125q0 -25 18.5 -37t51.5 -12q23 0 40 2t30 5v-38q-15 -4 -33 -6.5t-44 -2.5q-109 0 -109 81q0 30 16.5 56.5t58.5 60.5q-161 2 -236 71t-75 220v527h60v-527q0 -124 62.5 -178.5t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h60v-527
q0 -145 -68.5 -214t-215.5 -76q-46 -35 -64 -58.5t-18 -51.5z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="756" 
d="M339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367q0 -69 43 -107.5t141 -38.5q73 0 134.5 17t109.5 38v458h57v-550h-13q-28 -22 -47.5 -38.5t-31.5 -30.5t-17.5 -27.5t-5.5 -29.5q0 -25 18.5 -37t51.5 -12q23 0 40 2t30 5v-38q-15 -4 -33 -6.5t-44 -2.5
q-109 0 -109 81q0 18 6 35t19.5 34t35.5 36.5t55 42.5l-9 26q-51 -23 -117 -39.5t-137 -16.5z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="1276" 
d="M597 987h74l142 -140h-55l-124 113l-124 -113h-55zM46 802h63l222 -749h18l231 749h115l231 -749h18l223 749h63l-237 -802h-113l-233 749h-18l-233 -749h-113z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="1027" 
d="M477 802h66l133 -170h-53l-113 138l-113 -138h-53zM36 550h61l177 -508l196 508h89l195 -507l174 508l64 -1l-193 -550h-88l-197 506l-197 -506h-88zM514 512l2 1h-4zM752 36l5 1h-1zM276 36l-2 1l-2 -1h4z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="751" 
d="M339 987h74l142 -140h-55l-124 113l-124 -113h-55zM345 316l-327 486h72l286 -427l285 427h71l-327 -486v-316h-60v316z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="678" 
d="M306 802h66l133 -170h-53l-113 138l-113 -138h-53zM61 -163q15 -1 29.5 -1h30.5q66 0 102.5 21.5t61.5 72.5l35 73h-28l-258 547h59l236 -498h15l241 498h59l-305 -629q-18 -37 -37.5 -63t-44 -42t-56 -23t-72.5 -7q-18 0 -34.5 0.5t-33.5 2.5v48z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="751" 
d="M235 949h57v-63h-57v63zM460 949h57v-63h-57v63zM345 316l-327 486h72l286 -427l285 427h71l-327 -486v-316h-60v316z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="754" 
d="M476 987h68l-126 -140h-57zM40 55l562 691h-526v56h602v-55l-562 -691h598v-56h-674v55z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="657" 
d="M438 794h67l-149 -170h-54zM71 47l425 455h-412v48h488v-47l-426 -455h439v-48h-514v47z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="754" 
d="M359 949h66v-63h-66v63zM40 55l562 691h-526v56h602v-55l-562 -691h598v-56h-674v55z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="657" 
d="M297 754h66v-68h-66v68zM71 47l425 455h-412v48h488v-47l-426 -455h439v-48h-514v47z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="754" 
d="M213 987h55l124 -113l124 113h55l-142 -140h-74zM40 55l562 691h-526v56h602v-55l-562 -691h598v-56h-674v55z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="657" 
d="M164 802h53l113 -138l113 138h53l-133 -170h-66zM71 47l425 455h-412v48h488v-47l-426 -455h439v-48h-514v47z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="387" 
d="M126 626q0 50 10.5 86.5t33.5 60t61 34.5t92 11q26 0 49.5 -1.5t48.5 -3.5v-50q-23 2 -47 3.5t-47 1.5q-79 0 -111.5 -31.5t-32.5 -108.5v-628h-57v626z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="726" 
d="M88 428h244v193q0 51 10.5 88.5t34.5 61.5t62.5 35.5t94.5 11.5q26 0 50.5 -1.5t50.5 -3.5v-52q-25 2 -49 3.5t-48 1.5q-41 0 -69.5 -7.5t-46 -24.5t-25.5 -44t-8 -67v-195h246v-48h-246v-397q0 -51 -10.5 -88.5t-34.5 -61.5t-62 -35.5t-93 -11.5q-26 0 -50.5 1.5
t-50.5 3.5v52q25 -2 49 -3.5t48 -1.5q41 0 69 7.5t45.5 24.5t25 44t7.5 67v399h-244v48z" />
    <glyph glyph-name="Ohorn" unicode="&#x1a0;" horiz-adv-x="924" 
d="M462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q42 0 78 -4t68 -13h53q80 11 80 106h68q0 -69 -27.5 -105.5t-68.5 -49.5q66 -48 97.5 -134t31.5 -217q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5z
M462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5z" />
    <glyph glyph-name="ohorn" unicode="&#x1a1;" horiz-adv-x="726" 
d="M363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5q74 0 126 -16h51q80 11 80 106h68q0 -37 -8.5 -64t-23 -46t-34 -30.5t-41.5 -17.5q73 -69 73 -222q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5
t-56 -182.5t56 -183.5t176 -56.5z" />
    <glyph glyph-name="Uhorn" unicode="&#x1af;" horiz-adv-x="886" 
d="M443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132v527h60v-527q0 -124 62.5 -178.5t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h54q77 12 77 104h67q0 -40 -12 -69t-31.5 -48t-44.5 -28.5t-50 -12.5v-473q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5z
" />
    <glyph glyph-name="uhorn" unicode="&#x1b0;" horiz-adv-x="756" 
d="M339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367q0 -69 43 -107.5t141 -38.5q73 0 134.5 17t109.5 38v458h55q77 12 77 104h67q0 -40 -13 -69t-33 -48t-45.5 -28.5t-50.5 -12.5v-496h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="819" 
d="M494 987h68l-126 -140h-57zM355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436h368z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="719" 
d="M467 1047h67l-149 -170h-54zM359 617q-63 0 -99.5 24.5t-36.5 82.5t36.5 82t99.5 24t99.5 -24t36.5 -82t-36.5 -82.5t-99.5 -24.5zM359 654q45 0 71 15.5t26 54.5q0 38 -26 53.5t-71 15.5q-46 0 -71 -15.5t-25 -53.5q0 -39 25 -54.5t71 -15.5zM301 -16
q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379h-41l-13 40q-43 -20 -110.5 -38
t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="1258" 
d="M741 987h68l-126 -140h-57zM624 261h-378l-176 -261h-71l539 802h628v-55h-482v-316h450v-55h-450v-321h482v-55h-542v261zM624 316v431h-50l-291 -431h341z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="1172" 
d="M704 790h67l-149 -170h-54zM315 -16q-108 0 -167 40t-59 137q0 87 51 122t154 35h263v59q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54 8.5t51.5 5.5t55.5 3t65 1q113 0 169 -30.5t71 -92.5q29 63 88.5 93t155.5 30q68 0 118.5 -14
t83.5 -45t49.5 -80.5t16.5 -120.5v-34h-492q0 -64 11.5 -108.5t39 -73t75.5 -41.5t121 -13q63 0 117.5 7t99.5 16v-49q-45 -11 -97.5 -18.5t-121.5 -7.5q-97 0 -158.5 21t-95.5 66q-48 -34 -123.5 -60.5t-168.5 -26.5zM838 516q-55 0 -95.5 -11t-67 -35t-40.5 -61.5
t-18 -90.5h435q-2 54 -14.5 91.5t-38 61t-65.5 34.5t-96 11zM318 31q82 0 150.5 23t116.5 55q-15 32 -21.5 72.5t-6.5 90.5h-257q-82 0 -117.5 -25.5t-35.5 -88.5q0 -69 40.5 -98t130.5 -29z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="924" 
d="M546 987h68l-126 -140h-57zM76 15l80 89q-74 99 -74 297q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q88 0 154.5 -19t114.5 -61l75 80l39 -31l-79 -87q76 -100 76 -299q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5q-89 0 -155.5 19t-113.5 61l-75 -80z
M144 401q0 -165 55 -251l490 543q-39 35 -95 51.5t-132 16.5q-83 0 -143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5zM462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5q0 84 -13 145.5t-41 104.5l-491 -542q39 -35 95.5 -51.5t132.5 -16.5z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="726" 
d="M471 794h67l-149 -170h-54zM69 19l56 55q-54 71 -54 202q0 155 73.5 222.5t218.5 67.5q65 0 115 -12.5t86 -40.5l56 53l36 -35l-55 -55q53 -70 53 -200q0 -156 -73 -224t-218 -68q-130 0 -201 53l-56 -53zM131 276q0 -105 37 -161l356 356v1h-3l1 1q-54 41 -159 41
q-120 0 -176 -55.5t-56 -182.5zM363 36q120 0 176 56.5t56 183.5q0 104 -37 160l-357 -357l2 -1h1q54 -42 159 -42z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="804" 
d="M112 83q51 -15 123.5 -28t158.5 -13q72 0 121 8.5t79 27.5t43 49.5t13 75.5t-9.5 74t-36.5 48.5t-75.5 32t-126.5 24.5q-93 13 -151.5 28t-91.5 40t-45 62t-12 94q0 54 15.5 94t51 66t94 38.5t144.5 12.5q87 0 149 -10t113 -25v-59q-51 15 -116.5 25.5t-144.5 10.5
q-73 0 -120 -8.5t-74.5 -27t-38.5 -47t-11 -68.5q0 -41 9 -68t35.5 -45.5t74.5 -30.5t127 -23q93 -12 151.5 -28.5t92 -43t46 -66t12.5 -98.5q0 -61 -18 -103.5t-56.5 -68.5t-99 -37.5t-144.5 -11.5q-92 0 -161.5 11.5t-120.5 27.5v60zM373 -53h72l-137 -145h-55z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="644" 
d="M81 61q44 -9 105.5 -18t141.5 -9q52 0 87.5 4t58 15.5t32.5 31t10 50.5t-7 51t-27.5 33.5t-57.5 22t-98 15.5q-74 8 -120 19t-72 29t-35.5 44.5t-9.5 66.5q0 42 12 70.5t40 46.5t75 25.5t116 7.5q65 0 120 -7.5t100 -18.5v-49q-44 10 -95.5 17.5t-122.5 7.5
q-56 0 -92.5 -4.5t-58 -15.5t-30 -30t-8.5 -48q0 -27 6.5 -44.5t27 -29.5t57 -20t96.5 -15q74 -8 121 -20t73.5 -31t36 -48t9.5 -72q0 -45 -13.5 -74.5t-42.5 -47t-75 -25t-111 -7.5q-74 0 -139 8t-110 21v48zM291 -53h72l-137 -145h-55z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="764" 
d="M352 747h-314v55h688v-55h-314v-747h-60v747zM352 -53h72l-137 -145h-55z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="455" 
d="M318 -11q-55 0 -93 10t-60.5 31t-32.5 54t-10 78v340h-64v36l64 12v129h56v-129h215v-48h-215v-334q0 -37 7.5 -61.5t24.5 -39t44.5 -20.5t67.5 -6q39 0 78 6v-52q-40 -6 -82 -6zM254 -53h72l-137 -145h-55z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="275" 
d="M-81 -164q16 -2 38.5 -3t39.5 -1q59 0 85.5 27.5t26.5 98.5v592h57v-590q0 -93 -37.5 -133.5t-126.5 -40.5q-19 0 -42 1t-41 3v46z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="600" 
d="M267 802h66l133 -170h-53l-113 138l-113 -138h-53z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="600" 
d="M134 802h53l113 -138l113 138h53l-133 -170h-66z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="600" 
d="M300 649q-47 0 -78 7.5t-50 23t-26.5 40.5t-7.5 61h46q0 -25 5 -42.5t18 -28t35.5 -15t57.5 -4.5t57.5 4.5t35.5 15t18 28t5 42.5h46q0 -36 -7.5 -61t-26.5 -40.5t-50 -23t-78 -7.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="600" 
d="M267 754h66v-68h-66v68z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="600" 
d="M300 617q-63 0 -99.5 24.5t-36.5 82.5t36.5 82t99.5 24t99.5 -24t36.5 -82t-36.5 -82.5t-99.5 -24.5zM300 654q45 0 71 15.5t26 54.5q0 38 -26 53.5t-71 15.5q-46 0 -71 -15.5t-25 -53.5q0 -39 25 -54.5t71 -15.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="600" 
d="M253 -125q0 -25 18.5 -37t51.5 -12q23 0 40 2t30 5v-38q-15 -4 -33 -6.5t-44 -2.5q-109 0 -109 81q0 18 6 35t19.5 34.5t36 36.5t55.5 43l42 -7q-32 -24 -53.5 -42t-35 -33t-19 -29t-5.5 -30z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="600" 
d="M114 672q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-41.5 33t-46.5 15q-27 0 -39 -16t-12 -56v-20h-41v23z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="600" 
d="M235 802h64l-140 -170h-52zM430 802h64l-141 -170h-52z" />
    <glyph glyph-name="gravecomb" unicode="&#x300;" horiz-adv-x="0" 
d="M-176 802h68l136 -170h-55z" />
    <glyph glyph-name="acutecomb" unicode="&#x301;" horiz-adv-x="0" 
d="M108 802h67l-149 -170h-54z" />
    <glyph glyph-name="uni0302" unicode="&#x302;" horiz-adv-x="0" 
d="M-33 802h66l133 -170h-53l-113 138l-113 -138h-53z" />
    <glyph glyph-name="tildecomb" unicode="&#x303;" horiz-adv-x="0" 
d="M-186 672q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-42 33t-46 15q-27 0 -39 -16t-12 -56v-20h-41v23z" />
    <glyph glyph-name="uni0304" unicode="&#x304;" horiz-adv-x="0" 
d="M-149 741h298v-41h-298v41z" />
    <glyph glyph-name="uni0306" unicode="&#x306;" horiz-adv-x="0" 
d="M0 649q-47 0 -78 7.5t-50 23t-26.5 40.5t-7.5 61h46q0 -25 5 -42.5t18 -28t35.5 -15t57.5 -4.5t57.5 4.5t35.5 15t18 28t5 42.5h46q0 -36 -7.5 -61t-26.5 -40.5t-50 -23t-78 -7.5z" />
    <glyph glyph-name="uni0307" unicode="&#x307;" horiz-adv-x="0" 
d="M-33 754h66v-68h-66v68z" />
    <glyph glyph-name="uni0308" unicode="&#x308;" horiz-adv-x="0" 
d="M-138 754h56v-68h-56v68zM82 754h56v-68h-56v68z" />
    <glyph glyph-name="uni030A" unicode="&#x30a;" horiz-adv-x="0" 
d="M0 617q-63 0 -99.5 24.5t-36.5 82.5t36.5 82t99.5 24t99.5 -24t36.5 -82t-36.5 -82.5t-99.5 -24.5zM0 654q45 0 71 15.5t26 54.5q0 38 -26 53.5t-71 15.5q-46 0 -71 -15.5t-25 -53.5q0 -39 25 -54.5t71 -15.5z" />
    <glyph glyph-name="uni030B" unicode="&#x30b;" horiz-adv-x="0" 
d="M7 802h64l-140 -170h-52zM202 802h64l-141 -170h-52z" />
    <glyph glyph-name="uni030C" unicode="&#x30c;" horiz-adv-x="0" 
d="M-166 802h53l113 -138l113 138h53l-133 -170h-66z" />
    <glyph glyph-name="uni0312" unicode="&#x312;" horiz-adv-x="0" 
d="M33 632h-67l148 170h55z" />
    <glyph glyph-name="uni031B" unicode="&#x31b;" horiz-adv-x="333" 
 />
    <glyph glyph-name="uni0326" unicode="&#x326;" horiz-adv-x="0" 
d="M-33 -53h72l-137 -145h-55z" />
    <glyph glyph-name="uni0327" unicode="&#x327;" horiz-adv-x="0" 
d="M-19 1h41l-70 -70q7 1 13.5 1h14.5q62 0 85.5 -16.5t23.5 -55.5q0 -40 -24 -57t-88 -17q-49 0 -79 9v32q34 -8 78 -8q24 0 39.5 2t23.5 7t11 12.5t3 19.5q0 10 -3 18t-11 13.5t-23.5 8t-39.5 2.5q-20 0 -38.5 -1.5t-32.5 -3.5v29z" />
    <glyph glyph-name="uni0328" unicode="&#x328;" horiz-adv-x="0" 
d="M-81 -125q0 -25 18.5 -37t51.5 -12q23 0 40 2t30 5v-38q-15 -4 -33 -6.5t-44 -2.5q-109 0 -109 81q0 18 6 35t19.5 34.5t36 36.5t55.5 43l42 -7q-32 -24 -53.5 -42t-35 -33t-19 -29t-5.5 -30z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" 
d="M62 52h162q-85 43 -127.5 132.5t-42.5 222.5q0 110 18.5 188t57.5 127.5t101 72.5t149 23t149 -23t101 -72.5t57.5 -127.5t18.5 -188q0 -133 -42.5 -222.5t-127.5 -132.5h162v-52h-271v44q59 14 101 45.5t68 78t38 105.5t12 128q0 99 -14.5 168t-46 112t-82.5 62.5
t-123 19.5t-123 -19.5t-82.5 -62.5t-46 -112t-14.5 -168q0 -69 12 -128t38.5 -105.5t68 -78t100.5 -45.5v-44h-271v52z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" 
d="M131 495h-79v55h656v-55h-79v-495h-59v495h-380v-495h-59v495z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="1276" 
d="M483 987h68l115 -140h-57zM46 802h63l222 -749h18l231 749h115l231 -749h18l223 749h63l-237 -802h-113l-233 749h-18l-233 -749h-113z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="1027" 
d="M334 802h68l136 -170h-55zM36 550h61l177 -508l196 508h89l195 -507l174 508l64 -1l-193 -550h-88l-197 506l-197 -506h-88zM514 512l2 1h-4zM752 36l5 1h-1zM276 36l-2 1l-2 -1h4z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="1276" 
d="M718 987h68l-126 -140h-57zM46 802h63l222 -749h18l231 749h115l231 -749h18l223 749h63l-237 -802h-113l-233 749h-18l-233 -749h-113z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="1027" 
d="M618 794h67l-149 -170h-54zM36 550h61l177 -508l196 508h89l195 -507l174 508l64 -1l-193 -550h-88l-197 506l-197 -506h-88zM514 512l2 1h-4zM752 36l5 1h-1zM276 36l-2 1l-2 -1h4z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="1276" 
d="M493 949h57v-63h-57v63zM718 949h57v-63h-57v63zM46 802h63l222 -749h18l231 749h115l231 -749h18l223 749h63l-237 -802h-113l-233 749h-18l-233 -749h-113z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="1027" 
d="M372 754h56v-68h-56v68zM592 754h56v-68h-56v68zM36 550h61l177 -508l196 508h89l195 -507l174 508l64 -1l-193 -550h-88l-197 506l-197 -506h-88zM514 512l2 1h-4zM752 36l5 1h-1zM276 36l-2 1l-2 -1h4z" />
    <glyph glyph-name="uni1EA0" unicode="&#x1ea0;" horiz-adv-x="819" 
d="M355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436h368zM379 -105h61v-67h-61v67z" />
    <glyph glyph-name="uni1EA1" unicode="&#x1ea1;" horiz-adv-x="719" 
d="M301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379h-41l-13 40
q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5zM329 -105h61v-67h-61v67z" />
    <glyph glyph-name="uni1EA2" unicode="&#x1ea2;" horiz-adv-x="819" 
d="M384 923q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436
h368z" />
    <glyph glyph-name="uni1EA3" unicode="&#x1ea3;" horiz-adv-x="719" 
d="M334 685q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56
q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379h-41l-13 40q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5
t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5z" />
    <glyph glyph-name="uni1EA4" unicode="&#x1ea4;" horiz-adv-x="819" 
d="M373 987h74l142 -140h-55l-124 113l-124 -113h-55zM355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436h368zM518 1200h67l-149 -170h-54z" />
    <glyph glyph-name="uni1EA5" unicode="&#x1ea5;" horiz-adv-x="719" 
d="M326 802h66l133 -170h-53l-113 138l-113 -138h-53zM301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11
t79 -34t43 -58t13 -84v-379h-41l-13 40q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5zM467 1015h67l-149 -170h-54z" />
    <glyph glyph-name="uni1EA6" unicode="&#x1ea6;" horiz-adv-x="819" 
d="M373 987h74l142 -140h-55l-124 113l-124 -113h-55zM355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436h368zM234 1209h68l136 -170h-55z" />
    <glyph glyph-name="uni1EA7" unicode="&#x1ea7;" horiz-adv-x="719" 
d="M326 802h66l133 -170h-53l-113 138l-113 -138h-53zM301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11
t79 -34t43 -58t13 -84v-379h-41l-13 40q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5zM183 1024h68l136 -170h-55z" />
    <glyph glyph-name="uni1EA8" unicode="&#x1ea8;" horiz-adv-x="819" 
d="M538 1006q26 2 44.5 11t18.5 27q0 14 -10 21t-27 7q-11 0 -26.5 -4t-31.5 -18l-27 23q17 17 37.5 25.5t44.5 8.5h6q36 -1 58 -18t22 -45v-2q-1 -27 -21.5 -41t-45.5 -19v-6l-2 -15v-6h-35zM373 987h74l142 -140h-55l-124 113l-124 -113h-55zM355 802h107l322 -802h-63
l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436h368z" />
    <glyph glyph-name="uni1EA9" unicode="&#x1ea9;" horiz-adv-x="719" 
d="M483 826q26 2 44.5 11t18.5 27q0 14 -10 21t-27 7q-11 0 -26.5 -4t-31.5 -18l-27 23q17 17 37.5 25.5t44.5 8.5h6q36 -1 58 -18t22 -45v-2q-1 -27 -21.5 -41t-45.5 -19v-6l-2 -15v-6h-35zM326 802h66l133 -170h-53l-113 138l-113 -138h-53zM301 -16q-109 0 -160.5 40.5
t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379h-41l-13 40q-43 -20 -110.5 -38t-148.5 -18zM303 31
q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5z" />
    <glyph glyph-name="uni1EAA" unicode="&#x1eaa;" horiz-adv-x="819" 
d="M373 987h74l142 -140h-55l-124 113l-124 -113h-55zM355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436h368zM224 1079q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22
q0 -114 -88 -114q-34 0 -59 15t-46 33t-42 33t-46 15q-27 0 -39 -16t-12 -56v-20h-41v23z" />
    <glyph glyph-name="uni1EAB" unicode="&#x1eab;" horiz-adv-x="719" 
d="M326 802h66l133 -170h-53l-113 138l-113 -138h-53zM301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11
t79 -34t43 -58t13 -84v-379h-41l-13 40q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5zM173 894q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15
q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-42 33t-46 15q-27 0 -39 -16t-12 -56v-20h-41v23z" />
    <glyph glyph-name="uni1EAC" unicode="&#x1eac;" horiz-adv-x="819" 
d="M355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436h368zM379 -105h61v-67h-61v67zM377 1054h66l133 -170h-53l-113 138l-113 -138h-53z" />
    <glyph glyph-name="uni1EAD" unicode="&#x1ead;" horiz-adv-x="719" 
d="M301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379h-41l-13 40
q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5zM329 -105h61v-67h-61v67zM326 802h66l133 -170h-53l-113 138l-113 -138h-53z" />
    <glyph glyph-name="uni1EAE" unicode="&#x1eae;" horiz-adv-x="819" 
d="M410 842q-50 0 -83.5 8t-53.5 25.5t-28 45t-8 66.5h45q0 -28 5.5 -47.5t20 -32t39.5 -18t63 -5.5t63 5.5t39.5 18t20 32t5.5 47.5h46q0 -39 -8.5 -66.5t-28.5 -45t-53.5 -25.5t-83.5 -8zM355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23
l-171 -436h368zM518 1200h67l-149 -170h-54z" />
    <glyph glyph-name="uni1EAF" unicode="&#x1eaf;" horiz-adv-x="719" 
d="M359 649q-47 0 -78 7.5t-50 23t-26.5 40.5t-7.5 61h46q0 -25 5 -42.5t18 -28t35.5 -15t57.5 -4.5t57.5 4.5t35.5 15t18 28t5 42.5h46q0 -36 -7.5 -61t-26.5 -40.5t-50 -23t-78 -7.5zM301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56
q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379h-41l-13 40q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5
t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5zM467 994h67l-149 -170h-54z" />
    <glyph glyph-name="uni1EB0" unicode="&#x1eb0;" horiz-adv-x="819" 
d="M410 842q-50 0 -83.5 8t-53.5 25.5t-28 45t-8 66.5h45q0 -28 5.5 -47.5t20 -32t39.5 -18t63 -5.5t63 5.5t39.5 18t20 32t5.5 47.5h46q0 -39 -8.5 -66.5t-28.5 -45t-53.5 -25.5t-83.5 -8zM355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23
l-171 -436h368zM234 1209h68l136 -170h-55z" />
    <glyph glyph-name="uni1EB1" unicode="&#x1eb1;" horiz-adv-x="719" 
d="M359 649q-47 0 -78 7.5t-50 23t-26.5 40.5t-7.5 61h46q0 -25 5 -42.5t18 -28t35.5 -15t57.5 -4.5t57.5 4.5t35.5 15t18 28t5 42.5h46q0 -36 -7.5 -61t-26.5 -40.5t-50 -23t-78 -7.5zM301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56
q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379h-41l-13 40q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5
t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5zM183 1003h68l136 -170h-55z" />
    <glyph glyph-name="uni1EB2" unicode="&#x1eb2;" horiz-adv-x="819" 
d="M391 1074q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM410 842q-50 0 -83.5 8t-53.5 25.5t-28 45t-8 66.5h45q0 -28 5.5 -47.5t20 -32t39.5 -18
t63 -5.5t63 5.5t39.5 18t20 32t5.5 47.5h46q0 -39 -8.5 -66.5t-28.5 -45t-53.5 -25.5t-83.5 -8zM355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436h368z" />
    <glyph glyph-name="uni1EB3" unicode="&#x1eb3;" horiz-adv-x="719" 
d="M334 868q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM359 649q-47 0 -78 7.5t-50 23t-26.5 40.5t-7.5 61h46q0 -25 5 -42.5t18 -28t35.5 -15
t57.5 -4.5t57.5 4.5t35.5 15t18 28t5 42.5h46q0 -36 -7.5 -61t-26.5 -40.5t-50 -23t-78 -7.5zM301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5
t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379h-41l-13 40q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5z" />
    <glyph glyph-name="uni1EB4" unicode="&#x1eb4;" horiz-adv-x="819" 
d="M410 842q-50 0 -83.5 8t-53.5 25.5t-28 45t-8 66.5h45q0 -28 5.5 -47.5t20 -32t39.5 -18t63 -5.5t63 5.5t39.5 18t20 32t5.5 47.5h46q0 -39 -8.5 -66.5t-28.5 -45t-53.5 -25.5t-83.5 -8zM355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23
l-171 -436h368zM224 1079q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-42 33t-46 15q-27 0 -39 -16t-12 -56v-20h-41v23z" />
    <glyph glyph-name="uni1EB5" unicode="&#x1eb5;" horiz-adv-x="719" 
d="M359 649q-47 0 -78 7.5t-50 23t-26.5 40.5t-7.5 61h46q0 -25 5 -42.5t18 -28t35.5 -15t57.5 -4.5t57.5 4.5t35.5 15t18 28t5 42.5h46q0 -36 -7.5 -61t-26.5 -40.5t-50 -23t-78 -7.5zM301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56
q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379h-41l-13 40q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5
t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5zM173 873q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-42 33t-46 15q-27 0 -39 -16t-12 -56v-20h-41v23z" />
    <glyph glyph-name="uni1EB6" unicode="&#x1eb6;" horiz-adv-x="819" 
d="M355 802h107l322 -802h-63l-105 261h-412l-103 -261h-65zM594 316l-174 436h-23l-171 -436h368zM379 -105h61v-67h-61v67zM410 901q-47 0 -78 7.5t-50 23t-26.5 40.5t-7.5 61h46q0 -25 5 -42.5t18 -28t35.5 -15t57.5 -4.5t57.5 4.5t35.5 15t18 28t5 42.5h46
q0 -36 -7.5 -61t-26.5 -40.5t-50 -23t-78 -7.5z" />
    <glyph glyph-name="uni1EB7" unicode="&#x1eb7;" horiz-adv-x="719" 
d="M301 -16q-109 0 -160.5 40.5t-51.5 137.5q0 44 13 74.5t38.5 49.5t64 27t89.5 8h263v56q0 36 -9.5 62.5t-32.5 43.5t-62 25t-98 8q-63 0 -117 -5t-108 -13v50q29 5 54.5 8.5t51.5 5.5t55.5 3t66.5 1q72 0 121 -11t79 -34t43 -58t13 -84v-379h-41l-13 40
q-43 -20 -110.5 -38t-148.5 -18zM303 31q79 0 145.5 16.5t108.5 36.5v191h-257q-82 0 -117.5 -25.5t-35.5 -90.5q0 -34 8 -58t26 -39.5t48 -23t74 -7.5zM329 -105h61v-67h-61v67zM359 649q-47 0 -78 7.5t-50 23t-26.5 40.5t-7.5 61h46q0 -25 5 -42.5t18 -28t35.5 -15
t57.5 -4.5t57.5 4.5t35.5 15t18 28t5 42.5h46q0 -36 -7.5 -61t-26.5 -40.5t-50 -23t-78 -7.5z" />
    <glyph glyph-name="uni1EB8" unicode="&#x1eb8;" horiz-adv-x="764" 
d="M130 802h542v-55h-482v-316h451v-55h-451v-321h482v-55h-542v802zM379 -105h61v-67h-61v67z" />
    <glyph glyph-name="uni1EB9" unicode="&#x1eb9;" horiz-adv-x="687" 
d="M376 -16q-85 0 -143.5 16.5t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7t100 16v-50q-45 -11 -97.5 -18t-121.5 -7zM353 516
q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h431q0 55 -11.5 93t-36.5 62t-65 35t-97 11zM313 -105h61v-67h-61v67z" />
    <glyph glyph-name="uni1EBA" unicode="&#x1eba;" horiz-adv-x="764" 
d="M384 923q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM130 802h542v-55h-482v-316h451v-55h-451v-321h482v-55h-542v802z" />
    <glyph glyph-name="uni1EBB" unicode="&#x1ebb;" horiz-adv-x="687" 
d="M334 685q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM376 -16q-85 0 -143.5 16.5t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51
t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7t100 16v-50q-45 -11 -97.5 -18t-121.5 -7zM353 516q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h431q0 55 -11.5 93t-36.5 62t-65 35t-97 11z
" />
    <glyph glyph-name="uni1EBC" unicode="&#x1ebc;" horiz-adv-x="764" 
d="M130 802h542v-55h-482v-316h451v-55h-451v-321h482v-55h-542v802zM223 924q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-42 33t-46 15q-27 0 -39 -16t-12 -56v-20h-41v23z" />
    <glyph glyph-name="uni1EBD" unicode="&#x1ebd;" horiz-adv-x="687" 
d="M376 -16q-85 0 -143.5 16.5t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7t100 16v-50q-45 -11 -97.5 -18t-121.5 -7zM353 516
q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h431q0 55 -11.5 93t-36.5 62t-65 35t-97 11zM172 672q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-42 33t-46 15q-27 0 -39 -16
t-12 -56v-20h-41v23z" />
    <glyph glyph-name="uni1EBE" unicode="&#x1ebe;" horiz-adv-x="764" 
d="M372 987h74l142 -140h-55l-124 113l-124 -113h-55zM130 802h542v-55h-482v-316h451v-55h-451v-321h482v-55h-542v802zM517 1200h67l-149 -170h-54z" />
    <glyph glyph-name="uni1EBF" unicode="&#x1ebf;" horiz-adv-x="687" 
d="M325 802h66l133 -170h-53l-113 138l-113 -138h-53zM376 -16q-85 0 -143.5 16.5t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7
t100 16v-50q-45 -11 -97.5 -18t-121.5 -7zM353 516q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h431q0 55 -11.5 93t-36.5 62t-65 35t-97 11zM466 1015h67l-149 -170h-54z" />
    <glyph glyph-name="uni1EC0" unicode="&#x1ec0;" horiz-adv-x="764" 
d="M372 987h74l142 -140h-55l-124 113l-124 -113h-55zM130 802h542v-55h-482v-316h451v-55h-451v-321h482v-55h-542v802zM233 1209h68l136 -170h-55z" />
    <glyph glyph-name="uni1EC1" unicode="&#x1ec1;" horiz-adv-x="687" 
d="M325 802h66l133 -170h-53l-113 138l-113 -138h-53zM376 -16q-85 0 -143.5 16.5t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7
t100 16v-50q-45 -11 -97.5 -18t-121.5 -7zM353 516q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h431q0 55 -11.5 93t-36.5 62t-65 35t-97 11zM182 1024h68l136 -170h-55z" />
    <glyph glyph-name="uni1EC2" unicode="&#x1ec2;" horiz-adv-x="764" 
d="M538 1006q26 2 44.5 11t18.5 27q0 14 -10 21t-27 7q-11 0 -26.5 -4t-31.5 -18l-27 23q17 17 37.5 25.5t44.5 8.5h6q36 -1 58 -18t22 -45v-2q-1 -27 -21.5 -41t-45.5 -19v-6l-2 -15v-6h-35zM372 987h74l142 -140h-55l-124 113l-124 -113h-55zM130 802h542v-55h-482v-316
h451v-55h-451v-321h482v-55h-542v802z" />
    <glyph glyph-name="uni1EC3" unicode="&#x1ec3;" horiz-adv-x="687" 
d="M479 826q26 2 44.5 11t18.5 27q0 14 -10 21t-27 7q-11 0 -26.5 -4t-31.5 -18l-27 23q17 17 37.5 25.5t44.5 8.5h6q36 -1 58 -18t22 -45v-2q-1 -27 -21.5 -41t-45.5 -19v-6l-2 -15v-6h-35zM325 802h66l133 -170h-53l-113 138l-113 -138h-53zM376 -16q-85 0 -143.5 16.5
t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7t100 16v-50q-45 -11 -97.5 -18t-121.5 -7zM353 516q-56 0 -96.5 -11.5t-67 -35.5
t-40.5 -62t-17 -92h431q0 55 -11.5 93t-36.5 62t-65 35t-97 11z" />
    <glyph glyph-name="uni1EC4" unicode="&#x1ec4;" horiz-adv-x="764" 
d="M372 987h74l142 -140h-55l-124 113l-124 -113h-55zM130 802h542v-55h-482v-316h451v-55h-451v-321h482v-55h-542v802zM223 1079q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-42 33
t-46 15q-27 0 -39 -16t-12 -56v-20h-41v23z" />
    <glyph glyph-name="uni1EC5" unicode="&#x1ec5;" horiz-adv-x="687" 
d="M325 802h66l133 -170h-53l-113 138l-113 -138h-53zM376 -16q-85 0 -143.5 16.5t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7
t100 16v-50q-45 -11 -97.5 -18t-121.5 -7zM353 516q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h431q0 55 -11.5 93t-36.5 62t-65 35t-97 11zM172 894q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114
q-34 0 -59 15t-46 33t-42 33t-46 15q-27 0 -39 -16t-12 -56v-20h-41v23z" />
    <glyph glyph-name="uni1EC6" unicode="&#x1ec6;" horiz-adv-x="764" 
d="M130 802h542v-55h-482v-316h451v-55h-451v-321h482v-55h-542v802zM379 -105h61v-67h-61v67zM376 1054h66l133 -170h-53l-113 138l-113 -138h-53z" />
    <glyph glyph-name="uni1EC7" unicode="&#x1ec7;" horiz-adv-x="687" 
d="M376 -16q-85 0 -143.5 16.5t-94.5 51.5t-51.5 90t-15.5 133t17 133.5t52 90.5t88.5 51t125.5 16q68 0 118.5 -14t84 -45t50 -80.5t16.5 -120.5v-38h-492q1 -63 12 -107t39 -71.5t75.5 -40.5t120.5 -13q63 0 117 7t100 16v-50q-45 -11 -97.5 -18t-121.5 -7zM353 516
q-56 0 -96.5 -11.5t-67 -35.5t-40.5 -62t-17 -92h431q0 55 -11.5 93t-36.5 62t-65 35t-97 11zM313 -105h61v-67h-61v67zM325 802h66l133 -170h-53l-113 138l-113 -138h-53z" />
    <glyph glyph-name="uni1EC8" unicode="&#x1ec8;" horiz-adv-x="321" 
d="M138 923q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM130 802h60v-802h-60v802z" />
    <glyph glyph-name="uni1EC9" unicode="&#x1ec9;" horiz-adv-x="275" 
d="M112 685q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM109 550h57v-550h-57v550z" />
    <glyph glyph-name="uni1ECA" unicode="&#x1eca;" horiz-adv-x="321" 
d="M130 802h60v-802h-60v802zM129 -105h61v-67h-61v67z" />
    <glyph glyph-name="uni1ECB" unicode="&#x1ecb;" horiz-adv-x="275" 
d="M107 710h61v-67h-61v67zM109 550h57v-550h-57v550z" />
    <glyph glyph-name="uni1ECC" unicode="&#x1ecc;" horiz-adv-x="924" 
d="M462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q96 0 167.5 -23.5t119 -73.5t70.5 -129.5t23 -190.5q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5zM462 41q83 0 143 19.5t98.5 62.5t57 111.5
t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5zM432 -105h61v-67h-61v67z" />
    <glyph glyph-name="uni1ECD" unicode="&#x1ecd;" horiz-adv-x="726" 
d="M363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5t218 -67.5t73 -222.5q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5t176 -56.5zM333 -105h61v-67h-61v67z" />
    <glyph glyph-name="uni1ECE" unicode="&#x1ece;" horiz-adv-x="924" 
d="M436 923q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5
t118.5 73.5t168.5 23.5q96 0 167.5 -23.5t119 -73.5t70.5 -129.5t23 -190.5q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5zM462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5
t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5z" />
    <glyph glyph-name="uni1ECF" unicode="&#x1ecf;" horiz-adv-x="726" 
d="M337 685q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5t218 -67.5t73 -222.5
q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5t176 -56.5z" />
    <glyph glyph-name="uni1ED0" unicode="&#x1ed0;" horiz-adv-x="924" 
d="M425 987h74l142 -140h-55l-124 113l-124 -113h-55zM462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q96 0 167.5 -23.5t119 -73.5t70.5 -129.5t23 -190.5q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5z
M462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5zM570 1200h67l-149 -170h-54z" />
    <glyph glyph-name="uni1ED1" unicode="&#x1ed1;" horiz-adv-x="726" 
d="M330 802h66l133 -170h-53l-113 138l-113 -138h-53zM363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5t218 -67.5t73 -222.5q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5t176 -56.5z
M471 1015h67l-149 -170h-54z" />
    <glyph glyph-name="uni1ED2" unicode="&#x1ed2;" horiz-adv-x="924" 
d="M425 987h74l142 -140h-55l-124 113l-124 -113h-55zM462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q96 0 167.5 -23.5t119 -73.5t70.5 -129.5t23 -190.5q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5z
M462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5zM286 1209h68l136 -170h-55z" />
    <glyph glyph-name="uni1ED3" unicode="&#x1ed3;" horiz-adv-x="726" 
d="M330 802h66l133 -170h-53l-113 138l-113 -138h-53zM363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5t218 -67.5t73 -222.5q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5t176 -56.5z
M187 1024h68l136 -170h-55z" />
    <glyph glyph-name="uni1ED4" unicode="&#x1ed4;" horiz-adv-x="924" 
d="M578 1010q26 2 44.5 11t18.5 27q0 14 -10 21t-27 7q-11 0 -26.5 -4t-31.5 -18l-27 23q17 17 37.5 25.5t44.5 8.5h6q36 -1 58 -18t22 -45v-2q-1 -27 -21.5 -41t-45.5 -19v-6l-2 -15v-6h-35zM425 987h74l142 -140h-55l-124 113l-124 -113h-55zM462 -16q-97 0 -168.5 23.5
t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q96 0 167.5 -23.5t119 -73.5t70.5 -129.5t23 -190.5q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5zM462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5
t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5z" />
    <glyph glyph-name="uni1ED5" unicode="&#x1ed5;" horiz-adv-x="726" 
d="M479 826q26 2 44.5 11t18.5 27q0 14 -10 21t-27 7q-11 0 -26.5 -4t-31.5 -18l-27 23q17 17 37.5 25.5t44.5 8.5h6q36 -1 58 -18t22 -45v-2q-1 -27 -21.5 -41t-45.5 -19v-6l-2 -15v-6h-35zM330 802h66l133 -170h-53l-113 138l-113 -138h-53zM363 -16q-145 0 -218.5 68
t-73.5 224q0 155 73.5 222.5t218.5 67.5t218 -67.5t73 -222.5q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5t176 -56.5z" />
    <glyph glyph-name="uni1ED6" unicode="&#x1ed6;" horiz-adv-x="924" 
d="M425 987h74l142 -140h-55l-124 113l-124 -113h-55zM462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q96 0 167.5 -23.5t119 -73.5t70.5 -129.5t23 -190.5q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5z
M462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5zM276 1079q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15
q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-42 33t-46 15q-27 0 -39 -16t-12 -56v-20h-41v23z" />
    <glyph glyph-name="uni1ED7" unicode="&#x1ed7;" horiz-adv-x="726" 
d="M330 802h66l133 -170h-53l-113 138l-113 -138h-53zM363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5t218 -67.5t73 -222.5q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5t176 -56.5z
M177 894q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-42 33t-46 15q-27 0 -39 -16t-12 -56v-20h-41v23z" />
    <glyph glyph-name="uni1ED8" unicode="&#x1ed8;" horiz-adv-x="924" 
d="M462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q96 0 167.5 -23.5t119 -73.5t70.5 -129.5t23 -190.5q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5zM462 41q83 0 143 19.5t98.5 62.5t57 111.5
t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5zM432 -105h61v-67h-61v67zM429 1054h66l133 -170h-53l-113 138l-113 -138h-53z" />
    <glyph glyph-name="uni1ED9" unicode="&#x1ed9;" horiz-adv-x="726" 
d="M363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5t218 -67.5t73 -222.5q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5t176 -56.5zM333 -105h61v-67h-61v67zM330 802h66l133 -170h-53
l-113 138l-113 -138h-53z" />
    <glyph glyph-name="uni1EDA" unicode="&#x1eda;" horiz-adv-x="924" 
d="M462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q42 0 78 -4t68 -13h53q80 11 80 106h68q0 -69 -27.5 -105.5t-68.5 -49.5q66 -48 97.5 -134t31.5 -217q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5z
M462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5zM570 1045h67l-149 -170h-54z" />
    <glyph glyph-name="uni1EDB" unicode="&#x1edb;" horiz-adv-x="726" 
d="M363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5q74 0 126 -16h51q80 11 80 106h68q0 -37 -8.5 -64t-23 -46t-34 -30.5t-41.5 -17.5q73 -69 73 -222q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5
t-56 -182.5t56 -183.5t176 -56.5zM471 793h67l-149 -170h-54z" />
    <glyph glyph-name="uni1EDC" unicode="&#x1edc;" horiz-adv-x="924" 
d="M462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q42 0 78 -4t68 -13h53q80 11 80 106h68q0 -69 -27.5 -105.5t-68.5 -49.5q66 -48 97.5 -134t31.5 -217q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5z
M462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5zM286 1054h68l136 -170h-55z" />
    <glyph glyph-name="uni1EDD" unicode="&#x1edd;" horiz-adv-x="726" 
d="M363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5q74 0 126 -16h51q80 11 80 106h68q0 -37 -8.5 -64t-23 -46t-34 -30.5t-41.5 -17.5q73 -69 73 -222q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5
t-56 -182.5t56 -183.5t176 -56.5zM187 802h68l136 -170h-55z" />
    <glyph glyph-name="uni1EDE" unicode="&#x1ede;" horiz-adv-x="924" 
d="M436 923q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5
t118.5 73.5t168.5 23.5q42 0 78 -4t68 -13h53q80 11 80 106h68q0 -69 -27.5 -105.5t-68.5 -49.5q66 -48 97.5 -134t31.5 -217q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5zM462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5
t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5z" />
    <glyph glyph-name="uni1EDF" unicode="&#x1edf;" horiz-adv-x="726" 
d="M334 685q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5q74 0 126 -16h51
q80 11 80 106h68q0 -37 -8.5 -64t-23 -46t-34 -30.5t-41.5 -17.5q73 -69 73 -222q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5t-56 -182.5t56 -183.5t176 -56.5z" />
    <glyph glyph-name="uni1EE0" unicode="&#x1ee0;" horiz-adv-x="924" 
d="M462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q42 0 78 -4t68 -13h53q80 11 80 106h68q0 -69 -27.5 -105.5t-68.5 -49.5q66 -48 97.5 -134t31.5 -217q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5z
M462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5zM276 924q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15
q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-42 33t-46 15q-27 0 -39 -16t-12 -56v-20h-41v23z" />
    <glyph glyph-name="uni1EE1" unicode="&#x1ee1;" horiz-adv-x="726" 
d="M363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5q74 0 126 -16h51q80 11 80 106h68q0 -37 -8.5 -64t-23 -46t-34 -30.5t-41.5 -17.5q73 -69 73 -222q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5
t-56 -182.5t56 -183.5t176 -56.5zM177 672q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-42 33t-46 15q-27 0 -39 -16t-12 -56v-20h-41v23z" />
    <glyph glyph-name="uni1EE2" unicode="&#x1ee2;" horiz-adv-x="924" 
d="M462 -16q-97 0 -168.5 23.5t-118.5 73.5t-70 129t-23 191q0 111 23 190.5t70 129.5t118.5 73.5t168.5 23.5q42 0 78 -4t68 -13h53q80 11 80 106h68q0 -69 -27.5 -105.5t-68.5 -49.5q66 -48 97.5 -134t31.5 -217q0 -112 -23 -191t-70.5 -129t-119 -73.5t-167.5 -23.5z
M462 41q83 0 143 19.5t98.5 62.5t57 111.5t18.5 166.5t-18.5 166.5t-57 111.5t-98.5 62.5t-143 19.5t-143 -19.5t-99 -62.5t-57.5 -111.5t-18.5 -166.5t18.5 -166.5t57.5 -111.5t99 -62.5t143 -19.5zM432 -105h61v-67h-61v67z" />
    <glyph glyph-name="uni1EE3" unicode="&#x1ee3;" horiz-adv-x="726" 
d="M363 -16q-145 0 -218.5 68t-73.5 224q0 155 73.5 222.5t218.5 67.5q74 0 126 -16h51q80 11 80 106h68q0 -37 -8.5 -64t-23 -46t-34 -30.5t-41.5 -17.5q73 -69 73 -222q0 -156 -73 -224t-218 -68zM363 36q120 0 176 56.5t56 183.5t-56 182.5t-176 55.5t-176 -55.5
t-56 -182.5t56 -183.5t176 -56.5zM333 -105h61v-67h-61v67z" />
    <glyph glyph-name="uni1EE4" unicode="&#x1ee4;" horiz-adv-x="886" 
d="M443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132v527h60v-527q0 -124 62.5 -178.5t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h60v-527q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5zM413 -105h61v-67h-61v67z" />
    <glyph glyph-name="uni1EE5" unicode="&#x1ee5;" horiz-adv-x="756" 
d="M339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367q0 -69 43 -107.5t141 -38.5q73 0 134.5 17t109.5 38v458h57v-550h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5zM333 -105h61v-67h-61v67z" />
    <glyph glyph-name="uni1EE6" unicode="&#x1ee6;" horiz-adv-x="886" 
d="M417 923q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132v527h60v-527q0 -124 62.5 -178.5
t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h60v-527q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5z" />
    <glyph glyph-name="uni1EE7" unicode="&#x1ee7;" horiz-adv-x="756" 
d="M334 685q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367q0 -69 43 -107.5
t141 -38.5q73 0 134.5 17t109.5 38v458h57v-550h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5z" />
    <glyph glyph-name="uni1EE8" unicode="&#x1ee8;" horiz-adv-x="886" 
d="M443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132v527h60v-527q0 -124 62.5 -178.5t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h54q77 12 77 104h67q0 -40 -12 -69t-31.5 -48t-44.5 -28.5t-50 -12.5v-473q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5z
M551 1045h67l-149 -170h-54z" />
    <glyph glyph-name="uni1EE9" unicode="&#x1ee9;" horiz-adv-x="756" 
d="M339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367q0 -69 43 -107.5t141 -38.5q73 0 134.5 17t109.5 38v458h55q77 12 77 104h67q0 -40 -13 -69t-33 -48t-45.5 -28.5t-50.5 -12.5v-496h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5zM486 793h67l-149 -170
h-54z" />
    <glyph glyph-name="uni1EEA" unicode="&#x1eea;" horiz-adv-x="886" 
d="M443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132v527h60v-527q0 -124 62.5 -178.5t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h54q77 12 77 104h67q0 -40 -12 -69t-31.5 -48t-44.5 -28.5t-50 -12.5v-473q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5z
M267 1054h68l136 -170h-55z" />
    <glyph glyph-name="uni1EEB" unicode="&#x1eeb;" horiz-adv-x="756" 
d="M339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367q0 -69 43 -107.5t141 -38.5q73 0 134.5 17t109.5 38v458h55q77 12 77 104h67q0 -40 -13 -69t-33 -48t-45.5 -28.5t-50.5 -12.5v-496h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5zM202 802h68l136 -170
h-55z" />
    <glyph glyph-name="uni1EEC" unicode="&#x1eec;" horiz-adv-x="886" 
d="M436 923q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132v527h60v-527q0 -124 62.5 -178.5
t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h54q77 12 77 104h67q0 -40 -12 -69t-31.5 -48t-44.5 -28.5t-50 -12.5v-473q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5z" />
    <glyph glyph-name="uni1EED" unicode="&#x1eed;" horiz-adv-x="756" 
d="M334 685q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367q0 -69 43 -107.5
t141 -38.5q73 0 134.5 17t109.5 38v458h55q77 12 77 104h67q0 -40 -13 -69t-33 -48t-45.5 -28.5t-50.5 -12.5v-496h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5z" />
    <glyph glyph-name="uni1EEE" unicode="&#x1eee;" horiz-adv-x="886" 
d="M443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132v527h60v-527q0 -124 62.5 -178.5t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h54q77 12 77 104h67q0 -40 -12 -69t-31.5 -48t-44.5 -28.5t-50 -12.5v-473q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5z
M257 924q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-42 33t-46 15q-27 0 -39 -16t-12 -56v-20h-41v23z" />
    <glyph glyph-name="uni1EEF" unicode="&#x1eef;" horiz-adv-x="756" 
d="M339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367q0 -69 43 -107.5t141 -38.5q73 0 134.5 17t109.5 38v458h55q77 12 77 104h67q0 -40 -13 -69t-33 -48t-45.5 -28.5t-50.5 -12.5v-496h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5zM192 672q0 60 22.5 87
t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-42 33t-46 15q-27 0 -39 -16t-12 -56v-20h-41v23z" />
    <glyph glyph-name="uni1EF0" unicode="&#x1ef0;" horiz-adv-x="886" 
d="M443 -16q-84 0 -145 16.5t-101 52t-59 90.5t-19 132v527h60v-527q0 -124 62.5 -178.5t201.5 -54.5q69 0 119 13t82.5 41t47.5 72.5t15 106.5v527h54q77 12 77 104h67q0 -40 -12 -69t-31.5 -48t-44.5 -28.5t-50 -12.5v-473q0 -77 -19 -132t-59 -90.5t-101 -52t-145 -16.5z
M432 -105h61v-67h-61v67z" />
    <glyph glyph-name="uni1EF1" unicode="&#x1ef1;" horiz-adv-x="756" 
d="M339 -16q-59 0 -103 13.5t-73 38t-43.5 58.5t-14.5 75v381h57v-367q0 -69 43 -107.5t141 -38.5q73 0 134.5 17t109.5 38v458h55q77 12 77 104h67q0 -40 -13 -69t-33 -48t-45.5 -28.5t-50.5 -12.5v-496h-41l-13 40q-51 -23 -117 -39.5t-137 -16.5zM333 -105h61v-67h-61v67
z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="751" 
d="M225 987h68l115 -140h-57zM345 316l-327 486h72l286 -427l285 427h71l-327 -486v-316h-60v316z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="678" 
d="M163 802h68l136 -170h-55zM61 -163q15 -1 29.5 -1h30.5q66 0 102.5 21.5t61.5 72.5l35 73h-28l-258 547h59l236 -498h15l241 498h59l-305 -629q-18 -37 -37.5 -63t-44 -42t-56 -23t-72.5 -7q-18 0 -34.5 0.5t-33.5 2.5v48z" />
    <glyph glyph-name="uni1EF4" unicode="&#x1ef4;" horiz-adv-x="751" 
d="M345 316l-327 486h72l286 -427l285 427h71l-327 -486v-316h-60v316zM345 -105h61v-67h-61v67z" />
    <glyph glyph-name="uni1EF5" unicode="&#x1ef5;" horiz-adv-x="678" 
d="M61 -163q15 -1 29.5 -1h30.5q66 0 102.5 21.5t61.5 72.5l35 73h-28l-258 547h59l236 -498h15l241 498h59l-305 -629q-18 -37 -37.5 -63t-44 -42t-56 -23t-72.5 -7q-18 0 -34.5 0.5t-33.5 2.5v48zM368 -105h61v-67h-61v67z" />
    <glyph glyph-name="uni1EF6" unicode="&#x1ef6;" horiz-adv-x="751" 
d="M350 923q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM345 316l-327 486h72l286 -427l285 427h71l-327 -486v-316h-60v316z" />
    <glyph glyph-name="uni1EF7" unicode="&#x1ef7;" horiz-adv-x="678" 
d="M334 685q29 2 50 15t21 36q0 17 -12 26t-32 9q-13 0 -30 -5t-34 -22l-30 27q19 20 41.5 30.5t50.5 10.5h5q40 -1 64 -22t24 -54v-3q-1 -34 -24 -51t-50 -24v-8l-2 -18v-8h-36zM61 -163q15 -1 29.5 -1h30.5q66 0 102.5 21.5t61.5 72.5l35 73h-28l-258 547h59l236 -498h15
l241 498h59l-305 -629q-18 -37 -37.5 -63t-44 -42t-56 -23t-72.5 -7q-18 0 -34.5 0.5t-33.5 2.5v48z" />
    <glyph glyph-name="uni1EF8" unicode="&#x1ef8;" horiz-adv-x="751" 
d="M345 316l-327 486h72l286 -427l285 427h71l-327 -486v-316h-60v316zM190 924q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-42 33t-46 15q-27 0 -39 -16t-12 -56v-20h-41v23z" />
    <glyph glyph-name="uni1EF9" unicode="&#x1ef9;" horiz-adv-x="678" 
d="M61 -163q15 -1 29.5 -1h30.5q66 0 102.5 21.5t61.5 72.5l35 73h-28l-258 547h59l236 -498h15l241 498h59l-305 -629q-18 -37 -37.5 -63t-44 -42t-56 -23t-72.5 -7q-18 0 -34.5 0.5t-33.5 2.5v48zM153 672q0 60 22.5 87t66.5 27q34 0 59 -15t46 -33.5t42 -33.5t46 -15
q26 0 38 17.5t12 55.5v19h41v-22q0 -114 -88 -114q-34 0 -59 15t-46 33t-42 33t-46 15q-27 0 -39 -16t-12 -56v-20h-41v23z" />
    <glyph glyph-name="uni2000" unicode="&#x2000;" 
 />
    <glyph glyph-name="uni2001" unicode="&#x2001;" horiz-adv-x="1520" 
 />
    <glyph glyph-name="uni2002" unicode="&#x2002;" 
 />
    <glyph glyph-name="uni2003" unicode="&#x2003;" horiz-adv-x="1520" 
 />
    <glyph glyph-name="uni2004" unicode="&#x2004;" horiz-adv-x="507" 
 />
    <glyph glyph-name="uni2005" unicode="&#x2005;" horiz-adv-x="380" 
 />
    <glyph glyph-name="uni2006" unicode="&#x2006;" horiz-adv-x="253" 
 />
    <glyph glyph-name="uni2007" unicode="&#x2007;" horiz-adv-x="726" 
 />
    <glyph glyph-name="uni2008" unicode="&#x2008;" horiz-adv-x="380" 
 />
    <glyph glyph-name="uni2009" unicode="&#x2009;" horiz-adv-x="190" 
 />
    <glyph glyph-name="uni200A" unicode="&#x200a;" horiz-adv-x="152" 
 />
    <glyph glyph-name="uni200B" unicode="&#x200b;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni200C" unicode="&#x200c;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni200D" unicode="&#x200d;" horiz-adv-x="0" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" 
d="M0 249v54h760v-54h-760z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1520" 
d="M0 249v54h1520v-54h-1520z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="380" 
d="M179 533h-63l94 269h53z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="380" 
d="M201 802h63l-94 -269h-53z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="380" 
d="M201 85h63l-94 -269h-53z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="579" 
d="M179 533h-63l94 269h53zM378 533h-63l94 269h53z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="579" 
d="M201 802h63l-94 -269h-53zM400 802h63l-94 -269h-53z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="579" 
d="M400 86h63l-94 -269h-53zM201 85h63l-94 -269h-53z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" 
d="M351 400v95h-259v55h259v252h58v-252h259v-55h-259v-95l-6 -546h-46z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" 
d="M92 161h259v334h-259v55h259v252h58v-252h259v-55h-259v-334h259v-55h-259v-252h-58v252h-259v55z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="436" 
d="M219 177q-51 0 -76 23t-25 77q0 53 25 75.5t76 22.5q49 0 74.5 -22.5t25.5 -75.5q0 -54 -25.5 -77t-74.5 -23z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="795" 
d="M151 75h78v-75h-78v75zM359 75h78v-75h-78v75zM566 75h78v-75h-78v75z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1298" 
d="M211 481q-88 0 -130.5 39t-42.5 129q0 91 42.5 130t130.5 39t131 -39t43 -130q0 -90 -43 -129t-131 -39zM209 0h-59l532 802h59zM211 521q64 0 93 29.5t29 98.5t-29 99t-93 30q-63 0 -92 -30t-29 -99t29 -98.5t92 -29.5zM678 -16q-87 0 -130.5 39t-43.5 129t43.5 129.5
t130.5 39.5t130 -39.5t43 -129.5t-43 -129t-130 -39zM1088 -16q-87 0 -130.5 39t-43.5 129t43.5 129.5t130.5 39.5t130 -39.5t43 -129.5t-43 -129t-130 -39zM678 24q63 0 92.5 29.5t29.5 98.5t-29.5 98.5t-92.5 29.5q-64 0 -93 -29.5t-29 -98.5t29 -98.5t93 -29.5zM1088 24
q64 0 93 29.5t29 98.5t-29 98.5t-93 29.5t-92.5 -29.5t-28.5 -98.5t28.5 -98.5t92.5 -29.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="321" 
d="M218 509h62l-177 -232l177 -232h-62l-177 232z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="321" 
d="M103 44h-62l177 232l-177 232h61l178 -232z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="182" 
d="M-146 0h-59l533 802h59z" />
    <glyph glyph-name="uni2060" unicode="&#x2060;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni2070" unicode="&#x2070;" horiz-adv-x="419" 
d="M210 531q-97 0 -139.5 56t-42.5 183t42.5 183t139.5 56q49 0 83.5 -13.5t56.5 -42.5t32 -74.5t10 -108.5t-10 -108.5t-32 -74.5t-56.5 -42.5t-83.5 -13.5zM210 575q36 0 60.5 10t39.5 33.5t22 60.5t7 91t-7 91t-22 60.5t-39.5 33.5t-60.5 10t-60.5 -10t-39.5 -33.5
t-22 -60.5t-7 -91t7 -91t22 -60.5t39.5 -33.5t60.5 -10z" />
    <glyph glyph-name="uni2074" unicode="&#x2074;" horiz-adv-x="419" 
d="M365 802v-262h-51v106h-293v40l176 314h55l-176 -312h238v114h51z" />
    <glyph glyph-name="uni2075" unicode="&#x2075;" horiz-adv-x="419" 
d="M42 590q32 -8 67 -11.5t79 -3.5q40 0 66 6t41.5 18t21.5 30t6 42q0 51 -27.5 72t-93.5 21h-139v236h292v-42h-241v-152h95q90 0 128 -32t38 -103q0 -75 -41.5 -107.5t-143.5 -32.5q-48 0 -83.5 4t-64.5 10v45z" />
    <glyph glyph-name="uni2076" unicode="&#x2076;" horiz-adv-x="419" 
d="M217 531q-52 0 -87 12.5t-56.5 40.5t-30.5 74t-9 112q0 65 11.5 110.5t36 74t64 41.5t95.5 13q35 0 61 -4t48 -10v-45q-25 7 -49.5 11t-58.5 4q-43 0 -72.5 -9t-47.5 -30t-26 -55t-8 -84q31 11 64 19.5t78 8.5q84 0 119 -34t35 -106q0 -40 -11.5 -68t-33.5 -44.5
t-52.5 -24t-69.5 -7.5zM221 772q-42 0 -71.5 -7t-61.5 -18q1 -50 7 -83.5t21 -53.5t40 -28.5t62 -8.5q54 0 83.5 21t29.5 79q0 27 -5 46t-17.5 30.5t-34 17t-53.5 5.5z" />
    <glyph glyph-name="uni2077" unicode="&#x2077;" horiz-adv-x="419" 
d="M34 1000h352v-40l-244 -420h-61l248 418h-295v42z" />
    <glyph glyph-name="uni2078" unicode="&#x2078;" horiz-adv-x="419" 
d="M210 531q-97 0 -138 30.5t-41 96.5q0 54 22 80t64 34q-37 10 -57 35.5t-20 76.5q0 64 39 94.5t131 30.5t131 -30.5t39 -94.5q0 -51 -20 -76.5t-56 -35.5q41 -8 63.5 -34t22.5 -80q0 -66 -41.5 -96.5t-138.5 -30.5zM210 792q32 0 54.5 4t36.5 13.5t20 26t6 41.5
q0 45 -24 67t-93 22q-34 0 -57 -5.5t-36.5 -16.5t-19 -27.5t-5.5 -39.5q1 -50 27.5 -67.5t90.5 -17.5zM210 574q37 0 61 5.5t38.5 16.5t20.5 28t6 40q0 27 -7 44t-22 27.5t-39 14.5t-58 4q-67 0 -97 -18.5t-30 -71.5q0 -46 27 -68t100 -22z" />
    <glyph glyph-name="uni2079" unicode="&#x2079;" horiz-adv-x="419" 
d="M202 1009q52 0 87 -12.5t56.5 -40.5t30.5 -74t9 -112q0 -65 -11 -110.5t-36 -74t-64.5 -41.5t-95.5 -13q-35 0 -61 4t-48 10v45q25 -7 49.5 -11t57.5 -4q44 0 73.5 9t47.5 30t26 55.5t8 84.5q-31 -12 -63.5 -20.5t-79.5 -8.5q-83 0 -118 34t-35 106q0 81 44.5 112.5
t122.5 31.5zM88 867q0 -27 5 -46t17.5 -31t34 -17t53.5 -5q42 0 71.5 7t61.5 18q-1 50 -7 83.5t-21 53.5t-39.5 28.5t-62.5 8.5q-55 0 -84 -21t-29 -79z" />
    <glyph glyph-name="uni2080" unicode="&#x2080;" horiz-adv-x="419" 
d="M210 -207q-97 0 -139.5 56t-42.5 183t42.5 183t139.5 56q49 0 83.5 -13.5t56.5 -42.5t32 -74.5t10 -108.5t-10 -108.5t-32 -74.5t-56.5 -42.5t-83.5 -13.5zM210 -163q36 0 60.5 10t39.5 33.5t22 60.5t7 91t-7 91t-22 60.5t-39.5 33.5t-60.5 10t-60.5 -10t-39.5 -33.5
t-22 -60.5t-7 -91t7 -91t22 -60.5t39.5 -33.5t60.5 -10z" />
    <glyph glyph-name="uni2081" unicode="&#x2081;" horiz-adv-x="320" 
d="M196 262h49v-460h-51v409l-154 -73l-22 41z" />
    <glyph glyph-name="uni2082" unicode="&#x2082;" horiz-adv-x="419" 
d="M199 271q94 0 133.5 -27.5t39.5 -87.5q0 -24 -4.5 -41t-15.5 -30.5t-29 -25.5t-45 -28l-130 -74q-16 -10 -26 -17.5t-15.5 -15.5t-7 -18t-1.5 -25v-37h279v-42h-331v79q0 22 3.5 37.5t12 28t23.5 23.5t38 24l132 77q19 11 31.5 19t19.5 16.5t10 19.5t3 27q0 38 -25.5 56
t-97.5 18q-37 0 -72.5 -4.5t-63.5 -10.5v45q29 6 61.5 10t77.5 4z" />
    <glyph glyph-name="uni2083" unicode="&#x2083;" horiz-adv-x="419" 
d="M43 -149q32 -8 66.5 -11.5t77.5 -3.5q42 0 68.5 5.5t42.5 16t22 27t6 39.5q0 27 -6.5 44t-20.5 27.5t-36.5 14.5t-54.5 4h-109v42h109q59 0 84.5 16.5t25.5 68.5q0 23 -5.5 39t-20 27t-38.5 16t-61 5q-42 0 -75.5 -3.5t-63.5 -11.5v44q26 5 61 9.5t80 4.5
q97 0 136.5 -29.5t39.5 -94.5q0 -51 -19.5 -77t-54.5 -36q40 -8 61.5 -34.5t21.5 -80.5q0 -66 -43 -96t-148 -30q-47 0 -82.5 4t-63.5 10v44z" />
    <glyph glyph-name="uni2084" unicode="&#x2084;" horiz-adv-x="419" 
d="M365 64v-262h-51v106h-293v40l176 314h55l-176 -312h238v114h51z" />
    <glyph glyph-name="uni2085" unicode="&#x2085;" horiz-adv-x="419" 
d="M42 -148q32 -8 67 -11.5t79 -3.5q40 0 66 6t41.5 18t21.5 30t6 42q0 51 -27.5 72t-93.5 21h-139v236h292v-42h-241v-152h95q90 0 128 -32t38 -103q0 -75 -41.5 -107.5t-143.5 -32.5q-48 0 -83.5 4t-64.5 10v45z" />
    <glyph glyph-name="uni2086" unicode="&#x2086;" horiz-adv-x="419" 
d="M217 -207q-52 0 -87 12.5t-56.5 40.5t-30.5 74t-9 112q0 65 11.5 110.5t36 74t64 41.5t95.5 13q35 0 61 -4t48 -10v-45q-25 7 -49.5 11t-58.5 4q-43 0 -72.5 -9t-47.5 -30t-26 -55t-8 -84q31 11 64 19.5t78 8.5q84 0 119 -34t35 -106q0 -41 -11.5 -68.5t-33.5 -44
t-52.5 -24t-69.5 -7.5zM221 34q-42 0 -71.5 -7t-61.5 -18q1 -50 7 -83.5t21 -53.5t40 -28.5t62 -8.5q54 0 83.5 21t29.5 79q0 27 -5 46t-17.5 30.5t-34 17t-53.5 5.5z" />
    <glyph glyph-name="uni2087" unicode="&#x2087;" horiz-adv-x="419" 
d="M34 262h352v-40l-244 -420h-61l248 418h-295v42z" />
    <glyph glyph-name="uni2088" unicode="&#x2088;" horiz-adv-x="419" 
d="M210 -207q-97 0 -138 30.5t-41 96.5q0 54 22 80t64 34q-37 10 -57 35.5t-20 76.5q0 64 39 94.5t131 30.5t131 -30.5t39 -94.5q0 -51 -20 -76.5t-56 -35.5q41 -8 63.5 -34t22.5 -80q0 -66 -41.5 -96.5t-138.5 -30.5zM210 54q32 0 54.5 4t36.5 13.5t20 26t6 41.5
q0 45 -24 67t-93 22q-34 0 -57 -5.5t-36.5 -16.5t-19 -27.5t-5.5 -39.5q1 -50 27.5 -67.5t90.5 -17.5zM210 -164q37 0 61 5.5t38.5 16.5t20.5 28t6 40q0 27 -7 44t-22 27.5t-39 14.5t-58 4q-67 0 -97 -18.5t-30 -71.5q0 -46 27 -68t100 -22z" />
    <glyph glyph-name="uni2089" unicode="&#x2089;" horiz-adv-x="419" 
d="M202 271q52 0 87 -12.5t56.5 -40.5t30.5 -74t9 -112q0 -65 -11 -110.5t-36 -74t-64.5 -41.5t-95.5 -13q-35 0 -61 4t-48 10v45q25 -7 49.5 -11t57.5 -4q44 0 73.5 9t47.5 30t26 55.5t8 84.5q-31 -12 -63.5 -20.5t-79.5 -8.5q-83 0 -118 34t-35 106q0 81 44.5 112.5
t122.5 31.5zM88 129q0 -27 5 -46t17.5 -31t34 -17t53.5 -5q42 0 71.5 7t61.5 18q-1 50 -7 83.5t-21 53.5t-39.5 28.5t-62.5 8.5q-55 0 -84 -21t-29 -79z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="726" 
d="M41 328h97q-1 17 -1.5 35t-0.5 38t0.5 38.5t1.5 35.5h-97v48h101q9 78 31.5 134t61.5 92t96 52.5t134 16.5q61 0 104 -7t81 -18v-53q-41 11 -84 17.5t-100 6.5q-63 0 -109 -13t-77.5 -41.5t-49.5 -74.5t-26 -112h402v-48h-406q-1 -17 -1.5 -35.5t-0.5 -38.5t0.5 -38
t1.5 -35h406v-49h-402q8 -66 26 -112t49.5 -74.5t77.5 -41.5t109 -13q57 0 100 6.5t84 17.5v-53q-38 -11 -81 -18t-104 -7q-77 0 -134 16.5t-96 52.5t-61.5 92t-31.5 134h-101v49z" />
    <glyph glyph-name="uni2113" unicode="&#x2113;" 
d="M438 -16q-49 0 -86 11.5t-62.5 37.5t-39.5 69t-17 106q-20 -17 -40 -32.5t-39 -29.5l-28 43q24 20 51 41t55 44v348q0 53 10.5 90.5t32 61t54 34t76.5 10.5q39 0 70 -7.5t52.5 -25.5t33.5 -48t12 -75q0 -51 -24 -104t-64 -105t-91 -102.5t-105 -96.5v-13q0 -56 8 -95
t26 -63.5t46.5 -35.5t69.5 -11q61 0 103.5 34t81.5 90l43 -33q-47 -67 -100.5 -105t-128.5 -38zM289 322q44 39 84.5 80t72 84t50.5 85.5t19 84.5q0 64 -29 87t-83 23q-62 0 -88 -33t-26 -108v-303z" />
    <glyph glyph-name="uni2116" unicode="&#x2116;" horiz-adv-x="1462" 
d="M129 802h80l480 -717v717h60v-802h-82l-478 714v-714h-60v802zM1124 159q-120 0 -180.5 56.5t-60.5 186.5t60.5 186t180.5 56q119 0 179.5 -56t60.5 -186t-60.5 -186.5t-179.5 -56.5zM1124 211q94 0 137.5 44.5t43.5 146.5q0 100 -43.5 144.5t-137.5 44.5
q-95 0 -138.5 -44.5t-43.5 -144.5q0 -102 43.5 -146.5t138.5 -44.5zM912 55h423v-55h-423v55z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="924" 
d="M159 756h-121v46h294v-46h-121v-355h-52v355zM417 802h68l141 -269l141 269h68v-401h-53v322l-126 -238h-61l-126 238v-322h-52v401z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="924" 
d="M195 381v-258q42 -48 108 -70t159 -22q115 0 189 34.5t112 112.5h59q-41 -102 -130.5 -148t-229.5 -46q-101 0 -175.5 23.5t-123.5 73.5t-73 129.5t-24 190.5t24 190t73 129.5t123.5 74t175.5 23.5t175 -23.5t123.5 -74t73.5 -129.5t24 -190v-20h-663zM462 771
q-93 0 -159 -22t-108 -70v-254h534v253q-42 49 -108 71t-159 22z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" 
d="M94 276l294 294l36 -36l-231 -231h567v-54h-568l232 -232l-36 -36z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" 
d="M85 323l295 294l294 -294l-35 -36l-232 231v-518h-54v519l-232 -232z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" 
d="M340 12l240 240h-580v48h580l-240 239l31 31l295 -294l-295 -295z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" 
d="M356 20v530h48v-530l240 239l30 -31l-294 -294l-294 294l31 31z" />
    <glyph glyph-name="uni2196" unicode="&#x2196;" 
d="M136 514h416v-44h-337l409 -410l-34 -34l-410 409v-337h-44v416z" />
    <glyph glyph-name="uni2197" unicode="&#x2197;" 
d="M581 134v339l-410 -410l-34 34l410 410h-338v43h416v-416h-44z" />
    <glyph glyph-name="uni2198" unicode="&#x2198;" 
d="M624 0h-416v44h337l-409 410l35 34l409 -409v337h44v-416z" />
    <glyph glyph-name="uni2199" unicode="&#x2199;" 
d="M180 416v-338l410 410l34 -34l-409 -410h337v-44h-416v416h44z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="765" 
d="M387 -16q-145 0 -218 68t-73 222q0 76 18 130.5t52.5 89.5t85.5 51.5t116 16.5q54 0 108 -13.5t101 -31.5q-16 41 -42 78t-64 72t-89.5 68t-118.5 67h88q92 -47 153.5 -97.5t98.5 -110.5t52 -132t15 -161q0 -87 -18 -147t-53 -98t-88 -55t-124 -17zM155 274
q0 -127 56 -183.5t176 -56.5q58 0 100 14.5t69.5 46.5t40.5 82.5t13 123.5q0 93 -16 163q-45 19 -99 33.5t-117 14.5q-113 0 -168 -55t-55 -183z" />
    <glyph glyph-name="uni2206" unicode="&#x2206;" 
d="M330 802h102l298 -802h-700zM645 55l-252 692h-21l-257 -692h530z" />
    <glyph glyph-name="product" unicode="&#x220f;" 
d="M131 747h-79v55h656v-55h-79v-856h-59v856h-380v-856h-59v856z" />
    <glyph glyph-name="summation" unicode="&#x2211;" 
d="M89 -70l345 417l-339 416v39h578v-55h-490l327 -400l-331 -401h494v-55h-584v39z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M114 300h533v-49h-533v49z" />
    <glyph glyph-name="radical" unicode="&#x221a;" 
d="M47 550h201l147 -485l275 922h60l-295 -987h-80l-151 497h-157v53z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" 
d="M191 191q-30 0 -57 8.5t-47 30t-32 58t-12 92.5t12 92.5t32 58t47 30t57 8.5q49 0 88.5 -27t71.5 -75l30 -45l30 45q32 48 70.5 75t87.5 27q30 0 57 -8.5t47 -30t32 -58t12 -92.5t-12 -92.5t-32 -58t-47 -30t-57 -8.5q-49 0 -89.5 27.5t-72.5 76.5l-29 43l-28 -43
q-32 -48 -71 -76t-88 -28zM191 237q38 0 67 21.5t52 56.5l42 63l-47 69q-23 35 -50 55.5t-64 20.5q-21 0 -39.5 -7.5t-32 -24.5t-21 -44.5t-7.5 -66.5t7.5 -66.5t21 -44.5t32 -24.5t39.5 -7.5zM453 315q23 -35 51 -56.5t65 -21.5q21 0 39.5 7.5t32 24.5t21 44.5t7.5 66.5
t-7.5 66.5t-21 44.5t-32 24.5t-39.5 7.5q-38 0 -66.5 -20.5t-51.5 -55.5l-43 -65z" />
    <glyph glyph-name="integral" unicode="&#x222b;" 
d="M80 -158q23 -2 46.5 -3.5t46.5 -1.5q40 0 66.5 7.5t42.5 24t24 43t12 64.5l67 650q5 50 16 86t33 59.5t58 35t90 11.5q26 0 49.5 -1.5t48.5 -3.5v-51q-23 2 -46.5 3.5t-46.5 1.5q-40 0 -66.5 -7.5t-42.5 -24t-24 -43t-12 -64.5l-67 -650q-5 -50 -16 -86t-33 -59.5
t-58 -35t-90 -11.5q-26 0 -49.5 1.5t-48.5 3.5v51z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M114 355q0 70 30.5 102t92.5 32q42 0 78.5 -19t71 -41.5t67.5 -41.5t68 -19q41 0 59.5 20.5t18.5 68.5v25h47v-28q0 -135 -120 -135q-43 0 -79.5 19t-70.5 42t-67 42t-68 19q-43 0 -62 -20.5t-19 -69.5v-24h-47v28zM114 103q0 70 30.5 102t92.5 32q42 0 78.5 -19
t71 -41.5t67.5 -41.5t68 -19q41 0 59.5 20.5t18.5 68.5v25h47v-28q0 -135 -120 -135q-43 0 -79.5 19t-70.5 42t-67 42t-68 19q-43 0 -62 -20.5t-19 -69.5v-24h-47v28z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M114 146h170l139 261h-309v50h335l50 93h56l-50 -93h142v-50h-168l-140 -261h308v-50h-335l-51 -96h-55l51 96h-143v50z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M114 376l533 183v-53l-480 -164v-6l480 -165v-52l-533 182v75zM114 50h533v-50h-533v50z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M114 171l480 165v6l-480 164v53l533 -183v-75l-533 -182v52zM114 50h533v-50h-533v50z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" 
d="M96 401l247 401h74l247 -401l-247 -401h-74zM380 48l220 353l-220 354l-220 -354z" />
    <glyph glyph-name="uni25CC" unicode="&#x25cc;" horiz-adv-x="650" 
d="M325 -10q-59 0 -111 22.5t-90.5 61t-61 90.5t-22.5 111t22.5 111t61 90.5t90.5 61t111 22.5t111 -22.5t90.5 -61t61 -90.5t22.5 -111t-22.5 -111t-61 -90.5t-90.5 -61t-111 -22.5zM325 550q-57 0 -107 -21.5t-87.5 -59t-59 -87.5t-21.5 -107t21.5 -107t59 -87.5t87.5 -59
t107 -21.5t107 21.5t87.5 59t59 87.5t21.5 107t-21.5 107t-59 87.5t-87.5 59t-107 21.5z" />
    <glyph glyph-name="uniFEFF" unicode="&#xfeff;" horiz-adv-x="0" 
 />
    <glyph glyph-name="glyph1" horiz-adv-x="0" 
 />
    <glyph glyph-name="ampersand.1" horiz-adv-x="866" 
d="M589 161l-26 -39q-20 -29 -41 -54t-48 -43.5t-61.5 -29t-81.5 -10.5q-125 0 -187 54t-62 178q0 54 11.5 95.5t36.5 70.5t65 46.5t97 22.5l-2 2q-27 26 -46 47.5t-30.5 42.5t-17 43t-5.5 49q0 45 12.5 79.5t42 57t77.5 34t119 11.5q58 0 106.5 -7.5t85.5 -15.5v-54
q-40 8 -87.5 15.5t-99.5 7.5q-59 0 -97.5 -7.5t-60.5 -23.5t-30.5 -41t-8.5 -60q0 -19 3.5 -34.5t13 -32t26.5 -35t43 -44.5l246 -241l162 239h64l-188 -277l211 -207h-77zM142 216q0 -93 44 -137.5t146 -44.5q34 0 59.5 7t46.5 21.5t40.5 36.5t40.5 53l32 47l-219 214
q-55 -1 -91.5 -14t-58.5 -37.5t-31 -60.5t-9 -85z" />
    <glyph glyph-name="commaaccentbelow" horiz-adv-x="600" 
d="M324 -53h72l-137 -145h-55z" />
    <glyph glyph-name="commaturnedabove" horiz-adv-x="600" 
d="M265 632h-67l148 170h55z" />
    <glyph glyph-name="gravecomb.case" horiz-adv-x="0" 
d="M-151 987h68l115 -140h-57z" />
    <glyph glyph-name="acutecomb.case" horiz-adv-x="0" 
d="M84 987h68l-126 -140h-57z" />
    <glyph glyph-name="uni030B.case" horiz-adv-x="0" 
d="M-16 987h65l-121 -140h-54zM182 987h65l-121 -140h-54z" />
    <glyph glyph-name="uni0302.case" horiz-adv-x="0" 
d="M-37 987h74l142 -140h-55l-124 113l-124 -113h-55z" />
    <glyph glyph-name="uni030C.case" horiz-adv-x="0" 
d="M-179 987h55l124 -113l124 113h55l-142 -140h-74z" />
    <glyph glyph-name="uni0306.case" horiz-adv-x="0" 
d="M0 842q-50 0 -83.5 8t-53.5 25.5t-28 45t-8 66.5h45q0 -28 5.5 -47.5t20 -32t39.5 -18t63 -5.5t63 5.5t39.5 18t20 32t5.5 47.5h46q0 -39 -8.5 -66.5t-28.5 -45t-53.5 -25.5t-83.5 -8z" />
    <glyph glyph-name="uni030A.case" horiz-adv-x="0" 
d="M0 832q-63 0 -99.5 23.5t-36.5 81.5q0 57 36.5 80.5t99.5 23.5t99.5 -23.5t36.5 -80.5q0 -58 -36.5 -81.5t-99.5 -23.5zM0 869q45 0 71 15t26 53q0 37 -26 52t-71 15q-46 0 -71 -15t-25 -52q0 -38 25 -53t71 -15z" />
    <glyph glyph-name="tildecomb.case" horiz-adv-x="0" 
d="M-186 872q0 54 24 78t70 24q32 0 56 -12.5t44.5 -27.5t40 -27.5t42.5 -12.5q29 0 42 14.5t13 49.5v11h41v-13q0 -103 -94 -103q-32 0 -56 12.5t-44.5 28t-40 28t-43.5 12.5q-29 0 -41.5 -14.5t-12.5 -50.5v-11h-41v14z" />
    <glyph glyph-name="uni0307.case" horiz-adv-x="0" 
d="M-33 949h66v-63h-66v63z" />
    <glyph glyph-name="uni0308.case" horiz-adv-x="0" 
d="M-141 949h57v-63h-57v63zM84 949h57v-63h-57v63z" />
    <glyph glyph-name="uni0304.case" horiz-adv-x="0" 
d="M-166 933h332v-39h-332v39z" />
    <glyph glyph-name="uni030C.alt" horiz-adv-x="0" 
d="M151 802h58l-96 -256h-39z" />
    <glyph glyph-name="uni0327.1" horiz-adv-x="0" 
d="M-90 -198h-56l125 199h55z" />
    <glyph glyph-name="Ccedilla.1" horiz-adv-x="868" 
d="M424 -16q-86 3 -150.5 28t-107 75.5t-63.5 128t-21 185.5q0 111 22.5 190.5t68.5 129.5t114.5 73.5t161.5 23.5q143 0 228.5 -56t119.5 -182h-64q-29 97 -99 139t-185 42q-79 0 -137 -19.5t-95 -63t-55 -111.5t-18 -166q0 -97 18 -165.5t55 -112t95 -63t137 -19.5
q116 0 185.5 42.5t98.5 139.5h64q-32 -122 -114 -179t-218 -60l-53 -53q7 1 13.5 1h14.5q62 0 85.5 -16.5t23.5 -55.5q0 -40 -24 -57t-88 -17q-49 0 -79 9v32q34 -8 78 -8q24 0 39.5 2t23.5 7t11 12.5t3 19.5q0 10 -3 18t-11 13.5t-23.5 8t-39.5 2.5q-20 0 -38.5 -1.5
t-32.5 -3.5v29z" />
    <glyph glyph-name="germandbls.cap" horiz-adv-x="1608" 
d="M112 83q51 -15 123.5 -28t158.5 -13q72 0 121 8.5t79 27.5t43 49.5t13 75.5t-9.5 74t-36.5 48.5t-75.5 32t-126.5 24.5q-93 13 -151.5 28t-91.5 40t-45 62t-12 94q0 54 15.5 94t51 66t94 38.5t144.5 12.5q87 0 149 -10t113 -25v-59q-51 15 -116.5 25.5t-144.5 10.5
q-73 0 -120 -8.5t-74.5 -27t-38.5 -47t-11 -68.5q0 -41 9 -68t35.5 -45.5t74.5 -30.5t127 -23q93 -12 151.5 -28.5t92 -43t46 -66t12.5 -98.5q0 -61 -18 -103.5t-56.5 -68.5t-99 -37.5t-144.5 -11.5q-92 0 -161.5 11.5t-120.5 27.5v60zM916 83q51 -15 123.5 -28t158.5 -13
q72 0 121 8.5t79 27.5t43 49.5t13 75.5t-9.5 74t-36.5 48.5t-75.5 32t-126.5 24.5q-93 13 -151.5 28t-91.5 40t-45 62t-12 94q0 54 15.5 94t51 66t94 38.5t144.5 12.5q87 0 149 -10t113 -25v-59q-51 15 -116.5 25.5t-144.5 10.5q-73 0 -120 -8.5t-74.5 -27t-38.5 -47
t-11 -68.5q0 -41 9 -68t35.5 -45.5t74.5 -30.5t127 -23q93 -12 151.5 -28.5t92 -43t46 -66t12.5 -98.5q0 -61 -18 -103.5t-56.5 -68.5t-99 -37.5t-144.5 -11.5q-92 0 -161.5 11.5t-120.5 27.5v60z" />
    <glyph glyph-name="ccedilla.1" horiz-adv-x="630" 
d="M328 -15q-133 7 -195 76t-62 215q0 78 17.5 133t53.5 90t91 51t131 16q60 0 110.5 -7.5t88.5 -16.5v-50q-38 8 -87.5 15t-109.5 7q-64 0 -109 -13.5t-73 -42t-40.5 -73.5t-12.5 -109t12.5 -109t40.5 -74t73 -42.5t109 -13.5q62 0 114.5 6t92.5 16v-50q-41 -11 -93 -18
t-112 -7l-53 -53q7 1 13.5 1h14.5q62 0 85.5 -16.5t23.5 -55.5q0 -40 -24 -57t-88 -17q-49 0 -79 9v32q34 -8 78 -8q24 0 39.5 2t23.5 7t11 12.5t3 19.5q0 10 -3 18t-11 13.5t-23.5 8t-39.5 2.5q-20 0 -38.5 -1.5t-32.5 -3.5v29z" />
    <glyph glyph-name="i.trk" horiz-adv-x="275" 
d="M105 754h66v-68h-66v68zM109 550h57v-550h-57v550z" />
    <glyph glyph-name="exclamdown.case" horiz-adv-x="380" 
d="M229 802v-75h-78v75h78zM213 564l6 -394v-170h-59v170l7 394h46z" />
    <glyph glyph-name="questiondown.case" horiz-adv-x="632" 
d="M440 734h-78v75h78v-75zM431 433v-42h-101q-59 0 -99.5 -11.5t-66 -33.5t-36.5 -55t-11 -77q0 -41 10 -72.5t35.5 -52.5t69.5 -32t111 -11q68 0 130 7t113 19v-54q-50 -11 -109 -18t-134 -7q-80 0 -134.5 14t-88 42t-48 68.5t-14.5 93.5q0 113 62 170t202 57h51l5 133h47
z" />
    <glyph glyph-name="hyphen.case" horiz-adv-x="380" 
d="M37 375v52h306v-52h-306z" />
    <glyph glyph-name="guilsinglleft.case" horiz-adv-x="321" 
d="M218 633h62l-177 -232l177 -232h-62l-177 232z" />
    <glyph glyph-name="guilsinglright.case" horiz-adv-x="321" 
d="M103 169h-62l177 232l-177 232h61l178 -232z" />
    <glyph glyph-name="guillemotleft.case" horiz-adv-x="540" 
d="M218 633h62l-177 -232l177 -232h-62l-177 232zM437 633h62l-177 -232l177 -232h-62l-177 232z" />
    <glyph glyph-name="guillemotright.case" horiz-adv-x="540" 
d="M103 169h-62l177 232l-177 232h61l178 -232zM322 169h-62l177 232l-177 232h61l178 -232z" />
    <glyph glyph-name="parenleft.case" horiz-adv-x="358" 
d="M269 -73q-87 88 -132 206.5t-45 267.5q0 152 47 270.5t130 203.5h63q-87 -82 -134 -203.5t-47 -270.5q0 -148 46.5 -268.5t134.5 -205.5h-63z" />
    <glyph glyph-name="parenright.case" horiz-adv-x="358" 
d="M89 875q87 -88 132 -206.5t45 -267.5q0 -152 -47 -270.5t-130 -203.5h-63q87 82 134 203.5t47 270.5q0 148 -46.5 268.5t-134.5 205.5h63z" />
    <glyph glyph-name="braceleft.case" horiz-adv-x="358" 
d="M335 -72h-35q-92 0 -131.5 33t-39.5 112v188q0 32 -4 54t-15.5 35.5t-32 19.5t-52.5 6h-23v49h23q32 0 52.5 6t32 19.5t15.5 36t4 54.5v188q0 78 39.5 111.5t131.5 33.5h35v-49h-26q-72 0 -98 -23t-26 -77v-181q0 -64 -18.5 -97.5t-59.5 -45.5q41 -12 59.5 -45.5
t18.5 -97.5v-181q0 -54 26 -77.5t98 -23.5h26v-48z" />
    <glyph glyph-name="braceright.case" horiz-adv-x="358" 
d="M228 73q0 -79 -39.5 -112t-131.5 -33h-34v48h24q36 0 60.5 5.5t39 18t20.5 31.5t6 46v181q0 64 18.5 97.5t58.5 45.5q-40 12 -58.5 45.5t-18.5 97.5v181q0 54 -26.5 77t-98.5 23h-25v49h34q92 0 131.5 -33.5t39.5 -111.5v-188q0 -32 4 -54.5t15.5 -36t32 -19.5t53.5 -6
h23v-49h-23q-33 0 -53.5 -6t-32 -19.5t-15.5 -35.5t-4 -54v-188z" />
    <glyph glyph-name="bracketleft.case" horiz-adv-x="358" 
d="M99 875h236v-49h-181v-850h181v-49h-236v948z" />
    <glyph glyph-name="bracketright.case" horiz-adv-x="358" 
d="M259 -73h-236v49h181v850h-181v49h236v-948z" />
    <glyph glyph-name="endash.case" 
d="M0 374v54h760v-54h-760z" />
    <glyph glyph-name="emdash.case" horiz-adv-x="1520" 
d="M0 374v54h1520v-54h-1520z" />
    <glyph glyph-name="bullet.case" horiz-adv-x="436" 
d="M219 302q-51 0 -76 23t-25 77q0 53 25 75.5t76 22.5q49 0 74.5 -22.5t25.5 -75.5q0 -54 -25.5 -77t-74.5 -23z" />
    <glyph glyph-name="periodcentered.case" horiz-adv-x="380" 
d="M151 439h78v-75h-78v75z" />
    <glyph glyph-name="at.case" horiz-adv-x="980" 
d="M492 -16q-104 0 -181 23t-127.5 73.5t-75 129.5t-24.5 191q0 111 25 190.5t76 129.5t127.5 73.5t179.5 23.5q102 0 178 -23.5t126 -73.5t75 -129.5t25 -190.5v-131q0 -37 -6 -66t-20.5 -48.5t-39 -29.5t-62.5 -10q-63 0 -91.5 27t-36.5 76q-32 -14 -78 -26t-101 -12
q-83 0 -128 30.5t-45 104.5q0 67 39.5 94t117.5 27h192v40q0 27 -7 46.5t-23.5 32.5t-44.5 19t-70 6q-48 0 -89 -4t-81 -10v48q21 4 40 6.5t39 4t42.5 2t50.5 0.5q56 0 93.5 -8.5t60.5 -26.5t33 -45.5t10 -66.5v-212q0 -25 2.5 -46t11 -35t24 -22t40.5 -8q26 0 41 8t22.5 22
t10 35t2.5 46v132q0 100 -21.5 170t-65.5 114.5t-110 64.5t-156 20t-157 -20t-111 -64.5t-65.5 -114.5t-21.5 -170t22 -170.5t66 -114t110.5 -63.5t156.5 -20q17 0 40 1t39 3v-48q-16 -3 -39 -4t-40 -1zM473 225q50 0 92 10.5t72 23.5v136h-178q-63 0 -89.5 -17.5
t-26.5 -62.5q0 -48 31 -69t99 -21z" />
    <glyph glyph-name="dollar.weight" horiz-adv-x="726" 
d="M89 124q48 -14 117.5 -26.5t150.5 -12.5q64 0 107.5 7t70.5 23.5t39 44t12 67.5t-8.5 65.5t-33.5 42.5t-69 28t-115 21q-83 11 -135.5 25t-82 35.5t-40.5 54.5t-11 83q0 45 12.5 79t41 57t76 35.5t117.5 14.5v92h51v-92q69 -2 122 -11.5t96 -22.5v-54q-47 14 -107 24
t-134 10q-64 0 -105.5 -7t-66.5 -22t-35 -39t-10 -59q0 -37 8 -61t31.5 -40t66 -26t111.5 -20q85 -12 139 -27t85 -38.5t42.5 -58.5t11.5 -88q0 -51 -14.5 -87t-45.5 -59t-79 -35t-116 -14v-93l-51 -1v94q-79 2 -141.5 12.5t-107.5 25.5v53z" />
    <glyph glyph-name="cent.weight" horiz-adv-x="726" 
d="M418 802v-108q63 -2 116.5 -8t94.5 -17v-53q-45 10 -105 16t-132 6q-70 0 -118.5 -13t-79 -41.5t-44 -73t-13.5 -107.5t13.5 -107.5t44 -73t79 -42t118.5 -13.5q74 0 136.5 5.5t110.5 16.5v-53q-44 -11 -99.5 -17t-121.5 -8v-111h-50v111q-76 2 -131.5 20t-91.5 53
t-53 89.5t-17 129.5t17 129t53 89.5t91.5 53t131.5 19.5v108h50z" />
    <glyph glyph-name="zeroslash" horiz-adv-x="726" 
d="M363 -16q-82 0 -141 23.5t-96 73.5t-54.5 129t-17.5 191t17.5 191t54.5 129.5t96 73.5t141 23q83 0 141.5 -23t95.5 -73.5t54.5 -129.5t17.5 -191t-17.5 -191t-54.5 -129t-95.5 -73.5t-141.5 -23.5zM115 401q0 -83 9.5 -144t29.5 -104l390 541q-30 36 -74.5 52t-106.5 16
q-67 0 -114.5 -19.5t-77 -62.5t-43 -111.5t-13.5 -167.5zM363 40q68 0 115 19.5t76.5 62.5t43 111.5t13.5 167.5q0 83 -9.5 143.5t-29.5 104.5l-390 -541q30 -36 74.5 -52t106.5 -16z" />
    <glyph glyph-name="dollar.lt" 
d="M106 124q45 -13 109.5 -25t139.5 -14v302q-76 11 -124 25t-75.5 35.5t-37.5 54t-10 80.5q0 45 12.5 79t41 57t76 35.5t117.5 14.5v92h51v-92q69 -2 122 -11.5t96 -22.5v-54q-43 13 -97.5 22.5t-120.5 11.5v-277q78 -12 127.5 -27t78 -38.5t39 -58t10.5 -85.5t-14.5 -87
t-45.5 -59t-79 -35t-116 -14v-93l-51 -1v94q-79 2 -141.5 12.5t-107.5 25.5v53zM166 587q0 -36 7 -59t27.5 -38.5t57.5 -26t97 -19.5v270q-55 -1 -91.5 -9t-58 -23t-30.5 -38.5t-9 -56.5zM603 227q0 38 -7.5 62.5t-29.5 41.5t-60 28t-100 21v-295q54 2 92 10.5t61 25
t33.5 43t10.5 63.5z" />
    <glyph glyph-name="dollar.lt.weight" 
d="M106 124q48 -14 117.5 -26.5t150.5 -12.5q64 0 107.5 7t70.5 23.5t39 44t12 67.5t-8.5 65.5t-33.5 42.5t-69 28t-115 21q-83 11 -135.5 25t-82 35.5t-40.5 54.5t-11 83q0 45 12.5 79t41 57t76 35.5t117.5 14.5v92h51v-92q69 -2 122 -11.5t96 -22.5v-54q-47 14 -107 24
t-134 10q-64 0 -105.5 -7t-66.5 -22t-35 -39t-10 -59q0 -37 8 -61t31.5 -40t66 -26t111.5 -20q85 -12 139 -27t85 -38.5t42.5 -58.5t11.5 -88q0 -51 -14.5 -87t-45.5 -59t-79 -35t-116 -14v-93l-51 -1v94q-79 2 -141.5 12.5t-107.5 25.5v53z" />
    <glyph glyph-name="Euro.lt" 
d="M58 328h97q-1 17 -1.5 35t-0.5 38t0.5 38.5t1.5 35.5h-97v48h101q9 78 31.5 134t61.5 92t96 52.5t134 16.5q61 0 104 -7t81 -18v-53q-41 11 -84 17.5t-100 6.5q-63 0 -109 -13t-77.5 -41.5t-49.5 -74.5t-26 -112h402v-48h-406q-1 -17 -1.5 -35.5t-0.5 -38.5t0.5 -38
t1.5 -35h406v-49h-402q8 -66 26 -112t49.5 -74.5t77.5 -41.5t109 -13q57 0 100 6.5t84 17.5v-53q-38 -11 -81 -18t-104 -7q-77 0 -134 16.5t-96 52.5t-61.5 92t-31.5 134h-101v49z" />
    <glyph glyph-name="cent.lt" 
d="M435 802v-108q64 -2 118 -8t96 -17v-53q-42 10 -96.5 15t-117.5 7v-471q66 1 122.5 6t101.5 16v-53q-45 -11 -101 -17t-123 -8v-111h-50v111q-77 2 -133 20t-92 53t-53.5 89.5t-17.5 129.5t17.5 129t53.5 89.5t92 53t133 19.5v108h50zM150 403q0 -60 12.5 -104
t40.5 -72.5t73 -43t109 -16.5v471q-64 -2 -109 -16.5t-73 -43t-40.5 -72t-12.5 -103.5z" />
    <glyph glyph-name="cent.lt.weight" 
d="M435 802v-108q63 -2 116.5 -8t94.5 -17v-53q-45 10 -105 16t-132 6q-70 0 -118.5 -13t-79 -41.5t-44 -73t-13.5 -107.5t13.5 -107.5t44 -73t79 -42t118.5 -13.5q74 0 136.5 5.5t110.5 16.5v-53q-44 -11 -99.5 -17t-121.5 -8v-111h-50v111q-76 2 -131.5 20t-91.5 53
t-53 89.5t-17 129.5t17 129t53 89.5t91.5 53t131.5 19.5v108h50z" />
    <glyph glyph-name="sterling.lt" 
d="M132 51q62 0 84.5 26.5t22.5 84.5v218h-161v48h161v185q0 51 12.5 89.5t42 64t77 38.5t117.5 13q64 0 111 -8t87 -17v-53q-41 9 -88 16.5t-104 7.5q-58 0 -96 -9.5t-60.5 -28.5t-32 -48t-9.5 -67v-183h347v-48h-347v-219q0 -78 -44 -110h458v-51h-632v51h54z" />
    <glyph glyph-name="yen.lt" 
d="M97 328h254v147h-254v48h212l-256 280h70l257 -279l258 278h69l-256 -279h211v-48h-253v-147h253v-49h-253v-279h-58v279h-254v49z" />
    <glyph glyph-name="florin.lt" 
d="M105 428h244v193q0 51 10.5 88.5t34.5 61.5t62.5 35.5t94.5 11.5q26 0 50.5 -1.5t50.5 -3.5v-52q-25 2 -49 3.5t-48 1.5q-41 0 -69.5 -7.5t-46 -24.5t-25.5 -44t-8 -67v-195h246v-48h-246v-397q0 -51 -10.5 -88.5t-34.5 -61.5t-62 -35.5t-93 -11.5q-26 0 -50.5 1.5
t-50.5 3.5v52q25 -2 49 -3.5t48 -1.5q41 0 69 7.5t45.5 24.5t25 44t7.5 67v399h-244v48z" />
    <glyph glyph-name="zeroslash.lt" 
d="M380 -16q-82 0 -141 23.5t-96 73.5t-54.5 129t-17.5 191t17.5 191t54.5 129.5t96 73.5t141 23q83 0 141.5 -23t95.5 -73.5t54.5 -129.5t17.5 -191t-17.5 -191t-54.5 -129t-95.5 -73.5t-141.5 -23.5zM132 401q0 -83 9.5 -144t29.5 -104l390 541q-30 36 -74.5 52t-106.5 16
q-67 0 -114.5 -19.5t-77 -62.5t-43 -111.5t-13.5 -167.5zM380 40q68 0 115 19.5t76.5 62.5t43 111.5t13.5 167.5q0 83 -9.5 143.5t-29.5 104.5l-390 -541q30 -36 74.5 -52t106.5 -16z" />
    <glyph glyph-name="zero.lt" 
d="M380 -16q-82 0 -141 23.5t-96 73.5t-54.5 129t-17.5 191t17.5 191t54.5 129.5t96 73.5t141 23q83 0 141.5 -23t95.5 -73.5t54.5 -129.5t17.5 -191t-17.5 -191t-54.5 -129t-95.5 -73.5t-141.5 -23.5zM380 40q68 0 115 19.5t76.5 62.5t43 111.5t13.5 167.5t-13.5 167.5
t-43 111.5t-76.5 62.5t-115 19.5q-67 0 -114.5 -19.5t-77 -62.5t-43 -111.5t-13.5 -167.5t13.5 -167.5t43 -111.5t77 -62.5t114.5 -19.5z" />
    <glyph glyph-name="one.lt" 
d="M121 53h271v688l-274 -130l-25 48l305 143h53v-749h249v-53h-579v53z" />
    <glyph glyph-name="two.lt" 
d="M368 818q84 0 140 -12t89 -36t47 -60t14 -85q0 -41 -7.5 -69.5t-26 -52t-49.5 -45t-77 -48.5l-230 -135q-33 -19 -52.5 -33.5t-30 -30.5t-14 -35.5t-3.5 -48.5v-74h504v-53h-562v127q0 38 5.5 65t20 48.5t39 41t63.5 41.5l233 138q38 23 63 39t39 32.5t19.5 37t5.5 52.5
q0 36 -9.5 62.5t-35 44t-71 26t-117.5 8.5q-65 0 -124.5 -7t-107.5 -18v55q48 11 103.5 18t131.5 7z" />
    <glyph glyph-name="three.lt" 
d="M97 59q52 -11 113.5 -18t134.5 -7q77 0 127.5 11t80 33t41 54t11.5 74q0 51 -12.5 84.5t-40.5 53t-71.5 27.5t-105.5 8h-178v53h178q57 0 97.5 7t66 25.5t37.5 50.5t12 82q0 43 -10.5 75t-36.5 53.5t-70.5 32t-113.5 10.5q-71 0 -130 -6.5t-109 -18.5v50q48 11 105.5 18
t134.5 7q81 0 137 -13t90 -40t49 -67.5t15 -96.5q0 -92 -35.5 -137t-98.5 -59q72 -13 111.5 -59t39.5 -143q0 -55 -16.5 -96t-54 -68.5t-98.5 -41t-150 -13.5q-80 0 -140 7t-110 18v50z" />
    <glyph glyph-name="four.lt" 
d="M635 456v-456h-58v195h-521v47l315 560h62l-311 -554h455v208h58z" />
    <glyph glyph-name="five.lt" 
d="M95 64q54 -12 116.5 -18.5t137.5 -6.5q72 0 119.5 11.5t75.5 34.5t39.5 57.5t11.5 79.5q0 49 -12.5 83t-40 55.5t-71 31t-105.5 9.5h-233v401h490v-53h-432v-295h182q75 0 129 -13t88 -41t50 -72t16 -106q0 -61 -16 -105.5t-52 -74t-94.5 -44t-143.5 -14.5
q-81 0 -142.5 7t-112.5 18v55z" />
    <glyph glyph-name="six.lt" 
d="M394 -16q-89 0 -148.5 21t-95 69.5t-50.5 128.5t-15 198q0 113 18.5 192.5t60 129.5t107.5 72.5t161 22.5q60 0 102.5 -7t80.5 -18v-55q-40 11 -83 18t-99 7q-81 0 -135.5 -18.5t-88.5 -59.5t-48.5 -107t-14.5 -161q30 11 59 20.5t60.5 16.5t67 11t78.5 4
q140 0 201.5 -57t61.5 -183q0 -69 -19.5 -116t-56 -75.5t-88 -41t-116.5 -12.5zM402 416q-41 0 -74.5 -3.5t-63.5 -10t-58.5 -15t-59.5 -19.5q2 -96 14 -159.5t40.5 -101.5t76 -54t118.5 -16q51 0 91.5 9t69 31t43.5 58.5t15 91.5q0 51 -10 87t-34.5 58.5t-65 33
t-102.5 10.5z" />
    <glyph glyph-name="seven.lt" 
d="M81 802h598v-46l-438 -756h-70l442 749h-532v53z" />
    <glyph glyph-name="eight.lt" 
d="M380 -16q-84 0 -142 13.5t-93.5 41t-51.5 69t-16 97.5q0 95 40.5 140.5t114.5 59.5q-64 15 -101 59.5t-37 134.5q0 55 15 96t49 68.5t88.5 41t133.5 13.5t133.5 -13.5t88.5 -41t49 -68.5t15 -96q0 -90 -36.5 -134.5t-101.5 -59.5q74 -14 114.5 -59.5t40.5 -140.5
q0 -56 -16 -97.5t-51.5 -69t-93.5 -41t-142 -13.5zM380 432q61 0 104 7t70 25.5t39 49.5t12 79q0 42 -10.5 74t-36 53t-69 32t-109.5 11t-109.5 -11t-69 -32t-36 -53t-10.5 -74q0 -48 12 -79t39 -49.5t70 -25.5t104 -7zM380 40q71 0 118 10.5t74.5 32t38.5 53.5t11 74
q0 50 -13.5 82.5t-42.5 51.5t-75 26.5t-111 7.5q-64 0 -110 -7.5t-75.5 -26.5t-43 -51.5t-13.5 -82.5q0 -42 11 -74t39 -53.5t74.5 -32t117.5 -10.5z" />
    <glyph glyph-name="nine.lt" 
d="M364 818q89 0 148.5 -21t95.5 -69.5t51 -128.5t15 -198q0 -113 -19 -192.5t-60.5 -129.5t-107.5 -72.5t-161 -22.5q-60 0 -102.5 7t-80.5 18v55q41 -11 83.5 -18t98.5 -7q81 0 136 18.5t89 59.5t48.5 107t14.5 161q-30 -11 -58.5 -20.5t-60 -16.5t-67.5 -11t-79 -4
q-141 0 -202.5 57t-61.5 183q0 69 19.5 116t56 75.5t88.5 41t116 12.5zM145 575q0 -51 10 -87t34 -58.5t65 -33t103 -10.5q41 0 74.5 3.5t63.5 10.5t58.5 16t59.5 19q-1 95 -13.5 158.5t-41 101.5t-76 54t-119.5 16q-51 0 -91.5 -9t-68.5 -31t-43 -58.5t-15 -91.5z" />
    <glyph glyph-name="dollar.ot" 
d="M106 134q45 -14 111 -25.5t138 -13.5v251q-77 12 -125.5 24.5t-75.5 31t-36.5 46t-9.5 69.5q0 40 12.5 69t41 48.5t76 29.5t117.5 12v84h51v-84q65 -2 120 -11t98 -22v-54q-42 13 -99 22.5t-119 11.5v-229q78 -12 127.5 -25t78 -33t39 -49.5t10.5 -73.5q0 -45 -14.5 -77
t-45.5 -52.5t-79.5 -30t-115.5 -11.5v-102l-51 -1v103q-74 2 -139 12.5t-110 24.5v55zM164 519q0 -29 6.5 -47.5t27 -31t58 -21t99.5 -18.5v222q-56 -1 -93 -7.5t-58.5 -19t-30.5 -31.5t-9 -46zM604 211q0 32 -7.5 52t-29 33.5t-60.5 23t-101 19.5v-244q55 1 92.5 7.5
t61 20.5t34 35.5t10.5 52.5z" />
    <glyph glyph-name="dollar.ot.weight" 
d="M106 134q24 -7 56 -14.5t68 -12.5t75 -8.5t78 -3.5q62 0 104 6t68 19.5t37.5 35.5t11.5 55t-8.5 54t-33.5 35t-69 23.5t-116 19.5q-84 12 -137 24.5t-82 31.5t-39.5 47t-10.5 71q0 40 12.5 69t41 48.5t76 29.5t117.5 12v84h51v-84q65 -2 120 -11t98 -22v-54
q-47 14 -110.5 24t-131.5 10q-64 0 -106.5 -5.5t-67 -18t-34.5 -32t-10 -48.5q0 -30 7.5 -49t31 -31.5t66.5 -21.5t114 -20q85 -12 139 -25t85 -33t42.5 -50t11.5 -76q0 -45 -14.5 -77t-45.5 -52.5t-79.5 -30t-115.5 -11.5v-102l-51 -1v103q-74 2 -139 12.5t-110 24.5v55z
" />
    <glyph glyph-name="Euro.ot" 
d="M58 281h97q-1 17 -1.5 34t-0.5 36t0.5 36.5t1.5 34.5h-97v48h103q10 66 33 113t62 77t95 44t131 14q61 0 104 -7t81 -18v-53q-41 11 -84 17.5t-100 6.5q-62 0 -107.5 -10.5t-77 -33.5t-50.5 -60t-28 -90h403v-48h-408q-3 -33 -3 -71q0 -19 0.5 -36t1.5 -34h409v-48h-403
q9 -53 28 -90t50.5 -60.5t77 -34t107.5 -10.5q57 0 100 6.5t84 17.5v-53q-38 -11 -81 -18t-104 -7q-76 0 -132 14t-95 44.5t-62 77.5t-32 113h-103v48z" />
    <glyph glyph-name="Euro.ot.weight" 
d="M58 378h95q2 91 21 155.5t58.5 105.5t101 60t148.5 19q61 0 104 -7t81 -18v-53q-41 11 -84 17.5t-100 6.5q-74 0 -124.5 -15t-82.5 -49t-47 -88.5t-17 -133.5h411v-48h-411q2 -81 16.5 -136.5t46 -90t82.5 -50t126 -15.5q57 0 100 6.5t84 17.5v-53q-38 -11 -81 -18
t-104 -7q-88 0 -150 19.5t-101 61t-57.5 107t-20.5 158.5h-95v48z" />
    <glyph glyph-name="cent.ot" 
d="M435 702v-80q64 -2 118 -8t96 -17v-53q-42 10 -96 15t-118 7v-427q67 1 123.5 6t100.5 16v-54q-45 -11 -101 -16.5t-123 -7.5v-83h-50v83q-77 2 -133 18t-92 49t-53.5 83t-17.5 120q0 69 17.5 119t53.5 83t92 49t133 18v80h50zM150 353q0 -54 12.5 -93.5t40.5 -65.5
t73 -39.5t109 -15.5v427q-64 -2 -109 -15t-73 -39t-40.5 -65.5t-12.5 -93.5z" />
    <glyph glyph-name="cent.ot.weight" 
d="M435 702v-80q64 -2 118 -8t96 -17v-53q-46 11 -107 16.5t-133 5.5q-71 0 -120.5 -12t-80 -37.5t-44.5 -66t-14 -97.5t14 -97.5t44.5 -66.5t80 -38t120.5 -12q75 0 138.5 5.5t111.5 16.5v-54q-45 -11 -101 -16.5t-123 -7.5v-83h-50v83q-77 2 -133 18t-92 49t-53.5 83
t-17.5 120q0 69 17.5 119t53.5 83t92 49t133 18v80h50z" />
    <glyph glyph-name="sterling.ot" 
d="M132 48q62 0 84.5 26.5t22.5 84.5v171h-161v48h161v167q0 51 13 89.5t42 64t76.5 38.5t117.5 13q64 0 111 -7.5t87 -17.5v-53q-41 10 -88 17t-104 7q-58 0 -96 -9.5t-60.5 -28.5t-32 -47.5t-9.5 -67.5v-165h347v-48h-347v-171q0 -79 -49 -111h463v-48h-632v48h54z" />
    <glyph glyph-name="yen.ot" 
d="M97 281h254v141h-254v48h207l-251 233h70l256 -233h3l256 232h69l-251 -232h206v-48h-253v-141h253v-48h-253v-233h-58v233h-254v48z" />
    <glyph glyph-name="florin.ot" 
d="M105 378h244v163q0 51 10.5 88.5t34.5 61.5t62.5 35.5t94.5 11.5q26 0 50.5 -1.5t50.5 -3.5v-52q-25 2 -49 3.5t-48 1.5q-41 0 -69.5 -7.5t-46 -24.5t-25.5 -44t-8 -67v-165h246v-48h-246v-347q0 -51 -10.5 -88.5t-34.5 -61.5t-62 -35.5t-93 -11.5q-26 0 -50.5 1.5
t-50.5 3.5v52q25 -2 49 -3.5t48 -1.5q41 0 69 7.5t45.5 24.5t25 44t7.5 67v349h-244v48z" />
    <glyph glyph-name="zeroslash.ot" 
d="M380 -16q-82 0 -141 20.5t-96 65t-54.5 114t-17.5 167.5t17.5 168t54.5 114t96 64.5t141 20.5q83 0 141.5 -20.5t95.5 -64.5t54.5 -114t17.5 -168t-17.5 -167.5t-54.5 -114t-95.5 -65t-141.5 -20.5zM132 351q0 -70 8.5 -122.5t28.5 -89.5l389 468q-30 29 -74 42.5
t-104 13.5q-67 0 -114.5 -17t-77 -54t-43 -96.5t-13.5 -144.5zM380 39q68 0 115 17t76.5 54t43 96.5t13.5 144.5q0 141 -38 213l-388 -469q29 -29 73.5 -42.5t104.5 -13.5z" />
    <glyph glyph-name="zero.ot" 
d="M380 -16q-82 0 -141 20.5t-96 65t-54.5 114t-17.5 167.5t17.5 168t54.5 114t96 64.5t141 20.5q83 0 141.5 -20.5t95.5 -64.5t54.5 -114t17.5 -168t-17.5 -167.5t-54.5 -114t-95.5 -65t-141.5 -20.5zM380 39q68 0 115 17t76.5 54t43 96.5t13.5 144.5t-13.5 144.5t-43 96.5
t-76.5 54t-115 17q-67 0 -114.5 -17t-77 -54t-43 -96.5t-13.5 -144.5t13.5 -144.5t43 -96.5t77 -54t114.5 -17z" />
    <glyph glyph-name="one.ot" 
d="M121 48h271v593l-274 -130l-25 48l305 143h53v-654h249v-48h-579v48z" />
    <glyph glyph-name="two.ot" 
d="M360 718q79 0 133 -10t87 -30.5t47.5 -52t14.5 -75.5q0 -29 -5 -53.5t-20.5 -47t-44.5 -44.5t-76 -48l-230 -124q-33 -18 -52.5 -32t-30 -28.5t-13.5 -33t-3 -45.5v-46h489v-48h-547v92q0 36 5 62t19.5 47t39 39t63.5 39l236 127q39 22 61.5 39.5t33 33.5t13 32.5
t2.5 36.5q0 30 -10 51.5t-35.5 36t-68.5 21.5t-108 7q-72 0 -136.5 -7t-115.5 -18v54q51 11 111 18t141 7z" />
    <glyph glyph-name="three.ot" 
d="M97 -41q52 -11 113.5 -18t134.5 -7q77 0 127.5 11t80 33t41 54t11.5 74q0 51 -12.5 84.5t-40.5 53t-71.5 27.5t-105.5 8h-178v53h178q57 0 97.5 7t66 25.5t37.5 50.5t12 82q0 43 -10.5 75t-36.5 53.5t-70.5 32t-113.5 10.5q-71 0 -130 -6.5t-109 -18.5v50q48 11 105.5 18
t134.5 7q81 0 137 -13t90 -40t49 -67.5t15 -96.5q0 -92 -35.5 -137t-98.5 -59q72 -13 111.5 -59t39.5 -143q0 -55 -16.5 -96t-54 -68.5t-98.5 -41t-150 -13.5q-80 0 -140 7t-110 18v50z" />
    <glyph glyph-name="four.ot" 
d="M635 356v-456h-58v195h-521v47l315 560h62l-311 -554h455v208h58z" />
    <glyph glyph-name="five.ot" 
d="M95 -36q54 -12 116.5 -18.5t137.5 -6.5q72 0 119.5 11.5t75.5 34.5t39.5 57.5t11.5 79.5q0 49 -12.5 83t-40 55.5t-71 31t-105.5 9.5h-233v401h490v-53h-432v-295h182q75 0 129 -13t88 -41t50 -72t16 -106q0 -61 -16 -105.5t-52 -74t-94.5 -44t-143.5 -14.5
q-81 0 -142.5 7t-112.5 18v55z" />
    <glyph glyph-name="six.ot" 
d="M394 -16q-89 0 -148.5 21t-95 69.5t-50.5 128.5t-15 198q0 113 18.5 192.5t60 129.5t107.5 72.5t161 22.5q60 0 102.5 -7t80.5 -18v-55q-40 11 -83 18t-99 7q-81 0 -135.5 -18.5t-88.5 -59.5t-48.5 -107t-14.5 -161q30 11 59 20.5t60.5 16.5t67 11t78.5 4
q140 0 201.5 -57t61.5 -183q0 -69 -19.5 -116t-56 -75.5t-88 -41t-116.5 -12.5zM402 416q-41 0 -74.5 -3.5t-63.5 -10t-58.5 -15t-59.5 -19.5q2 -96 14 -159.5t40.5 -101.5t76 -54t118.5 -16q51 0 91.5 9t69 31t43.5 58.5t15 91.5q0 51 -10 87t-34.5 58.5t-65 33
t-102.5 10.5z" />
    <glyph glyph-name="seven.ot" 
d="M81 702h598v-46l-438 -756h-70l442 749h-532v53z" />
    <glyph glyph-name="eight.ot" 
d="M380 -16q-84 0 -142 13.5t-93.5 41t-51.5 69t-16 97.5q0 95 40.5 140.5t114.5 59.5q-64 15 -101 59.5t-37 134.5q0 55 15 96t49 68.5t88.5 41t133.5 13.5t133.5 -13.5t88.5 -41t49 -68.5t15 -96q0 -90 -36.5 -134.5t-101.5 -59.5q74 -14 114.5 -59.5t40.5 -140.5
q0 -56 -16 -97.5t-51.5 -69t-93.5 -41t-142 -13.5zM380 432q61 0 104 7t70 25.5t39 49.5t12 79q0 42 -10.5 74t-36 53t-69 32t-109.5 11t-109.5 -11t-69 -32t-36 -53t-10.5 -74q0 -48 12 -79t39 -49.5t70 -25.5t104 -7zM380 40q71 0 118 10.5t74.5 32t38.5 53.5t11 74
q0 50 -13.5 82.5t-42.5 51.5t-75 26.5t-111 7.5q-64 0 -110 -7.5t-75.5 -26.5t-43 -51.5t-13.5 -82.5q0 -42 11 -74t39 -53.5t74.5 -32t117.5 -10.5z" />
    <glyph glyph-name="nine.ot" 
d="M364 718q89 0 148.5 -21t95.5 -69.5t51 -128.5t15 -198q0 -113 -19 -192.5t-60.5 -129.5t-107.5 -72.5t-161 -22.5q-60 0 -102.5 7t-80.5 18v55q41 -11 83.5 -18t98.5 -7q81 0 136 18.5t89 59.5t48.5 107t14.5 161q-30 -11 -58.5 -20.5t-60 -16.5t-67.5 -11t-79 -4
q-141 0 -202.5 57t-61.5 183q0 69 19.5 116t56 75.5t88.5 41t116 12.5zM145 475q0 -51 10 -87t34 -58.5t65 -33t103 -10.5q41 0 74.5 3.5t63.5 10.5t58.5 16t59.5 19q-1 95 -13.5 158.5t-41 101.5t-76 54t-119.5 16q-51 0 -91.5 -9t-68.5 -31t-43 -58.5t-15 -91.5z" />
    <glyph glyph-name="dollar.op" horiz-adv-x="726" 
d="M89 134q45 -14 111 -25.5t138 -13.5v251q-77 12 -125.5 24.5t-75.5 31t-36.5 46t-9.5 69.5q0 40 12.5 69t41 48.5t76 29.5t117.5 12v84h51v-84q65 -2 120 -11t98 -22v-54q-42 13 -99 22.5t-119 11.5v-229q78 -12 127.5 -25t78 -33t39 -49.5t10.5 -73.5q0 -45 -14.5 -77
t-45.5 -52.5t-79.5 -30t-115.5 -11.5v-102l-51 -1v103q-74 2 -139 12.5t-110 24.5v55zM147 519q0 -29 6.5 -47.5t27 -31t58 -21t99.5 -18.5v222q-56 -1 -93 -7.5t-58.5 -19t-30.5 -31.5t-9 -46zM587 211q0 32 -7.5 52t-29 33.5t-60.5 23t-101 19.5v-244q55 1 92.5 7.5
t61 20.5t34 35.5t10.5 52.5z" />
    <glyph glyph-name="dollar.op.weight" horiz-adv-x="726" 
d="M89 134q24 -7 56 -14.5t68 -12.5t75 -8.5t78 -3.5q62 0 104 6t68 19.5t37.5 35.5t11.5 55t-8.5 54t-33.5 35t-69 23.5t-116 19.5q-84 12 -137 24.5t-82 31.5t-39.5 47t-10.5 71q0 40 12.5 69t41 48.5t76 29.5t117.5 12v84h51v-84q65 -2 120 -11t98 -22v-54
q-47 14 -110.5 24t-131.5 10q-64 0 -106.5 -5.5t-67 -18t-34.5 -32t-10 -48.5q0 -30 7.5 -49t31 -31.5t66.5 -21.5t114 -20q85 -12 139 -25t85 -33t42.5 -50t11.5 -76q0 -45 -14.5 -77t-45.5 -52.5t-79.5 -30t-115.5 -11.5v-102l-51 -1v103q-74 2 -139 12.5t-110 24.5v55z
" />
    <glyph glyph-name="Euro.op" horiz-adv-x="726" 
d="M41 281h97q-1 17 -1.5 34t-0.5 36t0.5 36.5t1.5 34.5h-97v48h103q10 66 33 113t62 77t95 44t131 14q61 0 104 -7t81 -18v-53q-41 11 -84 17.5t-100 6.5q-62 0 -107.5 -10.5t-77 -33.5t-50.5 -60t-28 -90h403v-48h-408q-3 -33 -3 -71q0 -19 0.5 -36t1.5 -34h409v-48h-403
q9 -53 28 -90t50.5 -60.5t77 -34t107.5 -10.5q57 0 100 6.5t84 17.5v-53q-38 -11 -81 -18t-104 -7q-76 0 -132 14t-95 44.5t-62 77.5t-32 113h-103v48z" />
    <glyph glyph-name="Euro.op.weight" horiz-adv-x="726" 
d="M41 378h95q2 91 21 155.5t58.5 105.5t101 60t148.5 19q61 0 104 -7t81 -18v-53q-41 11 -84 17.5t-100 6.5q-74 0 -124.5 -15t-82.5 -49t-47 -88.5t-17 -133.5h411v-48h-411q2 -81 16.5 -136.5t46 -90t82.5 -50t126 -15.5q57 0 100 6.5t84 17.5v-53q-38 -11 -81 -18
t-104 -7q-88 0 -150 19.5t-101 61t-57.5 107t-20.5 158.5h-95v48z" />
    <glyph glyph-name="cent.op" horiz-adv-x="726" 
d="M418 702v-80q64 -2 118 -8t96 -17v-53q-42 10 -96 15t-118 7v-427q67 1 123.5 6t100.5 16v-54q-45 -11 -101 -16.5t-123 -7.5v-83h-50v83q-77 2 -133 18t-92 49t-53.5 83t-17.5 120q0 69 17.5 119t53.5 83t92 49t133 18v80h50zM133 353q0 -54 12.5 -93.5t40.5 -65.5
t73 -39.5t109 -15.5v427q-64 -2 -109 -15t-73 -39t-40.5 -65.5t-12.5 -93.5z" />
    <glyph glyph-name="cent.op.weight" horiz-adv-x="726" 
d="M418 702v-80q64 -2 118 -8t96 -17v-53q-46 11 -107 16.5t-133 5.5q-71 0 -120.5 -12t-80 -37.5t-44.5 -66t-14 -97.5t14 -97.5t44.5 -66.5t80 -38t120.5 -12q75 0 138.5 5.5t111.5 16.5v-54q-45 -11 -101 -16.5t-123 -7.5v-83h-50v83q-77 2 -133 18t-92 49t-53.5 83
t-17.5 120q0 69 17.5 119t53.5 83t92 49t133 18v80h50z" />
    <glyph glyph-name="sterling.op" horiz-adv-x="726" 
d="M115 48q62 0 84.5 26.5t22.5 84.5v171h-161v48h161v167q0 51 13 89.5t42 64t76.5 38.5t117.5 13q64 0 111 -7.5t87 -17.5v-53q-41 10 -88 17t-104 7q-58 0 -96 -9.5t-60.5 -28.5t-32 -47.5t-9.5 -67.5v-165h347v-48h-347v-171q0 -79 -49 -111h463v-48h-632v48h54z" />
    <glyph glyph-name="yen.op" horiz-adv-x="726" 
d="M80 281h254v141h-254v48h207l-251 233h70l256 -233h3l256 232h69l-251 -232h206v-48h-253v-141h253v-48h-253v-233h-58v233h-254v48z" />
    <glyph glyph-name="florin.op" horiz-adv-x="726" 
d="M88 378h244v163q0 51 10.5 88.5t34.5 61.5t62.5 35.5t94.5 11.5q26 0 50.5 -1.5t50.5 -3.5v-52q-25 2 -49 3.5t-48 1.5q-41 0 -69.5 -7.5t-46 -24.5t-25.5 -44t-8 -67v-165h246v-48h-246v-347q0 -51 -10.5 -88.5t-34.5 -61.5t-62 -35.5t-93 -11.5q-26 0 -50.5 1.5
t-50.5 3.5v52q25 -2 49 -3.5t48 -1.5q41 0 69 7.5t45.5 24.5t25 44t7.5 67v349h-244v48z" />
    <glyph glyph-name="zeroslash.op" horiz-adv-x="726" 
d="M363 -16q-82 0 -141 20.5t-96 65t-54.5 114t-17.5 167.5t17.5 168t54.5 114t96 64.5t141 20.5q83 0 141.5 -20.5t95.5 -64.5t54.5 -114t17.5 -168t-17.5 -167.5t-54.5 -114t-95.5 -65t-141.5 -20.5zM115 351q0 -70 8.5 -122.5t28.5 -89.5l389 468q-30 29 -74 42.5
t-104 13.5q-67 0 -114.5 -17t-77 -54t-43 -96.5t-13.5 -144.5zM363 39q68 0 115 17t76.5 54t43 96.5t13.5 144.5q0 141 -38 213l-388 -469q29 -29 73.5 -42.5t104.5 -13.5z" />
    <glyph glyph-name="zero.op" horiz-adv-x="726" 
d="M363 -16q-82 0 -141 20.5t-96 65t-54.5 114t-17.5 167.5t17.5 168t54.5 114t96 64.5t141 20.5q83 0 141.5 -20.5t95.5 -64.5t54.5 -114t17.5 -168t-17.5 -167.5t-54.5 -114t-95.5 -65t-141.5 -20.5zM363 39q68 0 115 17t76.5 54t43 96.5t13.5 144.5t-13.5 144.5t-43 96.5
t-76.5 54t-115 17q-67 0 -114.5 -17t-77 -54t-43 -96.5t-13.5 -144.5t13.5 -144.5t43 -96.5t77 -54t114.5 -17z" />
    <glyph glyph-name="one.op" horiz-adv-x="547" 
d="M347 702h52v-702h-58v638l-279 -130l-26 51z" />
    <glyph glyph-name="two.op" horiz-adv-x="726" 
d="M343 718q79 0 133 -10t87 -30.5t47.5 -52t14.5 -75.5q0 -29 -5 -53.5t-20.5 -47t-44.5 -44.5t-76 -48l-230 -124q-33 -18 -52.5 -32t-30 -28.5t-13.5 -33t-3 -45.5v-46h489v-48h-547v92q0 36 5 62t19.5 47t39 39t63.5 39l236 127q39 22 61.5 39.5t33 33.5t13 32.5
t2.5 36.5q0 30 -10 51.5t-35.5 36t-68.5 21.5t-108 7q-72 0 -136.5 -7t-115.5 -18v54q51 11 111 18t141 7z" />
    <glyph glyph-name="three.op" horiz-adv-x="726" 
d="M80 -41q52 -11 113.5 -18t134.5 -7q77 0 127.5 11t80 33t41 54t11.5 74q0 51 -12.5 84.5t-40.5 53t-71.5 27.5t-105.5 8h-178v53h178q57 0 97.5 7t66 25.5t37.5 50.5t12 82q0 43 -10.5 75t-36.5 53.5t-70.5 32t-113.5 10.5q-71 0 -130 -6.5t-109 -18.5v50q48 11 105.5 18
t134.5 7q81 0 137 -13t90 -40t49 -67.5t15 -96.5q0 -92 -35.5 -137t-98.5 -59q72 -13 111.5 -59t39.5 -143q0 -55 -16.5 -96t-54 -68.5t-98.5 -41t-150 -13.5q-80 0 -140 7t-110 18v50z" />
    <glyph glyph-name="four.op" horiz-adv-x="726" 
d="M618 356v-456h-58v195h-521v47l315 560h62l-311 -554h455v208h58z" />
    <glyph glyph-name="five.op" horiz-adv-x="726" 
d="M78 -36q54 -12 116.5 -18.5t137.5 -6.5q72 0 119.5 11.5t75.5 34.5t39.5 57.5t11.5 79.5q0 49 -12.5 83t-40 55.5t-71 31t-105.5 9.5h-233v401h490v-53h-432v-295h182q75 0 129 -13t88 -41t50 -72t16 -106q0 -61 -16 -105.5t-52 -74t-94.5 -44t-143.5 -14.5
q-81 0 -142.5 7t-112.5 18v55z" />
    <glyph glyph-name="six.op" horiz-adv-x="726" 
d="M377 -16q-89 0 -148.5 21t-95 69.5t-50.5 128.5t-15 198q0 113 18.5 192.5t60 129.5t107.5 72.5t161 22.5q60 0 102.5 -7t80.5 -18v-55q-40 11 -83 18t-99 7q-81 0 -135.5 -18.5t-88.5 -59.5t-48.5 -107t-14.5 -161q30 11 59 20.5t60.5 16.5t67 11t78.5 4
q140 0 201.5 -57t61.5 -183q0 -69 -19.5 -116t-56 -75.5t-88 -41t-116.5 -12.5zM385 416q-41 0 -74.5 -3.5t-63.5 -10t-58.5 -15t-59.5 -19.5q2 -96 14 -159.5t40.5 -101.5t76 -54t118.5 -16q51 0 91.5 9t69 31t43.5 58.5t15 91.5q0 51 -10 87t-34.5 58.5t-65 33
t-102.5 10.5z" />
    <glyph glyph-name="seven.op" horiz-adv-x="726" 
d="M64 702h598v-46l-438 -756h-70l442 749h-532v53z" />
    <glyph glyph-name="eight.op" horiz-adv-x="726" 
d="M363 -16q-84 0 -142 13.5t-93.5 41t-51.5 69t-16 97.5q0 95 40.5 140.5t114.5 59.5q-64 15 -101 59.5t-37 134.5q0 55 15 96t49 68.5t88.5 41t133.5 13.5t133.5 -13.5t88.5 -41t49 -68.5t15 -96q0 -90 -36.5 -134.5t-101.5 -59.5q74 -14 114.5 -59.5t40.5 -140.5
q0 -56 -16 -97.5t-51.5 -69t-93.5 -41t-142 -13.5zM363 432q61 0 104 7t70 25.5t39 49.5t12 79q0 42 -10.5 74t-36 53t-69 32t-109.5 11t-109.5 -11t-69 -32t-36 -53t-10.5 -74q0 -48 12 -79t39 -49.5t70 -25.5t104 -7zM363 40q71 0 118 10.5t74.5 32t38.5 53.5t11 74
q0 50 -13.5 82.5t-42.5 51.5t-75 26.5t-111 7.5q-64 0 -110 -7.5t-75.5 -26.5t-43 -51.5t-13.5 -82.5q0 -42 11 -74t39 -53.5t74.5 -32t117.5 -10.5z" />
    <glyph glyph-name="nine.op" horiz-adv-x="726" 
d="M349 718q89 0 148.5 -21t95.5 -69.5t51 -128.5t15 -198q0 -113 -19 -192.5t-60.5 -129.5t-107.5 -72.5t-161 -22.5q-60 0 -102.5 7t-80.5 18v55q41 -11 83.5 -18t98.5 -7q81 0 136 18.5t89 59.5t48.5 107t14.5 161q-30 -11 -58.5 -20.5t-60 -16.5t-67.5 -11t-79 -4
q-141 0 -202.5 57t-61.5 183q0 69 19.5 116t56 75.5t88.5 41t116 12.5zM130 475q0 -51 10 -87t34 -58.5t65 -33t103 -10.5q41 0 74.5 3.5t63.5 10.5t58.5 16t59.5 19q-1 95 -13.5 158.5t-41 101.5t-76 54t-119.5 16q-51 0 -91.5 -9t-68.5 -31t-43 -58.5t-15 -91.5z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="419" 
d="M210 333q-97 0 -139.5 56t-42.5 183t42.5 183t139.5 56q49 0 83.5 -13.5t56.5 -42.5t32 -74.5t10 -108.5t-10 -108.5t-32 -74.5t-56.5 -42.5t-83.5 -13.5zM210 377q36 0 60.5 10t39.5 33.5t22 60.5t7 91t-7 91t-22 60.5t-39.5 33.5t-60.5 10t-60.5 -10t-39.5 -33.5
t-22 -60.5t-7 -91t7 -91t22 -60.5t39.5 -33.5t60.5 -10z" />
    <glyph glyph-name="one.numr" horiz-adv-x="320" 
d="M196 802h49v-460h-51v409l-154 -73l-22 41z" />
    <glyph glyph-name="two.numr" horiz-adv-x="419" 
d="M199 811q94 0 133.5 -27.5t39.5 -87.5q0 -24 -4.5 -41t-15.5 -30.5t-29 -25.5t-45 -28l-130 -74q-16 -10 -26 -17.5t-15.5 -15.5t-7 -18t-1.5 -25v-37h279v-42h-331v79q0 22 3.5 37.5t12 28t23.5 23.5t38 24l132 77q19 11 31.5 19t19.5 16.5t10 19.5t3 27q0 38 -25.5 56
t-97.5 18q-37 0 -72.5 -4.5t-63.5 -10.5v45q29 6 61.5 10t77.5 4z" />
    <glyph glyph-name="three.numr" horiz-adv-x="419" 
d="M43 391q32 -8 66.5 -11.5t77.5 -3.5q42 0 68.5 5.5t42.5 16t22 27.5t6 39q0 27 -6.5 44t-20.5 27.5t-36.5 14.5t-54.5 4h-109v42h109q59 0 84.5 16.5t25.5 68.5q0 23 -5.5 39t-20 27t-38.5 16t-61 5q-42 0 -75.5 -3.5t-63.5 -11.5v44q26 5 61 9.5t80 4.5
q97 0 136.5 -29.5t39.5 -94.5q0 -51 -19.5 -77t-54.5 -36q40 -8 61.5 -34.5t21.5 -80.5q0 -66 -43 -96t-148 -30q-47 0 -82.5 4t-63.5 10v44z" />
    <glyph glyph-name="four.numr" horiz-adv-x="419" 
d="M365 604v-262h-51v106h-293v40l176 314h55l-176 -312h238v114h51z" />
    <glyph glyph-name="five.numr" horiz-adv-x="419" 
d="M42 392q32 -8 67 -11.5t79 -3.5q40 0 66 6t41.5 18t21.5 30t6 42q0 51 -27.5 72t-93.5 21h-139v236h292v-42h-241v-152h95q90 0 128 -32t38 -103q0 -75 -41.5 -107.5t-143.5 -32.5q-48 0 -83.5 4t-64.5 10v45z" />
    <glyph glyph-name="six.numr" horiz-adv-x="419" 
d="M217 333q-52 0 -87 12.5t-56.5 40.5t-30.5 74t-9 112q0 65 11.5 110.5t36 74t64 41.5t95.5 13q35 0 61 -4t48 -10v-45q-25 7 -49.5 11t-58.5 4q-43 0 -72.5 -9t-47.5 -30t-26 -55t-8 -84q31 11 64 19.5t78 8.5q84 0 119 -34t35 -106q0 -40 -11.5 -68t-33.5 -44.5
t-52.5 -24t-69.5 -7.5zM221 574q-42 0 -71.5 -7t-61.5 -18q1 -50 7 -83.5t21 -53.5t40 -28.5t62 -8.5q54 0 83.5 21t29.5 79q0 27 -5 46t-17.5 30.5t-34 17t-53.5 5.5z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="419" 
d="M34 802h352v-40l-244 -420h-61l248 418h-295v42z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="419" 
d="M210 333q-97 0 -138 30.5t-41 96.5q0 54 22 80t64 34q-37 10 -57 35.5t-20 76.5q0 64 39 94.5t131 30.5t131 -30.5t39 -94.5q0 -51 -20 -76.5t-56 -35.5q41 -8 63.5 -34t22.5 -80q0 -66 -41.5 -96.5t-138.5 -30.5zM210 594q32 0 54.5 4t36.5 13.5t20 26t6 41.5
q0 45 -24 67t-93 22q-34 0 -57 -5.5t-36.5 -16.5t-19 -27.5t-5.5 -39.5q1 -50 27.5 -67.5t90.5 -17.5zM210 376q37 0 61 5.5t38.5 16.5t20.5 28t6 40q0 27 -7 44t-22 27.5t-39 14.5t-58 4q-67 0 -97 -18.5t-30 -71.5q0 -46 27 -68t100 -22z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="419" 
d="M202 811q52 0 87 -12.5t56.5 -40.5t30.5 -74t9 -112q0 -65 -11 -110.5t-36 -74t-64.5 -41.5t-95.5 -13q-35 0 -61 4t-48 10v45q25 -7 49.5 -11t57.5 -4q44 0 73.5 9t47.5 30t26 55.5t8 84.5q-31 -12 -63.5 -20.5t-79.5 -8.5q-83 0 -118 34t-35 106q0 81 44.5 112.5
t122.5 31.5zM88 669q0 -27 5 -46t17.5 -31t34 -17t53.5 -5q42 0 71.5 7t61.5 18q-1 50 -7 83.5t-21 53.5t-39.5 28.5t-62.5 8.5q-55 0 -84 -21t-29 -79z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="420" 
d="M210 -9q-97 0 -139.5 56t-42.5 183t42.5 183t139.5 56q49 0 83.5 -13.5t56.5 -42.5t32 -74.5t10 -108.5t-10 -108.5t-32 -74.5t-56.5 -42.5t-83.5 -13.5zM210 35q36 0 60.5 10t39.5 33.5t22 60.5t7 91t-7 91t-22 60.5t-39.5 33.5t-60.5 10t-60.5 -10t-39.5 -33.5
t-22 -60.5t-7 -91t7 -91t22 -60.5t39.5 -33.5t60.5 -10z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="322" 
d="M196 460h49v-460h-51v409l-154 -73l-22 41z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="420" 
d="M199 469q94 0 133.5 -27.5t39.5 -87.5q0 -24 -4.5 -41t-15.5 -30.5t-29 -25.5t-45 -28l-130 -74q-16 -10 -26 -17.5t-15.5 -15.5t-7 -18t-1.5 -25v-37h279v-42h-331v79q0 22 3.5 37.5t12 28t23.5 23.5t38 24l132 77q19 11 31.5 19t19.5 16.5t10 19.5t3 27q0 38 -25.5 56
t-97.5 18q-37 0 -72.5 -4.5t-63.5 -10.5v45q29 6 61.5 10t77.5 4z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="420" 
d="M43 49q32 -8 66.5 -11.5t77.5 -3.5q42 0 68.5 5.5t42.5 16t22 27.5t6 39q0 27 -6.5 44t-20.5 27.5t-36.5 14.5t-54.5 4h-109v42h109q59 0 84.5 16.5t25.5 68.5q0 23 -5.5 39t-20 27t-38.5 16t-61 5q-42 0 -75.5 -3.5t-63.5 -11.5v44q26 5 61 9.5t80 4.5q97 0 136.5 -29.5
t39.5 -94.5q0 -51 -19.5 -77t-54.5 -36q40 -8 61.5 -34.5t21.5 -80.5q0 -66 -43 -96t-148 -30q-47 0 -82.5 4t-63.5 10v44z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="420" 
d="M365 262v-262h-51v106h-293v40l176 314h55l-176 -312h238v114h51z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="420" 
d="M42 50q32 -8 67 -11.5t79 -3.5q40 0 66 6t41.5 18t21.5 30t6 42q0 51 -27.5 72t-93.5 21h-139v236h292v-42h-241v-152h95q90 0 128 -32t38 -103q0 -75 -41.5 -107.5t-143.5 -32.5q-48 0 -83.5 4t-64.5 10v45z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="420" 
d="M217 -9q-52 0 -87 12.5t-56.5 40.5t-30.5 74t-9 112q0 65 11.5 110.5t36 74t64 41.5t95.5 13q35 0 61 -4t48 -10v-45q-25 7 -49.5 11t-58.5 4q-43 0 -72.5 -9t-47.5 -30t-26 -55t-8 -84q31 11 64 19.5t78 8.5q84 0 119 -34t35 -106q0 -40 -11.5 -68t-33.5 -44.5
t-52.5 -24t-69.5 -7.5zM221 232q-42 0 -71.5 -7t-61.5 -18q1 -50 7 -83.5t21 -53.5t40 -28.5t62 -8.5q54 0 83.5 21t29.5 79q0 27 -5 46t-17.5 30.5t-34 17t-53.5 5.5z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="420" 
d="M34 460h352v-40l-244 -420h-61l248 418h-295v42z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="420" 
d="M210 -9q-97 0 -138 30.5t-41 96.5q0 54 22 80t64 34q-37 10 -57 35.5t-20 76.5q0 64 39 94.5t131 30.5t131 -30.5t39 -94.5q0 -51 -20 -76.5t-56 -35.5q41 -8 63.5 -34t22.5 -80q0 -66 -41.5 -96.5t-138.5 -30.5zM210 252q32 0 54.5 4t36.5 13.5t20 26t6 41.5
q0 45 -24 67t-93 22q-34 0 -57 -5.5t-36.5 -16.5t-19 -27.5t-5.5 -39.5q1 -50 27.5 -67.5t90.5 -17.5zM210 34q37 0 61 5.5t38.5 16.5t20.5 28t6 40q0 27 -7 44t-22 27.5t-39 14.5t-58 4q-67 0 -97 -18.5t-30 -71.5q0 -46 27 -68t100 -22z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="420" 
d="M202 469q52 0 87 -12.5t56.5 -40.5t30.5 -74t9 -112q0 -65 -11 -110.5t-36 -74t-64.5 -41.5t-95.5 -13q-35 0 -61 4t-48 10v45q25 -7 49.5 -11t57.5 -4q44 0 73.5 9t47.5 30t26 55.5t8 84.5q-31 -12 -63.5 -20.5t-79.5 -8.5q-83 0 -118 34t-35 106q0 81 44.5 112.5
t122.5 31.5zM88 327q0 -27 5 -46t17.5 -31t34 -17t53.5 -5q42 0 71.5 7t61.5 18q-1 50 -7 83.5t-21 53.5t-39.5 28.5t-62.5 8.5q-55 0 -84 -21t-29 -79z" />
    <hkern u1="&#x23;" g2="nine.op" k="10" />
    <hkern u1="&#x23;" g2="four.op" k="80" />
    <hkern u1="&#x23;" u2="&#x37;" k="-30" />
    <hkern u1="&#x23;" u2="&#x34;" k="60" />
    <hkern u1="&#x28;" u2="&#x237;" k="-50" />
    <hkern u1="&#x28;" u2="j" k="-50" />
    <hkern u1="&#x2a;" u2="X" k="25" />
    <hkern u1="&#x2a;" u2="&#x2f;" k="110" />
    <hkern u1="&#x2f;" u2="&#xdf;" k="20" />
    <hkern u1="&#x2f;" u2="x" k="20" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="120" />
    <hkern u1="&#x31;" u2="&#x20ac;" k="2" />
    <hkern u1="&#x31;" u2="&#x192;" k="20" />
    <hkern u1="&#x31;" u2="&#xb0;" k="20" />
    <hkern u1="&#x31;" u2="&#x33;" k="20" />
    <hkern u1="&#x31;" u2="&#x31;" k="60" />
    <hkern u1="&#x32;" u2="&#x192;" k="60" />
    <hkern u1="&#x32;" u2="&#xa5;" k="3" />
    <hkern u1="&#x32;" u2="&#x37;" k="10" />
    <hkern u1="&#x32;" u2="&#x34;" k="10" />
    <hkern u1="&#x32;" u2="&#x33;" k="17" />
    <hkern u1="&#x32;" u2="&#x32;" k="10" />
    <hkern u1="&#x32;" u2="&#x31;" k="10" />
    <hkern u1="&#x33;" u2="&#x3e;" k="30" />
    <hkern u1="&#x33;" u2="&#x39;" k="10" />
    <hkern u1="&#x33;" u2="&#x37;" k="10" />
    <hkern u1="&#x33;" u2="&#x34;" k="-2" />
    <hkern u1="&#x33;" u2="&#x33;" k="20" />
    <hkern u1="&#x33;" u2="&#x32;" k="10" />
    <hkern u1="&#x33;" u2="&#x31;" k="20" />
    <hkern u1="&#x34;" u2="&#x37;" k="17" />
    <hkern u1="&#x34;" u2="&#x33;" k="34" />
    <hkern u1="&#x34;" u2="&#x31;" k="102" />
    <hkern u1="&#x35;" u2="&#xb0;" k="30" />
    <hkern u1="&#x35;" u2="&#x39;" k="10" />
    <hkern u1="&#x35;" u2="&#x37;" k="30" />
    <hkern u1="&#x35;" u2="&#x35;" k="10" />
    <hkern u1="&#x35;" u2="&#x33;" k="30" />
    <hkern u1="&#x35;" u2="&#x32;" k="20" />
    <hkern u1="&#x35;" u2="&#x31;" k="70" />
    <hkern u1="&#x37;" u2="&#x192;" k="70" />
    <hkern u1="&#x37;" u2="&#xb0;" k="-20" />
    <hkern u1="&#x37;" u2="&#xa3;" k="50" />
    <hkern u1="&#x37;" u2="&#x3e;" k="90" />
    <hkern u1="&#x37;" u2="&#x3c;" k="140" />
    <hkern u1="&#x37;" u2="&#x35;" k="20" />
    <hkern u1="&#x37;" u2="&#x34;" k="100" />
    <hkern u1="&#x37;" u2="&#x33;" k="10" />
    <hkern u1="&#x37;" u2="&#x32;" k="40" />
    <hkern u1="&#x37;" u2="&#x23;" k="90" />
    <hkern u1="&#x3c;" g2="seven.op" k="10" />
    <hkern u1="&#x3c;" u2="&#x37;" k="40" />
    <hkern u1="&#x3c;" u2="&#x31;" k="90" />
    <hkern u1="&#x3e;" g2="seven.op" k="110" />
    <hkern u1="&#x3e;" u2="&#x37;" k="100" />
    <hkern u1="&#x3f;" u2="X" k="30" />
    <hkern u1="&#x40;" u2="X" k="20" />
    <hkern u1="&#x40;" u2="V" k="50" />
    <hkern u1="B" g2="bullet.case" k="10" />
    <hkern u1="B" u2="&#x2122;" k="40" />
    <hkern u1="B" u2="&#xae;" k="13" />
    <hkern u1="B" u2="v" k="12" />
    <hkern u1="B" u2="X" k="20" />
    <hkern u1="B" u2="V" k="30" />
    <hkern u1="F" u2="&#xbf;" k="200" />
    <hkern u1="F" u2="&#xb0;" k="-20" />
    <hkern u1="F" u2="x" k="20" />
    <hkern u1="F" u2="v" k="10" />
    <hkern u1="F" u2="&#x3f;" k="-40" />
    <hkern u1="F" u2="&#x2f;" k="112" />
    <hkern u1="P" g2="at.case" k="-20" />
    <hkern u1="P" g2="bullet.case" k="-20" />
    <hkern u1="P" u2="v" k="-3" />
    <hkern u1="P" u2="X" k="52" />
    <hkern u1="P" u2="&#x2f;" k="80" />
    <hkern u1="P" u2="&#x2a;" k="-40" />
    <hkern u1="V" g2="at.case" k="40" />
    <hkern u1="V" g2="bullet.case" k="60" />
    <hkern u1="V" g2="questiondown.case" k="83" />
    <hkern u1="V" u2="&#x2022;" k="70" />
    <hkern u1="V" u2="&#xdf;" k="30" />
    <hkern u1="V" u2="&#xbf;" k="140" />
    <hkern u1="V" u2="&#xa9;" k="30" />
    <hkern u1="V" u2="&#xa1;" k="50" />
    <hkern u1="V" u2="x" k="40" />
    <hkern u1="V" u2="v" k="40" />
    <hkern u1="V" u2="V" k="-3" />
    <hkern u1="V" u2="&#x40;" k="50" />
    <hkern u1="V" u2="&#x2f;" k="80" />
    <hkern u1="V" u2="&#x23;" k="75" />
    <hkern u1="X" g2="at.case" k="70" />
    <hkern u1="X" g2="bullet.case" k="110" />
    <hkern u1="X" u2="&#x2022;" k="40" />
    <hkern u1="X" u2="&#xae;" k="12" />
    <hkern u1="X" u2="&#xa9;" k="40" />
    <hkern u1="X" u2="x" k="-2" />
    <hkern u1="X" u2="v" k="50" />
    <hkern u1="X" u2="&#x40;" k="20" />
    <hkern u1="X" u2="&#x2a;" k="25" />
    <hkern u1="\" u2="&#xdf;" k="20" />
    <hkern u1="\" u2="v" k="43" />
    <hkern u1="\" u2="V" k="80" />
    <hkern u1="v" u2="&#x2022;" k="20" />
    <hkern u1="v" u2="&#xbf;" k="34" />
    <hkern u1="v" u2="v" k="-4" />
    <hkern u1="v" u2="X" k="50" />
    <hkern u1="v" u2="V" k="40" />
    <hkern u1="v" u2="&#x2f;" k="43" />
    <hkern u1="x" u2="&#x2122;" k="40" />
    <hkern u1="x" u2="&#x2022;" k="40" />
    <hkern u1="x" u2="x" k="-3" />
    <hkern u1="x" u2="\" k="20" />
    <hkern u1="x" u2="X" k="-2" />
    <hkern u1="x" u2="V" k="40" />
    <hkern u1="&#xa1;" u2="V" k="50" />
    <hkern u1="&#xa3;" u2="&#x31;" k="22" />
    <hkern u1="&#xa5;" u2="&#x34;" k="12" />
    <hkern u1="&#xa5;" u2="&#x32;" k="22" />
    <hkern u1="&#xa5;" u2="&#x31;" k="5" />
    <hkern u1="&#xa9;" u2="X" k="40" />
    <hkern u1="&#xa9;" u2="V" k="30" />
    <hkern u1="&#xae;" u2="X" k="12" />
    <hkern u1="&#xb0;" g2="seven.op" k="-20" />
    <hkern u1="&#xb0;" g2="four.op" k="110" />
    <hkern u1="&#xb0;" u2="&#x37;" k="-10" />
    <hkern u1="&#xb0;" u2="&#x34;" k="80" />
    <hkern u1="&#xb0;" u2="&#x32;" k="20" />
    <hkern u1="&#xbf;" u2="&#xdf;" k="50" />
    <hkern u1="&#xbf;" u2="x" k="42" />
    <hkern u1="&#xbf;" u2="v" k="62" />
    <hkern u1="&#xbf;" u2="X" k="40" />
    <hkern u1="&#xbf;" u2="V" k="130" />
    <hkern u1="&#xbf;" u2="&#x237;" k="-100" />
    <hkern u1="&#xbf;" u2="j" k="-90" />
    <hkern u1="&#xde;" g2="at.case" k="-30" />
    <hkern u1="&#xde;" g2="bullet.case" k="-20" />
    <hkern u1="&#xde;" g2="parenleft.case" k="-30" />
    <hkern u1="&#xde;" u2="v" k="-3" />
    <hkern u1="&#xde;" u2="X" k="72" />
    <hkern u1="&#xde;" u2="V" k="30" />
    <hkern u1="&#xde;" u2="&#x2f;" k="50" />
    <hkern u1="&#xde;" u2="&#x2a;" k="-10" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="30" />
    <hkern u1="&#xdf;" u2="&#x2022;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xb0;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xae;" k="20" />
    <hkern u1="&#xdf;" u2="x" k="10" />
    <hkern u1="&#xdf;" u2="v" k="10" />
    <hkern u1="&#xdf;" u2="\" k="20" />
    <hkern u1="&#xdf;" u2="X" k="5" />
    <hkern u1="&#xdf;" u2="V" k="30" />
    <hkern u1="&#xdf;" u2="&#x2f;" k="20" />
    <hkern u1="&#xf0;" u2="&#xae;" k="2" />
    <hkern u1="&#xf0;" u2="x" k="23" />
    <hkern u1="&#xf0;" u2="v" k="20" />
    <hkern u1="&#xf0;" u2="X" k="30" />
    <hkern u1="&#xf0;" u2="V" k="35" />
    <hkern u1="&#xf0;" u2="&#x2a;" k="2" />
    <hkern u1="&#x13d;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x13d;" u2="&#x21a;" k="80" />
    <hkern u1="&#x13d;" u2="&#x17d;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x17b;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x179;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x178;" k="80" />
    <hkern u1="&#x13d;" u2="&#x176;" k="80" />
    <hkern u1="&#x13d;" u2="&#x166;" k="80" />
    <hkern u1="&#x13d;" u2="&#x164;" k="80" />
    <hkern u1="&#x13d;" u2="&#x162;" k="80" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="80" />
    <hkern u1="&#x13d;" u2="Z" k="-20" />
    <hkern u1="&#x13d;" u2="Y" k="80" />
    <hkern u1="&#x13d;" u2="V" k="60" />
    <hkern u1="&#x13d;" u2="T" k="80" />
    <hkern u1="&#x192;" u2="&#x37;" k="20" />
    <hkern u1="&#x192;" u2="&#x34;" k="42" />
    <hkern u1="&#x192;" u2="&#x32;" k="30" />
    <hkern u1="&#x192;" u2="&#x31;" k="23" />
    <hkern u1="&#x2022;" u2="x" k="40" />
    <hkern u1="&#x2022;" u2="v" k="20" />
    <hkern u1="&#x2022;" u2="X" k="40" />
    <hkern u1="&#x2022;" u2="V" k="70" />
    <hkern u1="&#x2044;" g2="seven.dnom" k="-20" />
    <hkern u1="&#x20ac;" u2="&#x31;" k="2" />
    <hkern g1="questiondown.case" u2="V" k="72" />
    <hkern g1="bullet.case" u2="X" k="110" />
    <hkern g1="bullet.case" u2="V" k="60" />
    <hkern g1="at.case" u2="X" k="70" />
    <hkern g1="at.case" u2="V" k="35" />
    <hkern g1="sterling.op" g2="nine.op" k="10" />
    <hkern g1="sterling.op" g2="three.op" k="20" />
    <hkern g1="sterling.op" g2="one.op" k="20" />
    <hkern g1="yen.op" g2="nine.op" k="10" />
    <hkern g1="yen.op" g2="four.op" k="20" />
    <hkern g1="florin.op" g2="nine.op" k="10" />
    <hkern g1="florin.op" g2="seven.op" k="30" />
    <hkern g1="florin.op" g2="five.op" k="30" />
    <hkern g1="florin.op" g2="four.op" k="32" />
    <hkern g1="florin.op" g2="three.op" k="20" />
    <hkern g1="florin.op" g2="two.op" k="20" />
    <hkern g1="florin.op" g2="one.op" k="30" />
    <hkern g1="one.op" g2="three.op" k="30" />
    <hkern g1="one.op" g2="one.op" k="30" />
    <hkern g1="two.op" g2="nine.op" k="8" />
    <hkern g1="two.op" g2="seven.op" k="25" />
    <hkern g1="two.op" g2="three.op" k="34" />
    <hkern g1="three.op" g2="nine.op" k="1" />
    <hkern g1="three.op" g2="one.op" k="20" />
    <hkern g1="three.op" g2="florin.op" k="20" />
    <hkern g1="three.op" g2="yen.op" k="20" />
    <hkern g1="four.op" g2="nine.op" k="10" />
    <hkern g1="four.op" g2="seven.op" k="27" />
    <hkern g1="four.op" g2="one.op" k="110" />
    <hkern g1="four.op" u2="&#xb0;" k="70" />
    <hkern g1="five.op" g2="nine.op" k="28" />
    <hkern g1="five.op" g2="seven.op" k="40" />
    <hkern g1="five.op" g2="three.op" k="34" />
    <hkern g1="five.op" g2="one.op" k="50" />
    <hkern g1="five.op" g2="florin.op" k="10" />
    <hkern g1="five.op" u2="&#xb0;" k="10" />
    <hkern g1="five.op" u2="&#x23;" k="-30" />
    <hkern g1="seven.op" g2="five.op" k="20" />
    <hkern g1="seven.op" g2="four.op" k="80" />
    <hkern g1="seven.op" g2="florin.op" k="70" />
    <hkern g1="seven.op" u2="&#xb0;" k="-20" />
    <hkern g1="seven.op" u2="&#x3e;" k="30" />
    <hkern g1="seven.op" u2="&#x3c;" k="70" />
    <hkern g1="nine.op" g2="nine.op" k="12" />
    <hkern g1="nine.op" g2="seven.op" k="50" />
    <hkern g1="nine.op" g2="five.op" k="3" />
    <hkern g1="nine.op" g2="three.op" k="19" />
    <hkern g1="nine.op" g2="one.op" k="20" />
    <hkern g1="nine.op" u2="&#xb0;" k="20" />
    <hkern g1="four.numr" u2="&#x2044;" k="-30" />
    <hkern g1="seven.numr" u2="&#x2044;" k="60" />
    <hkern g1="seven.op"
	g2="period,ellipsis"
	k="180" />
    <hkern g1="seven.op"
	g2="zeroslash.op,zero.op"
	k="10" />
    <hkern g1="seven.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="190" />
    <hkern g1="seven.op"
	g2="plus,divide,minus"
	k="90" />
    <hkern g1="seven.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="30" />
    <hkern g1="seven.op"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="30" />
    <hkern g1="F"
	g2="period,ellipsis"
	k="130" />
    <hkern g1="F"
	g2="AE,AEacute"
	k="110" />
    <hkern g1="F"
	g2="J,Jcircumflex"
	k="73" />
    <hkern g1="F"
	g2="comma,quotesinglbase,quotedblbase"
	k="130" />
    <hkern g1="F"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="F"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-3" />
    <hkern g1="F"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-10" />
    <hkern g1="F"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-6" />
    <hkern g1="F"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="F"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="F"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="17" />
    <hkern g1="F"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="F"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="17" />
    <hkern g1="F"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="38" />
    <hkern g1="F"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="10" />
    <hkern g1="F"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="17" />
    <hkern g1="F"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="X"
	k="100" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="V"
	k="60" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="83" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="P"
	g2="period,ellipsis"
	k="100" />
    <hkern g1="P"
	g2="AE,AEacute"
	k="120" />
    <hkern g1="P"
	g2="J,Jcircumflex"
	k="45" />
    <hkern g1="P"
	g2="guilsinglright.case,guillemotright.case"
	k="-10" />
    <hkern g1="P"
	g2="guillemotright,guilsinglright"
	k="-20" />
    <hkern g1="P"
	g2="comma,quotesinglbase,quotedblbase"
	k="170" />
    <hkern g1="P"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="P"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-30" />
    <hkern g1="P"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="P"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="32" />
    <hkern g1="P"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-2" />
    <hkern g1="P"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-40" />
    <hkern g1="P"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-6" />
    <hkern g1="P"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-3" />
    <hkern g1="P"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="-10" />
    <hkern g1="P"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="62" />
    <hkern g1="P"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-10" />
    <hkern g1="P"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="9" />
    <hkern g1="P"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-3" />
    <hkern g1="P"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-15" />
    <hkern g1="P"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-2" />
    <hkern g1="six,six.op"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="six,six.op"
	g2="zeroslash.op,zero.op"
	k="1" />
    <hkern g1="six,six.op"
	g2="degree"
	k="40" />
    <hkern g1="six,six.op"
	g2="three.op"
	k="60" />
    <hkern g1="six,six.op"
	g2="five.op"
	k="20" />
    <hkern g1="six,six.op"
	g2="nine.op"
	k="20" />
    <hkern g1="six,six.op"
	g2="two"
	k="20" />
    <hkern g1="six,six.op"
	g2="three"
	k="47" />
    <hkern g1="six,six.op"
	g2="seven"
	k="70" />
    <hkern g1="six,six.op"
	g2="one"
	k="62" />
    <hkern g1="six,six.op"
	g2="five"
	k="20" />
    <hkern g1="six,six.op"
	g2="quotedbl,quotesingle"
	k="70" />
    <hkern g1="six,six.op"
	g2="nine"
	k="40" />
    <hkern g1="six,six.op"
	g2="Euro"
	k="2" />
    <hkern g1="six,six.op"
	g2="florin"
	k="20" />
    <hkern g1="six,six.op"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="six,six.op"
	g2="yen"
	k="13" />
    <hkern g1="six,six.op"
	g2="seven.op"
	k="40" />
    <hkern g1="six,six.op"
	g2="one.op"
	k="50" />
    <hkern g1="six,six.op"
	g2="ordfeminine,ordmasculine"
	k="40" />
    <hkern g1="six,six.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="70" />
    <hkern g1="six,six.op"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="six,six.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-3" />
    <hkern g1="six,six.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="Thorn"
	g2="period,ellipsis"
	k="60" />
    <hkern g1="Thorn"
	g2="AE,AEacute"
	k="60" />
    <hkern g1="Thorn"
	g2="J,Jcircumflex"
	k="30" />
    <hkern g1="Thorn"
	g2="guilsinglright.case,guillemotright.case"
	k="-20" />
    <hkern g1="Thorn"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="Thorn"
	g2="guilsinglleft.case,guillemotleft.case"
	k="-30" />
    <hkern g1="Thorn"
	g2="comma,quotesinglbase,quotedblbase"
	k="100" />
    <hkern g1="Thorn"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="Thorn"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-60" />
    <hkern g1="Thorn"
	g2="hyphen,periodcentered,endash,emdash"
	k="-30" />
    <hkern g1="Thorn"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="42" />
    <hkern g1="Thorn"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="70" />
    <hkern g1="Thorn"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="Thorn"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-3" />
    <hkern g1="Thorn"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="Thorn"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-5" />
    <hkern g1="Thorn"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-3" />
    <hkern g1="Thorn"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="30" />
    <hkern g1="Thorn"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-10" />
    <hkern g1="Thorn"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-3" />
    <hkern g1="Thorn"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-15" />
    <hkern g1="Thorn"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-3" />
    <hkern g1="guillemotright,guilsinglright"
	g2="X"
	k="30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="V"
	k="70" />
    <hkern g1="guillemotright,guilsinglright"
	g2="v"
	k="12" />
    <hkern g1="guillemotright,guilsinglright"
	g2="x"
	k="42" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="100" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="96" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="guillemotright,guilsinglright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="3" />
    <hkern g1="two"
	g2="dollar,dollar.weight"
	k="10" />
    <hkern g1="two"
	g2="cent,cent.weight"
	k="30" />
    <hkern g1="two"
	g2="plus,divide,minus"
	k="60" />
    <hkern g1="two"
	g2="zero,six,zeroslash,six.op"
	k="10" />
    <hkern g1="two"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="10" />
    <hkern g1="V"
	g2="period,ellipsis"
	k="140" />
    <hkern g1="V"
	g2="AE,AEacute"
	k="90" />
    <hkern g1="V"
	g2="J,Jcircumflex"
	k="38" />
    <hkern g1="V"
	g2="guilsinglright.case,guillemotright.case"
	k="32" />
    <hkern g1="V"
	g2="guillemotright,guilsinglright"
	k="50" />
    <hkern g1="V"
	g2="guilsinglleft.case,guillemotleft.case"
	k="60" />
    <hkern g1="V"
	g2="guillemotleft,guilsinglleft"
	k="70" />
    <hkern g1="V"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="V"
	g2="comma,quotesinglbase,quotedblbase"
	k="160" />
    <hkern g1="V"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="V"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="80" />
    <hkern g1="V"
	g2="hyphen,periodcentered,endash,emdash"
	k="70" />
    <hkern g1="V"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-2" />
    <hkern g1="V"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-2" />
    <hkern g1="V"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-2" />
    <hkern g1="V"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="V"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="V"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="V"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="40" />
    <hkern g1="V"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="80" />
    <hkern g1="V"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="30" />
    <hkern g1="V"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="V"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="50" />
    <hkern g1="V"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="50" />
    <hkern g1="V"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="70" />
    <hkern g1="V"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="40" />
    <hkern g1="V"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="V"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="70" />
    <hkern g1="asterisk"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="asterisk"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="asterisk"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="3" />
    <hkern g1="asterisk"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="asterisk"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="68" />
    <hkern g1="asterisk"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="asterisk"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="three.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="three.op"
	g2="cent.op,cent.op.weight"
	k="20" />
    <hkern g1="three.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="three.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="uni2075"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2075"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="germandbls"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="germandbls"
	g2="quotedbl,quotesingle"
	k="3" />
    <hkern g1="germandbls"
	g2="comma,quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="germandbls"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="germandbls"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="germandbls"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="germandbls"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="55" />
    <hkern g1="germandbls"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="germandbls"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="germandbls"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="germandbls"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="1" />
    <hkern g1="germandbls"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="12" />
    <hkern g1="germandbls"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="germandbls"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="5" />
    <hkern g1="germandbls"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="5" />
    <hkern g1="dollar.op,dollar.op.weight"
	g2="nine.op"
	k="10" />
    <hkern g1="dollar.op,dollar.op.weight"
	g2="one.op"
	k="70" />
    <hkern g1="five.op"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="five.op"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="five.op"
	g2="cent.op,cent.op.weight"
	k="30" />
    <hkern g1="five.op"
	g2="ordfeminine,ordmasculine"
	k="30" />
    <hkern g1="five.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="five.op"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="five.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="five.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="30" />
    <hkern g1="dcaron,lcaron"
	g2="AE,AEacute"
	k="28" />
    <hkern g1="dcaron,lcaron"
	g2="X"
	k="-120" />
    <hkern g1="dcaron,lcaron"
	g2="V"
	k="-150" />
    <hkern g1="dcaron,lcaron"
	g2="v"
	k="-30" />
    <hkern g1="dcaron,lcaron"
	g2="x"
	k="-20" />
    <hkern g1="dcaron,lcaron"
	g2="parenright"
	k="-110" />
    <hkern g1="dcaron,lcaron"
	g2="germandbls"
	k="-30" />
    <hkern g1="dcaron,lcaron"
	g2="asterisk"
	k="-109" />
    <hkern g1="dcaron,lcaron"
	g2="bracketright,braceright"
	k="-110" />
    <hkern g1="dcaron,lcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-100" />
    <hkern g1="dcaron,lcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-120" />
    <hkern g1="dcaron,lcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-120" />
    <hkern g1="dcaron,lcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-140" />
    <hkern g1="dcaron,lcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-20" />
    <hkern g1="dcaron,lcaron"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-50" />
    <hkern g1="dcaron,lcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-50" />
    <hkern g1="dcaron,lcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-30" />
    <hkern g1="dcaron,lcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="3" />
    <hkern g1="dcaron,lcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="-40" />
    <hkern g1="dcaron,lcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="dcaron,lcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-90" />
    <hkern g1="dcaron,lcaron"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-40" />
    <hkern g1="dcaron,lcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-40" />
    <hkern g1="dcaron,lcaron"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="-80" />
    <hkern g1="dcaron,lcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="dcaron,lcaron"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,uni2116"
	k="-80" />
    <hkern g1="four"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="four"
	g2="cent,cent.weight"
	k="3" />
    <hkern g1="four"
	g2="ordfeminine,ordmasculine"
	k="40" />
    <hkern g1="four"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="one"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="one"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="one"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="one"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="20" />
    <hkern g1="seven"
	g2="period,ellipsis"
	k="160" />
    <hkern g1="seven"
	g2="dollar,dollar.weight"
	k="30" />
    <hkern g1="seven"
	g2="cent,cent.weight"
	k="30" />
    <hkern g1="seven"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="seven"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="seven"
	g2="comma,quotesinglbase,quotedblbase"
	k="180" />
    <hkern g1="seven"
	g2="plus,divide,minus"
	k="160" />
    <hkern g1="seven"
	g2="zero,six,zeroslash,six.op"
	k="8" />
    <hkern g1="seven"
	g2="hyphen,periodcentered,endash,emdash"
	k="60" />
    <hkern g1="seven"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="100" />
    <hkern g1="five"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="five"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="five"
	g2="ordfeminine,ordmasculine"
	k="30" />
    <hkern g1="five"
	g2="comma,quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="five"
	g2="hyphen,periodcentered,endash,emdash"
	k="-3" />
    <hkern g1="onesuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="onesuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="two"
	k="20" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="seven"
	k="-10" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="four"
	k="80" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="four.op"
	k="110" />
    <hkern g1="nine.op"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="nine.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="nine.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="nine.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="B"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="B"
	g2="AE,AEacute"
	k="10" />
    <hkern g1="B"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="B"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="B"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="42" />
    <hkern g1="B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="B"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="B"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="11" />
    <hkern g1="B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="K,uni0136"
	g2="guilsinglright.case,guillemotright.case"
	k="90" />
    <hkern g1="K,uni0136"
	g2="degree"
	k="13" />
    <hkern g1="K,uni0136"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="K,uni0136"
	g2="guilsinglleft.case,guillemotleft.case"
	k="132" />
    <hkern g1="K,uni0136"
	g2="v"
	k="100" />
    <hkern g1="K,uni0136"
	g2="guillemotleft,guilsinglleft"
	k="50" />
    <hkern g1="K,uni0136"
	g2="germandbls"
	k="20" />
    <hkern g1="K,uni0136"
	g2="asterisk"
	k="10" />
    <hkern g1="K,uni0136"
	g2="at.case"
	k="90" />
    <hkern g1="K,uni0136"
	g2="bullet"
	k="100" />
    <hkern g1="K,uni0136"
	g2="bullet.case"
	k="120" />
    <hkern g1="K,uni0136"
	g2="copyright"
	k="70" />
    <hkern g1="K,uni0136"
	g2="registered"
	k="30" />
    <hkern g1="K,uni0136"
	g2="at"
	k="20" />
    <hkern g1="K,uni0136"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="140" />
    <hkern g1="K,uni0136"
	g2="hyphen,periodcentered,endash,emdash"
	k="110" />
    <hkern g1="K,uni0136"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="K,uni0136"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="70" />
    <hkern g1="K,uni0136"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="45" />
    <hkern g1="K,uni0136"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="60" />
    <hkern g1="K,uni0136"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="100" />
    <hkern g1="K,uni0136"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="K,uni0136"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="32" />
    <hkern g1="K,uni0136"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="75" />
    <hkern g1="K,uni0136"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="40" />
    <hkern g1="K,uni0136"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="60" />
    <hkern g1="K,uni0136"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="50" />
    <hkern g1="K,uni0136"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="72" />
    <hkern g1="K,uni0136"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="20" />
    <hkern g1="K,uni0136"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="70" />
    <hkern g1="threesuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="threesuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="three"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="three"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="three"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="three"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="X"
	g2="AE,AEacute"
	k="-2" />
    <hkern g1="X"
	g2="guilsinglright.case,guillemotright.case"
	k="70" />
    <hkern g1="X"
	g2="guilsinglleft.case,guillemotleft.case"
	k="100" />
    <hkern g1="X"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="X"
	g2="z,zacute,zdotaccent,zcaron"
	k="17" />
    <hkern g1="X"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="90" />
    <hkern g1="X"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="X"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="17" />
    <hkern g1="X"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="X"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="53" />
    <hkern g1="X"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="X"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="50" />
    <hkern g1="X"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="X"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-2" />
    <hkern g1="X"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="X"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="X"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="30" />
    <hkern g1="X"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="X"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="50" />
    <hkern g1="X"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="40" />
    <hkern g1="uni2077"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2077"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="twosuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="twosuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="V"
	k="50" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="96" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="2" />
    <hkern g1="eth"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="eth"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="eth"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="eth"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="eth"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="eth"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="eth"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="30" />
    <hkern g1="eth"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="46" />
    <hkern g1="eth"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="eth"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="eth"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="eth"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="eth"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="5" />
    <hkern g1="eth"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="5" />
    <hkern g1="eth"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="eth"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-1" />
    <hkern g1="eth"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="1" />
    <hkern g1="two.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="two.op"
	g2="plus,divide,minus"
	k="30" />
    <hkern g1="period,ellipsis"
	g2="zeroslash.op,zero.op"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="V"
	k="140" />
    <hkern g1="period,ellipsis"
	g2="three.op"
	k="10" />
    <hkern g1="period,ellipsis"
	g2="five.op"
	k="30" />
    <hkern g1="period,ellipsis"
	g2="nine.op"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="seven"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="one"
	k="203" />
    <hkern g1="period,ellipsis"
	g2="quotedbl,quotesingle"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="v"
	k="60" />
    <hkern g1="period,ellipsis"
	g2="seven.op"
	k="120" />
    <hkern g1="period,ellipsis"
	g2="one.op"
	k="210" />
    <hkern g1="period,ellipsis"
	g2="asterisk"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="four"
	k="10" />
    <hkern g1="period,ellipsis"
	g2="eight,eight.op"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-3" />
    <hkern g1="period,ellipsis"
	g2="zero,six,zeroslash,six.op"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="150" />
    <hkern g1="period,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="180" />
    <hkern g1="period,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="period,ellipsis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="50" />
    <hkern g1="period,ellipsis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="colon,semicolon"
	g2="V"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="seven"
	k="20" />
    <hkern g1="colon,semicolon"
	g2="eight,eight.op"
	k="2" />
    <hkern g1="colon,semicolon"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="100" />
    <hkern g1="colon,semicolon"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="quotedbl,quotesingle"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="quotedbl,quotesingle"
	g2="zeroslash.op,zero.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="three.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="five.op"
	k="30" />
    <hkern g1="quotedbl,quotesingle"
	g2="nine.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="two"
	k="30" />
    <hkern g1="quotedbl,quotesingle"
	g2="three"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="x"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="four"
	k="70" />
    <hkern g1="quotedbl,quotesingle"
	g2="four.op"
	k="130" />
    <hkern g1="quotedbl,quotesingle"
	g2="slash"
	k="180" />
    <hkern g1="quotedbl,quotesingle"
	g2="two.op"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="quotedbl,quotesingle"
	g2="eight,eight.op"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="60" />
    <hkern g1="quotedbl,quotesingle"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="25" />
    <hkern g1="quotedbl,quotesingle"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="x"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="x"
	g2="guillemotleft,guilsinglleft"
	k="42" />
    <hkern g1="x"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-2" />
    <hkern g1="x"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="x"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="x"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="93" />
    <hkern g1="x"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="x"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="10" />
    <hkern g1="x"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-1" />
    <hkern g1="x"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="5" />
    <hkern g1="x"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-3" />
    <hkern g1="x"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="2" />
    <hkern g1="x"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="x"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="x"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="15" />
    <hkern g1="x"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="25" />
    <hkern g1="slash"
	g2="period,ellipsis"
	k="130" />
    <hkern g1="slash"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="slash"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="slash"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="slash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="slash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="slash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="slash"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="slash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="slash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="slash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="slash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="22" />
    <hkern g1="slash"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="eight,eight.op"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="eight,eight.op"
	g2="three.op"
	k="25" />
    <hkern g1="eight,eight.op"
	g2="nine.op"
	k="1" />
    <hkern g1="eight,eight.op"
	g2="one"
	k="10" />
    <hkern g1="eight,eight.op"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="eight,eight.op"
	g2="colon,semicolon"
	k="2" />
    <hkern g1="eight,eight.op"
	g2="cent.op,cent.op.weight"
	k="2" />
    <hkern g1="eight,eight.op"
	g2="numbersign"
	k="-10" />
    <hkern g1="eight,eight.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="eight,eight.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="cent.op,cent.op.weight"
	g2="three.op"
	k="20" />
    <hkern g1="cent.op,cent.op.weight"
	g2="one.op"
	k="10" />
    <hkern g1="v"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="v"
	g2="guillemotleft,guilsinglleft"
	k="12" />
    <hkern g1="v"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="v"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="v"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="v"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="v"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="76" />
    <hkern g1="v"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="v"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-1" />
    <hkern g1="v"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-2" />
    <hkern g1="v"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-1" />
    <hkern g1="v"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-3" />
    <hkern g1="v"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="v"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="v"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="15" />
    <hkern g1="v"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-1" />
    <hkern g1="v"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="15" />
    <hkern g1="cent,cent.weight"
	g2="seven"
	k="10" />
    <hkern g1="cent,cent.weight"
	g2="one"
	k="2" />
    <hkern g1="uni2074"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2074"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="zeroslash.op,zero.op"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="zeroslash.op,zero.op"
	g2="three.op"
	k="40" />
    <hkern g1="zeroslash.op,zero.op"
	g2="five.op"
	k="3" />
    <hkern g1="zeroslash.op,zero.op"
	g2="nine.op"
	k="1" />
    <hkern g1="zeroslash.op,zero.op"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="zeroslash.op,zero.op"
	g2="seven.op"
	k="20" />
    <hkern g1="zeroslash.op,zero.op"
	g2="one.op"
	k="2" />
    <hkern g1="zeroslash.op,zero.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="70" />
    <hkern g1="zeroslash.op,zero.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="X"
	k="70" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="V"
	k="40" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="66" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="uni2076"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2076"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="four.op"
	g2="quotedbl,quotesingle"
	k="60" />
    <hkern g1="four.op"
	g2="ordfeminine,ordmasculine"
	k="50" />
    <hkern g1="four.op"
	g2="plus,divide,minus"
	k="40" />
    <hkern g1="four.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="four.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="uni2078"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2078"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="plus,divide,minus"
	g2="three.op"
	k="50" />
    <hkern g1="plus,divide,minus"
	g2="five.op"
	k="30" />
    <hkern g1="plus,divide,minus"
	g2="two"
	k="20" />
    <hkern g1="plus,divide,minus"
	g2="three"
	k="50" />
    <hkern g1="plus,divide,minus"
	g2="seven"
	k="110" />
    <hkern g1="plus,divide,minus"
	g2="one"
	k="146" />
    <hkern g1="plus,divide,minus"
	g2="seven.op"
	k="140" />
    <hkern g1="plus,divide,minus"
	g2="four.op"
	k="30" />
    <hkern g1="plus,divide,minus"
	g2="zero,six,zeroslash,six.op"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="zero,nine,zeroslash"
	g2="two"
	k="10" />
    <hkern g1="zero,nine,zeroslash"
	g2="three"
	k="28" />
    <hkern g1="zero,nine,zeroslash"
	g2="seven"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="one"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="zero,nine,zeroslash"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="hyphen,periodcentered,endash,emdash"
	k="-3" />
    <hkern g1="zero,nine,zeroslash"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-3" />
    <hkern g1="zero,nine,zeroslash"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="20" />
    <hkern g1="f,longs,f_f"
	g2="period,ellipsis"
	k="100" />
    <hkern g1="f,longs,f_f"
	g2="V"
	k="-27" />
    <hkern g1="f,longs,f_f"
	g2="degree"
	k="-38" />
    <hkern g1="f,longs,f_f"
	g2="quotedbl,quotesingle"
	k="-35" />
    <hkern g1="f,longs,f_f"
	g2="v"
	k="-30" />
    <hkern g1="f,longs,f_f"
	g2="x"
	k="-10" />
    <hkern g1="f,longs,f_f"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="f,longs,f_f"
	g2="asterisk"
	k="-62" />
    <hkern g1="f,longs,f_f"
	g2="bullet"
	k="20" />
    <hkern g1="f,longs,f_f"
	g2="registered"
	k="-54" />
    <hkern g1="f,longs,f_f"
	g2="slash"
	k="53" />
    <hkern g1="f,longs,f_f"
	g2="numbersign"
	k="3" />
    <hkern g1="f,longs,f_f"
	g2="comma,quotesinglbase,quotedblbase"
	k="100" />
    <hkern g1="f,longs,f_f"
	g2="backslash"
	k="-80" />
    <hkern g1="f,longs,f_f"
	g2="question"
	k="-42" />
    <hkern g1="f,longs,f_f"
	g2="trademark"
	k="-50" />
    <hkern g1="f,longs,f_f"
	g2="questiondown"
	k="50" />
    <hkern g1="f,longs,f_f"
	g2="z,zacute,zdotaccent,zcaron"
	k="-1" />
    <hkern g1="f,longs,f_f"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-20" />
    <hkern g1="f,longs,f_f"
	g2="hyphen,periodcentered,endash,emdash"
	k="20" />
    <hkern g1="f,longs,f_f"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-50" />
    <hkern g1="f,longs,f_f"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-50" />
    <hkern g1="f,longs,f_f"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-18" />
    <hkern g1="f,longs,f_f"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-2" />
    <hkern g1="f,longs,f_f"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-62" />
    <hkern g1="f,longs,f_f"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-15" />
    <hkern g1="f,longs,f_f"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-5" />
    <hkern g1="f,longs,f_f"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="f,longs,f_f"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-3" />
    <hkern g1="f,longs,f_f"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="f,longs,f_f"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-6" />
    <hkern g1="f,longs,f_f"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-12" />
    <hkern g1="uni2070,uni2079"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2070,uni2079"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="X"
	k="3" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="V"
	k="120" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="three.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="five.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="nine.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="seven"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="one"
	k="152" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="quotedbl,quotesingle"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="v"
	k="40" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="seven.op"
	k="90" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="one.op"
	k="160" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="asterisk"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-3" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="zero,six,zeroslash,six.op"
	k="10" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="130" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="180" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="70" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="60" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="30" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="J,Jcircumflex"
	k="12" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="X"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="V"
	k="22" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="bullet"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="bullet.case"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="registered"
	k="12" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="trademark"
	k="40" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-2" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="1" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="13" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-5" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="zeroslash.op,zero.op"
	k="70" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="X"
	k="8" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="V"
	k="-3" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="three.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="five.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="nine.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="two"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="v"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="one.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="four"
	k="110" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="four.op"
	k="160" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="slash"
	k="150" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="two.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="eight,eight.op"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="z,zacute,zdotaccent,zcaron"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="zero,six,zeroslash,six.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="17" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="70" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="period,ellipsis"
	k="130" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="X"
	k="52" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="V"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="degree"
	k="-20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="v"
	k="-2" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="germandbls"
	k="2" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="asterisk"
	k="-20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="bullet"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="registered"
	k="-10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="slash"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="140" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="trademark"
	k="40" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="questiondown"
	k="42" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="22" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="90" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="35" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-3" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-2" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="15" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-3" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-1" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="15" />
    <hkern g1="Euro"
	g2="zero,six,zeroslash,six.op"
	k="10" />
    <hkern g1="questiondown"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="questiondown"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="140" />
    <hkern g1="questiondown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="170" />
    <hkern g1="questiondown"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="110" />
    <hkern g1="questiondown"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="53" />
    <hkern g1="questiondown"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="questiondown"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="32" />
    <hkern g1="questiondown"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="questiondown"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="30" />
    <hkern g1="questiondown"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="62" />
    <hkern g1="questiondown"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="40" />
    <hkern g1="questiondown"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="52" />
    <hkern g1="questiondown"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="60" />
    <hkern g1="questiondown"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="60" />
    <hkern g1="questiondown"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="50" />
    <hkern g1="questiondown"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="50" />
    <hkern g1="questiondown"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,uni2116"
	k="52" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="X"
	k="90" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="V"
	k="80" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="100" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="150" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-10" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="V"
	k="60" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="bullet"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="numbersign"
	k="-3" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="backslash"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="question"
	k="25" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="trademark"
	k="50" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-2" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="118" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="1" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="zeroslash.op,zero.op"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="X"
	k="50" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="V"
	k="70" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="three.op"
	k="60" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="nine.op"
	k="-10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="seven"
	k="70" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="one"
	k="145" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="v"
	k="10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="x"
	k="50" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="seven.op"
	k="100" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="one.op"
	k="20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="four"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="eight,eight.op"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="zero,six,zeroslash,six.op"
	k="-5" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="90" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="130" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-2" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-3" />
    <hkern g1="bullet.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="60" />
    <hkern g1="bullet.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="90" />
    <hkern g1="bullet.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="100" />
    <hkern g1="bullet.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="bullet.case"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="bullet.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="period,ellipsis"
	k="-3" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="J,Jcircumflex"
	k="-3" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="v"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="x"
	k="-2" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="bullet.case"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="numbersign"
	k="-20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="question"
	k="-22" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="-2" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="60" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-2" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="-2" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="numbersign"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="numbersign"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-10" />
    <hkern g1="numbersign"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-6" />
    <hkern g1="numbersign"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-3" />
    <hkern g1="numbersign"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="40" />
    <hkern g1="numbersign"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="76" />
    <hkern g1="numbersign"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="numbersign"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="40" />
    <hkern g1="questiondown.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-3" />
    <hkern g1="questiondown.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="questiondown.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="70" />
    <hkern g1="questiondown.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="questiondown.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-20" />
    <hkern g1="questiondown.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="questiondown.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="degree"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-10" />
    <hkern g1="degree"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-30" />
    <hkern g1="degree"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-10" />
    <hkern g1="degree"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-20" />
    <hkern g1="degree"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="degree"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="90" />
    <hkern g1="degree"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="degree"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="50" />
    <hkern g1="registered"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="registered"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="registered"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-2" />
    <hkern g1="registered"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="registered"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="90" />
    <hkern g1="registered"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="40" />
    <hkern g1="copyright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="copyright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="62" />
    <hkern g1="copyright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="copyright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="AE,AEacute"
	k="150" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="J,Jcircumflex"
	k="63" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="X"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="V"
	k="-2" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guillemotright,guilsinglright"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="degree"
	k="-30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guilsinglleft.case,guillemotleft.case"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="v"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="x"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guillemotleft,guilsinglleft"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="germandbls"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="at.case"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bullet"
	k="130" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bullet.case"
	k="90" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="copyright"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="registered"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="at"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="slash"
	k="140" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="numbersign"
	k="70" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bracketright,braceright"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="backslash"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="question"
	k="-50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="trademark"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="questiondown"
	k="160" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="z,zacute,zdotaccent,zcaron"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-2" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="hyphen,periodcentered,endash,emdash"
	k="90" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-5" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="exclamdown"
	k="70" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="questiondown.case"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-2" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="40" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="47" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="90" />
    <hkern g1="backslash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="140" />
    <hkern g1="backslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="112" />
    <hkern g1="backslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="60" />
    <hkern g1="backslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="32" />
    <hkern g1="backslash"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="40" />
    <hkern g1="backslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="23" />
    <hkern g1="backslash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="backslash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="backslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="backslash"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="40" />
    <hkern g1="backslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="62" />
    <hkern g1="backslash"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="AE,AEacute"
	k="90" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex"
	k="23" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="V"
	k="-2" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guilsinglright.case,guillemotright.case"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotright,guilsinglright"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guilsinglleft.case,guillemotleft.case"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="colon,semicolon"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="germandbls"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="at.case"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet.case"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="copyright"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="registered"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="at"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="slash"
	k="60" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="numbersign"
	k="60" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="90" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="trademark"
	k="-20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="questiondown"
	k="88" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,periodcentered,endash,emdash"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-2" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="exclamdown"
	k="34" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="45" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="45" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="X"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="V"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="degree"
	k="-10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="v"
	k="-1" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="bullet"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="slash"
	k="32" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="backslash"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="question"
	k="8" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="questiondown"
	k="17" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="50" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="68" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-1" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-2" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="5" />
    <hkern g1="bullet"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="130" />
    <hkern g1="bullet"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="150" />
    <hkern g1="bullet"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="bullet"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="bullet"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="bullet"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="bullet"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="exclamdown"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="70" />
    <hkern g1="exclamdown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="102" />
    <hkern g1="exclamdown"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="34" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="period,ellipsis"
	k="180" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="AE,AEacute"
	k="156" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J,Jcircumflex"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="V"
	k="-2" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guilsinglright.case,guillemotright.case"
	k="66" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotright,guilsinglright"
	k="96" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guilsinglleft.case,guillemotleft.case"
	k="83" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="v"
	k="76" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="x"
	k="93" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotleft,guilsinglleft"
	k="96" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="colon,semicolon"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="germandbls"
	k="52" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="asterisk"
	k="3" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at.case"
	k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="bullet"
	k="150" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="bullet.case"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="copyright"
	k="62" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at"
	k="110" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="slash"
	k="112" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="numbersign"
	k="110" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="180" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="questiondown"
	k="220" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="150" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="130" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-3" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="exclamdown"
	k="102" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="questiondown.case"
	k="34" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="68" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="65" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="85" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="123" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="43" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="103" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="106" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="106" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="122" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="56" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="45" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="3" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="126" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="period,ellipsis"
	k="-2" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="V"
	k="50" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="bullet"
	k="27" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="numbersign"
	k="-3" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="backslash"
	k="20" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="question"
	k="17" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-3" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="40" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="98" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-1" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="-1" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-2" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="1" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="X"
	k="50" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="V"
	k="40" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="degree"
	k="-20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="v"
	k="-3" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="x"
	k="-1" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="guillemotleft,guilsinglleft"
	k="3" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="bullet"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="at"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="slash"
	k="53" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="numbersign"
	k="13" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="100" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="backslash"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="trademark"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="questiondown"
	k="59" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="85" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-2" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-1" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-2" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="25" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-1" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="15" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="period,ellipsis"
	k="50" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="J,Jcircumflex"
	k="3" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="X"
	k="40" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="V"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="guillemotright,guilsinglright"
	k="-10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="x"
	k="1" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="asterisk"
	k="-20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="at.case"
	k="-10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="slash"
	k="50" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="backslash"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="trademark"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="questiondown"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="62" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-1" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-7" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-2" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="V"
	k="60" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="guillemotright,guilsinglright"
	k="2" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="x"
	k="-2" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="bullet"
	k="50" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="copyright"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="backslash"
	k="40" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="trademark"
	k="70" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="hyphen,periodcentered,endash,emdash"
	k="70" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="76" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="95" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-2" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="25" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="40" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="15" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="25" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="40" />
    <hkern g1="at.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="at.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="80" />
    <hkern g1="at.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="at.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="30" />
    <hkern g1="at.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="at"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="at"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="110" />
    <hkern g1="at"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="at"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="period,ellipsis"
	k="-20" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="AE,AEacute"
	k="-6" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="J,Jcircumflex"
	k="-30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="X"
	k="-5" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="V"
	k="98" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="degree"
	k="130" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="quotedbl,quotesingle"
	k="170" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="guilsinglleft.case,guillemotleft.case"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="v"
	k="50" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="asterisk"
	k="170" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="at.case"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="bullet"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="bullet.case"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="registered"
	k="150" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="numbersign"
	k="-40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="comma,quotesinglbase,quotedblbase"
	k="-30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="backslash"
	k="113" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="question"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="trademark"
	k="180" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="z,zacute,zdotaccent,zcaron"
	k="-4" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-5" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="100" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="hyphen,periodcentered,endash,emdash"
	k="-30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="170" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="142" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="159" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="45" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="-3" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-3" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="28" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="47" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="X"
	k="13" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="V"
	k="70" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="degree"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="v"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="x"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="germandbls"
	k="1" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="backslash"
	k="30" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="trademark"
	k="60" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="90" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="123" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="2" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="2" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="2" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="three.op"
	k="30" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="seven"
	k="130" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="one"
	k="110" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="seven.op"
	k="50" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="zero,six,zeroslash,six.op"
	k="20" />
    <hkern g1="bracketleft,braceleft"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="X"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="V"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="v"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="x"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="asterisk"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="bullet.case"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="registered"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="slash"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="backslash"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="trademark"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="18" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="50" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="1" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="X"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="V"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="degree"
	k="-20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="guillemotleft,guilsinglleft"
	k="-10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="slash"
	k="52" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="backslash"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="trademark"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="hyphen,periodcentered,endash,emdash"
	k="-40" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="17" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="40" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-2" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="8" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="V"
	k="60" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="registered"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="numbersign"
	k="-20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="trademark"
	k="20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="z,zacute,zdotaccent,zcaron"
	k="-1" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="30" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="90" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-2" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="-1" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-2" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-2" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-1" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="AE,AEacute"
	k="-5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="J,Jcircumflex"
	k="-2" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="X"
	k="-2" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="V"
	k="80" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guilsinglright.case,guillemotright.case"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="degree"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="quotedbl,quotesingle"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guilsinglleft.case,guillemotleft.case"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="v"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="x"
	k="-3" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="asterisk"
	k="68" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="at.case"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="bullet"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="bullet.case"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="copyright"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="registered"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="numbersign"
	k="-3" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="backslash"
	k="80" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="question"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="trademark"
	k="100" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="100" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="103" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="45" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="17" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-3" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="8" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="trademark"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="question"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="72" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="X"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="V"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="degree"
	k="30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="v"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="x"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="asterisk"
	k="30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="registered"
	k="32" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="numbersign"
	k="-30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="backslash"
	k="50" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="trademark"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="126" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-1" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-2" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="AE,AEacute"
	k="-20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="J,Jcircumflex"
	k="-10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="v"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="x"
	k="-1" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="-3" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-3" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-3" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="17" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="period,ellipsis"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="J,Jcircumflex"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="X"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="x"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="slash"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="hyphen,periodcentered,endash,emdash"
	k="-2" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="1" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="1" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="18" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="1" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="period,ellipsis"
	k="50" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="J,Jcircumflex"
	k="15" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="X"
	k="40" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="V"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="guillemotright,guilsinglright"
	k="2" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="degree"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="x"
	k="15" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="asterisk"
	k="-10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="at.case"
	k="-10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="slash"
	k="62" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="77" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="backslash"
	k="22" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="trademark"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="1" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="47" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="45" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-1" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="5" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-15" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-1" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="X"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="V"
	k="70" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="degree"
	k="50" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="v"
	k="15" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="x"
	k="25" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="asterisk"
	k="30" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="registered"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="numbersign"
	k="-20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="comma,quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="trademark"
	k="50" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="hyphen,periodcentered,endash,emdash"
	k="-3" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="90" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="126" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="1" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="1" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-1" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-1" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="V"
	k="40" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="trademark"
	k="30" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-2" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="106" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="V"
	k="55" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="degree"
	k="30" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="v"
	k="10" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="asterisk"
	k="20" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="registered"
	k="50" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="backslash"
	k="40" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="trademark"
	k="80" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-2" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="85" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="106" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="d,i,j,l,igrave,iacute,icircumflex,idieresis,dcroat,itilde,imacron,ibreve,iogonek,ij,jcircumflex,lacute,uni013C,lslash,f_f_i,f_f_l,i.trk,f_j,fl,f_f_j"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="3" />
  </font>
</defs></svg>

<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Wed Jun 23 16:37:10 2021
 By <PERSON><PERSON>sey,,,
Copyright (c) 2019 by Peugeot. All rights reserved.
</metadata>
<defs>
<font id="VN-PeugeotNew-Regular" horiz-adv-x="760" >
  <font-face 
    font-family="VN-Peugeot New"
    font-weight="400"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 0 0 0 0 0 0 0 0"
    ascent="802"
    descent="-198"
    x-height="559"
    cap-height="802"
    bbox="-232 -258 1573 1200"
    underline-thickness="101"
    underline-position="-245"
    unicode-range="U+0020-FEFF"
  />
<missing-glyph horiz-adv-x="615" 
d="M0 802h615v-1000h-615v1000zM339 195l15 85q105 2 149.5 41t44.5 115q0 38 -15 68t-48.5 50t-88 30.5t-132.5 10.5q-23 0 -48.5 -1.5t-51 -4t-49 -6t-43.5 -7.5v-124q48 9 96.5 14.5t93.5 5.5q42 0 67.5 -2t39 -7.5t17.5 -14.5t4 -23q0 -12 -2.5 -21t-11.5 -14
t-25.5 -7.5t-44.5 -2.5h-114v-70l21 -115h126zM357 0v153h-162v-153h162z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="785" 
d="M127 468h-71v70l71 21v50q0 53 11 92t35.5 65t64 39t96.5 13q26 0 47.5 -1.5t45.5 -4.5v-93q-20 2 -40.5 3t-40.5 1q-31 0 -53 -5.5t-35.5 -18t-19.5 -33.5t-6 -52v-55h257v50q0 53 11 92t36.5 65t67.5 39t103 13q29 0 52.5 -1.5t49.5 -4.5v-93q-23 2 -45 3t-44 1
q-35 0 -59 -5.5t-38.5 -18t-21 -33.5t-6.5 -52v-55h197v-91h-197v-468h-106v468h-257v-468h-105v468z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="1046" 
d="M119 468h-71v70l71 21v50q0 53 11 92t35.5 65t64 39t96.5 13q26 0 47.5 -1.5t45.5 -4.5v-93q-20 2 -40.5 3t-40.5 1q-31 0 -53 -5.5t-35.5 -18t-19.5 -33.5t-6 -52v-55h256v50q0 53 10.5 92t33.5 65t60.5 39t92.5 13q22 0 39 -1.5t39 -4.5v-93q-18 2 -33 3t-32 1
q-58 0 -81 -23.5t-23 -85.5v-55h361v-559h-106v468h-255v-468h-106v468h-256v-468h-105v468zM838 788h113v-98h-113v98z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="1060" 
d="M119 468h-71v70l71 21v50q0 53 11 92t35.5 65t64 39t96.5 13q26 0 47.5 -1.5t45.5 -4.5v-93q-20 2 -40.5 3t-40.5 1q-31 0 -53 -5.5t-35.5 -18t-19.5 -33.5t-6 -52v-55h257v50q0 53 10.5 92t34.5 65t63 39t96 13q26 0 47.5 -1.5t46.5 -4.5v-93q-21 2 -41 3t-40 1
q-31 0 -52.5 -5.5t-34.5 -18t-18.5 -33.5t-5.5 -52v-55h175v-91h-175v-468h-106v468h-257v-468h-105v468zM856 802h106v-802h-106v802z" />
    <glyph glyph-name="f_b" unicode="fb" horiz-adv-x="1157" 
d="M119 468h-71v70l71 21v50q0 53 10.5 92t35 65t64.5 39t98 13q25 0 45.5 -1.5t44.5 -4.5v-93q-20 2 -39 3t-39 1q-32 0 -54 -5.5t-35.5 -18t-19.5 -33.5t-6 -52v-55h176v-91h-176v-468h-105v468zM494 802h106v-273q51 20 104 33t107 13q67 0 119.5 -16.5t88.5 -52t55 -92
t19 -136.5q0 -79 -19 -134.5t-55 -91t-88.5 -52t-119.5 -16.5q-58 0 -114 14.5t-110 37.5l-18 -36h-75v802zM787 477q-51 0 -98.5 -12t-88.5 -31v-308q42 -19 88.5 -31.5t98.5 -12.5q51 0 88.5 10.5t61.5 33.5t35.5 61t11.5 92q0 55 -11.5 92.5t-35.5 61t-61.5 34
t-88.5 10.5z" />
    <glyph glyph-name="f_h" unicode="fh" horiz-adv-x="1174" 
d="M119 468h-71v70l71 21v50q0 53 10.5 92.5t35 65.5t64.5 38.5t98 12.5q25 0 45.5 -1.5t44.5 -4.5v-93q-20 2 -39 3t-39 1q-32 0 -54 -5.5t-35.5 -18t-19.5 -33.5t-6 -52v-55h176v-91h-176v-468h-105v468zM494 802h106v-279q53 23 110 37.5t122 14.5q63 0 110 -14.5
t77.5 -41t45.5 -64t15 -82.5v-373h-106v356q0 58 -37 88t-120 30q-62 0 -116 -16t-101 -37v-421h-106v802z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="685" 
d="M119 468h-71v70l71 21v50q0 53 10.5 92t34 65t62 39t93.5 13q21 0 38 -2t37 -4v-93q-16 2 -31.5 3t-30.5 1q-30 0 -50.5 -5.5t-33 -18t-18.5 -33.5t-6 -52v-55h363v-559h-107v468h-256v-468h-105v468zM477 788h112v-98h-112v98z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="684" 
d="M119 468h-71v70l71 21v50q0 53 10.5 92t34 65t62 39t93.5 13q21 0 38 -2t37 -4v-93q-16 2 -31.5 3t-30.5 1q-30 0 -50.5 -5.5t-33 -18t-18.5 -33.5t-6 -52v-55h362v-578q0 -51 -9 -88t-31 -60.5t-58 -35t-89 -11.5q-23 0 -45 1t-43 4v95q16 -2 36.5 -3t36.5 -1
q51 0 73.5 21.5t22.5 77.5v487h-256v-468h-105v468zM478 788h112v-98h-112v98z" />
    <glyph glyph-name="f_k" unicode="fk" horiz-adv-x="1100" 
d="M119 468h-71v70l71 21v50q0 53 10.5 92t35 65t64.5 39t98 13q25 0 45.5 -1.5t44.5 -4.5v-93q-20 2 -39 3t-39 1q-32 0 -54 -5.5t-35.5 -18t-19.5 -33.5t-6 -52v-55h176v-91h-176v-468h-105v468zM494 802h106v-469l302 226h148l-328 -244l345 -315h-148l-319 299v-299
h-106v802z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="698" 
d="M119 468h-71v70l71 21v50q0 53 10.5 92t35 65t64.5 39t98 13q25 0 45.5 -1.5t44.5 -4.5v-93q-20 2 -39 3t-39 1q-32 0 -54 -5.5t-35.5 -18t-19.5 -33.5t-6 -52v-55h176v-91h-176v-468h-105v468zM494 802h106v-802h-106v802z" />
    <glyph glyph-name="f_f_b" unicode="ffb" horiz-adv-x="1518" 
d="M119 468h-71v70l71 21v50q0 53 11 92t35.5 65t64 39t96.5 13q26 0 47.5 -1.5t45.5 -4.5v-93q-20 2 -40.5 3t-40.5 1q-31 0 -53 -5.5t-35.5 -18t-19.5 -33.5t-6 -52v-55h257v50q0 53 10.5 92t34.5 65t63 39t96 13q26 0 47.5 -1.5t46.5 -4.5v-93q-21 2 -41 3t-40 1
q-31 0 -52.5 -5.5t-34.5 -18t-18.5 -33.5t-5.5 -52v-55h175v-91h-175v-468h-106v468h-257v-468h-105v468zM856 802h106v-274q51 21 104 34t107 13q67 0 119.5 -16.5t89 -52t55.5 -92t19 -136.5q0 -79 -19 -134.5t-55.5 -91t-89 -52t-119.5 -16.5q-58 0 -114 14.5t-110 37.5
l-18 -36h-75v802zM1150 477q-52 0 -99.5 -12t-88.5 -32v-307q42 -19 88.5 -31.5t99.5 -12.5q51 0 88 10.5t61 33.5t35.5 61t11.5 92q0 55 -11.5 92.5t-35.5 61t-61 34t-88 10.5z" />
    <glyph glyph-name="f_f_h" unicode="ffh" horiz-adv-x="1537" 
d="M119 468h-71v70l71 21v50q0 53 11 92t35.5 65t64 39t96.5 13q26 0 47.5 -1.5t45.5 -4.5v-93q-20 2 -40.5 3t-40.5 1q-31 0 -53 -5.5t-35.5 -18t-19.5 -33.5t-6 -52v-55h257v50q0 53 10.5 92t34.5 65t63 39t96 13q26 0 47.5 -1.5t46.5 -4.5v-93q-21 2 -41 3t-40 1
q-31 0 -52.5 -5.5t-34.5 -18t-18.5 -33.5t-5.5 -52v-55h175v-91h-175v-468h-106v468h-257v-468h-105v468zM856 802h106v-279q53 23 110.5 37.5t121.5 14.5q63 0 110 -14.5t77.5 -41t45.5 -64t15 -82.5v-373h-105v356q0 58 -37 88t-121 30q-62 0 -116 -16t-101 -37v-421h-106
v802z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="1046" 
d="M119 468h-71v70l71 21v50q0 53 11 92t35.5 65t64 39t96.5 13q26 0 47.5 -1.5t45.5 -4.5v-93q-20 2 -40.5 3t-40.5 1q-31 0 -53 -5.5t-35.5 -18t-19.5 -33.5t-6 -52v-55h256v50q0 53 10.5 92t33.5 65t60.5 39t92.5 13q22 0 40 -1.5t39 -4.5v-93q-17 2 -33 3t-33 1
q-58 0 -81 -23.5t-23 -85.5v-55h361v-578q0 -51 -9 -88t-31 -60.5t-57.5 -35t-88.5 -11.5q-23 0 -45.5 1t-42.5 4v95q15 -2 36 -3t36 -1q51 0 74 21.5t23 77.5v487h-256v-468h-106v468h-256v-468h-105v468zM839 788h112v-98h-112v98z" />
    <glyph glyph-name="f_f_k" unicode="ffk" horiz-adv-x="1455" 
d="M119 468h-71v70l71 21v50q0 53 11 92t35.5 65t64 39t96.5 13q26 0 47.5 -1.5t45.5 -4.5v-93q-20 2 -40.5 3t-40.5 1q-31 0 -53 -5.5t-35.5 -18t-19.5 -33.5t-6 -52v-55h257v50q0 53 10.5 92t34.5 65t63 39t96 13q26 0 47.5 -1.5t46.5 -4.5v-93q-21 2 -41 3t-40 1
q-31 0 -52.5 -5.5t-34.5 -18t-18.5 -33.5t-5.5 -52v-55h175v-91h-175v-468h-106v468h-257v-468h-105v468zM856 802h106v-469l302 226h148l-328 -244l346 -315h-149l-319 299v-299h-106v802z" />
    <glyph glyph-name=".notdef" horiz-adv-x="615" 
d="M0 802h615v-1000h-615v1000zM339 195l15 85q105 2 149.5 41t44.5 115q0 38 -15 68t-48.5 50t-88 30.5t-132.5 10.5q-23 0 -48.5 -1.5t-51 -4t-49 -6t-43.5 -7.5v-124q48 9 96.5 14.5t93.5 5.5q42 0 67.5 -2t39 -7.5t17.5 -14.5t4 -23q0 -12 -2.5 -21t-11.5 -14
t-25.5 -7.5t-44.5 -2.5h-114v-70l21 -115h126zM357 0v153h-162v-153h162z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni000D" horiz-adv-x="338" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="300" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="380" 
d="M135 612v190h109v-190l-12 -363h-84zM129 116h122v-116h-122v116z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="600" 
d="M133 802h115l-13 -293h-90zM353 802h115l-13 -293h-90z" />
    <glyph glyph-name="numbersign" unicode="#" 
d="M131 215h-109l30 93h112l67 186h-110l31 93h112l77 215h96l-77 -215h173l77 215h96l-77 -215h109l-31 -93h-111l-67 -186h109l-30 -93h-112l-77 -215h-96l77 215h-173l-77 -215h-96zM433 308l67 186h-173l-67 -186h173z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="736" 
d="M87 166q48 -12 109 -23.5t129 -14.5v234q-73 11 -121 25.5t-76 38t-39 58.5t-11 87q0 46 12.5 81t41.5 60t76 39t117 17v93h89v-93q60 -2 110.5 -10.5t97.5 -20.5v-100q-48 12 -99 21t-109 11v-210q74 -11 122 -26.5t76.5 -40.5t40 -62t11.5 -91q0 -50 -14 -87.5
t-44 -62.5t-77.5 -39t-114.5 -17v-93h-89v93q-69 2 -130 12.5t-108 24.5v96zM184 582q0 -27 5.5 -45t20.5 -30.5t42.5 -20.5t72.5 -15v198q-41 -2 -68.5 -7.5t-43.5 -16t-22.5 -26t-6.5 -37.5zM558 236q0 27 -5.5 45.5t-21.5 31t-44 21.5t-73 16v-222q41 2 68.5 9t44.5 20
t24 32.5t7 46.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="912" 
d="M219 474q-96 0 -142.5 39t-46.5 133t46.5 133t142.5 39t142.5 -39t46.5 -133t-46.5 -133t-142.5 -39zM226 0h-109l571 802h109zM219 546q52 0 73 23t21 77t-21 76.5t-73 22.5t-73 -22.5t-21 -76.5t21 -77t73 -23zM694 -16q-96 0 -142.5 39t-46.5 133t46.5 133t142.5 39
t142.5 -39t46.5 -133t-46.5 -133t-142.5 -39zM694 56q52 0 73.5 23t21.5 77t-21.5 76.5t-73.5 22.5t-72.5 -22.5t-20.5 -76.5t20.5 -77t72.5 -23z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="810" 
d="M767 70l-61 -70l-478 364q-27 -8 -45.5 -23t-29.5 -41q-5 -15 -7.5 -33t-2.5 -41q0 -60 26.5 -88.5t83.5 -38.5q21 -4 47.5 -5.5t58.5 -1.5q15 0 34 0.5t38.5 2.5t38 6t32.5 10l85 -64q-51 -31 -111.5 -39t-118.5 -8q-40 0 -84 3t-84.5 14.5t-74.5 35t-54 64.5
q-17 42 -17 98q0 38 8 76.5t31 70.5q20 23 38.5 33t47.5 16l-68 51l-3 4q-1 2 -5 6q-36 44 -36 132q0 9 1 20.5t3.5 23.5t5.5 23.5t7 19.5q14 36 42.5 57t64 32.5t73.5 16t71 5.5q34 -1 72 -5.5t73 -15.5t63.5 -32.5t44.5 -57.5q1 -1 1 -4l3 -9q0 -2 0.5 -2.5t0.5 -2.5
q5 -16 7.5 -32.5t2.5 -36.5q0 -89 -34 -132q-2 -3 -5.5 -5t-6.5 -4q-12 -13 -33 -25q-18 -11 -48.5 -24t-78.5 -25l-70 53q21 3 46 8.5t49 14.5t43.5 24t28.5 37q9 24 9 63q0 30 -9 54q-10 22 -28.5 34.5t-40.5 19.5t-45.5 9t-44.5 3q-19 -1 -43 -3.5t-46.5 -9t-40.5 -19.5
t-27 -34q-10 -23 -10 -45q0 -1 0.5 -13.5t8.5 -34t23 -40.5t34 -33l351 -270q2 5 10 24t15.5 46t12 54t4.5 48q0 11 -1 23h99q0 -43 -6 -87q-5 -38 -16.5 -84t-34.5 -88z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="380" 
d="M133 802h115l-13 -293h-90z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="384" 
d="M252 -146q-82 89 -127 204.5t-45 269.5q0 156 47 272.5t125 201.5h106q-78 -81 -124.5 -202.5t-46.5 -271.5q0 -149 46 -270t125 -204h-106z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="384" 
d="M132 802q82 -89 127 -204.5t45 -269.5q0 -156 -47 -272.5t-125 -201.5h-106q78 81 124.5 202.5t46.5 271.5q0 149 -46 270t-125 204h106z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="453" 
d="M316 398l-89 146l-90 -146l-67 49l111 131l-167 40l26 78l159 -66l-14 172h83l-14 -172l159 66l26 -78l-167 -40l112 -131z" />
    <glyph glyph-name="plus" unicode="+" 
d="M107 323h229v236h88v-236h229v-87h-229v-236h-88v236h-229v87z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="380" 
d="M180 123h114l-112 -294h-95z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="380" 
d="M32 230v100h316v-100h-316z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="380" 
d="M129 116h122v-116h-122v116z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="412" 
d="M298 802h104l-288 -948h-103z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="736" 
d="M368 -16q-86 0 -147 24t-100 74.5t-57 129.5t-18 189t18 189t57 129.5t100 74.5t147 24t147 -24t100 -74.5t57 -129.5t18 -189t-18 -189t-57 -129.5t-100 -74.5t-147 -24zM368 87q59 0 99 16.5t65 53.5t36 97t11 147t-11 147t-36 97t-65 53.5t-99 16.5q-58 0 -98.5 -16.5
t-65.5 -53.5t-36 -97t-11 -147t11 -147t36 -97t65.5 -53.5t98.5 -16.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="572" 
d="M341 802h103v-802h-109v695l-261 -124l-45 84z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="736" 
d="M349 818q86 0 145 -12t95 -37.5t51.5 -64.5t15.5 -92q0 -44 -8.5 -75t-27.5 -55.5t-50.5 -46.5t-78.5 -49l-223 -127q-26 -15 -42 -26.5t-25 -23.5t-12 -27t-3 -38v-44h479v-100h-588v145q0 39 6 67.5t22 51.5t42.5 43t66.5 43l229 131l50 30t31.5 25t16 29.5t4.5 41.5
q0 29 -8 49t-30.5 33.5t-62 19.5t-102.5 6q-64 0 -127 -7.5t-114 -18.5v103q51 11 109.5 18.5t138.5 7.5z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="736" 
d="M72 100q57 -11 116 -18t137 -7q70 0 115 8.5t71.5 25.5t36.5 43t10 62q0 42 -10.5 69.5t-33.5 43.5t-59.5 22.5t-89.5 6.5h-196v101h196q48 0 82 5.5t55.5 20.5t31.5 41t10 67q0 36 -9.5 62t-33 42.5t-63.5 24.5t-102 8q-75 0 -133.5 -7t-112.5 -18v89q46 11 107.5 18.5
t141.5 7.5q86 0 146 -12t98 -38.5t55 -67.5t17 -98q0 -88 -34 -133.5t-95 -63.5q69 -16 106.5 -62t37.5 -138q0 -58 -18.5 -100t-59.5 -69t-105.5 -39.5t-157.5 -12.5q-82 0 -145.5 7.5t-111.5 18.5v90z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="736" 
d="M647 456v-456h-107v179h-504v85l302 538h115l-293 -522h380v176h107z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="736" 
d="M71 112q57 -12 117.5 -18.5t138.5 -6.5q67 0 111 8.5t69.5 26.5t35.5 45.5t10 64.5q0 39 -10.5 66.5t-34.5 44.5t-62 24.5t-93 7.5h-248v427h521v-102h-413v-223h156q80 0 136 -14.5t91.5 -44.5t51.5 -76t16 -110q0 -67 -18 -114.5t-58 -77t-103.5 -43t-153.5 -13.5
q-84 0 -147.5 7.5t-112.5 18.5v102z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="736" 
d="M379 -16q-92 0 -154 22.5t-100 72t-54 129t-16 193.5t20 193t64 129t114 72.5t170 22.5q63 0 109 -7.5t84 -18.5v-102q-43 11 -86.5 18t-103.5 7q-72 0 -121 -13t-79.5 -44.5t-44 -84t-14.5 -130.5q51 20 107 33.5t133 13.5q73 0 124.5 -15t83.5 -46t47 -78t15 -111
q0 -72 -21 -121t-59.5 -79t-93.5 -43t-124 -13zM384 390q-67 0 -116 -11.5t-101 -29.5q2 -77 13 -128.5t35.5 -82t65 -43.5t100.5 -13q45 0 79.5 7.5t58 25t35.5 47t12 73.5q0 43 -8.5 72.5t-29.5 48t-56 26.5t-88 8z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="736" 
d="M56 802h624v-84l-418 -718h-130l420 700h-496v102z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="736" 
d="M368 -16q-85 0 -145.5 13t-99 40.5t-56.5 69.5t-18 100q0 90 38.5 135.5t108.5 62.5q-62 18 -97 63t-35 132q0 56 17 97t54 68t94.5 40t138.5 13q80 0 137.5 -13t94.5 -40t54.5 -68t17.5 -97q0 -87 -35 -132t-97 -63q70 -17 108.5 -62.5t38.5 -135.5q0 -58 -18 -100
t-56.5 -69.5t-99 -40.5t-145.5 -13zM368 457q53 0 89.5 6t59.5 20t33.5 39t10.5 62q0 33 -9 57.5t-31 41t-59 24.5t-94 8t-94 -8t-59 -24.5t-31 -41t-9 -57.5q0 -37 10.5 -61.5t33.5 -39t59.5 -20.5t89.5 -6zM368 87q60 0 100 8.5t63.5 25t33.5 41.5t10 59q0 39 -11.5 65
t-36.5 41t-64 21.5t-95 6.5q-55 0 -94.5 -6.5t-64.5 -21.5t-36.5 -41t-11.5 -65q0 -34 10 -59t33.5 -41.5t63.5 -25t100 -8.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="736" 
d="M357 818q92 0 154 -22.5t100 -72t54 -129t16 -193.5t-20 -193t-64 -129t-114 -72.5t-170 -22.5q-63 0 -109 7.5t-84 18.5v102q22 -6 43 -10.5t43.5 -7.5t48 -5t55.5 -2q72 0 121 13.5t79.5 45t44 83.5t14.5 130q-51 -20 -107 -33.5t-133 -13.5q-73 0 -124.5 15t-83.5 46
t-47 78t-15 111q0 72 21 121t59.5 79t93.5 43t124 13zM170 567q0 -43 8.5 -72.5t29.5 -48t56 -26.5t88 -8q67 0 116 11.5t101 29.5q-2 77 -13 128t-35.5 82t-65 44t-100.5 13q-45 0 -79.5 -7.5t-58 -25t-35.5 -47t-12 -73.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="380" 
d="M129 558h122v-116h-122v116zM129 116h122v-116h-122v116z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="380" 
d="M140 543h122v-116h-122v116zM160 123h114l-112 -294h-95z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M107 338l546 190v-92l-448 -156l448 -156v-92l-546 191v115z" />
    <glyph glyph-name="equal" unicode="=" 
d="M107 472h546v-88h-546v88zM107 177h546v-88h-546v88z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M107 124l449 156l-449 156v92l546 -190v-115l-546 -191v92z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="657" 
d="M186 386v70h119q57 0 95.5 8t61 24.5t32 41.5t9.5 59q0 33 -9 57.5t-32.5 40t-65 23t-106.5 7.5q-67 0 -131.5 -7t-119.5 -20v101q51 11 115 18.5t136 7.5q90 0 151 -14t98.5 -42t54 -69t16.5 -95q0 -58 -16 -100.5t-50.5 -70.5t-88 -42t-129.5 -14h-31l-11 -121h-85z
M181 116h121v-116h-121v116z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="979" 
d="M459 72q-83 0 -129.5 31t-46.5 105q0 68 38 97t120 29h156v18q0 19 -5 32.5t-18 22.5t-35 13.5t-56 4.5q-45 0 -85.5 -3.5t-82.5 -9.5v84q23 4 42 6.5t38.5 4t41.5 2t51 0.5q59 0 99 -9t64 -27t34.5 -45.5t10.5 -65.5v-213q0 -39 11.5 -62t49.5 -23q19 0 30.5 6t18 17
t8.5 27t2 35v139q0 92 -18.5 155t-58.5 102.5t-101 57t-147 17.5t-147.5 -17.5t-101 -57t-58 -102.5t-18.5 -155q0 -91 19 -154t59 -102t101.5 -56.5t145.5 -17.5q17 0 39 1t39 3v-85q-17 -3 -38 -4t-40 -1q-111 0 -190.5 23.5t-130.5 73.5t-75 129t-24 190q0 110 25 188.5
t76.5 129.5t130.5 75t188 24q108 0 186.5 -24t129.5 -75t75.5 -129.5t24.5 -188.5v-134q0 -87 -35 -125.5t-117 -38.5q-69 0 -106 27t-48 82q-29 -11 -64.5 -19t-77.5 -8zM488 149q31 0 58.5 6.5t50.5 15.5v90h-122q-51 0 -72 -12t-21 -43q0 -30 24.5 -43.5t81.5 -13.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="866" 
d="M340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="859" 
d="M116 802h383q69 0 119 -11t82.5 -35.5t47.5 -64.5t15 -98q0 -88 -31.5 -132t-88.5 -58q33 -6 59.5 -19.5t45.5 -36t29 -55.5t10 -78q0 -62 -17 -103.5t-52 -66t-90 -34.5t-130 -10h-382v802zM227 698v-241h274q40 0 68 5.5t46 19t26 36.5t8 59t-8.5 59.5t-26.5 37
t-47.5 19t-71.5 5.5h-268zM227 352v-248h271q47 0 80 6t54 20.5t30.5 38.5t9.5 60q0 37 -9.5 60.5t-29 37.5t-50.5 19.5t-73 5.5h-283z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="878" 
d="M454 -16q-99 0 -171 24t-119.5 74.5t-70.5 129.5t-23 189t23 188.5t70.5 129.5t119.5 75t171 24q156 0 246 -60t122 -192h-118q-26 78 -86.5 112t-163.5 34q-73 0 -124.5 -17t-83.5 -54.5t-47 -96.5t-15 -143t15 -143t47 -96.5t83.5 -54.5t124.5 -17q103 0 164 34.5
t86 112.5h118q-32 -132 -122 -192.5t-246 -60.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="912" 
d="M116 802h332q103 0 177.5 -23.5t122.5 -72t71 -124.5t23 -181q0 -107 -23 -183t-71 -124.5t-122.5 -71t-177.5 -22.5h-332v802zM227 698v-594h221q76 0 129.5 16.5t87 52t48.5 92t15 136.5t-15 136t-48.5 92t-87 52.5t-129.5 16.5h-221z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="783" 
d="M116 802h587v-104h-476v-242h441v-105h-441v-247h476v-104h-587v802z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="746" 
d="M116 802h576v-104h-465v-257h431v-104h-431v-337h-111v802z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="919" 
d="M472 -16q-106 0 -182 24t-125 74.5t-72 129.5t-23 189t23 188.5t71 129.5t122.5 75t177.5 24q163 0 254.5 -60.5t122.5 -191.5h-116q-26 78 -89.5 112t-171.5 34q-76 0 -129.5 -17t-87 -54.5t-48.5 -96.5t-15 -143t15 -143t49.5 -96.5t89.5 -54.5t134 -17q137 0 201 48.5
t64 161.5v24h-275v104h387v-131q0 -75 -21 -133.5t-67 -98.5t-117 -60.5t-172 -20.5z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="940" 
d="M116 802h111v-345h486v345h111v-802h-111v352h-486v-352h-111v802z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="343" 
d="M116 802h111v-802h-111v802z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="421" 
d="M85 -14q-18 0 -32 1.5t-31 3.5v99q14 -2 26 -2h26q36 0 60 7t38.5 21.5t20 36.5t5.5 52v597h111v-598q0 -55 -10 -96t-35.5 -68t-69 -40.5t-109.5 -13.5z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="811" 
d="M116 802h110v-349l402 349h146l-435 -378l443 -424h-150l-406 394v-394h-110v802z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="687" 
d="M116 802h111v-698h423v-104h-534v802z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1077" 
d="M115 802h164l252 -506h15l252 506h164v-802h-111v652l-243 -480h-138l-244 481v-653h-111v802z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="950" 
d="M115 802h143l465 -655v655h111v-802h-147l-461 648v-648h-111v802z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="941" 
d="M470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24t180.5 -24t124.5 -75t72.5 -129.5t23.5 -188.5t-23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24zM470 90q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5
t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="844" 
d="M116 802h373q83 0 141.5 -14.5t96.5 -47.5t55.5 -86t17.5 -130q0 -78 -17.5 -131.5t-55.5 -86.5t-96.5 -47.5t-141.5 -14.5h-262v-244h-111v802zM227 698v-350h261q53 0 90.5 7.5t61.5 27.5t35 54t11 86t-11 85.5t-35 53.5t-61.5 28t-90.5 8h-261z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="941" 
d="M470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24t180.5 -24t124.5 -75t72.5 -129.5t23.5 -188.5t-23 -188.5t-72 -129.5l72 -91l-81 -66l-78 97q-44 -20 -98.5 -29.5t-120.5 -9.5zM470 90q90 0 147 22l-106 132l83 67
l109 -136q54 73 54 226q0 84 -15.5 143t-50 96.5t-89 54.5t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="874" 
d="M116 802h373q83 0 141.5 -14t96.5 -46.5t55.5 -84.5t17.5 -128q0 -115 -41 -176.5t-131 -83.5l161 -269h-132l-147 256h-283v-256h-111v802zM227 698v-338h261q53 0 90.5 7.5t61.5 26.5t35 51.5t11 83.5q0 50 -11 82.5t-35 51.5t-61.5 27t-90.5 8h-261z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="827" 
d="M101 136q60 -17 136.5 -30t159.5 -13q68 0 113 6t72 20t38.5 37t11.5 57q0 35 -8 58t-32 38t-67 25t-114 20q-95 11 -157 26.5t-98 41.5t-50 67t-14 103q0 57 18 99.5t58.5 70.5t105 42t158.5 14q81 0 146 -10t126 -26v-110q-63 16 -129.5 26.5t-141.5 10.5
q-69 0 -113 -6t-69.5 -19.5t-35 -35t-9.5 -51.5q0 -33 8 -53.5t31 -34.5t66.5 -23.5t113.5 -19.5q96 -11 158 -27t98 -44t50.5 -70.5t14.5 -106.5q0 -62 -19 -106.5t-61 -73t-108 -41.5t-160 -13q-89 0 -164 12t-133 29v111z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="791" 
d="M340 698h-304v104h720v-104h-304v-698h-112v698z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="911" 
d="M456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5t-20 148v498h112v-498q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h111v-498q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="870" 
d="M29 802h120l271 -702h32l268 702h122l-311 -802h-190z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1331" 
d="M39 802h117l194 -702h21l191 702h206l191 -702h22l193 702h118l-222 -802h-199l-196 702h-19l-196 -702h-200z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="878" 
d="M356 410l-298 392h140l242 -318l241 318h138l-296 -392l311 -410h-141l-254 335l-254 -335h-141z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="800" 
d="M344 314l-328 488h131l253 -379l253 379h131l-330 -489v-313h-110v314z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="780" 
d="M40 103l528 594h-498v105h640v-104l-527 -593h557v-105h-700v103z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="384" 
d="M88 802h270v-90h-169v-768h169v-90h-270v948z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="412" 
d="M11 802h104l287 -948h-104z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="384" 
d="M296 -146h-270v90h169v768h-169v90h270v-948z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M322 802h116l232 -510h-93l-197 429l-197 -429h-93z" />
    <glyph glyph-name="underscore" unicode="_" 
d="M0 -198v101h760v-101h-760z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="600" 
d="M170 802h120l140 -170h-99z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="734" 
d="M311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68
q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="761" 
d="M98 802h106v-274q51 21 104 34t107 13q67 0 119.5 -16.5t89 -52t55.5 -92t19 -136.5q0 -79 -19 -134.5t-55.5 -91t-89 -52t-119.5 -16.5q-58 0 -114 14.5t-110 37.5l-18 -36h-75v802zM392 477q-52 0 -99.5 -12t-88.5 -32v-307q42 -19 88.5 -31.5t99.5 -12.5q51 0 88 10.5
t61 33.5t35.5 61t11.5 92q0 55 -11.5 92.5t-35.5 61t-61 34t-88 10.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="649" 
d="M375 -16q-80 0 -139 16t-97.5 51.5t-57 91.5t-18.5 136q0 81 18.5 137t57 91.5t97.5 51.5t139 16q60 0 111.5 -7.5t96.5 -18.5v-94q-48 9 -97 15t-106 6q-58 0 -98 -10.5t-64 -34t-34.5 -61t-10.5 -91.5t10.5 -91t34.5 -60.5t64 -34t98 -10.5q60 0 111.5 5.5t101.5 14.5
v-93q-48 -12 -101.5 -19t-116.5 -7z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="761" 
d="M345 -16q-66 0 -118.5 16.5t-89 52t-55.5 91.5t-19 136q0 78 19 134t55.5 92t89 52.5t118.5 16.5q54 0 107 -13.5t104 -33.5v274h107v-802h-75l-18 36q-54 -22 -110.5 -37t-114.5 -15zM173 279q0 -55 11.5 -93t35.5 -61.5t61 -34t88 -10.5q52 0 99 12.5t88 32.5v310
q-41 19 -88 31t-99 12q-51 0 -88 -10.5t-61 -34t-35.5 -61.5t-11.5 -93z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="704" 
d="M383 -16q-87 0 -148 16t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138t56 92t94 51t132.5 16q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5t113 16.5v-93q-54 -13 -111 -19.5t-126 -6.5zM361 481
q-91 0 -134 -33.5t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="424" 
d="M127 468h-71v70l71 21v50q0 53 11 92t36.5 65t67.5 39t103 13q28 0 52.5 -1.5t50.5 -4.5v-93q-23 2 -45.5 3t-45.5 1q-35 0 -59 -5.5t-38.5 -18t-21 -33.5t-6.5 -52v-55h199v-91h-199v-468h-105v468z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="756" 
d="M345 -5q-66 0 -118.5 16t-89 51.5t-55.5 91t-19 133.5q0 77 19 131.5t55.5 89t89 51t118.5 16.5q57 -1 112.5 -15.5t107.5 -37.5l19 37h74v-528q0 -67 -18 -113t-54 -74t-90.5 -40t-128.5 -12q-32 0 -61 1t-58.5 3t-60 6t-65.5 10v96q38 -7 69.5 -11t59.5 -6.5t54.5 -3.5
t54.5 -1q57 0 94 8t59 25t30.5 43.5t8.5 63.5v15q-50 -20 -102 -33t-105 -13zM173 287q0 -107 47 -151t149 -44q51 0 97 11.5t86 29.5v302q-40 18 -86 30t-97 12q-102 0 -149 -43t-47 -147z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="779" 
d="M98 802h106v-279q53 23 110.5 37.5t121.5 14.5q63 0 110 -14.5t77.5 -41t45.5 -64t15 -82.5v-373h-105v356q0 58 -37 88t-121 30q-62 0 -116 -16t-101 -37v-421h-106v802z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="303" 
d="M101 717h104v-92h-104v92zM98 559h107v-559h-107v559z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="302" 
d="M96 788h112v-98h-112v98zM-70 -114q15 -2 35.5 -3t36.5 -1q51 0 73.5 21.5t22.5 77.5v578h106v-578q0 -51 -9 -88t-31 -60.5t-57.5 -35t-88.5 -11.5q-23 0 -45.5 1t-42.5 4v95z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="705" 
d="M98 802h106v-469l302 226h148l-328 -244l346 -315h-149l-319 299v-299h-106v802z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="302" 
d="M98 802h106v-802h-106v802z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1212" 
d="M98 559h75l20 -41q53 25 110 41t121 16q69 0 117 -18.5t76 -52.5q60 30 125 50.5t139 20.5q60 0 104.5 -14.5t73.5 -41t43 -64t14 -82.5v-373h-105v356q0 58 -34 88t-111 30q-60 0 -112 -14t-99 -37q2 -12 3.5 -24t1.5 -26v-373h-106v356q0 58 -34 88t-110 30
q-59 0 -110 -14t-96 -37v-423h-106v559z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="779" 
d="M193 518q55 25 115 41t128 16q63 0 110 -14.5t77.5 -41t45.5 -64t15 -82.5v-373h-105v356q0 58 -37 88t-121 30q-61 0 -115 -14.5t-102 -37.5v-422h-106v559h75z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="744" 
d="M372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5t232 -68.5t77 -227.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81q105 0 152.5 46t47.5 152q0 107 -47.5 152.5t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35
t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="761" 
d="M98 559h75l18 -37q54 23 110 38t114 15q67 0 119.5 -16.5t89 -52t55.5 -92t19 -136.5q0 -78 -19 -134t-55.5 -91.5t-89 -52t-119.5 -16.5q-54 0 -107 13.5t-104 33.5v-229h-106v757zM392 477q-52 0 -99.5 -13t-88.5 -32v-305q41 -18 88.5 -32t99.5 -14q51 0 88 10.5
t61 34t35.5 61.5t11.5 92q0 55 -11.5 92.5t-35.5 61t-61 34t-88 10.5z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="761" 
d="M345 -16q-66 0 -118.5 16.5t-89 52t-55.5 92t-19 135.5t19 135t55.5 91.5t89 52t118.5 16.5q58 0 114 -15t110 -38l19 37h75v-757h-107v228q-51 -20 -104 -33t-107 -13zM173 279q0 -54 11.5 -92t35.5 -61.5t61 -34t88 -10.5q52 0 99 13.5t88 32.5v305q-41 18 -88 31.5
t-99 13.5q-51 0 -88 -10.5t-61 -34t-35.5 -61t-11.5 -92.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="436" 
d="M98 559h75l20 -40q46 23 97 36t110 14v-103q-56 -2 -104.5 -11.5t-91.5 -24.5v-430h-106v559z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="669" 
d="M74 106q54 -12 120.5 -21t144.5 -9q46 0 77.5 3t50.5 10.5t27.5 20.5t8.5 34q0 23 -6 38t-24 25t-51 16t-87 12q-78 8 -127.5 19t-78.5 31t-40 50t-11 75q0 44 13 75t43.5 51t81.5 29.5t126 9.5q66 0 125.5 -7t113.5 -19v-93q-55 12 -111 19t-125 7q-49 0 -81 -3
t-50.5 -10.5t-25.5 -20t-7 -32.5t6 -33t23.5 -22t50 -15t85.5 -12q78 -7 128.5 -19t79.5 -32.5t40.5 -52.5t11.5 -79q0 -45 -14 -77t-45.5 -52t-81.5 -29t-122 -9q-74 0 -145.5 8t-123.5 22v92z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="476" 
d="M333 -12q-64 0 -106 11.5t-67 35t-35 58.5t-10 83v292h-71v70l71 21v134h105v-134h199v-91h-199v-276q0 -34 6.5 -55t21 -33t38.5 -16t59 -4q40 0 79 6v-96q-23 -3 -44.5 -4.5t-46.5 -1.5z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="779" 
d="M343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357q0 -57 36.5 -87.5t119.5 -30.5q63 0 117 16t101 38v421h105v-559h-73l-21 41q-54 -25 -114 -41t-129 -16z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="711" 
d="M25 559h117l212 -478h2l213 478h117l-247 -559h-168z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1076" 
d="M32 559h112l144 -479h3l164 479h167l163 -479l2 1l137 478h120l-172 -559h-166l-168 477l-168 -477h-166z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="694" 
d="M277 288l-237 271h132l175 -203l175 203h131l-235 -271l250 -288h-132l-189 218l-190 -218h-131z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="704" 
d="M74 -117q17 -1 33.5 -1.5t36.5 -0.5q35 0 60 3.5t43 12t30 21.5t21 33l24 55h-54l-243 553h113l202 -460h23l204 460h112l-277 -616q-18 -43 -39.5 -73t-49 -48.5t-64 -27t-85.5 -8.5q-26 0 -46.5 0.5t-43.5 3.5v93z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="672" 
d="M63 90l389 378h-376v91h519v-91l-390 -378h403v-90h-545v90z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="384" 
d="M358 -145h-63q-46 0 -79.5 8.5t-56 28t-33.5 50.5t-11 77v171q0 26 -3.5 44t-13 29t-27 16t-44.5 5h-23v90h23q27 0 44.5 5t27 16t13 29t3.5 44v170q0 46 11 77t33.5 50.5t56 28t79.5 8.5h63v-90h-40q-30 0 -49.5 -4.5t-31 -14.5t-16.5 -26t-5 -39v-154
q0 -61 -18.5 -95.5t-58.5 -49.5q77 -29 77 -145v-154q0 -47 21.5 -66t80.5 -19h40v-90z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="343" 
d="M122 802h100v-948h-100v948z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="384" 
d="M269 19q0 -46 -11 -77t-33.5 -50.5t-56 -28t-79.5 -8.5h-63v90h36q31 0 51.5 4.5t32.5 14.5t17 26t5 40v154q0 60 18 95t58 50q-40 15 -58 49.5t-18 95.5v154q0 23 -5 39t-16.5 26t-31 14.5t-49.5 4.5h-40v90h63q46 0 79.5 -8.5t56 -28t33.5 -50.5t11 -77v-170
q0 -26 3.5 -44t13 -29t26.5 -16t44 -5h24v-90h-24q-27 0 -44 -5t-26.5 -16t-13 -29t-3.5 -44v-171z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M61 216q0 103 40.5 150t119.5 47q52 0 94.5 -24.5t80 -54t71 -54t66.5 -24.5q45 0 64 29.5t19 94.5v25h83v-37q0 -197 -160 -197q-52 0 -94.5 24.5t-79.5 54.5t-70.5 54.5t-66.5 24.5q-46 0 -65.5 -29t-19.5 -95v-26h-82v37z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="300" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="380" 
d="M251 559v-116h-122v116h122zM232 310l12 -363v-190h-109v190l13 363h84z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="736" 
d="M453 802v-105q56 -2 105.5 -7.5t94.5 -16.5v-100q-48 9 -97 13.5t-103 6.5v-381q57 1 108 5.5t102 14.5v-100q-48 -11 -99.5 -16t-110.5 -7v-109h-88v109q-77 3 -133.5 20.5t-93 52.5t-54.5 89.5t-18 130.5q0 77 18 131.5t54.5 90t93 53t133.5 20.5v105h88zM178 402
q0 -48 9.5 -83t31.5 -58t58 -34.5t88 -14.5v381q-52 -3 -88 -15t-58 -35t-31.5 -58t-9.5 -83z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="736" 
d="M111 96q55 0 75 25t20 84v154h-151v89h151v150q0 56 14 97t45.5 68.5t81.5 41t123 13.5q66 0 116.5 -8t88.5 -16v-100q-42 10 -87 16.5t-102 6.5q-50 0 -83.5 -7.5t-54 -22.5t-29 -38t-8.5 -55v-146h328v-89h-328v-155q0 -69 -36 -108h423v-96h-643v96h56z" />
    <glyph glyph-name="currency" unicode="&#xa4;" 
d="M101 94l99 98q-27 47 -27 124q0 79 26 122l-98 97l59 58l99 -98q45 22 120 22q74 0 121 -22l100 99l59 -59l-97 -98q25 -44 25 -121q0 -76 -26 -123l98 -99l-59 -59l-102 101q-45 -22 -119 -22q-73 0 -118 22l-101 -100zM379 195q63 0 90 27.5t27 93.5t-27 93t-90 27
q-62 0 -89 -27t-27 -93t27 -93.5t89 -27.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="736" 
d="M76 342h237v120h-237v89h164l-211 251h124l213 -251h4l214 251h123l-211 -251h164v-89h-238v-120h238v-89h-238v-253h-109v253h-237v89z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="343" 
d="M122 432v370h100v-370h-100zM122 224h100v-370h-100v370z" />
    <glyph glyph-name="section" unicode="&#xa7;" 
d="M108 -70q53 -17 119.5 -30t139.5 -13q50 0 84.5 5.5t56 18t31 33t9.5 51.5q0 30 -6.5 50t-25.5 33t-53.5 21.5t-90.5 15.5q-87 10 -143.5 24t-89.5 36.5t-46 56.5t-13 85q0 44 21 84.5t56 66.5q-29 25 -40 62t-11 91q0 51 14 88t47 61t87.5 36t134.5 12q69 0 123 -11
t107 -25v-103q-54 15 -109.5 26.5t-119.5 11.5q-52 0 -86.5 -5t-55 -16.5t-29 -30t-8.5 -45.5q0 -30 6.5 -49t25 -32t53 -21t90.5 -15q87 -10 144 -24t90.5 -37t46.5 -57.5t13 -86.5q0 -44 -21 -82.5t-53 -67.5q26 -24 37 -60.5t11 -90.5q0 -56 -16 -96t-50 -65.5t-88 -37.5
t-131 -12q-78 0 -144 13.5t-117 27.5v103zM386 214q54 -6 95 -14t71 -20q16 17 25 39t9 48q0 33 -7.5 55.5t-30.5 37.5t-65 25t-111 19q-54 6 -94.5 14.5t-70.5 20.5q-35 -41 -35 -94q0 -33 7.5 -54.5t30.5 -35.5t65 -23t111 -18z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="600" 
d="M145 768h88v-98h-88v98zM367 768h88v-98h-88v98z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="941" 
d="M470 -16q-108 0 -185.5 24t-127.5 75t-73.5 130t-23.5 188t23.5 188t73.5 130t127.5 75t185.5 24t185.5 -24t128 -75t74 -130t23.5 -188t-23.5 -188t-74 -130t-128 -75t-185.5 -24zM470 66q84 0 145 18.5t100 58.5t57.5 103.5t18.5 154.5t-18.5 154.5t-57.5 103.5
t-100 58.5t-145 18.5t-144.5 -18.5t-99.5 -58.5t-57.5 -103.5t-18.5 -154.5t18.5 -154.5t57.5 -103.5t99.5 -58.5t144.5 -18.5zM470 145q-121 0 -178.5 60t-57.5 196q0 135 57.5 195.5t178.5 60.5q97 0 152.5 -37.5t74.5 -119.5h-104q-16 35 -44 51t-79 16q-72 0 -103 -38.5
t-31 -127.5t31 -127.5t103 -38.5q51 0 79 16t44 52h105q-20 -82 -75.5 -120t-152.5 -38z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="426" 
d="M180 474q-66 0 -105 25.5t-39 85.5q0 53 29.5 76.5t100.5 23.5h124v12q0 13 -3.5 22t-14 15t-29 8.5t-48.5 2.5q-33 0 -66.5 -2.5t-68.5 -7.5v72q20 3 36 5t31.5 3.5t34 2t42.5 0.5q51 0 85.5 -7.5t55 -22t29 -36.5t8.5 -52v-217h-62l-14 20q-23 -13 -55 -21t-71 -8z
M187 537q31 0 57 5.5t46 15.5v66h-109q-30 0 -42 -9.5t-12 -32.5q0 -24 12.5 -34.5t47.5 -10.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="596" 
d="M202 512h110l-166 -232l166 -232h-109l-168 232zM450 512h110l-166 -232l166 -232h-109l-168 232z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M107 472h546v-383h-88v295h-458v88z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="471" 
d="M235 401q-108 0 -156.5 49.5t-48.5 159.5q0 109 48.5 158.5t156.5 49.5t157 -49.5t49 -158.5q0 -110 -49 -159.5t-157 -49.5zM235 444q42 0 72 8t49.5 27.5t28.5 51.5t9 79t-9 79t-28.5 51.5t-49.5 27.5t-72 8t-71.5 -8t-49 -27.5t-28.5 -51.5t-9 -79q0 -48 9 -79.5
t28.5 -51t49.5 -27.5t71 -8zM139 731h111q51 0 73.5 -18t22.5 -66q0 -35 -12 -53t-39 -25l48 -81h-57l-42 76h-53v-76h-52v243zM191 687v-78h58q24 0 34 7.5t10 30.5t-10 31t-34 9h-58z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="600" 
d="M129 756h342v-73h-342v73z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="433" 
d="M219 474q-96 0 -142.5 39t-46.5 133t46.5 133t142.5 39t142.5 -39t46.5 -133t-46.5 -133t-142.5 -39zM219 546q52 0 73 23t21 77t-21 76.5t-73 22.5t-73 -22.5t-21 -76.5t21 -77t73 -23z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M107 402h229v157h88v-157h229v-87h-229v-157h-88v157h-229v87zM107 88h546v-88h-546v88z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="425" 
d="M204 1009q101 0 144.5 -29.5t43.5 -96.5q0 -26 -5.5 -44t-17.5 -32.5t-30.5 -27t-45.5 -27.5l-124 -68q-22 -13 -28.5 -21.5t-6.5 -29.5v-16h259v-77h-353v95q0 22 4 38t13.5 29.5t26 25t40.5 24.5l129 70q25 14 34 23.5t9 32.5q0 14 -4 24t-15.5 16t-30.5 8.5t-49 2.5
q-36 0 -75 -5t-68 -10v80q30 6 66 10.5t84 4.5z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="425" 
d="M37 624q35 -8 68 -11.5t78 -3.5q36 0 58.5 3t36 10.5t18.5 19.5t5 29q0 38 -18.5 51t-68.5 13h-123v77h123q44 0 62.5 11.5t18.5 48.5q0 17 -4.5 28.5t-16.5 18.5t-32 10t-52 3q-46 0 -78 -4t-67 -11v77q25 5 62.5 10t85.5 5q104 0 150.5 -27.5t46.5 -95.5
q0 -50 -19.5 -76t-54.5 -38q40 -11 61 -36.5t21 -76.5q0 -71 -49.5 -99.5t-160.5 -28.5q-49 0 -87 4.5t-64 10.5v78z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="600" 
d="M310 802h120l-161 -170h-99z" />
    <glyph glyph-name="uni00B5" unicode="&#xb5;" 
d="M103 559h106v-342q0 -66 36 -100.5t118 -34.5q54 0 100 12t88 34v431h106v-559h-75l-17 34q-45 -24 -94 -37t-104 -13q-54 0 -93 15.5t-65 44.5v-242h-106v757z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" 
d="M280 299q-58 3 -99 17.5t-67.5 44.5t-39 77t-12.5 113q0 70 14 118t44.5 77.5t77.5 42.5t113 13h321v-948h-101v856h-149v-856h-102v445z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="380" 
d="M129 338h122v-116h-122v116z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="600" 
d="M275 2h58l-62 -60h16q65 0 91.5 -17t26.5 -59q0 -45 -28.5 -62.5t-97.5 -17.5q-24 0 -45 1.5t-40 6.5v48q20 -4 40 -6t43 -2q45 0 58 7t13 25q0 16 -13 23.5t-58 7.5q-20 0 -37 -1.5t-34 -3.5v44z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="337" 
d="M191 1000h91v-460h-93v368l-138 -66l-39 72z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="438" 
d="M219 474q-96 0 -142.5 39t-46.5 133t46.5 133t142.5 39t142.5 -39t46.5 -133t-46.5 -133t-142.5 -39zM219 546q52 0 73 23t21 77t-21 76.5t-73 22.5t-73 -22.5t-21 -76.5t21 -77t73 -23z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="596" 
d="M146 48h-111l167 232l-167 232h109l168 -232zM394 48h-111l167 232l-167 232h109l168 -232z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="977" 
d="M191 802h91v-460h-93v368l-138 -66l-39 72zM213 0h-108l571 802h108zM941 262v-262h-93v93h-278v72l165 295h99l-165 -292h179v94h93z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="977" 
d="M191 802h91v-460h-93v368l-138 -66l-39 72zM213 0h-108l571 802h108zM755 469q101 0 144.5 -29.5t43.5 -96.5q0 -26 -5.5 -44t-17.5 -32.5t-30.5 -27t-45.5 -27.5l-124 -68q-22 -13 -28.5 -21.5t-6.5 -29.5v-16h259v-77h-353v95q0 22 4 38t13.5 29.5t26 25t40.5 24.5
l129 70q25 14 34 23.5t9 32.5q0 14 -4 24t-15.5 16t-30.5 8.5t-49 2.5q-36 0 -75 -5t-68 -10v80q30 6 66 10.5t84 4.5z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1067" 
d="M37 426q35 -8 68 -11.5t78 -3.5q36 0 58.5 3t36 10.5t18.5 19.5t5 29q0 38 -18.5 51t-68.5 13h-123v77h123q44 0 62.5 11.5t18.5 48.5q0 17 -4.5 28.5t-16.5 18.5t-32 10t-52 3q-46 0 -78 -4t-67 -11v77q25 5 62.5 10t85.5 5q104 0 150.5 -27.5t46.5 -95.5
q0 -50 -19.5 -76t-54.5 -38q40 -11 61 -36.5t21 -76.5q0 -71 -49.5 -99.5t-160.5 -28.5q-49 0 -87 4.5t-64 10.5v78zM301 0h-108l571 802h108zM1031 262v-262h-93v93h-278v72l165 295h99l-165 -292h179v94h93z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="657" 
d="M476 443h-121v116h121v-116zM471 173v-70h-119q-57 0 -95.5 -8t-61 -24.5t-32 -41.5t-9.5 -59q0 -33 9 -57.5t32.5 -40t65 -23t106.5 -7.5q67 0 131.5 7t119.5 20v-101q-51 -11 -115 -18.5t-136 -7.5q-90 0 -151 14t-98.5 42t-54 69t-16.5 95q0 58 16 100.5t50.5 70.5
t88 42t129.5 14h31l11 121h85z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="866" 
d="M254 987h121l116 -140h-104zM340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="866" 
d="M492 987h120l-132 -140h-105zM340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="866" 
d="M367 987h133l138 -140h-96l-109 97l-109 -97h-96zM340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="866" 
d="M231 868q0 57 27.5 84t78.5 27q33 0 60 -9.5t49.5 -21.5t41.5 -21.5t37 -9.5q22 0 31.5 10.5t9.5 37.5v9h69v-15q0 -111 -106 -111q-33 0 -59.5 10t-49 21.5t-42 21.5t-37.5 10q-21 0 -30.5 -10.5t-9.5 -38.5v-10h-70v16zM340 802h185l312 -802h-118l-88 227h-396
l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="866" 
d="M271 960h92v-85h-92v85zM503 960h92v-85h-92v85zM340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="866" 
d="M340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1302" 
d="M634 227h-360l-146 -227h-131l515 802h709v-104h-476v-242h442v-105h-442v-247h476v-104h-587v227zM634 331v367h-57l-236 -367h293z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="878" 
d="M822 237q-29 -120 -105.5 -180.5t-207.5 -70.5l-116 -184h-97l117 183q-88 5 -152.5 31.5t-107 77t-63 127t-20.5 180.5q0 110 23 188.5t70.5 129.5t119.5 75t171 24q156 0 246 -60t122 -192h-118q-26 78 -86.5 112t-163.5 34q-73 0 -124.5 -17t-83.5 -54.5t-47 -96.5
t-15 -143t15 -143t47 -96.5t83.5 -54.5t124.5 -17q103 0 164 34.5t86 112.5h118z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="783" 
d="M236 987h121l116 -140h-104zM116 802h587v-104h-476v-242h441v-105h-441v-247h476v-104h-587v802z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="783" 
d="M474 987h120l-132 -140h-105zM116 802h587v-104h-476v-242h441v-105h-441v-247h476v-104h-587v802z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="783" 
d="M349 987h133l138 -140h-96l-109 97l-109 -97h-96zM116 802h587v-104h-476v-242h441v-105h-441v-247h476v-104h-587v802z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="783" 
d="M253 960h92v-85h-92v85zM485 960h92v-85h-92v85zM116 802h587v-104h-476v-242h441v-105h-441v-247h476v-104h-587v802z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="343" 
d="M-8 987h121l116 -140h-104zM116 802h111v-802h-111v802z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="343" 
d="M230 987h120l-132 -140h-105zM116 802h111v-802h-111v802z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="343" 
d="M105 987h133l138 -140h-96l-109 97l-109 -97h-96zM116 802h111v-802h-111v802z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="343" 
d="M9 960h92v-85h-92v85zM241 960h92v-85h-92v85zM116 802h111v-802h-111v802z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="912" 
d="M116 351h-116v105h116v346h332q103 0 177.5 -23.5t122.5 -72t71 -124.5t23 -181q0 -107 -23 -183t-71 -124.5t-122.5 -71t-177.5 -22.5h-332v351zM227 351v-247h221q76 0 129.5 16.5t87 52t48.5 92t15 136.5t-15 136t-48.5 92t-87 52.5t-129.5 16.5h-221v-242h213v-105
h-213z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="950" 
d="M273 868q0 57 27.5 84t78.5 27q33 0 60 -9.5t49.5 -21.5t41.5 -21.5t37 -9.5q22 0 31.5 10.5t9.5 37.5v9h69v-15q0 -111 -106 -111q-33 0 -59.5 10t-49 21.5t-42 21.5t-37.5 10q-21 0 -30.5 -10.5t-9.5 -38.5v-10h-70v16zM115 802h143l465 -655v655h111v-802h-147
l-461 648v-648h-111v802z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="941" 
d="M291 987h121l116 -140h-104zM470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24t180.5 -24t124.5 -75t72.5 -129.5t23.5 -188.5t-23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24zM470 90q78 0 132.5 17t89 54t50 96.5
t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="941" 
d="M529 987h120l-132 -140h-105zM470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24t180.5 -24t124.5 -75t72.5 -129.5t23.5 -188.5t-23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24zM470 90q78 0 132.5 17t89 54t50 96.5
t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="941" 
d="M404 987h133l138 -140h-96l-109 97l-109 -97h-96zM470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24t180.5 -24t124.5 -75t72.5 -129.5t23.5 -188.5t-23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24zM470 90
q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="941" 
d="M268 868q0 57 27.5 84t78.5 27q33 0 60 -9.5t49.5 -21.5t41.5 -21.5t37 -9.5q22 0 31.5 10.5t9.5 37.5v9h69v-15q0 -111 -106 -111q-33 0 -59.5 10t-49 21.5t-42 21.5t-37.5 10q-21 0 -30.5 -10.5t-9.5 -38.5v-10h-70v16zM470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5
t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24t180.5 -24t124.5 -75t72.5 -129.5t23.5 -188.5t-23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24zM470 90q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5
t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="941" 
d="M308 960h92v-85h-92v85zM540 960h92v-85h-92v85zM470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24t180.5 -24t124.5 -75t72.5 -129.5t23.5 -188.5t-23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24zM470 90q78 0 132.5 17
t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M119 81l199 199l-198 199l61 61l199 -198l199 199l62 -62l-199 -199l199 -200l-62 -61l-199 199l-199 -199z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="941" 
d="M54 40l77 82h1l1 1q-32 51 -47.5 119t-15.5 159q0 110 23.5 188.5t72 129.5t124 75t180.5 24q173 0 267 -66l67 66l75 -56l-75 -79l2 -3h3l-2 -2q33 -50 48.5 -118.5t15.5 -158.5q0 -110 -23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24q-173 0 -267 66l-66 -66z
M184 401q0 -60 8 -107.5t25 -82.5l439 460q-66 41 -186 41q-78 0 -132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143zM470 90q78 0 132.5 17t89 54t50 96.5t15.5 143.5q0 60 -8 107.5t-25 82.5l-439 -459q34 -22 79.5 -32t105.5 -10z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="911" 
d="M277 987h121l116 -140h-104zM456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5t-20 148v498h112v-498q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h111v-498q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="911" 
d="M515 987h120l-132 -140h-105zM456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5t-20 148v498h112v-498q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h111v-498q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="911" 
d="M390 987h133l138 -140h-96l-109 97l-109 -97h-96zM456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5t-20 148v498h112v-498q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h111v-498q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="911" 
d="M294 960h92v-85h-92v85zM526 960h92v-85h-92v85zM456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5t-20 148v498h112v-498q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h111v-498q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="800" 
d="M460 987h120l-132 -140h-105zM344 314l-328 488h131l253 -379l253 379h131l-330 -489v-313h-110v314z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="844" 
d="M227 802v-122h262q83 0 141.5 -14.5t96.5 -47t55.5 -84.5t17.5 -129t-17.5 -130t-55.5 -85.5t-96.5 -47t-141.5 -14.5h-262v-128h-111v802h111zM227 575v-342h261q53 0 90.5 7.5t61.5 26.5t35 52.5t11 84.5t-11 84t-35 52.5t-61.5 27t-90.5 7.5h-261z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="746" 
d="M434 -4q-37 0 -62 2t-53 7v94q13 -2 23.5 -3.5t21.5 -2.5t22.5 -1.5t26.5 -0.5q36 0 66 6t51.5 21.5t33 42t11.5 68.5q0 80 -41.5 112t-135.5 32h-79v88h80q81 0 117.5 28.5t36.5 99.5t-40 102t-134 31q-93 0 -134 -30t-41 -97v-595h-106v595q0 51 14 92.5t47 70.5
t87 44.5t134 15.5q145 0 212.5 -52t67.5 -161q0 -84 -33 -129t-96 -60q72 -13 112.5 -59t40.5 -136q0 -62 -17 -104.5t-49 -69.5t-78.5 -39t-105.5 -12z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="734" 
d="M156 802h120l140 -170h-99zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87v-379h-73l-20 39
q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="734" 
d="M457 796h120l-161 -170h-99zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87v-379h-73l-20 39
q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="734" 
d="M309 802h116l132 -170h-95l-95 119l-95 -119h-95zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87
v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="734" 
d="M165 668q0 64 26 94t77 30q33 0 60 -11t50 -24t43.5 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35
q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5
t105.5 -22.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="734" 
d="M212 768h88v-98h-88v98zM434 768h88v-98h-88v98zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87
v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="734" 
d="M367 617q-72 0 -109 25.5t-37 86.5t37 86t109 25t109 -25t37 -86t-37 -86.5t-109 -25.5zM367 677q42 0 62.5 11.5t20.5 40.5t-20.5 40t-62.5 11t-62 -11t-20 -40t20 -40.5t62 -11.5zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48
t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5z
" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1173" 
d="M317 -16q-56 0 -100.5 10t-75.5 32t-47.5 56.5t-16.5 83.5q0 89 51.5 127.5t163.5 38.5h241v43q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57 8.5t52 5.5t56 3t69 1q106 0 164 -26.5t82 -75.5q32 51 90.5 76.5t147.5 25.5q68 0 120 -14.5
t87 -46t53 -82t18 -122.5v-64h-473q2 -46 13 -77.5t35.5 -51t65 -28t101.5 -8.5q63 0 118 5.5t113 16.5v-93q-53 -13 -110 -19.5t-126 -6.5q-96 0 -159.5 19t-100.5 63q-50 -34 -119.5 -58t-155.5 -24zM830 481q-92 0 -135 -34t-51 -115h369q-6 82 -48.5 115.5t-134.5 33.5z
M324 68q70 0 128 19.5t101 47.5q-16 47 -19 111h-227q-68 0 -96 -19t-28 -68q0 -52 32 -71.5t109 -19.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="649" 
d="M593 10q-39 -10 -82 -16.5t-92 -8.5l-116 -183h-97l118 184q-67 5 -116 24t-81.5 54.5t-48 88.5t-15.5 126q0 81 18.5 137t57 91.5t97.5 51.5t139 16q60 0 111.5 -7.5t96.5 -18.5v-94q-48 9 -97 15t-106 6q-58 0 -98 -10.5t-64 -34t-34.5 -61t-10.5 -91.5t10.5 -91
t34.5 -60.5t64 -34t98 -10.5q60 0 111.5 5.5t101.5 14.5v-93z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="704" 
d="M154 802h120l140 -170h-99zM383 -16q-87 0 -148 16t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138t56 92t94 51t132.5 16q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5t113 16.5v-93
q-54 -13 -111 -19.5t-126 -6.5zM361 481q-91 0 -134 -33.5t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="704" 
d="M455 796h120l-161 -170h-99zM383 -16q-87 0 -148 16t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138t56 92t94 51t132.5 16q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5t113 16.5v-93
q-54 -13 -111 -19.5t-126 -6.5zM361 481q-91 0 -134 -33.5t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="704" 
d="M307 802h116l132 -170h-95l-95 119l-95 -119h-95zM383 -16q-87 0 -148 16t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138t56 92t94 51t132.5 16q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5
t113 16.5v-93q-54 -13 -111 -19.5t-126 -6.5zM361 481q-91 0 -134 -33.5t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="704" 
d="M210 768h88v-98h-88v98zM432 768h88v-98h-88v98zM383 -16q-87 0 -148 16t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138t56 92t94 51t132.5 16q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5
t113 16.5v-93q-54 -13 -111 -19.5t-126 -6.5zM361 481q-91 0 -134 -33.5t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="303" 
d="M-59 802h120l140 -170h-99zM98 559h107v-559h-107v559z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="303" 
d="M242 796h120l-161 -170h-99zM98 559h107v-559h-107v559z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="303" 
d="M94 802h116l132 -170h-95l-95 119l-95 -119h-95zM98 559h107v-559h-107v559z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="303" 
d="M-3 768h88v-98h-88v98zM219 768h88v-98h-88v98zM98 559h107v-559h-107v559z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="738" 
d="M215 670l135 44q-30 22 -65 43.5t-75 44.5h154q48 -26 89 -55l174 56l20 -68l-127 -41q84 -78 119.5 -172t35.5 -213q0 -89 -19 -151t-56.5 -100.5t-94.5 -56t-133 -17.5q-155 0 -232 67.5t-77 223.5q0 80 19 137t55.5 93t89 53t118.5 17q42 0 83.5 -9t82.5 -23
q-17 32 -41 60.5t-54 56.5l-181 -58zM565 309q0 36 -3 68.5t-11 63.5q-40 16 -85.5 27t-96.5 11t-88 -11t-61 -35t-35.5 -63t-11.5 -95q0 -104 46.5 -149t152.5 -45q51 0 88 12t60 39t34 70.5t11 106.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="779" 
d="M187 668q0 64 26 94t77 30q33 0 60 -11t50 -24t43.5 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24zM193 518q55 25 115 41t128 16q63 0 110 -14.5t77.5 -41t45.5 -64
t15 -82.5v-373h-105v356q0 58 -37 88t-121 30q-61 0 -115 -14.5t-102 -37.5v-422h-106v559h75z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="744" 
d="M161 802h120l140 -170h-99zM372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5t232 -68.5t77 -227.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81q105 0 152.5 46t47.5 152q0 107 -47.5 152.5t-152.5 45.5
q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="744" 
d="M462 796h120l-161 -170h-99zM372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5t232 -68.5t77 -227.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81q105 0 152.5 46t47.5 152q0 107 -47.5 152.5t-152.5 45.5
q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="744" 
d="M314 802h116l132 -170h-95l-95 119l-95 -119h-95zM372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5t232 -68.5t77 -227.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81q105 0 152.5 46t47.5 152q0 107 -47.5 152.5
t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="744" 
d="M170 668q0 64 26 94t77 30q33 0 60 -11t50 -24t43.5 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24zM372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5
q0 159 77 227.5t232 68.5t232 -68.5t77 -227.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81q105 0 152.5 46t47.5 152q0 107 -47.5 152.5t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="744" 
d="M217 768h88v-98h-88v98zM439 768h88v-98h-88v98zM372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5t232 -68.5t77 -227.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81q105 0 152.5 46t47.5 152q0 107 -47.5 152.5
t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M331 529h99v-96h-99v96zM107 323h546v-87h-546v87zM331 125h99v-95h-99v95z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="744" 
d="M57 52l48 43q-42 68 -42 184q0 159 77 227.5t232 68.5q62 0 110.5 -10t86.5 -32l47 42l70 -68l-47 -43q42 -67 42 -185q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5q-62 0 -111 10t-86 32l-48 -42zM173 279q0 -67 18 -109l301 285q-44 22 -120 22
q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5zM372 81q105 0 152.5 46t47.5 152q0 69 -19 110l-302 -285q45 -23 121 -23z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="779" 
d="M178 802h120l140 -170h-99zM343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357q0 -57 36.5 -87.5t119.5 -30.5q63 0 117 16t101 38v421h105v-559h-73l-21 41q-54 -25 -114 -41t-129 -16z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="779" 
d="M479 796h120l-161 -170h-99zM343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357q0 -57 36.5 -87.5t119.5 -30.5q63 0 117 16t101 38v421h105v-559h-73l-21 41q-54 -25 -114 -41t-129 -16z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="779" 
d="M331 802h116l132 -170h-95l-95 119l-95 -119h-95zM343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357q0 -57 36.5 -87.5t119.5 -30.5q63 0 117 16t101 38v421h105v-559h-73l-21 41q-54 -25 -114 -41t-129 -16z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="779" 
d="M234 768h88v-98h-88v98zM456 768h88v-98h-88v98zM343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357q0 -57 36.5 -87.5t119.5 -30.5q63 0 117 16t101 38v421h105v-559h-73l-21 41q-54 -25 -114 -41t-129 -16z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="704" 
d="M442 796h120l-161 -170h-99zM74 -117q17 -1 33.5 -1.5t36.5 -0.5q35 0 60 3.5t43 12t30 21.5t21 33l24 55h-54l-243 553h113l202 -460h23l204 460h112l-277 -616q-18 -43 -39.5 -73t-49 -48.5t-64 -27t-85.5 -8.5q-26 0 -46.5 0.5t-43.5 3.5v93z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="761" 
d="M98 802h106v-274q51 20 104 33.5t107 13.5q67 0 119.5 -16.5t89 -52t55.5 -92t19 -136.5q0 -79 -19 -134.5t-55.5 -91t-89 -52t-119.5 -16.5q-54 0 -107 13.5t-104 33.5v-229h-106v1000zM392 475q-53 0 -100 -12.5t-88 -32.5v-302q41 -20 88 -32t100 -12q102 0 149 44
t47 151q0 108 -47 152t-149 44z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="704" 
d="M197 768h88v-98h-88v98zM419 768h88v-98h-88v98zM74 -117q17 -1 33.5 -1.5t36.5 -0.5q35 0 60 3.5t43 12t30 21.5t21 33l24 55h-54l-243 553h113l202 -460h23l204 460h112l-277 -616q-18 -43 -39.5 -73t-49 -48.5t-64 -27t-85.5 -8.5q-26 0 -46.5 0.5t-43.5 3.5v93z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="866" 
d="M250 949h366v-68h-366v68zM340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="734" 
d="M196 756h342v-73h-342v73zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87v-379h-73l-20 39
q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="866" 
d="M433 842q-57 0 -95 8t-61 25.5t-32.5 45t-9.5 66.5h81q0 -19 5 -33t18 -23t36 -13t58 -4t58 4t36 13t18 23t5 33h82q0 -39 -9.5 -66.5t-32.5 -45t-61 -25.5t-96 -8zM340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="734" 
d="M367 643q-53 0 -88.5 8t-57 25t-30.5 44.5t-9 66.5h82q0 -23 4.5 -37t16 -22t31.5 -11t51 -3t51 3t31.5 11t16 22t4.5 37h82q0 -39 -9 -66.5t-30 -44.5t-56.5 -25t-89.5 -8zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33
t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="866" 
d="M340 802h185l312 -802h-38q-30 -19 -49 -32.5t-30.5 -25.5t-15.5 -23t-4 -24q0 -42 65 -42q23 0 40.5 2.5t33.5 4.5v-64q-20 -4 -40.5 -7t-49.5 -3q-68 0 -98 21.5t-30 66.5q0 36 20.5 65t75.5 65l-86 223h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="734" 
d="M311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87v-379h-23q-30 -19 -49 -32.5t-30.5 -25.5t-15.5 -23
t-4 -24q0 -42 65 -42q23 0 40.5 2.5t33.5 4.5v-64q-20 -4 -40.5 -7t-49.5 -3q-68 0 -98 21.5t-30 66.5q0 20 6 37t19.5 34t36 34.5t56.5 38.5l-10 21q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="878" 
d="M513 987h120l-132 -140h-105zM454 -16q-99 0 -171 24t-119.5 74.5t-70.5 129.5t-23 189t23 188.5t70.5 129.5t119.5 75t171 24q156 0 246 -60t122 -192h-118q-26 78 -86.5 112t-163.5 34q-73 0 -124.5 -17t-83.5 -54.5t-47 -96.5t-15 -143t15 -143t47 -96.5t83.5 -54.5
t124.5 -17q103 0 164 34.5t86 112.5h118q-32 -132 -122 -192.5t-246 -60.5z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="649" 
d="M448 796h120l-161 -170h-99zM375 -16q-80 0 -139 16t-97.5 51.5t-57 91.5t-18.5 136q0 81 18.5 137t57 91.5t97.5 51.5t139 16q60 0 111.5 -7.5t96.5 -18.5v-94q-48 9 -97 15t-106 6q-58 0 -98 -10.5t-64 -34t-34.5 -61t-10.5 -91.5t10.5 -91t34.5 -60.5t64 -34t98 -10.5
q60 0 111.5 5.5t101.5 14.5v-93q-48 -12 -101.5 -19t-116.5 -7z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="878" 
d="M388 987h133l138 -140h-96l-109 97l-109 -97h-96zM454 -16q-99 0 -171 24t-119.5 74.5t-70.5 129.5t-23 189t23 188.5t70.5 129.5t119.5 75t171 24q156 0 246 -60t122 -192h-118q-26 78 -86.5 112t-163.5 34q-73 0 -124.5 -17t-83.5 -54.5t-47 -96.5t-15 -143t15 -143
t47 -96.5t83.5 -54.5t124.5 -17q103 0 164 34.5t86 112.5h118q-32 -132 -122 -192.5t-246 -60.5z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="649" 
d="M300 802h116l132 -170h-95l-95 119l-95 -119h-95zM375 -16q-80 0 -139 16t-97.5 51.5t-57 91.5t-18.5 136q0 81 18.5 137t57 91.5t97.5 51.5t139 16q60 0 111.5 -7.5t96.5 -18.5v-94q-48 9 -97 15t-106 6q-58 0 -98 -10.5t-64 -34t-34.5 -61t-10.5 -91.5t10.5 -91
t34.5 -60.5t64 -34t98 -10.5q60 0 111.5 5.5t101.5 14.5v-93q-48 -12 -101.5 -19t-116.5 -7z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="878" 
d="M398 960h112v-85h-112v85zM454 -16q-99 0 -171 24t-119.5 74.5t-70.5 129.5t-23 189t23 188.5t70.5 129.5t119.5 75t171 24q156 0 246 -60t122 -192h-118q-26 78 -86.5 112t-163.5 34q-73 0 -124.5 -17t-83.5 -54.5t-47 -96.5t-15 -143t15 -143t47 -96.5t83.5 -54.5
t124.5 -17q103 0 164 34.5t86 112.5h118q-32 -132 -122 -192.5t-246 -60.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="649" 
d="M302 768h112v-98h-112v98zM375 -16q-80 0 -139 16t-97.5 51.5t-57 91.5t-18.5 136q0 81 18.5 137t57 91.5t97.5 51.5t139 16q60 0 111.5 -7.5t96.5 -18.5v-94q-48 9 -97 15t-106 6q-58 0 -98 -10.5t-64 -34t-34.5 -61t-10.5 -91.5t10.5 -91t34.5 -60.5t64 -34t98 -10.5
q60 0 111.5 5.5t101.5 14.5v-93q-48 -12 -101.5 -19t-116.5 -7z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="878" 
d="M249 987h96l109 -98l109 98h96l-138 -140h-133zM454 -16q-99 0 -171 24t-119.5 74.5t-70.5 129.5t-23 189t23 188.5t70.5 129.5t119.5 75t171 24q156 0 246 -60t122 -192h-118q-26 78 -86.5 112t-163.5 34q-73 0 -124.5 -17t-83.5 -54.5t-47 -96.5t-15 -143t15 -143
t47 -96.5t83.5 -54.5t124.5 -17q103 0 164 34.5t86 112.5h118q-32 -132 -122 -192.5t-246 -60.5z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="649" 
d="M168 802h95l95 -120l95 120h95l-132 -170h-116zM375 -16q-80 0 -139 16t-97.5 51.5t-57 91.5t-18.5 136q0 81 18.5 137t57 91.5t97.5 51.5t139 16q60 0 111.5 -7.5t96.5 -18.5v-94q-48 9 -97 15t-106 6q-58 0 -98 -10.5t-64 -34t-34.5 -61t-10.5 -91.5t10.5 -91
t34.5 -60.5t64 -34t98 -10.5q60 0 111.5 5.5t101.5 14.5v-93q-48 -12 -101.5 -19t-116.5 -7z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="912" 
d="M251 987h96l109 -98l109 98h96l-138 -140h-133zM116 802h332q103 0 177.5 -23.5t122.5 -72t71 -124.5t23 -181q0 -107 -23 -183t-71 -124.5t-122.5 -71t-177.5 -22.5h-332v802zM227 698v-594h221q76 0 129.5 16.5t87 52t48.5 92t15 136.5t-15 136t-48.5 92t-87 52.5
t-129.5 16.5h-221z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="761" 
d="M345 -16q-66 0 -118.5 16.5t-89 52t-55.5 91.5t-19 136q0 78 19 134t55.5 92t89 52.5t118.5 16.5q54 0 107 -13.5t104 -33.5v274h107v-802h-75l-18 36q-54 -22 -110.5 -37t-114.5 -15zM793 802h105l-102 -255h-69zM173 279q0 -55 11.5 -93t35.5 -61.5t61 -34t88 -10.5
q52 0 99 12.5t88 32.5v310q-41 19 -88 31t-99 12q-51 0 -88 -10.5t-61 -34t-35.5 -61.5t-11.5 -93z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="912" 
d="M116 351h-116v105h116v346h332q103 0 177.5 -23.5t122.5 -72t71 -124.5t23 -181q0 -107 -23 -183t-71 -124.5t-122.5 -71t-177.5 -22.5h-332v351zM227 351v-247h221q76 0 129.5 16.5t87 52t48.5 92t15 136.5t-15 136t-48.5 92t-87 52.5t-129.5 16.5h-221v-242h213v-105
h-213z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="761" 
d="M345 -16q-66 0 -118.5 16.5t-89 52t-55.5 91.5t-19 136q0 78 19 134t55.5 92t89 52.5t118.5 16.5q54 0 107 -13.5t104 -33.5v107h-217v88h217v79h107v-79h98v-88h-98v-635h-75l-18 36q-54 -22 -110.5 -37t-114.5 -15zM173 279q0 -55 11.5 -93t35.5 -61.5t61 -34t88 -10.5
q52 0 99 12.5t88 32.5v310q-41 19 -88 31t-99 12q-51 0 -88 -10.5t-61 -34t-35.5 -61.5t-11.5 -93z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="783" 
d="M232 949h366v-68h-366v68zM116 802h587v-104h-476v-242h441v-105h-441v-247h476v-104h-587v802z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="704" 
d="M194 756h342v-73h-342v73zM383 -16q-87 0 -148 16t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138t56 92t94 51t132.5 16q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5t113 16.5v-93
q-54 -13 -111 -19.5t-126 -6.5zM361 481q-91 0 -134 -33.5t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="783" 
d="M415 842q-57 0 -95 8t-61 25.5t-32.5 45t-9.5 66.5h81q0 -19 5 -33t18 -23t36 -13t58 -4t58 4t36 13t18 23t5 33h82q0 -39 -9.5 -66.5t-32.5 -45t-61 -25.5t-96 -8zM116 802h587v-104h-476v-242h441v-105h-441v-247h476v-104h-587v802z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="704" 
d="M365 643q-53 0 -88.5 8t-57 25t-30.5 44.5t-9 66.5h82q0 -23 4.5 -37t16 -22t31.5 -11t51 -3t51 3t31.5 11t16 22t4.5 37h82q0 -39 -9 -66.5t-30 -44.5t-56.5 -25t-89.5 -8zM383 -16q-87 0 -148 16t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138t56 92t94 51t132.5 16
q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5t113 16.5v-93q-54 -13 -111 -19.5t-126 -6.5zM361 481q-91 0 -134 -33.5t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="783" 
d="M359 960h112v-85h-112v85zM116 802h587v-104h-476v-242h441v-105h-441v-247h476v-104h-587v802z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="704" 
d="M309 768h112v-98h-112v98zM383 -16q-87 0 -148 16t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138t56 92t94 51t132.5 16q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5t113 16.5v-93
q-54 -13 -111 -19.5t-126 -6.5zM361 481q-91 0 -134 -33.5t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="783" 
d="M116 802h587v-104h-476v-242h441v-105h-441v-247h476v-104h-40q-30 -19 -49 -32.5t-30.5 -25.5t-15.5 -23t-4 -24q0 -42 65 -42q23 0 40.5 2.5t33.5 4.5v-64q-20 -4 -40.5 -7t-49.5 -3q-68 0 -98 21.5t-30 66.5q0 18 4.5 33t15 30t28 30.5t42.5 32.5h-459v802z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="704" 
d="M383 -16q-87 0 -148 16t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138t56 92t94 51t132.5 16q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5t113 16.5v-93l-37 -8q-31 -19 -50.5 -33.5t-31 -26.5
t-16 -23t-4.5 -24q0 -42 65 -42q23 0 40.5 2.5t33.5 4.5v-64q-20 -4 -40.5 -7t-49.5 -3q-68 0 -98 21.5t-30 66.5q0 32 15.5 58t56.5 55q-43 -3 -91 -3zM361 481q-91 0 -134 -33.5t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="783" 
d="M210 987h96l109 -98l109 98h96l-138 -140h-133zM116 802h587v-104h-476v-242h441v-105h-441v-247h476v-104h-587v802z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="704" 
d="M175 802h95l95 -120l95 120h95l-132 -170h-116zM383 -16q-87 0 -148 16t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138t56 92t94 51t132.5 16q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5
t113 16.5v-93q-54 -13 -111 -19.5t-126 -6.5zM361 481q-91 0 -134 -33.5t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="919" 
d="M399 987h133l138 -140h-96l-109 97l-109 -97h-96zM472 -16q-106 0 -182 24t-125 74.5t-72 129.5t-23 189t23 188.5t71 129.5t122.5 75t177.5 24q163 0 254.5 -60.5t122.5 -191.5h-116q-26 78 -89.5 112t-171.5 34q-76 0 -129.5 -17t-87 -54.5t-48.5 -96.5t-15 -143
t15 -143t49.5 -96.5t89.5 -54.5t134 -17q137 0 201 48.5t64 161.5v24h-275v104h387v-131q0 -75 -21 -133.5t-67 -98.5t-117 -60.5t-172 -20.5z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="756" 
d="M331 802h116l132 -170h-95l-95 119l-95 -119h-95zM345 -5q-66 0 -118.5 16t-89 51.5t-55.5 91t-19 133.5q0 77 19 131.5t55.5 89t89 51t118.5 16.5q57 -1 112.5 -15.5t107.5 -37.5l19 37h74v-528q0 -67 -18 -113t-54 -74t-90.5 -40t-128.5 -12q-32 0 -61 1t-58.5 3t-60 6
t-65.5 10v96q38 -7 69.5 -11t59.5 -6.5t54.5 -3.5t54.5 -1q57 0 94 8t59 25t30.5 43.5t8.5 63.5v15q-50 -20 -102 -33t-105 -13zM173 287q0 -107 47 -151t149 -44q51 0 97 11.5t86 29.5v302q-40 18 -86 30t-97 12q-102 0 -149 -43t-47 -147z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="919" 
d="M465 842q-57 0 -95 8t-61 25.5t-32.5 45t-9.5 66.5h81q0 -19 5 -33t18 -23t36 -13t58 -4t58 4t36 13t18 23t5 33h82q0 -39 -9.5 -66.5t-32.5 -45t-61 -25.5t-96 -8zM472 -16q-106 0 -182 24t-125 74.5t-72 129.5t-23 189t23 188.5t71 129.5t122.5 75t177.5 24
q163 0 254.5 -60.5t122.5 -191.5h-116q-26 78 -89.5 112t-171.5 34q-76 0 -129.5 -17t-87 -54.5t-48.5 -96.5t-15 -143t15 -143t49.5 -96.5t89.5 -54.5t134 -17q137 0 201 48.5t64 161.5v24h-275v104h387v-131q0 -75 -21 -133.5t-67 -98.5t-117 -60.5t-172 -20.5z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="756" 
d="M389 643q-53 0 -88.5 8t-57 25t-30.5 44.5t-9 66.5h82q0 -23 4.5 -37t16 -22t31.5 -11t51 -3t51 3t31.5 11t16 22t4.5 37h82q0 -39 -9 -66.5t-30 -44.5t-56.5 -25t-89.5 -8zM345 -5q-66 0 -118.5 16t-89 51.5t-55.5 91t-19 133.5q0 77 19 131.5t55.5 89t89 51t118.5 16.5
q57 -1 112.5 -15.5t107.5 -37.5l19 37h74v-528q0 -67 -18 -113t-54 -74t-90.5 -40t-128.5 -12q-32 0 -61 1t-58.5 3t-60 6t-65.5 10v96q38 -7 69.5 -11t59.5 -6.5t54.5 -3.5t54.5 -1q57 0 94 8t59 25t30.5 43.5t8.5 63.5v15q-50 -20 -102 -33t-105 -13zM173 287
q0 -107 47 -151t149 -44q51 0 97 11.5t86 29.5v302q-40 18 -86 30t-97 12q-102 0 -149 -43t-47 -147z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="919" 
d="M409 960h112v-85h-112v85zM472 -16q-106 0 -182 24t-125 74.5t-72 129.5t-23 189t23 188.5t71 129.5t122.5 75t177.5 24q163 0 254.5 -60.5t122.5 -191.5h-116q-26 78 -89.5 112t-171.5 34q-76 0 -129.5 -17t-87 -54.5t-48.5 -96.5t-15 -143t15 -143t49.5 -96.5
t89.5 -54.5t134 -17q137 0 201 48.5t64 161.5v24h-275v104h387v-131q0 -75 -21 -133.5t-67 -98.5t-117 -60.5t-172 -20.5z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="756" 
d="M333 768h112v-98h-112v98zM345 -5q-66 0 -118.5 16t-89 51.5t-55.5 91t-19 133.5q0 77 19 131.5t55.5 89t89 51t118.5 16.5q57 -1 112.5 -15.5t107.5 -37.5l19 37h74v-528q0 -67 -18 -113t-54 -74t-90.5 -40t-128.5 -12q-32 0 -61 1t-58.5 3t-60 6t-65.5 10v96
q38 -7 69.5 -11t59.5 -6.5t54.5 -3.5t54.5 -1q57 0 94 8t59 25t30.5 43.5t8.5 63.5v15q-50 -20 -102 -33t-105 -13zM173 287q0 -107 47 -151t149 -44q51 0 97 11.5t86 29.5v302q-40 18 -86 30t-97 12q-102 0 -149 -43t-47 -147z" />
    <glyph glyph-name="uni0122" unicode="&#x122;" horiz-adv-x="919" 
d="M472 -16q-106 0 -182 24t-125 74.5t-72 129.5t-23 189t23 188.5t71 129.5t122.5 75t177.5 24q163 0 254.5 -60.5t122.5 -191.5h-116q-26 78 -89.5 112t-171.5 34q-76 0 -129.5 -17t-87 -54.5t-48.5 -96.5t-15 -143t15 -143t49.5 -96.5t89.5 -54.5t134 -17q137 0 201 48.5
t64 161.5v24h-275v104h387v-131q0 -75 -21 -133.5t-67 -98.5t-117 -60.5t-172 -20.5zM411 -53h123l-152 -145h-99z" />
    <glyph glyph-name="uni0123" unicode="&#x123;" horiz-adv-x="756" 
d="M448 632h-119l160 170h99zM345 -5q-66 0 -118.5 16t-89 51.5t-55.5 91t-19 133.5q0 77 19 131.5t55.5 89t89 51t118.5 16.5q57 -1 112.5 -15.5t107.5 -37.5l19 37h74v-528q0 -67 -18 -113t-54 -74t-90.5 -40t-128.5 -12q-32 0 -61 1t-58.5 3t-60 6t-65.5 10v96
q38 -7 69.5 -11t59.5 -6.5t54.5 -3.5t54.5 -1q57 0 94 8t59 25t30.5 43.5t8.5 63.5v15q-50 -20 -102 -33t-105 -13zM173 287q0 -107 47 -151t149 -44q51 0 97 11.5t86 29.5v302q-40 18 -86 30t-97 12q-102 0 -149 -43t-47 -147z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="940" 
d="M404 987h133l138 -140h-96l-109 97l-109 -97h-96zM116 802h111v-345h486v345h111v-802h-111v352h-486v-352h-111v802z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="779" 
d="M323 987h133l138 -140h-96l-109 97l-109 -97h-96zM98 802h106v-279q53 23 110.5 37.5t121.5 14.5q63 0 110 -14.5t77.5 -41t45.5 -64t15 -82.5v-373h-105v356q0 58 -37 88t-121 30q-62 0 -116 -16t-101 -37v-421h-106v802z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="940" 
d="M116 583h-116v104h116v115h111v-115h486v115h111v-115h116v-104h-116v-583h-111v352h-486v-352h-111v583zM713 457v126h-486v-126h486z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="779" 
d="M98 635h-98v88h98v79h106v-79h218v-88h-218v-112q53 23 110.5 37.5t121.5 14.5q63 0 110 -14.5t77.5 -41t45.5 -64t15 -82.5v-373h-105v356q0 58 -37 88t-121 30q-62 0 -116 -16t-101 -37v-421h-106v635z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="343" 
d="M-31 868q0 57 27.5 84t78.5 27q33 0 60 -9.5t49.5 -21.5t41.5 -21.5t37 -9.5q22 0 31.5 10.5t9.5 37.5v9h69v-15q0 -111 -106 -111q-33 0 -59.5 10t-49 21.5t-42 21.5t-37.5 10q-21 0 -30.5 -10.5t-9.5 -38.5v-10h-70v16zM116 802h111v-802h-111v802z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="303" 
d="M-50 668q0 64 26 94t77 30q33 0 60 -11t50 -24t43.5 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24zM98 559h107v-559h-107v559z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="343" 
d="M-12 949h366v-68h-366v68zM116 802h111v-802h-111v802z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="303" 
d="M-19 756h342v-73h-342v73zM98 559h107v-559h-107v559z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="343" 
d="M171 842q-57 0 -95 8t-61 25.5t-32.5 45t-9.5 66.5h81q0 -19 5 -33t18 -23t36 -13t58 -4t58 4t36 13t18 23t5 33h82q0 -39 -9.5 -66.5t-32.5 -45t-61 -25.5t-96 -8zM116 802h111v-802h-111v802z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="303" 
d="M152 643q-53 0 -88.5 8t-57 25t-30.5 44.5t-9 66.5h82q0 -23 4.5 -37t16 -22t31.5 -11t51 -3t51 3t31.5 11t16 22t4.5 37h82q0 -39 -9 -66.5t-30 -44.5t-56.5 -25t-89.5 -8zM98 559h107v-559h-107v559z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="343" 
d="M116 802h111v-802h-22q-30 -19 -49 -32.5t-30.5 -25.5t-15.5 -23t-4 -24q0 -42 65 -42q23 0 40.5 2.5t33.5 4.5v-64q-20 -4 -40.5 -7t-49.5 -3q-68 0 -98 21.5t-30 66.5q0 18 4.5 33t15 30t28 30.5t42.5 32.5h-1v802z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="303" 
d="M96 788h112v-98h-112v98zM84 -105q0 -42 65 -42q23 0 40.5 2.5t33.5 4.5v-64q-20 -4 -40.5 -7t-49.5 -3q-68 0 -98 21.5t-30 66.5q0 35 20 64t73 64v557h107v-559h-22q-30 -19 -49 -32.5t-30.5 -25.5t-15.5 -23t-4 -24z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="343" 
d="M115 960h112v-85h-112v85zM116 802h111v-802h-111v802z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="303" 
d="M98 559h107v-559h-107v559z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="764" 
d="M116 802h111v-802h-111v802zM428 -14q-18 0 -32 1.5t-31 3.5v99q14 -2 26 -2h26q36 0 60 7t38.5 21.5t20 36.5t5.5 52v597h111v-598q0 -55 -10 -96t-35.5 -68t-69 -40.5t-109.5 -13.5z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="606" 
d="M96 788h112v-98h-112v98zM399 788h112v-98h-112v98zM98 559h107v-559h-107v559zM233 -114q15 -2 35.5 -3t36.5 -1q51 0 73.5 21.5t22.5 77.5v578h106v-578q0 -51 -9 -88t-31 -60.5t-57.5 -35t-88.5 -11.5q-23 0 -45.5 1t-42.5 4v95z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="421" 
d="M187 987h133l138 -140h-96l-109 97l-109 -97h-96zM85 -14q-18 0 -32 1.5t-31 3.5v99q14 -2 26 -2h26q36 0 60 7t38.5 21.5t20 36.5t5.5 52v597h111v-598q0 -55 -10 -96t-35.5 -68t-69 -40.5t-109.5 -13.5z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="302" 
d="M93 802h116l132 -170h-95l-95 119l-95 -119h-95zM-70 -127q15 -2 35.5 -3t36.5 -1q51 0 73.5 23.5t22.5 83.5v583h106v-578q0 -51 -9 -88t-31 -60.5t-57.5 -35t-88.5 -11.5q-23 0 -45.5 1t-42.5 4v82z" />
    <glyph glyph-name="uni0136" unicode="&#x136;" horiz-adv-x="811" 
d="M116 802h110v-349l402 349h146l-435 -378l443 -424h-150l-406 394v-394h-110v802zM348 -53h123l-152 -145h-99z" />
    <glyph glyph-name="uni0137" unicode="&#x137;" horiz-adv-x="705" 
d="M98 802h106v-469l302 226h148l-328 -244l346 -315h-149l-319 299v-299h-106v802zM288 -53h123l-152 -145h-99z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="697" 
d="M98 559h106v-226l302 226h148l-328 -244l346 -315h-149l-319 299v-299h-106v559z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="687" 
d="M437 987h120l-132 -140h-105zM116 802h111v-698h423v-104h-534v802z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="302" 
d="M210 987h120l-132 -140h-105zM98 802h106v-802h-106v802z" />
    <glyph glyph-name="uni013B" unicode="&#x13b;" horiz-adv-x="687" 
d="M116 802h111v-698h423v-104h-534v802zM336 -53h123l-152 -145h-99z" />
    <glyph glyph-name="uni013C" unicode="&#x13c;" horiz-adv-x="302" 
d="M98 802h106v-802h-106v802zM93 -53h123l-152 -145h-99z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="687" 
d="M116 802h111v-698h423v-104h-534v802zM370 802h105l-102 -255h-69z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="302" 
d="M98 802h106v-802h-106v802zM334 802h105l-102 -255h-69z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="687" 
d="M116 802h111v-698h423v-104h-534v802zM482 506h122v-116h-122v116z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="424" 
d="M98 802h106v-802h-106v802zM331 338h122v-116h-122v116z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="687" 
d="M116 312l-116 -55v117l116 55v373h111v-321l205 96v-115l-205 -97v-261h423v-104h-534v312z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="302" 
d="M98 242l-98 -46v99l98 46v461h106v-410l98 46v-99l-98 -46v-293h-106v242z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="950" 
d="M534 987h120l-132 -140h-105zM115 802h143l465 -655v655h111v-802h-147l-461 648v-648h-111v802z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="779" 
d="M479 796h120l-161 -170h-99zM193 518q55 25 115 41t128 16q63 0 110 -14.5t77.5 -41t45.5 -64t15 -82.5v-373h-105v356q0 58 -37 88t-121 30q-61 0 -115 -14.5t-102 -37.5v-422h-106v559h75z" />
    <glyph glyph-name="uni0145" unicode="&#x145;" horiz-adv-x="950" 
d="M115 802h143l465 -655v655h111v-802h-147l-461 648v-648h-111v802zM405 -53h123l-152 -145h-99z" />
    <glyph glyph-name="uni0146" unicode="&#x146;" horiz-adv-x="779" 
d="M193 518q55 25 115 41t128 16q63 0 110 -14.5t77.5 -41t45.5 -64t15 -82.5v-373h-105v356q0 58 -37 88t-121 30q-61 0 -115 -14.5t-102 -37.5v-422h-106v559h75zM331 -53h123l-152 -145h-99z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="950" 
d="M270 987h96l109 -98l109 98h96l-138 -140h-133zM115 802h143l465 -655v655h111v-802h-147l-461 648v-648h-111v802z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="779" 
d="M199 802h95l95 -120l95 120h95l-132 -170h-116zM193 518q55 25 115 41t128 16q63 0 110 -14.5t77.5 -41t45.5 -64t15 -82.5v-373h-105v356q0 58 -37 88t-121 30q-61 0 -115 -14.5t-102 -37.5v-422h-106v559h75z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="941" 
d="M180 802h114l-112 -295h-95zM355 518q55 25 115 41t128 16q63 0 110 -14.5t77.5 -41t45.5 -64t15 -82.5v-373h-105v356q0 58 -37 88t-121 30q-61 0 -115 -14.5t-102 -37.5v-422h-106v559h75z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="950" 
d="M115 802h139l469 -623v623h111v-798q0 -56 -10 -97t-35.5 -68t-69 -40t-109.5 -13q-17 0 -31.5 1.5t-32.5 3.5v91q14 -2 26.5 -2h26.5q38 0 62 6t37.5 19.5t19 34.5t5.5 51v9l-497 659v-659h-111v802z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="779" 
d="M410 -115q15 -2 36 -2.5t36 -0.5q51 0 74 21.5t23 77.5v375q0 64 -37 97.5t-121 33.5q-61 0 -115 -15t-102 -36v-436h-106v559h75l20 -41q56 25 115.5 41t127.5 16q63 0 110 -14.5t77.5 -41t45.5 -64t15 -82.5v-392q0 -51 -9 -88t-31 -60.5t-57.5 -35t-88.5 -11.5
q-23 0 -45.5 1t-42.5 4v94z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="941" 
d="M287 949h366v-68h-366v68zM470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24t180.5 -24t124.5 -75t72.5 -129.5t23.5 -188.5t-23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24zM470 90q78 0 132.5 17t89 54t50 96.5
t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="744" 
d="M201 756h342v-73h-342v73zM372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5t232 -68.5t77 -227.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81q105 0 152.5 46t47.5 152q0 107 -47.5 152.5t-152.5 45.5
q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="941" 
d="M470 842q-57 0 -95 8t-61 25.5t-32.5 45t-9.5 66.5h81q0 -19 5 -33t18 -23t36 -13t58 -4t58 4t36 13t18 23t5 33h82q0 -39 -9.5 -66.5t-32.5 -45t-61 -25.5t-96 -8zM470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24
t180.5 -24t124.5 -75t72.5 -129.5t23.5 -188.5t-23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24zM470 90q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5
t132 -17z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="744" 
d="M372 643q-53 0 -88.5 8t-57 25t-30.5 44.5t-9 66.5h82q0 -23 4.5 -37t16 -22t31.5 -11t51 -3t51 3t31.5 11t16 22t4.5 37h82q0 -39 -9 -66.5t-30 -44.5t-56.5 -25t-89.5 -8zM372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5t232 -68.5
t77 -227.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81q105 0 152.5 46t47.5 152q0 107 -47.5 152.5t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="941" 
d="M412 987h113l-121 -140h-96zM639 987h113l-121 -140h-96zM470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24t180.5 -24t124.5 -75t72.5 -129.5t23.5 -188.5t-23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24zM470 90
q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="744" 
d="M336 802h112l-140 -170h-92zM553 802h111l-141 -170h-91zM372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5t232 -68.5t77 -227.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81q105 0 152.5 46t47.5 152
q0 107 -47.5 152.5t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1352" 
d="M70 402q0 107 23 183t71 124t122.5 70.5t177.5 22.5h808v-104h-476v-242h441v-105h-441v-247h476v-104h-808q-103 0 -177.5 23.5t-122.5 72.5t-71 125t-23 181zM685 104v594h-221q-76 0 -129.5 -16.5t-87 -52.5t-48.5 -92t-15 -135q0 -80 15 -136.5t48.5 -92.5t87 -52.5
t129.5 -16.5h221z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1213" 
d="M367 -16q-76 0 -133.5 16.5t-95 51.5t-56.5 91.5t-19 135.5q0 80 19 136t56.5 91.5t95 52t133.5 16.5q97 0 161.5 -30t96.5 -95q32 66 94 95.5t158 29.5q68 0 119.5 -14.5t87 -46t53.5 -82t18 -122.5v-63h-473q2 -46 13 -78t35 -52t65 -28.5t103 -8.5q63 0 118 5.5
t113 16.5v-92q-53 -13 -109 -19.5t-124 -6.5q-109 0 -174.5 29.5t-97.5 93.5q-32 -63 -96 -93t-161 -30zM870 482q-92 0 -135 -33.5t-51 -114.5h369q-3 41 -15 69t-34 45.5t-55 25.5t-79 8zM372 81q105 0 152.5 46t47.5 152q0 107 -47.5 152.5t-152.5 45.5q-53 0 -90.5 -11
t-61.5 -35t-35.5 -61.5t-11.5 -90.5t11.5 -90.5t35.5 -61.5t61.5 -35t90.5 -11z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="874" 
d="M496 987h120l-132 -140h-105zM116 802h373q83 0 141.5 -14t96.5 -46.5t55.5 -84.5t17.5 -128q0 -115 -41 -176.5t-131 -83.5l161 -269h-132l-147 256h-283v-256h-111v802zM227 698v-338h261q53 0 90.5 7.5t61.5 26.5t35 51.5t11 83.5q0 50 -11 82.5t-35 51.5t-61.5 27
t-90.5 8h-261z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="436" 
d="M348 796h120l-161 -170h-99zM98 559h75l20 -40q46 23 97 36t110 14v-103q-56 -2 -104.5 -11.5t-91.5 -24.5v-430h-106v559z" />
    <glyph glyph-name="uni0156" unicode="&#x156;" horiz-adv-x="874" 
d="M116 802h373q83 0 141.5 -14t96.5 -46.5t55.5 -84.5t17.5 -128q0 -115 -41 -176.5t-131 -83.5l161 -269h-132l-147 256h-283v-256h-111v802zM227 698v-338h261q53 0 90.5 7.5t61.5 26.5t35 51.5t11 83.5q0 50 -11 82.5t-35 51.5t-61.5 27t-90.5 8h-261zM373 -53h123
l-152 -145h-99z" />
    <glyph glyph-name="uni0157" unicode="&#x157;" horiz-adv-x="436" 
d="M98 559h75l20 -40q46 23 97 36t110 14v-103q-56 -2 -104.5 -11.5t-91.5 -24.5v-430h-106v559zM91 -53h123l-152 -145h-99z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="874" 
d="M232 987h96l109 -98l109 98h96l-138 -140h-133zM116 802h373q83 0 141.5 -14t96.5 -46.5t55.5 -84.5t17.5 -128q0 -115 -41 -176.5t-131 -83.5l161 -269h-132l-147 256h-283v-256h-111v802zM227 698v-338h261q53 0 90.5 7.5t61.5 26.5t35 51.5t11 83.5q0 50 -11 82.5
t-35 51.5t-61.5 27t-90.5 8h-261z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="436" 
d="M68 802h95l95 -120l95 120h95l-132 -170h-116zM98 559h75l20 -40q46 23 97 36t110 14v-103q-56 -2 -104.5 -11.5t-91.5 -24.5v-430h-106v559z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="827" 
d="M477 987h120l-132 -140h-105zM101 136q60 -17 136.5 -30t159.5 -13q68 0 113 6t72 20t38.5 37t11.5 57q0 35 -8 58t-32 38t-67 25t-114 20q-95 11 -157 26.5t-98 41.5t-50 67t-14 103q0 57 18 99.5t58.5 70.5t105 42t158.5 14q81 0 146 -10t126 -26v-110
q-63 16 -129.5 26.5t-141.5 10.5q-69 0 -113 -6t-69.5 -19.5t-35 -35t-9.5 -51.5q0 -33 8 -53.5t31 -34.5t66.5 -23.5t113.5 -19.5q96 -11 158 -27t98 -44t50.5 -70.5t14.5 -106.5q0 -62 -19 -106.5t-61 -73t-108 -41.5t-160 -13q-89 0 -164 12t-133 29v111z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="669" 
d="M425 796h120l-161 -170h-99zM74 106q54 -12 120.5 -21t144.5 -9q46 0 77.5 3t50.5 10.5t27.5 20.5t8.5 34q0 23 -6 38t-24 25t-51 16t-87 12q-78 8 -127.5 19t-78.5 31t-40 50t-11 75q0 44 13 75t43.5 51t81.5 29.5t126 9.5q66 0 125.5 -7t113.5 -19v-93q-55 12 -111 19
t-125 7q-49 0 -81 -3t-50.5 -10.5t-25.5 -20t-7 -32.5t6 -33t23.5 -22t50 -15t85.5 -12q78 -7 128.5 -19t79.5 -32.5t40.5 -52.5t11.5 -79q0 -45 -14 -77t-45.5 -52t-81.5 -29t-122 -9q-74 0 -145.5 8t-123.5 22v92z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="827" 
d="M352 987h133l138 -140h-96l-109 97l-109 -97h-96zM101 136q60 -17 136.5 -30t159.5 -13q68 0 113 6t72 20t38.5 37t11.5 57q0 35 -8 58t-32 38t-67 25t-114 20q-95 11 -157 26.5t-98 41.5t-50 67t-14 103q0 57 18 99.5t58.5 70.5t105 42t158.5 14q81 0 146 -10t126 -26
v-110q-63 16 -129.5 26.5t-141.5 10.5q-69 0 -113 -6t-69.5 -19.5t-35 -35t-9.5 -51.5q0 -33 8 -53.5t31 -34.5t66.5 -23.5t113.5 -19.5q96 -11 158 -27t98 -44t50.5 -70.5t14.5 -106.5q0 -62 -19 -106.5t-61 -73t-108 -41.5t-160 -13q-89 0 -164 12t-133 29v111z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="669" 
d="M277 802h116l132 -170h-95l-95 119l-95 -119h-95zM74 106q54 -12 120.5 -21t144.5 -9q46 0 77.5 3t50.5 10.5t27.5 20.5t8.5 34q0 23 -6 38t-24 25t-51 16t-87 12q-78 8 -127.5 19t-78.5 31t-40 50t-11 75q0 44 13 75t43.5 51t81.5 29.5t126 9.5q66 0 125.5 -7t113.5 -19
v-93q-55 12 -111 19t-125 7q-49 0 -81 -3t-50.5 -10.5t-25.5 -20t-7 -32.5t6 -33t23.5 -22t50 -15t85.5 -12q78 -7 128.5 -19t79.5 -32.5t40.5 -52.5t11.5 -79q0 -45 -14 -77t-45.5 -52t-81.5 -29t-122 -9q-74 0 -145.5 8t-123.5 22v92z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="827" 
d="M101 136q60 -17 136.5 -30t159.5 -13q68 0 113 6t72 20t38.5 37t11.5 57q0 35 -8 58t-32 38t-67 25t-114 20q-95 11 -157 26.5t-98 41.5t-50 67t-14 103q0 57 18 99.5t58.5 70.5t105 42t158.5 14q81 0 146 -10t126 -26v-110q-63 16 -129.5 26.5t-141.5 10.5
q-69 0 -113 -6t-69.5 -19.5t-35 -35t-9.5 -51.5q0 -33 8 -53.5t31 -34.5t66.5 -23.5t113.5 -19.5q96 -11 158 -27t98 -44t50.5 -70.5t14.5 -106.5q0 -59 -17 -102t-54.5 -71.5t-96.5 -43t-143 -16.5l-44 -43h16q65 0 91.5 -17t26.5 -59q0 -45 -28.5 -62.5t-97.5 -17.5
q-24 0 -45 1.5t-40 6.5v48q20 -4 40 -6t43 -2q45 0 58 7t13 25q0 16 -13 23.5t-58 7.5q-20 0 -37 -1.5t-34 -3.5v44l50 48q-81 2 -151 13.5t-124 27.5v111z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="669" 
d="M74 106q54 -12 120.5 -21t144.5 -9q46 0 77.5 3t50.5 10.5t27.5 20.5t8.5 34q0 23 -6 38t-24 25t-51 16t-87 12q-78 8 -127.5 19t-78.5 31t-40 50t-11 75q0 44 13 75t43.5 51t81.5 29.5t126 9.5q66 0 125.5 -7t113.5 -19v-93q-55 12 -111 19t-125 7q-49 0 -81 -3
t-50.5 -10.5t-25.5 -20t-7 -32.5t6 -33t23.5 -22t50 -15t85.5 -12q78 -7 128.5 -19t79.5 -32.5t40.5 -52.5t11.5 -79q0 -45 -13.5 -76.5t-44 -51.5t-79 -29.5t-118.5 -9.5l-43 -42h16q65 0 91.5 -17t26.5 -59q0 -45 -28.5 -62.5t-97.5 -17.5q-24 0 -45 1.5t-40 6.5v48
q20 -4 40 -6t43 -2q45 0 58 7t13 25q0 16 -13 23.5t-58 7.5q-20 0 -37 -1.5t-34 -3.5v44l51 49q-62 2 -119 10t-101 19v92z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="827" 
d="M213 987h96l109 -98l109 98h96l-138 -140h-133zM101 136q60 -17 136.5 -30t159.5 -13q68 0 113 6t72 20t38.5 37t11.5 57q0 35 -8 58t-32 38t-67 25t-114 20q-95 11 -157 26.5t-98 41.5t-50 67t-14 103q0 57 18 99.5t58.5 70.5t105 42t158.5 14q81 0 146 -10t126 -26
v-110q-63 16 -129.5 26.5t-141.5 10.5q-69 0 -113 -6t-69.5 -19.5t-35 -35t-9.5 -51.5q0 -33 8 -53.5t31 -34.5t66.5 -23.5t113.5 -19.5q96 -11 158 -27t98 -44t50.5 -70.5t14.5 -106.5q0 -62 -19 -106.5t-61 -73t-108 -41.5t-160 -13q-89 0 -164 12t-133 29v111z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="669" 
d="M145 802h95l95 -120l95 120h95l-132 -170h-116zM74 106q54 -12 120.5 -21t144.5 -9q46 0 77.5 3t50.5 10.5t27.5 20.5t8.5 34q0 23 -6 38t-24 25t-51 16t-87 12q-78 8 -127.5 19t-78.5 31t-40 50t-11 75q0 44 13 75t43.5 51t81.5 29.5t126 9.5q66 0 125.5 -7t113.5 -19
v-93q-55 12 -111 19t-125 7q-49 0 -81 -3t-50.5 -10.5t-25.5 -20t-7 -32.5t6 -33t23.5 -22t50 -15t85.5 -12q78 -7 128.5 -19t79.5 -32.5t40.5 -52.5t11.5 -79q0 -45 -14 -77t-45.5 -52t-81.5 -29t-122 -9q-74 0 -145.5 8t-123.5 22v92z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="791" 
d="M368 0h-28v698h-304v104h720v-104h-304v-698h-26l-60 -58h16q65 0 91.5 -17t26.5 -59q0 -45 -28.5 -62.5t-97.5 -17.5q-24 0 -45 1.5t-40 6.5v48q20 -4 40 -6t43 -2q45 0 58 7t13 25q0 16 -13 23.5t-58 7.5q-20 0 -37 -1.5t-34 -3.5v44z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="476" 
d="M264 -8q-43 6 -71.5 20.5t-46 37t-24.5 54.5t-7 72v292h-71v70l71 21v134h105v-134h199v-91h-199v-276q0 -34 6.5 -55t21 -33t38.5 -16t59 -4q40 0 79 6v-96q-26 -4 -52.5 -5t-53.5 -1l-48 -46h16q65 0 91.5 -17t26.5 -59q0 -45 -28.5 -62.5t-97.5 -17.5q-24 0 -45 1.5
t-40 6.5v48q20 -4 40 -6t43 -2q45 0 58 7t13 25q0 16 -13 23.5t-58 7.5q-20 0 -37 -1.5t-34 -3.5v44z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="791" 
d="M190 987h96l109 -98l109 98h96l-138 -140h-133zM340 698h-304v104h720v-104h-304v-698h-112v698z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="476" 
d="M350 860h105l-102 -255h-69zM333 -12q-64 0 -106 11.5t-67 35t-35 58.5t-10 83v292h-71v70l71 21v134h105v-134h199v-91h-199v-276q0 -34 6.5 -55t21 -33t38.5 -16t59 -4q40 0 79 6v-96q-23 -3 -44.5 -4.5t-46.5 -1.5z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="791" 
d="M340 324h-192v104h192v270h-304v104h720v-104h-304v-270h192v-104h-192v-324h-112v324z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="476" 
d="M45 327h70v141h-71v70l71 21v134h105v-134h199v-91h-199v-141h161v-88h-161v-47q0 -34 6.5 -55t21 -33t38.5 -16t59 -4q40 0 79 6v-96q-23 -3 -44.5 -4.5t-46.5 -1.5q-64 0 -106 11.5t-67 35t-35 58.5t-10 83v63h-70v88z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="911" 
d="M254 868q0 57 27.5 84t78.5 27q33 0 60 -9.5t49.5 -21.5t41.5 -21.5t37 -9.5q22 0 31.5 10.5t9.5 37.5v9h69v-15q0 -111 -106 -111q-33 0 -59.5 10t-49 21.5t-42 21.5t-37.5 10q-21 0 -30.5 -10.5t-9.5 -38.5v-10h-70v16zM456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5
t-20 148v498h112v-498q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h111v-498q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="779" 
d="M187 668q0 64 26 94t77 30q33 0 60 -11t50 -24t43.5 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24zM343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357
q0 -57 36.5 -87.5t119.5 -30.5q63 0 117 16t101 38v421h105v-559h-73l-21 41q-54 -25 -114 -41t-129 -16z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="911" 
d="M273 949h366v-68h-366v68zM456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5t-20 148v498h112v-498q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h111v-498q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="779" 
d="M218 756h342v-73h-342v73zM343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357q0 -57 36.5 -87.5t119.5 -30.5q63 0 117 16t101 38v421h105v-559h-73l-21 41q-54 -25 -114 -41t-129 -16z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="911" 
d="M456 842q-57 0 -95 8t-61 25.5t-32.5 45t-9.5 66.5h81q0 -19 5 -33t18 -23t36 -13t58 -4t58 4t36 13t18 23t5 33h82q0 -39 -9.5 -66.5t-32.5 -45t-61 -25.5t-96 -8zM456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5t-20 148v498h112v-498q0 -59 13 -99.5t41.5 -65
t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h111v-498q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="779" 
d="M389 643q-53 0 -88.5 8t-57 25t-30.5 44.5t-9 66.5h82q0 -23 4.5 -37t16 -22t31.5 -11t51 -3t51 3t31.5 11t16 22t4.5 37h82q0 -39 -9 -66.5t-30 -44.5t-56.5 -25t-89.5 -8zM343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357q0 -57 36.5 -87.5
t119.5 -30.5q63 0 117 16t101 38v421h105v-559h-73l-21 41q-54 -25 -114 -41t-129 -16z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="911" 
d="M456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5t-20 148v498h112v-498q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h111v-498q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="779" 
d="M389 617q-72 0 -109 25.5t-37 86.5t37 86t109 25t109 -25t37 -86t-37 -86.5t-109 -25.5zM389 677q42 0 62.5 11.5t20.5 40.5t-20.5 40t-62.5 11t-62 -11t-20 -40t20 -40.5t62 -11.5zM343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357q0 -57 36.5 -87.5
t119.5 -30.5q63 0 117 16t101 38v421h105v-559h-73l-21 41q-54 -25 -114 -41t-129 -16z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="911" 
d="M398 987h113l-121 -140h-96zM625 987h113l-121 -140h-96zM456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5t-20 148v498h112v-498q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h111v-498q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17z
" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="779" 
d="M353 802h112l-140 -170h-92zM570 802h111l-141 -170h-91zM343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357q0 -57 36.5 -87.5t119.5 -30.5q63 0 117 16t101 38v421h105v-559h-73l-21 41q-54 -25 -114 -41t-129 -16z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="911" 
d="M431 -105q0 -42 65 -42q23 0 40.5 2.5t33.5 4.5v-64q-20 -4 -40.5 -7t-49.5 -3q-68 0 -98 21.5t-30 66.5q0 31 15 56.5t53 54.5q-85 3 -145 22t-98.5 57.5t-56.5 97.5t-18 142v498h112v-498q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5
v498h111v-498q0 -81 -17 -139.5t-54 -97t-94.5 -58.5t-137.5 -24q-45 -29 -60.5 -48t-15.5 -42z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="779" 
d="M343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357q0 -57 36.5 -87.5t119.5 -30.5q63 0 117 16t101 38v421h105v-559h-19q-31 -19 -50.5 -33.5t-31 -26.5t-16 -23t-4.5 -24q0 -42 65 -42q23 0 40.5 2.5t33.5 4.5v-64q-20 -4 -40.5 -7t-49.5 -3
q-68 0 -98 21.5t-30 66.5q0 20 6 37t19.5 34t36.5 35t57 39l-13 24q-54 -25 -114 -41t-129 -16z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="1331" 
d="M589 987h133l138 -140h-96l-109 97l-109 -97h-96zM39 802h117l194 -702h21l191 702h206l191 -702h22l193 702h118l-222 -802h-199l-196 702h-19l-196 -702h-200z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="1076" 
d="M472 802h116l132 -170h-95l-95 119l-95 -119h-95zM32 559h112l144 -479h3l164 479h167l163 -479l2 1l137 478h120l-172 -559h-166l-168 477l-168 -477h-166z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="800" 
d="M335 987h133l138 -140h-96l-109 97l-109 -97h-96zM344 314l-328 488h131l253 -379l253 379h131l-330 -489v-313h-110v314z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="704" 
d="M294 802h116l132 -170h-95l-95 119l-95 -119h-95zM74 -117q17 -1 33.5 -1.5t36.5 -0.5q35 0 60 3.5t43 12t30 21.5t21 33l24 55h-54l-243 553h113l202 -460h23l204 460h112l-277 -616q-18 -43 -39.5 -73t-49 -48.5t-64 -27t-85.5 -8.5q-26 0 -46.5 0.5t-43.5 3.5v93z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="800" 
d="M239 960h92v-85h-92v85zM471 960h92v-85h-92v85zM344 314l-328 488h131l253 -379l253 379h131l-330 -489v-313h-110v314z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="780" 
d="M462 987h120l-132 -140h-105zM40 103l528 594h-498v105h640v-104l-527 -593h557v-105h-700v103z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="672" 
d="M430 796h120l-161 -170h-99zM63 90l389 378h-376v91h519v-91l-390 -378h403v-90h-545v90z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="780" 
d="M347 960h112v-85h-112v85zM40 103l528 594h-498v105h640v-104l-527 -593h557v-105h-700v103z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="672" 
d="M284 768h112v-98h-112v98zM63 90l389 378h-376v91h519v-91l-390 -378h403v-90h-545v90z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="780" 
d="M198 987h96l109 -98l109 98h96l-138 -140h-133zM40 103l528 594h-498v105h640v-104l-527 -593h557v-105h-700v103z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="672" 
d="M150 802h95l95 -120l95 120h95l-132 -170h-116zM63 90l389 378h-376v91h519v-91l-390 -378h403v-90h-545v90z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="424" 
d="M127 609q0 53 11 92t36.5 65t67.5 39t103 13q28 0 52.5 -1.5t50.5 -4.5v-93q-23 2 -45.5 3t-45.5 1q-35 0 -59 -5.5t-38.5 -18t-21 -33.5t-6.5 -52v-614h-105v609z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="736" 
d="M78 448h230v149q0 56 12 97.5t39 69t71 41t109 13.5q31 0 56.5 -1.5t54.5 -4.5v-99q-26 3 -50 3.5t-49 0.5q-38 0 -64 -5.5t-42 -19t-23 -35.5t-7 -54v-155h235v-89h-235v-352q0 -56 -11.5 -97.5t-38.5 -69t-70.5 -41t-106.5 -13.5q-30 0 -55.5 1.5t-54.5 4.5v99
q25 -3 49 -3.5t49 -0.5q38 0 63 5.5t40.5 19t22 35.5t6.5 54v358h-230v89z" />
    <glyph glyph-name="Ohorn" unicode="&#x1a0;" horiz-adv-x="941" 
d="M470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24q44 0 82.5 -4t72.5 -13h8q43 0 62 25q15 21 15 56v12h114q0 -47 -17.5 -86.5t-60.5 -62.5q65 -50 95 -134.5t30 -209.5q0 -110 -23.5 -189t-72.5 -129.5t-124.5 -74.5
t-180.5 -24zM470 90q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17z" />
    <glyph glyph-name="ohorn" unicode="&#x1a1;" horiz-adv-x="744" 
d="M372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5q39 0 73 -4t63 -13h20q43 0 62 25q15 21 15 56v12h114q0 -55 -23.5 -97.5t-77.5 -61.5q32 -36 47.5 -88.5t15.5 -124.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81
q105 0 152.5 46t47.5 152q0 107 -47.5 152.5t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46z" />
    <glyph glyph-name="Uhorn" unicode="&#x1af;" horiz-adv-x="911" 
d="M456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5t-20 148v498h112v-498q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h66q28 6 40 27q10 24 10 53v5h107q0 -59 -26 -100.5t-86 -56.5v-426q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17
z" />
    <glyph glyph-name="uhorn" unicode="&#x1b0;" horiz-adv-x="779" 
d="M343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357q0 -57 36.5 -87.5t119.5 -30.5q63 0 117 16t101 38v421h60q29 5 41 27q10 23 10 53v5h107q0 -60 -26.5 -101t-86.5 -56v-487h-73l-21 41q-54 -25 -114 -41t-129 -16z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="866" 
d="M492 987h120l-132 -140h-105zM340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="734" 
d="M457 1055h120l-161 -170h-99zM367 617q-72 0 -109 25.5t-37 86.5t37 86t109 25t109 -25t37 -86t-37 -86.5t-109 -25.5zM367 677q42 0 62.5 11.5t20.5 40.5t-20.5 40t-62.5 11t-62 -11t-20 -40t20 -40.5t62 -11.5zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131
t163.5 39h241v35q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20
t-28 -70t28.5 -72.5t105.5 -22.5z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="1302" 
d="M755 987h120l-132 -140h-105zM634 227h-360l-146 -227h-131l515 802h709v-104h-476v-242h442v-105h-442v-247h476v-104h-587v227zM634 331v367h-57l-236 -367h293z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="1173" 
d="M687 787h120l-161 -170h-99zM317 -16q-56 0 -100.5 10t-75.5 32t-47.5 56.5t-16.5 83.5q0 89 51.5 127.5t163.5 38.5h241v43q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57 8.5t52 5.5t56 3t69 1q106 0 164 -26.5t82 -75.5q32 51 90.5 76.5
t147.5 25.5q68 0 120 -14.5t87 -46t53 -82t18 -122.5v-64h-473q2 -46 13 -77.5t35.5 -51t65 -28t101.5 -8.5q63 0 118 5.5t113 16.5v-93q-53 -13 -110 -19.5t-126 -6.5q-96 0 -159.5 19t-100.5 63q-50 -34 -119.5 -58t-155.5 -24zM830 481q-92 0 -135 -34t-51 -115h369
q-6 82 -48.5 115.5t-134.5 33.5zM324 68q70 0 128 19.5t101 47.5q-16 47 -19 111h-227q-68 0 -96 -19t-28 -68q0 -52 32 -71.5t109 -19.5z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="941" 
d="M529 987h120l-132 -140h-105zM54 40l77 82h1l1 1q-32 51 -47.5 119t-15.5 159q0 110 23.5 188.5t72 129.5t124 75t180.5 24q173 0 267 -66l67 66l75 -56l-75 -79l2 -3h3l-2 -2q33 -50 48.5 -118.5t15.5 -158.5q0 -110 -23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24
q-173 0 -267 66l-66 -66zM184 401q0 -60 8 -107.5t25 -82.5l439 460q-66 41 -186 41q-78 0 -132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143zM470 90q78 0 132.5 17t89 54t50 96.5t15.5 143.5q0 60 -8 107.5t-25 82.5l-439 -459q34 -22 79.5 -32t105.5 -10z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="744" 
d="M462 796h120l-161 -170h-99zM57 52l48 43q-42 68 -42 184q0 159 77 227.5t232 68.5q62 0 110.5 -10t86.5 -32l47 42l70 -68l-47 -43q42 -67 42 -185q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5q-62 0 -111 10t-86 32l-48 -42zM173 279q0 -67 18 -109l301 285
q-44 22 -120 22q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5zM372 81q105 0 152.5 46t47.5 152q0 69 -19 110l-302 -285q45 -23 121 -23z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="827" 
d="M101 136q60 -17 136.5 -30t159.5 -13q68 0 113 6t72 20t38.5 37t11.5 57q0 35 -8 58t-32 38t-67 25t-114 20q-95 11 -157 26.5t-98 41.5t-50 67t-14 103q0 57 18 99.5t58.5 70.5t105 42t158.5 14q81 0 146 -10t126 -26v-110q-63 16 -129.5 26.5t-141.5 10.5
q-69 0 -113 -6t-69.5 -19.5t-35 -35t-9.5 -51.5q0 -33 8 -53.5t31 -34.5t66.5 -23.5t113.5 -19.5q96 -11 158 -27t98 -44t50.5 -70.5t14.5 -106.5q0 -62 -19 -106.5t-61 -73t-108 -41.5t-160 -13q-89 0 -164 12t-133 29v111zM365 -53h123l-152 -145h-99z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="669" 
d="M74 106q54 -12 120.5 -21t144.5 -9q46 0 77.5 3t50.5 10.5t27.5 20.5t8.5 34q0 23 -6 38t-24 25t-51 16t-87 12q-78 8 -127.5 19t-78.5 31t-40 50t-11 75q0 44 13 75t43.5 51t81.5 29.5t126 9.5q66 0 125.5 -7t113.5 -19v-93q-55 12 -111 19t-125 7q-49 0 -81 -3
t-50.5 -10.5t-25.5 -20t-7 -32.5t6 -33t23.5 -22t50 -15t85.5 -12q78 -7 128.5 -19t79.5 -32.5t40.5 -52.5t11.5 -79q0 -45 -14 -77t-45.5 -52t-81.5 -29t-122 -9q-74 0 -145.5 8t-123.5 22v92zM282 -53h123l-152 -145h-99z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="791" 
d="M340 698h-304v104h720v-104h-304v-698h-112v698zM340 -53h123l-152 -145h-99z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="476" 
d="M333 -12q-64 0 -106 11.5t-67 35t-35 58.5t-10 83v292h-71v70l71 21v134h105v-134h199v-91h-199v-276q0 -34 6.5 -55t21 -33t38.5 -16t59 -4q40 0 79 6v-96q-23 -3 -44.5 -4.5t-46.5 -1.5zM244 -53h123l-152 -145h-99z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="302" 
d="M-70 -127q15 -2 35.5 -3t36.5 -1q51 0 73.5 23.5t22.5 83.5v583h106v-578q0 -51 -9 -88t-31 -60.5t-57.5 -35t-88.5 -11.5q-23 0 -45.5 1t-42.5 4v82z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="600" 
d="M242 802h116l132 -170h-95l-95 119l-95 -119h-95z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="600" 
d="M110 802h95l95 -120l95 120h95l-132 -170h-116z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="600" 
d="M300 643q-53 0 -88.5 8t-57 25t-30.5 44.5t-9 66.5h82q0 -23 4.5 -37t16 -22t31.5 -11t51 -3t51 3t31.5 11t16 22t4.5 37h82q0 -39 -9 -66.5t-30 -44.5t-56.5 -25t-89.5 -8z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="600" 
d="M244 768h112v-98h-112v98z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="600" 
d="M300 617q-72 0 -109 25.5t-37 86.5t37 86t109 25t109 -25t37 -86t-37 -86.5t-109 -25.5zM300 677q42 0 62.5 11.5t20.5 40.5t-20.5 40t-62.5 11t-62 -11t-20 -40t20 -40.5t62 -11.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="600" 
d="M269 -105q0 -42 65 -42q23 0 40.5 2.5t33.5 4.5v-64q-20 -4 -40.5 -7t-49.5 -3q-68 0 -98 21.5t-30 66.5q0 20 6 37t20 34.5t37 35.5t58 39l73 -10q-35 -21 -57.5 -36.5t-35 -28.5t-17.5 -24.5t-5 -25.5z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="600" 
d="M97 668q0 64 26 94t77 30q33 0 60 -11t50 -24t43.5 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="600" 
d="M195 802h112l-140 -170h-92zM412 802h111l-141 -170h-91z" />
    <glyph glyph-name="gravecomb" unicode="&#x300;" horiz-adv-x="0" 
d="M-211 802h120l140 -170h-99z" />
    <glyph glyph-name="acutecomb" unicode="&#x301;" horiz-adv-x="0" 
d="M90 802h120l-161 -170h-99z" />
    <glyph glyph-name="uni0302" unicode="&#x302;" horiz-adv-x="0" 
 />
    <glyph glyph-name="tildecomb" unicode="&#x303;" horiz-adv-x="0" 
d="M-202 668q0 64 26 94t77 30q33 0 59.5 -11t50 -24t44 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24z" />
    <glyph glyph-name="uni0304" unicode="&#x304;" horiz-adv-x="0" 
d="M-171 756h342v-73h-342v73z" />
    <glyph glyph-name="uni0306" unicode="&#x306;" horiz-adv-x="0" 
d="M0 643q-53 0 -88.5 8t-57 25t-30.5 44.5t-9 66.5h82q0 -23 4.5 -37t16 -22t31.5 -11t51 -3t51 3t31.5 11t16 22t4.5 37h82q0 -39 -9 -66.5t-30 -44.5t-56.5 -25t-89.5 -8z" />
    <glyph glyph-name="uni0307" unicode="&#x307;" horiz-adv-x="0" 
d="M-56 768h112v-98h-112v98z" />
    <glyph glyph-name="uni0308" unicode="&#x308;" horiz-adv-x="0" 
d="M-155 768h88v-98h-88v98zM67 768h88v-98h-88v98z" />
    <glyph glyph-name="uni030A" unicode="&#x30a;" horiz-adv-x="0" 
d="M0 617q-72 0 -109 25.5t-37 86.5t37 86t109 25t109 -25t37 -86t-37 -86.5t-109 -25.5zM0 677q42 0 62.5 11.5t20.5 40.5t-20.5 40t-62.5 11t-62 -11t-20 -40t20 -40.5t62 -11.5z" />
    <glyph glyph-name="uni030B" unicode="&#x30b;" horiz-adv-x="0" 
d="M-36 802h112l-140 -170h-92zM181 802h111l-141 -170h-91z" />
    <glyph glyph-name="uni030C" unicode="&#x30c;" horiz-adv-x="0" 
d="M-190 802h95l95 -120l95 120h95l-132 -170h-116z" />
    <glyph glyph-name="uni0312" unicode="&#x312;" horiz-adv-x="0" 
d="M59 632h-119l160 170h99z" />
    <glyph glyph-name="uni0326" unicode="&#x326;" horiz-adv-x="0" 
d="M-58 -53h123l-152 -145h-99z" />
    <glyph glyph-name="uni0327" unicode="&#x327;" horiz-adv-x="0" 
d="M-28 2h58l-62 -60h16q65 0 91.5 -17t26.5 -59q0 -45 -28.5 -62.5t-97.5 -17.5q-24 0 -45 1.5t-40 6.5v48q20 -4 40 -6t43 -2q45 0 58 7t13 25q0 16 -13 23.5t-58 7.5q-20 0 -37 -1.5t-34 -3.5v44z" />
    <glyph glyph-name="uni0328" unicode="&#x328;" horiz-adv-x="0" 
d="M-68 -105q0 -42 65 -42q23 0 40.5 2.5t33.5 4.5v-64q-20 -4 -40.5 -7t-49.5 -3q-68 0 -98 21.5t-30 66.5q0 20 6 37t20 34.5t37 35.5t58 39l73 -10q-35 -21 -57.5 -36.5t-35 -28.5t-17.5 -24.5t-5 -25.5z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" 
d="M50 96h126q-130 90 -130 321q0 106 18.5 182t59 124.5t104 71.5t152.5 23t152.5 -23t104 -71.5t59 -124.5t18.5 -182q0 -231 -130 -321h126v-96h-287v78q51 12 85.5 40t56 69.5t31 95.5t9.5 118q0 88 -11.5 148.5t-38 97.5t-69.5 53.5t-106 16.5t-106 -16.5t-69.5 -53.5
t-38 -97.5t-11.5 -148.5q0 -64 9.5 -118t31 -95.5t56 -69.5t85.5 -40v-78h-287v96z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" 
d="M116 454h-76v105h680v-105h-76v-454h-111v454h-306v-454h-111v454z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="1331" 
d="M476 987h121l116 -140h-104zM39 802h117l194 -702h21l191 702h206l191 -702h22l193 702h118l-222 -802h-199l-196 702h-19l-196 -702h-200z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="1076" 
d="M319 802h120l140 -170h-99zM32 559h112l144 -479h3l164 479h167l163 -479l2 1l137 478h120l-172 -559h-166l-168 477l-168 -477h-166z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="1331" 
d="M714 987h120l-132 -140h-105zM39 802h117l194 -702h21l191 702h206l191 -702h22l193 702h118l-222 -802h-199l-196 702h-19l-196 -702h-200z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="1076" 
d="M620 796h120l-161 -170h-99zM32 559h112l144 -479h3l164 479h167l163 -479l2 1l137 478h120l-172 -559h-166l-168 477l-168 -477h-166z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="1331" 
d="M493 960h92v-85h-92v85zM725 960h92v-85h-92v85zM39 802h117l194 -702h21l191 702h206l191 -702h22l193 702h118l-222 -802h-199l-196 702h-19l-196 -702h-200z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="1076" 
d="M375 768h88v-98h-88v98zM597 768h88v-98h-88v98zM32 559h112l144 -479h3l164 479h167l163 -479l2 1l137 478h120l-172 -559h-166l-168 477l-168 -477h-166z" />
    <glyph glyph-name="uni1EA0" unicode="&#x1ea0;" horiz-adv-x="866" 
d="M340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317zM368 -102h130v-115h-130v115z" />
    <glyph glyph-name="uni1EA1" unicode="&#x1ea1;" horiz-adv-x="734" 
d="M311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68
q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5zM315 -125h104v-92h-104v92z" />
    <glyph glyph-name="uni1EA2" unicode="&#x1ea2;" horiz-adv-x="866" 
d="M395 939q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331
l-146 379h-26l-145 -379h317z" />
    <glyph glyph-name="uni1EA3" unicode="&#x1ea3;" horiz-adv-x="734" 
d="M329 701q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35
q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5
t105.5 -22.5z" />
    <glyph glyph-name="uni1EA4" unicode="&#x1ea4;" horiz-adv-x="866" 
d="M367 987h133l138 -140h-96l-109 97l-109 -97h-96zM340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317zM523 1194h120l-161 -170h-99z" />
    <glyph glyph-name="uni1EA5" unicode="&#x1ea5;" horiz-adv-x="734" 
d="M309 802h116l132 -170h-95l-95 119l-95 -119h-95zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87
v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5zM457 1009h120l-161 -170h-99z" />
    <glyph glyph-name="uni1EA6" unicode="&#x1ea6;" horiz-adv-x="866" 
d="M367 987h133l138 -140h-96l-109 97l-109 -97h-96zM340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317zM222 1200h120l140 -170h-99z" />
    <glyph glyph-name="uni1EA7" unicode="&#x1ea7;" horiz-adv-x="734" 
d="M309 802h116l132 -170h-95l-95 119l-95 -119h-95zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87
v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5zM156 1015h120l140 -170h-99z" />
    <glyph glyph-name="uni1EA8" unicode="&#x1ea8;" horiz-adv-x="866" 
d="M562 1010q26 1 43.5 8t17.5 21q0 7 -6.5 12.5t-16.5 5.5t-25 -2.5t-32 -18.5l-46 37q22 23 48 32t55 9h6q44 -1 69 -21.5t25 -52.5q-1 -29 -21 -47.5t-48 -25.5l-2 -19h-62zM367 987h133l138 -140h-96l-109 97l-109 -97h-96zM340 802h185l312 -802h-118l-88 227h-396
l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317z" />
    <glyph glyph-name="uni1EA9" unicode="&#x1ea9;" horiz-adv-x="734" 
d="M494 829q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM309 802h116l132 -170h-95l-95 119l-95 -119h-95zM311 -16
q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68
q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5z" />
    <glyph glyph-name="uni1EAA" unicode="&#x1eaa;" horiz-adv-x="866" 
d="M367 987h133l138 -140h-96l-109 97l-109 -97h-96zM340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317zM231 1066q0 64 26 94t77 30q33 0 59.5 -11t50 -24t44 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124
q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24z" />
    <glyph glyph-name="uni1EAB" unicode="&#x1eab;" horiz-adv-x="734" 
d="M309 802h116l132 -170h-95l-95 119l-95 -119h-95zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87
v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5zM165 881q0 64 26 94t77 30q33 0 59.5 -11t50 -24t44 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11
t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24z" />
    <glyph glyph-name="uni1EAC" unicode="&#x1eac;" horiz-adv-x="866" 
d="M367 987h133l138 -140h-96l-109 97l-109 -97h-96zM340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317zM368 -217v115h130v-115h-130z" />
    <glyph glyph-name="uni1EAD" unicode="&#x1ead;" horiz-adv-x="734" 
d="M309 802h116l132 -170h-95l-95 119l-95 -119h-95zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87
v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5zM315 -125h104v-92h-104v92z" />
    <glyph glyph-name="uni1EAE" unicode="&#x1eae;" horiz-adv-x="866" 
d="M433 842q-57 0 -95 8t-61 25.5t-32.5 45t-9.5 66.5h81q0 -19 5 -33t18 -23t36 -13t58 -4t58 4t36 13t18 23t5 33h82q0 -39 -9.5 -66.5t-32.5 -45t-61 -25.5t-96 -8zM340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317z
M523 1194h120l-161 -170h-99z" />
    <glyph glyph-name="uni1EAF" unicode="&#x1eaf;" horiz-adv-x="734" 
d="M367 643q-53 0 -88.5 8t-57 25t-30.5 44.5t-9 66.5h82q0 -23 4.5 -37t16 -22t31.5 -11t51 -3t51 3t31.5 11t16 22t4.5 37h82q0 -39 -9 -66.5t-30 -44.5t-56.5 -25t-89.5 -8zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33
t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5zM457 994
h120l-161 -170h-99z" />
    <glyph glyph-name="uni1EB0" unicode="&#x1eb0;" horiz-adv-x="866" 
d="M433 842q-57 0 -95 8t-61 25.5t-32.5 45t-9.5 66.5h81q0 -19 5 -33t18 -23t36 -13t58 -4t58 4t36 13t18 23t5 33h82q0 -39 -9.5 -66.5t-32.5 -45t-61 -25.5t-96 -8zM340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317z
M222 1200h120l140 -170h-99z" />
    <glyph glyph-name="uni1EB1" unicode="&#x1eb1;" horiz-adv-x="734" 
d="M367 643q-53 0 -88.5 8t-57 25t-30.5 44.5t-9 66.5h82q0 -23 4.5 -37t16 -22t31.5 -11t51 -3t51 3t31.5 11t16 22t4.5 37h82q0 -39 -9 -66.5t-30 -44.5t-56.5 -25t-89.5 -8zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33
t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5zM156 1000
h120l140 -170h-99z" />
    <glyph glyph-name="uni1EB2" unicode="&#x1eb2;" horiz-adv-x="866" 
d="M388 1077q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM433 842q-57 0 -95 8t-61 25.5t-32.5 45t-9.5 66.5h81q0 -19 5 -33
t18 -23t36 -13t58 -4t58 4t36 13t18 23t5 33h82q0 -39 -9.5 -66.5t-32.5 -45t-61 -25.5t-96 -8zM340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317z" />
    <glyph glyph-name="uni1EB3" unicode="&#x1eb3;" horiz-adv-x="734" 
d="M329 874q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM367 643q-53 0 -88.5 8t-57 25t-30.5 44.5t-9 66.5h82q0 -23 4.5 -37
t16 -22t31.5 -11t51 -3t51 3t31.5 11t16 22t4.5 37h82q0 -39 -9 -66.5t-30 -44.5t-56.5 -25t-89.5 -8zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3
t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5z" />
    <glyph glyph-name="uni1EB4" unicode="&#x1eb4;" horiz-adv-x="866" 
d="M433 842q-57 0 -95 8t-61 25.5t-32.5 45t-9.5 66.5h81q0 -19 5 -33t18 -23t36 -13t58 -4t58 4t36 13t18 23t5 33h82q0 -39 -9.5 -66.5t-32.5 -45t-61 -25.5t-96 -8zM340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317z
M231 1066q0 64 26 94t77 30q33 0 59.5 -11t50 -24t44 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24z" />
    <glyph glyph-name="uni1EB5" unicode="&#x1eb5;" horiz-adv-x="734" 
d="M367 643q-53 0 -88.5 8t-57 25t-30.5 44.5t-9 66.5h82q0 -23 4.5 -37t16 -22t31.5 -11t51 -3t51 3t31.5 11t16 22t4.5 37h82q0 -39 -9 -66.5t-30 -44.5t-56.5 -25t-89.5 -8zM311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33
t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5zM165 866
q0 64 26 94t77 30q33 0 59.5 -11t50 -24t44 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24z" />
    <glyph glyph-name="uni1EB6" unicode="&#x1eb6;" horiz-adv-x="866" 
d="M340 802h185l312 -802h-118l-88 227h-396l-87 -227h-119zM591 331l-146 379h-26l-145 -379h317zM368 -102h130v-115h-130v115zM433 886q-53 0 -88.5 8t-57 25t-30.5 44.5t-9 66.5h82q0 -23 4.5 -37t16 -22t31.5 -11t51 -3t51 3t31.5 11t16 22t4.5 37h82q0 -39 -9 -66.5
t-30 -44.5t-56.5 -25t-89.5 -8z" />
    <glyph glyph-name="uni1EB7" unicode="&#x1eb7;" horiz-adv-x="734" 
d="M311 -16q-113 0 -173.5 43t-60.5 143q0 92 51.5 131t163.5 39h241v35q0 28 -8 48t-28.5 33t-56.5 19t-92 6q-60 0 -116 -5t-113 -13v94q31 5 57.5 8.5t53.5 5.5t57.5 3t69.5 1q81 0 135 -12t86.5 -36t46.5 -61t14 -87v-379h-73l-20 39q-43 -23 -103 -39t-132 -16zM317 68
q67 0 122.5 15t93.5 35v135h-226q-68 0 -96 -20t-28 -70t28.5 -72.5t105.5 -22.5zM315 -125h104v-92h-104v92zM367 643q-53 0 -88.5 8t-57 25t-30.5 44.5t-9 66.5h82q0 -23 4.5 -37t16 -22t31.5 -11t51 -3t51 3t31.5 11t16 22t4.5 37h82q0 -39 -9 -66.5t-30 -44.5t-56.5 -25
t-89.5 -8z" />
    <glyph glyph-name="uni1EB8" unicode="&#x1eb8;" horiz-adv-x="783" 
d="M116 802h587v-104h-476v-242h441v-105h-441v-247h476v-104h-587v802zM327 -102h130v-115h-130v115z" />
    <glyph glyph-name="uni1EB9" unicode="&#x1eb9;" horiz-adv-x="704" 
d="M383 -16q-87 0 -148 16t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138t56 92t94 51t132.5 16q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5t113 16.5v-93q-54 -13 -111 -19.5t-126 -6.5zM361 481
q-91 0 -134 -33.5t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5zM300 -125h104v-92h-104v92z" />
    <glyph glyph-name="uni1EBA" unicode="&#x1eba;" horiz-adv-x="783" 
d="M354 939q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM116 802h587v-104h-476v-242h441v-105h-441v-247h476v-104h-587v802z" />
    <glyph glyph-name="uni1EBB" unicode="&#x1ebb;" horiz-adv-x="704" 
d="M315 701q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM383 -16q-87 0 -148 16t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138
t56 92t94 51t132.5 16q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5t113 16.5v-93q-54 -13 -111 -19.5t-126 -6.5zM361 481q-91 0 -134 -33.5t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5z
" />
    <glyph glyph-name="uni1EBC" unicode="&#x1ebc;" horiz-adv-x="783" 
d="M116 802h587v-104h-476v-242h441v-105h-441v-247h476v-104h-587v802zM213 911q0 64 26 94t77 30q33 0 59.5 -11t50 -24t44 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24z
" />
    <glyph glyph-name="uni1EBD" unicode="&#x1ebd;" horiz-adv-x="704" 
d="M383 -16q-87 0 -148 16t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138t56 92t94 51t132.5 16q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5t113 16.5v-93q-54 -13 -111 -19.5t-126 -6.5zM361 481
q-91 0 -134 -33.5t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5zM163 668q0 64 26 94t77 30q33 0 59.5 -11t50 -24t44 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24z
" />
    <glyph glyph-name="uni1EBE" unicode="&#x1ebe;" horiz-adv-x="783" 
d="M349 987h133l138 -140h-96l-109 97l-109 -97h-96zM116 802h587v-104h-476v-242h441v-105h-441v-247h476v-104h-587v802zM505 1194h120l-161 -170h-99z" />
    <glyph glyph-name="uni1EBF" unicode="&#x1ebf;" horiz-adv-x="704" 
d="M307 802h116l132 -170h-95l-95 119l-95 -119h-95zM383 -16q-87 0 -148 16t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138t56 92t94 51t132.5 16q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5
t113 16.5v-93q-54 -13 -111 -19.5t-126 -6.5zM361 481q-91 0 -134 -33.5t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5zM455 1009h120l-161 -170h-99z" />
    <glyph glyph-name="uni1EC0" unicode="&#x1ec0;" horiz-adv-x="783" 
d="M349 987h133l138 -140h-96l-109 97l-109 -97h-96zM116 802h587v-104h-476v-242h441v-105h-441v-247h476v-104h-587v802zM204 1200h120l140 -170h-99z" />
    <glyph glyph-name="uni1EC1" unicode="&#x1ec1;" horiz-adv-x="704" 
d="M307 802h116l132 -170h-95l-95 119l-95 -119h-95zM383 -16q-87 0 -148 16t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138t56 92t94 51t132.5 16q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5
t113 16.5v-93q-54 -13 -111 -19.5t-126 -6.5zM361 481q-91 0 -134 -33.5t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5zM154 1015h120l140 -170h-99z" />
    <glyph glyph-name="uni1EC2" unicode="&#x1ec2;" horiz-adv-x="783" 
d="M562 1020q26 1 43.5 8t17.5 21q0 7 -6.5 12.5t-16.5 5.5t-25 -2.5t-32 -18.5l-46 37q22 23 48 32t55 9h6q44 -1 69 -21.5t25 -52.5q-1 -29 -21 -47.5t-48 -25.5l-2 -19h-62zM349 987h133l138 -140h-96l-109 97l-109 -97h-96zM116 802h587v-104h-476v-242h441v-105h-441
v-247h476v-104h-587v802z" />
    <glyph glyph-name="uni1EC3" unicode="&#x1ec3;" horiz-adv-x="704" 
d="M494 829q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM307 802h116l132 -170h-95l-95 119l-95 -119h-95zM383 -16q-87 0 -148 16
t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138t56 92t94 51t132.5 16q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5t113 16.5v-93q-54 -13 -111 -19.5t-126 -6.5zM361 481q-91 0 -134 -33.5
t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5z" />
    <glyph glyph-name="uni1EC4" unicode="&#x1ec4;" horiz-adv-x="783" 
d="M349 987h133l138 -140h-96l-109 97l-109 -97h-96zM116 802h587v-104h-476v-242h441v-105h-441v-247h476v-104h-587v802zM213 1066q0 64 26 94t77 30q33 0 59.5 -11t50 -24t44 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24
t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24z" />
    <glyph glyph-name="uni1EC5" unicode="&#x1ec5;" horiz-adv-x="704" 
d="M307 802h116l132 -170h-95l-95 119l-95 -119h-95zM383 -16q-87 0 -148 16t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138t56 92t94 51t132.5 16q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5
t113 16.5v-93q-54 -13 -111 -19.5t-126 -6.5zM361 481q-91 0 -134 -33.5t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5zM163 881q0 64 26 94t77 30q33 0 59.5 -11t50 -24t44 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24
t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24z" />
    <glyph glyph-name="uni1EC6" unicode="&#x1ec6;" horiz-adv-x="783" 
d="M349 987h133l138 -140h-96l-109 97l-109 -97h-96zM116 802h587v-104h-476v-242h441v-105h-441v-247h476v-104h-587v802zM327 -102h130v-115h-130v115z" />
    <glyph glyph-name="uni1EC7" unicode="&#x1ec7;" horiz-adv-x="704" 
d="M307 802h116l132 -170h-95l-95 119l-95 -119h-95zM383 -16q-87 0 -148 16t-99 51.5t-55.5 91.5t-17.5 135q0 81 18.5 138t56 92t94 51t132.5 16q68 0 120.5 -14.5t88.5 -46t54.5 -82t18.5 -122.5v-63h-473q2 -46 13 -78t35.5 -51.5t65.5 -28t102 -8.5q63 0 118 5.5
t113 16.5v-93q-54 -13 -111 -19.5t-126 -6.5zM361 481q-91 0 -134 -33.5t-52 -113.5h362q-1 41 -11 69t-31 45.5t-54 25t-80 7.5zM300 -125h104v-92h-104v92z" />
    <glyph glyph-name="uni1EC8" unicode="&#x1ec8;" horiz-adv-x="343" 
d="M132 916q26 1 43.5 8t17.5 21q0 7 -6.5 12.5t-16.5 5.5t-25 -2.5t-32 -18.5l-46 37q22 23 48 32t55 9h6q44 -1 69 -21.5t25 -52.5q-1 -29 -21 -47.5t-48 -25.5l-2 -19h-62zM116 802h111v-802h-111v802z" />
    <glyph glyph-name="uni1EC9" unicode="&#x1ec9;" horiz-adv-x="303" 
d="M108 701q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM98 559h107v-559h-107v559z" />
    <glyph glyph-name="uni1ECA" unicode="&#x1eca;" horiz-adv-x="343" 
d="M116 802h111v-802h-111v802zM119 -125h104v-92h-104v92z" />
    <glyph glyph-name="uni1ECB" unicode="&#x1ecb;" horiz-adv-x="303" 
d="M101 717h104v-92h-104v92zM98 559h107v-559h-107v559zM101 -125h104v-92h-104v92z" />
    <glyph glyph-name="uni1ECC" unicode="&#x1ecc;" horiz-adv-x="941" 
d="M470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24t180.5 -24t124.5 -75t72.5 -129.5t23.5 -188.5t-23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24zM470 90q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5
t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17zM419 -125h104v-92h-104v92z" />
    <glyph glyph-name="uni1ECD" unicode="&#x1ecd;" horiz-adv-x="744" 
d="M372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5t232 -68.5t77 -227.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81q105 0 152.5 46t47.5 152q0 107 -47.5 152.5t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35
t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46zM307 -102h130v-115h-130v115z" />
    <glyph glyph-name="uni1ECE" unicode="&#x1ece;" horiz-adv-x="941" 
d="M433 939q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5
t72 129.5t124 75t180.5 24t180.5 -24t124.5 -75t72.5 -129.5t23.5 -188.5t-23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24zM470 90q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143
t50 -96.5t88.5 -54.5t132 -17z" />
    <glyph glyph-name="uni1ECF" unicode="&#x1ecf;" horiz-adv-x="744" 
d="M335 692q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5
q0 159 77 227.5t232 68.5t232 -68.5t77 -227.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81q105 0 152.5 46t47.5 152q0 107 -47.5 152.5t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46z" />
    <glyph glyph-name="uni1ED0" unicode="&#x1ed0;" horiz-adv-x="941" 
d="M404 987h133l138 -140h-96l-109 97l-109 -97h-96zM470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24t180.5 -24t124.5 -75t72.5 -129.5t23.5 -188.5t-23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24zM470 90
q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17zM560 1194h120l-161 -170h-99z" />
    <glyph glyph-name="uni1ED1" unicode="&#x1ed1;" horiz-adv-x="744" 
d="M314 802h116l132 -170h-95l-95 119l-95 -119h-95zM372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5t232 -68.5t77 -227.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81q105 0 152.5 46t47.5 152q0 107 -47.5 152.5
t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46zM462 1009h120l-161 -170h-99z" />
    <glyph glyph-name="uni1ED2" unicode="&#x1ed2;" horiz-adv-x="941" 
d="M404 987h133l138 -140h-96l-109 97l-109 -97h-96zM470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24t180.5 -24t124.5 -75t72.5 -129.5t23.5 -188.5t-23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24zM470 90
q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17zM259 1200h120l140 -170h-99z" />
    <glyph glyph-name="uni1ED3" unicode="&#x1ed3;" horiz-adv-x="744" 
d="M314 802h116l132 -170h-95l-95 119l-95 -119h-95zM372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5t232 -68.5t77 -227.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81q105 0 152.5 46t47.5 152q0 107 -47.5 152.5
t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46zM161 1015h120l140 -170h-99z" />
    <glyph glyph-name="uni1ED4" unicode="&#x1ed4;" horiz-adv-x="941" 
d="M597 1020q26 1 43.5 8t17.5 21q0 7 -6.5 12.5t-16.5 5.5t-25 -2.5t-32 -18.5l-46 37q22 23 48 32t55 9h6q44 -1 69 -21.5t25 -52.5q-1 -29 -21 -47.5t-48 -25.5l-2 -19h-62zM404 987h133l138 -140h-96l-109 97l-109 -97h-96zM470 -16q-105 0 -180.5 24t-124 74.5
t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24t180.5 -24t124.5 -75t72.5 -129.5t23.5 -188.5t-23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24zM470 90q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5
t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17z" />
    <glyph glyph-name="uni1ED5" unicode="&#x1ed5;" horiz-adv-x="744" 
d="M494 829q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM314 802h116l132 -170h-95l-95 119l-95 -119h-95zM372 -16
q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5t232 -68.5t77 -227.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81q105 0 152.5 46t47.5 152q0 107 -47.5 152.5t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5
t-11.5 -90.5q0 -106 46.5 -152t152.5 -46z" />
    <glyph glyph-name="uni1ED6" unicode="&#x1ed6;" horiz-adv-x="941" 
d="M404 987h133l138 -140h-96l-109 97l-109 -97h-96zM470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24t180.5 -24t124.5 -75t72.5 -129.5t23.5 -188.5t-23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24zM470 90
q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17zM268 1066q0 64 26 94t77 30q33 0 59.5 -11t50 -24t44 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23
q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24z" />
    <glyph glyph-name="uni1ED7" unicode="&#x1ed7;" horiz-adv-x="744" 
d="M314 802h116l132 -170h-95l-95 119l-95 -119h-95zM372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5t232 -68.5t77 -227.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81q105 0 152.5 46t47.5 152q0 107 -47.5 152.5
t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46zM170 881q0 64 26 94t77 30q33 0 59.5 -11t50 -24t44 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5
t-9 -38.5v-16h-70v24z" />
    <glyph glyph-name="uni1ED8" unicode="&#x1ed8;" horiz-adv-x="941" 
d="M404 987h133l138 -140h-96l-109 97l-109 -97h-96zM470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24t180.5 -24t124.5 -75t72.5 -129.5t23.5 -188.5t-23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24zM470 90
q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17zM419 -125h104v-92h-104v92z" />
    <glyph glyph-name="uni1ED9" unicode="&#x1ed9;" horiz-adv-x="744" 
d="M314 802h116l132 -170h-95l-95 119l-95 -119h-95zM372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5t232 -68.5t77 -227.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81q105 0 152.5 46t47.5 152q0 107 -47.5 152.5
t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46zM307 -102h130v-115h-130v115z" />
    <glyph glyph-name="uni1EDA" unicode="&#x1eda;" horiz-adv-x="941" 
d="M470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24q44 0 82.5 -4t72.5 -13h8q43 0 62 25q15 21 15 56v12h114q0 -47 -17.5 -86.5t-60.5 -62.5q65 -50 95 -134.5t30 -209.5q0 -110 -23.5 -189t-72.5 -129.5t-124.5 -74.5
t-180.5 -24zM470 90q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17zM560 1039h120l-161 -170h-99z" />
    <glyph glyph-name="uni1EDB" unicode="&#x1edb;" horiz-adv-x="744" 
d="M372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5q39 0 73 -4t63 -13h20q43 0 62 25q15 21 15 56v12h114q0 -55 -23.5 -97.5t-77.5 -61.5q32 -36 47.5 -88.5t15.5 -124.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81
q105 0 152.5 46t47.5 152q0 107 -47.5 152.5t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46zM462 796h120l-161 -170h-99z" />
    <glyph glyph-name="uni1EDC" unicode="&#x1edc;" horiz-adv-x="941" 
d="M470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24q44 0 82.5 -4t72.5 -13h8q43 0 62 25q15 21 15 56v12h114q0 -47 -17.5 -86.5t-60.5 -62.5q65 -50 95 -134.5t30 -209.5q0 -110 -23.5 -189t-72.5 -129.5t-124.5 -74.5
t-180.5 -24zM470 90q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17zM259 1045h120l140 -170h-99z" />
    <glyph glyph-name="uni1EDD" unicode="&#x1edd;" horiz-adv-x="744" 
d="M372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5q39 0 73 -4t63 -13h20q43 0 62 25q15 21 15 56v12h114q0 -55 -23.5 -97.5t-77.5 -61.5q32 -36 47.5 -88.5t15.5 -124.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81
q105 0 152.5 46t47.5 152q0 107 -47.5 152.5t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46zM161 802h120l140 -170h-99z" />
    <glyph glyph-name="uni1EDE" unicode="&#x1ede;" horiz-adv-x="941" 
d="M433 939q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5
t72 129.5t124 75t180.5 24q44 0 82.5 -4t72.5 -13h8q43 0 62 25q15 21 15 56v12h114q0 -47 -17.5 -86.5t-60.5 -62.5q65 -50 95 -134.5t30 -209.5q0 -110 -23.5 -189t-72.5 -129.5t-124.5 -74.5t-180.5 -24zM470 90q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5
t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17z" />
    <glyph glyph-name="uni1EDF" unicode="&#x1edf;" horiz-adv-x="744" 
d="M335 692q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5
q0 159 77 227.5t232 68.5q39 0 73 -4t63 -13h20q43 0 62 25q15 21 15 56v12h114q0 -55 -23.5 -97.5t-77.5 -61.5q32 -36 47.5 -88.5t15.5 -124.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81q105 0 152.5 46t47.5 152q0 107 -47.5 152.5t-152.5 45.5
q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46z" />
    <glyph glyph-name="uni1EE0" unicode="&#x1ee0;" horiz-adv-x="941" 
d="M470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24q44 0 82.5 -4t72.5 -13h8q43 0 62 25q15 21 15 56v12h114q0 -47 -17.5 -86.5t-60.5 -62.5q65 -50 95 -134.5t30 -209.5q0 -110 -23.5 -189t-72.5 -129.5t-124.5 -74.5
t-180.5 -24zM470 90q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17zM268 911q0 64 26 94t77 30q33 0 59.5 -11t50 -24t44 -24t40.5 -11q21 0 29.5 12
t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24z" />
    <glyph glyph-name="uni1EE1" unicode="&#x1ee1;" horiz-adv-x="744" 
d="M372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5q39 0 73 -4t63 -13h20q43 0 62 25q15 21 15 56v12h114q0 -55 -23.5 -97.5t-77.5 -61.5q32 -36 47.5 -88.5t15.5 -124.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81
q105 0 152.5 46t47.5 152q0 107 -47.5 152.5t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46zM170 668q0 64 26 94t77 30q33 0 59.5 -11t50 -24t44 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124
q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24z" />
    <glyph glyph-name="uni1EE2" unicode="&#x1ee2;" horiz-adv-x="941" 
d="M470 -16q-105 0 -180.5 24t-124 74.5t-72 129.5t-23.5 189t23.5 188.5t72 129.5t124 75t180.5 24q44 0 82.5 -4t72.5 -13h8q43 0 62 25q15 21 15 56v12h114q0 -47 -17.5 -86.5t-60.5 -62.5q65 -50 95 -134.5t30 -209.5q0 -110 -23.5 -189t-72.5 -129.5t-124.5 -74.5
t-180.5 -24zM470 90q78 0 132.5 17t89 54t50 96.5t15.5 143.5t-15.5 143.5t-50 96.5t-89 54t-132.5 17t-132 -17t-88.5 -54.5t-50 -96.5t-15.5 -143t15.5 -143t50 -96.5t88.5 -54.5t132 -17zM419 -125h104v-92h-104v92z" />
    <glyph glyph-name="uni1EE3" unicode="&#x1ee3;" horiz-adv-x="744" 
d="M372 -16q-78 0 -136 16.5t-96.5 51.5t-57.5 91.5t-19 135.5q0 159 77 227.5t232 68.5q39 0 73 -4t63 -13h20q43 0 62 25q15 21 15 56v12h114q0 -55 -23.5 -97.5t-77.5 -61.5q32 -36 47.5 -88.5t15.5 -124.5q0 -79 -19 -135.5t-57.5 -91.5t-96.5 -51.5t-136 -16.5zM372 81
q105 0 152.5 46t47.5 152q0 107 -47.5 152.5t-152.5 45.5q-53 0 -90.5 -11t-61.5 -35t-35.5 -61.5t-11.5 -90.5q0 -106 46.5 -152t152.5 -46zM307 -102h130v-115h-130v115z" />
    <glyph glyph-name="uni1EE4" unicode="&#x1ee4;" horiz-adv-x="911" 
d="M456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5t-20 148v498h112v-498q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h111v-498q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17zM419 -125h104v-92h-104v92z" />
    <glyph glyph-name="uni1EE5" unicode="&#x1ee5;" horiz-adv-x="779" 
d="M343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357q0 -57 36.5 -87.5t119.5 -30.5q63 0 117 16t101 38v421h105v-559h-73l-21 41q-54 -25 -114 -41t-129 -16zM307 -102h130v-115h-130v115z" />
    <glyph glyph-name="uni1EE6" unicode="&#x1ee6;" horiz-adv-x="911" 
d="M433 939q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5t-20 148v498h112v-498
q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h111v-498q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17z" />
    <glyph glyph-name="uni1EE7" unicode="&#x1ee7;" horiz-adv-x="779" 
d="M335 692q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357
q0 -57 36.5 -87.5t119.5 -30.5q63 0 117 16t101 38v421h105v-559h-73l-21 41q-54 -25 -114 -41t-129 -16z" />
    <glyph glyph-name="uni1EE8" unicode="&#x1ee8;" horiz-adv-x="911" 
d="M456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5t-20 148v498h112v-498q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h66q28 6 40 27q10 24 10 53v5h107q0 -59 -26 -100.5t-86 -56.5v-426q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17
zM546 1039h120l-161 -170h-99z" />
    <glyph glyph-name="uni1EE9" unicode="&#x1ee9;" horiz-adv-x="779" 
d="M343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357q0 -57 36.5 -87.5t119.5 -30.5q63 0 117 16t101 38v421h60q29 5 41 27q10 23 10 53v5h107q0 -60 -26.5 -101t-86.5 -56v-487h-73l-21 41q-54 -25 -114 -41t-129 -16zM479 796h120l-161 -170h-99z" />
    <glyph glyph-name="uni1EEA" unicode="&#x1eea;" horiz-adv-x="911" 
d="M456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5t-20 148v498h112v-498q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h66q28 6 40 27q10 24 10 53v5h107q0 -59 -26 -100.5t-86 -56.5v-426q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17
zM245 1045h120l140 -170h-99z" />
    <glyph glyph-name="uni1EEB" unicode="&#x1eeb;" horiz-adv-x="779" 
d="M343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357q0 -57 36.5 -87.5t119.5 -30.5q63 0 117 16t101 38v421h60q29 5 41 27q10 23 10 53v5h107q0 -60 -26.5 -101t-86.5 -56v-487h-73l-21 41q-54 -25 -114 -41t-129 -16zM178 802h120l140 -170h-99z" />
    <glyph glyph-name="uni1EEC" unicode="&#x1eec;" horiz-adv-x="911" 
d="M433 939q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5t-20 148v498h112v-498
q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h66q28 6 40 27q10 24 10 53v5h107q0 -59 -26 -100.5t-86 -56.5v-426q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17z" />
    <glyph glyph-name="uni1EED" unicode="&#x1eed;" horiz-adv-x="779" 
d="M335 692q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357
q0 -57 36.5 -87.5t119.5 -30.5q63 0 117 16t101 38v421h60q29 5 41 27q10 23 10 53v5h107q0 -60 -26.5 -101t-86.5 -56v-487h-73l-21 41q-54 -25 -114 -41t-129 -16z" />
    <glyph glyph-name="uni1EEE" unicode="&#x1eee;" horiz-adv-x="911" 
d="M456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5t-20 148v498h112v-498q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h66q28 6 40 27q10 24 10 53v5h107q0 -59 -26 -100.5t-86 -56.5v-426q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17
zM254 911q0 64 26 94t77 30q33 0 59.5 -11t50 -24t44 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24z" />
    <glyph glyph-name="uni1EEF" unicode="&#x1eef;" horiz-adv-x="779" 
d="M343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357q0 -57 36.5 -87.5t119.5 -30.5q63 0 117 16t101 38v421h60q29 5 41 27q10 23 10 53v5h107q0 -60 -26.5 -101t-86.5 -56v-487h-73l-21 41q-54 -25 -114 -41t-129 -16zM187 668q0 64 26 94t77 30
q33 0 59.5 -11t50 -24t44 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24z" />
    <glyph glyph-name="uni1EF0" unicode="&#x1ef0;" horiz-adv-x="911" 
d="M456 -16q-94 0 -161.5 17t-110 55.5t-62.5 99.5t-20 148v498h112v-498q0 -59 13 -99.5t41.5 -65t74.5 -35.5t113 -11t113 11t75 35.5t42 65t13 99.5v498h66q28 6 40 27q10 24 10 53v5h107q0 -59 -26 -100.5t-86 -56.5v-426q0 -87 -20 -148t-63 -99.5t-110 -55.5t-161 -17
zM404 -125h104v-92h-104v92z" />
    <glyph glyph-name="uni1EF1" unicode="&#x1ef1;" horiz-adv-x="779" 
d="M343 -16q-63 0 -110 14.5t-77.5 41t-46 63.5t-15.5 83v373h107v-357q0 -57 36.5 -87.5t119.5 -30.5q63 0 117 16t101 38v421h60q29 5 41 27q10 23 10 53v5h107q0 -60 -26.5 -101t-86.5 -56v-487h-73l-21 41q-54 -25 -114 -41t-129 -16zM307 -102h130v-115h-130v115z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="800" 
d="M222 987h121l116 -140h-104zM344 314l-328 488h131l253 -379l253 379h131l-330 -489v-313h-110v314z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="704" 
d="M141 802h120l140 -170h-99zM74 -117q17 -1 33.5 -1.5t36.5 -0.5q35 0 60 3.5t43 12t30 21.5t21 33l24 55h-54l-243 553h113l202 -460h23l204 460h112l-277 -616q-18 -43 -39.5 -73t-49 -48.5t-64 -27t-85.5 -8.5q-26 0 -46.5 0.5t-43.5 3.5v93z" />
    <glyph glyph-name="uni1EF4" unicode="&#x1ef4;" horiz-adv-x="800" 
d="M344 314l-328 488h131l253 -379l253 379h131l-330 -489v-313h-110v314zM348 -125h104v-92h-104v92z" />
    <glyph glyph-name="uni1EF5" unicode="&#x1ef5;" horiz-adv-x="704" 
d="M74 -117q17 -1 33.5 -1.5t36.5 -0.5q35 0 60 3.5t43 12t30 21.5t21 33l24 55h-54l-243 553h113l202 -460h23l204 460h112l-277 -616q-18 -43 -39.5 -73t-49 -48.5t-64 -27t-85.5 -8.5q-26 0 -46.5 0.5t-43.5 3.5v93zM409 -102h130v-115h-130v115z" />
    <glyph glyph-name="uni1EF6" unicode="&#x1ef6;" horiz-adv-x="800" 
d="M363 939q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM344 314l-328 488h131l253 -379l253 379h131l-330 -489v-313h-110v314z
" />
    <glyph glyph-name="uni1EF7" unicode="&#x1ef7;" horiz-adv-x="704" 
d="M335 692q27 1 45 10t18 24q0 8 -6.5 14.5t-17.5 6.5t-26 -3t-33 -21l-47 44q23 26 50.5 36.5t56.5 10.5h5q45 -1 70.5 -24.5t25.5 -59.5v-3q-1 -34 -21.5 -55t-49.5 -29q0 -7 -1 -12t-1 -11h-61zM74 -117q17 -1 33.5 -1.5t36.5 -0.5q35 0 60 3.5t43 12t30 21.5t21 33
l24 55h-54l-243 553h113l202 -460h23l204 460h112l-277 -616q-18 -43 -39.5 -73t-49 -48.5t-64 -27t-85.5 -8.5q-26 0 -46.5 0.5t-43.5 3.5v93z" />
    <glyph glyph-name="uni1EF8" unicode="&#x1ef8;" horiz-adv-x="800" 
d="M344 314l-328 488h131l253 -379l253 379h131l-330 -489v-313h-110v314zM199 911q0 64 26 94t77 30q33 0 59.5 -11t50 -24t44 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24z
" />
    <glyph glyph-name="uni1EF9" unicode="&#x1ef9;" horiz-adv-x="704" 
d="M74 -117q17 -1 33.5 -1.5t36.5 -0.5q35 0 60 3.5t43 12t30 21.5t21 33l24 55h-54l-243 553h113l202 -460h23l204 460h112l-277 -616q-18 -43 -39.5 -73t-49 -48.5t-64 -27t-85.5 -8.5q-26 0 -46.5 0.5t-43.5 3.5v93zM150 668q0 64 26 94t77 30q33 0 59.5 -11t50 -24
t44 -24t40.5 -11q21 0 29.5 12t8.5 38v15h69v-23q0 -124 -101 -124q-33 0 -59.5 11t-50 24t-44 24t-40.5 11q-21 0 -30 -11.5t-9 -38.5v-16h-70v24z" />
    <glyph glyph-name="uni2000" unicode="&#x2000;" 
 />
    <glyph glyph-name="uni2001" unicode="&#x2001;" horiz-adv-x="1520" 
 />
    <glyph glyph-name="uni2002" unicode="&#x2002;" 
 />
    <glyph glyph-name="uni2003" unicode="&#x2003;" horiz-adv-x="1520" 
 />
    <glyph glyph-name="uni2004" unicode="&#x2004;" horiz-adv-x="507" 
 />
    <glyph glyph-name="uni2005" unicode="&#x2005;" horiz-adv-x="380" 
 />
    <glyph glyph-name="uni2006" unicode="&#x2006;" horiz-adv-x="253" 
 />
    <glyph glyph-name="uni2007" unicode="&#x2007;" horiz-adv-x="736" 
 />
    <glyph glyph-name="uni2008" unicode="&#x2008;" horiz-adv-x="380" 
 />
    <glyph glyph-name="uni2009" unicode="&#x2009;" horiz-adv-x="190" 
 />
    <glyph glyph-name="uni200A" unicode="&#x200a;" horiz-adv-x="152" 
 />
    <glyph glyph-name="uni200B" unicode="&#x200b;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni200C" unicode="&#x200c;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni200D" unicode="&#x200d;" horiz-adv-x="0" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" 
d="M0 230v101h760v-101h-760z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1520" 
d="M0 230v101h1520v-101h-1520z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="380" 
d="M200 507h-114l112 295h95z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="380" 
d="M180 802h114l-112 -295h-95z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="380" 
d="M180 123h114l-112 -294h-95z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="608" 
d="M200 507h-114l112 295h95zM428 507h-114l112 295h95z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="608" 
d="M180 802h114l-112 -295h-95zM408 802h114l-112 -295h-95z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="608" 
d="M180 123h114l-112 -294h-95zM408 123h114l-112 -294h-95z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" 
d="M326 365v89h-242v105h242v243h108v-243h241v-105h-241v-89l-12 -511h-84z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" 
d="M84 202h242v252h-242v105h242v243h108v-243h241v-105h-241v-252h241v-105h-241v-243h-108v243h-242v105z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="469" 
d="M235 148q-66 0 -100 30.5t-34 101.5q0 70 34 100.5t100 30.5q65 0 99 -30.5t34 -100.5q0 -71 -34 -101.5t-99 -30.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="848" 
d="M129 116h122v-116h-122v116zM363 116h122v-116h-122v116zM597 116h122v-116h-122v116z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1336" 
d="M219 474q-96 0 -142.5 39t-46.5 133t46.5 133t142.5 39t142.5 -39t46.5 -133t-46.5 -133t-142.5 -39zM226 0h-109l571 802h109zM219 546q52 0 73 23t21 77t-21 76.5t-73 22.5t-73 -22.5t-21 -76.5t21 -77t73 -23zM694 -16q-96 0 -142.5 39t-46.5 133t46.5 133t142.5 39
t142.5 -39t46.5 -133t-46.5 -133t-142.5 -39zM1120 -16q-96 0 -142.5 39t-46.5 133t46.5 133t142.5 39t142 -39t46 -133t-46 -133t-142 -39zM694 56q52 0 73.5 23t21.5 77t-21.5 76.5t-73.5 22.5t-72.5 -22.5t-20.5 -76.5t20.5 -77t72.5 -23zM1120 56q51 0 72.5 23t21.5 77
t-21.5 76.5t-72.5 22.5q-52 0 -73 -22.5t-21 -76.5t21 -77t73 -23z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="348" 
d="M202 512h110l-166 -232l166 -232h-109l-168 232z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="348" 
d="M146 48h-111l167 232l-167 232h109l168 -232z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="215" 
d="M-124 0h-108l571 802h108z" />
    <glyph glyph-name="uni2060" unicode="&#x2060;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni2070" unicode="&#x2070;" horiz-adv-x="425" 
d="M213 531q-102 0 -147 57.5t-45 181.5t45 181.5t147 57.5q104 0 149.5 -57.5t45.5 -181.5t-45.5 -181.5t-149.5 -57.5zM213 610q29 0 47.5 8t30 26.5t16 49t4.5 76.5t-4.5 76.5t-16 49t-30 26.5t-47.5 8q-27 0 -45.5 -8t-30 -26.5t-16 -49t-4.5 -76.5t4.5 -76.5t16 -49
t30 -26.5t45.5 -8z" />
    <glyph glyph-name="uni2074" unicode="&#x2074;" horiz-adv-x="425" 
d="M390 802v-262h-93v93h-278v72l165 295h99l-165 -292h179v94h93z" />
    <glyph glyph-name="uni2075" unicode="&#x2075;" horiz-adv-x="425" 
d="M36 626q35 -7 68.5 -11t80.5 -4q35 0 57.5 3.5t35.5 11.5t18 21t5 32q0 37 -20.5 51.5t-75.5 14.5h-152v255h318v-77h-225v-102h74q49 0 82.5 -8.5t54.5 -26t30.5 -44t9.5 -62.5q0 -43 -12 -71.5t-37 -46t-64.5 -24.5t-95.5 -7q-48 0 -87 4.5t-65 10.5v80z" />
    <glyph glyph-name="uni2076" unicode="&#x2076;" horiz-adv-x="425" 
d="M218 531q-54 0 -91.5 13.5t-60 43t-32.5 74.5t-10 108q0 66 12 111.5t39 74t69.5 41t102.5 12.5q38 0 67.5 -4.5t50.5 -10.5v-80q-28 7 -52.5 11t-61.5 4q-36 0 -61 -5.5t-40 -19t-22 -37t-8 -60.5q26 10 54.5 16.5t66.5 6.5q88 0 124 -37t36 -110q0 -84 -49 -118
t-134 -34zM220 753q-29 0 -53 -5.5t-47 -13.5q1 -36 6.5 -60.5t16.5 -39.5t30 -21t47 -6q43 0 64 15.5t21 57.5q0 21 -4 35t-13.5 22.5t-26 12t-41.5 3.5z" />
    <glyph glyph-name="uni2077" unicode="&#x2077;" horiz-adv-x="425" 
d="M27 1000h375v-72l-227 -388h-113l234 383h-269v77z" />
    <glyph glyph-name="uni2078" unicode="&#x2078;" horiz-adv-x="425" 
d="M214 531q-100 0 -146.5 30t-46.5 99q0 49 21.5 75t59.5 37q-34 12 -53.5 38t-19.5 74q0 67 45 96t140 29t140 -29t45 -96q0 -48 -19 -74t-53 -38q38 -11 59.5 -37t21.5 -75q0 -69 -47.5 -99t-146.5 -30zM214 808q51 0 70.5 13t19.5 48q0 32 -18.5 47t-71.5 15t-71 -15
t-19 -47q1 -35 20 -48t70 -13zM214 608q55 0 76 15.5t21 49.5q0 38 -22.5 52.5t-74.5 14.5t-74.5 -14.5t-22.5 -52.5q0 -34 21 -49.5t76 -15.5z" />
    <glyph glyph-name="uni2079" unicode="&#x2079;" horiz-adv-x="425" 
d="M207 1009q54 0 91.5 -13.5t60.5 -43t33 -74.5t10 -108q0 -66 -12 -111.5t-39 -74t-69.5 -41t-103.5 -12.5q-38 0 -67 4.5t-50 10.5v80q28 -7 52.5 -11t61.5 -4q36 0 61 5.5t40 19t22 37t8 60.5q-26 -10 -54.5 -16.5t-66.5 -6.5q-88 0 -124 37t-36 110q0 84 49 118t133 34
zM120 860q0 -42 18 -57.5t67 -15.5q30 0 54 5.5t47 13.5q-1 36 -6.5 60.5t-17 39.5t-30 21t-47.5 6q-43 0 -64 -15.5t-21 -57.5z" />
    <glyph glyph-name="uni2080" unicode="&#x2080;" horiz-adv-x="425" 
d="M213 -207q-102 0 -147 57.5t-45 181.5t45 181.5t147 57.5q104 0 149.5 -57.5t45.5 -181.5t-45.5 -181.5t-149.5 -57.5zM213 -128q29 0 47.5 8t30 26.5t16 49t4.5 76.5t-4.5 76.5t-16 49t-30 26.5t-47.5 8q-27 0 -45.5 -8t-30 -26.5t-16 -49t-4.5 -76.5t4.5 -76.5t16 -49
t30 -26.5t45.5 -8z" />
    <glyph glyph-name="uni2081" unicode="&#x2081;" horiz-adv-x="337" 
d="M191 262h91v-460h-93v368l-138 -66l-39 72z" />
    <glyph glyph-name="uni2082" unicode="&#x2082;" horiz-adv-x="425" 
d="M204 271q101 0 144.5 -29.5t43.5 -96.5q0 -26 -5.5 -44t-17.5 -32.5t-30.5 -27t-45.5 -27.5l-124 -68q-22 -13 -28.5 -21.5t-6.5 -29.5v-16h259v-77h-353v95q0 22 4 38t13.5 29.5t26 25t40.5 24.5l129 70q25 14 34 23.5t9 32.5q0 14 -4 24t-15.5 16t-30.5 8.5t-49 2.5
q-36 0 -75 -5t-68 -10v80q30 6 66 10.5t84 4.5z" />
    <glyph glyph-name="uni2083" unicode="&#x2083;" horiz-adv-x="425" 
d="M37 -114q35 -8 68 -11.5t78 -3.5q36 0 58.5 3t36 10.5t18.5 19.5t5 29q0 38 -18.5 51t-68.5 13h-123v77h123q44 0 62.5 11.5t18.5 48.5q0 17 -4.5 28.5t-16.5 18.5t-32 10t-52 3q-46 0 -78 -4t-67 -11v77q25 5 62.5 10t85.5 5q104 0 150.5 -27.5t46.5 -95.5
q0 -50 -19.5 -76t-54.5 -38q40 -11 61 -36.5t21 -76.5q0 -71 -49.5 -99.5t-160.5 -28.5q-49 0 -87 4.5t-64 10.5v78z" />
    <glyph glyph-name="uni2084" unicode="&#x2084;" horiz-adv-x="425" 
d="M390 64v-262h-93v93h-278v72l165 295h99l-165 -292h179v94h93z" />
    <glyph glyph-name="uni2085" unicode="&#x2085;" horiz-adv-x="425" 
d="M36 -112q35 -7 68.5 -11t80.5 -4q35 0 57.5 3.5t35.5 11.5t18 21t5 32q0 37 -20.5 51.5t-75.5 14.5h-152v255h318v-77h-225v-102h74q49 0 82.5 -8.5t54.5 -26t30.5 -44t9.5 -62.5q0 -43 -12 -71.5t-37 -46t-64.5 -24.5t-95.5 -7q-48 0 -87 4.5t-65 10.5v80z" />
    <glyph glyph-name="uni2086" unicode="&#x2086;" horiz-adv-x="425" 
d="M218 -207q-54 0 -91.5 13.5t-60 43t-32.5 74.5t-10 108q0 66 12 111.5t39 74t69.5 41t102.5 12.5q38 0 67.5 -4.5t50.5 -10.5v-80q-28 7 -52.5 11t-61.5 4q-36 0 -61 -5.5t-40 -19t-22 -37t-8 -60.5q26 10 54.5 16.5t66.5 6.5q88 0 124 -37t36 -110q0 -84 -49 -118
t-134 -34zM220 15q-29 0 -53 -5.5t-47 -13.5q1 -36 6.5 -60.5t16.5 -39.5t30 -21t47 -6q43 0 64 15.5t21 57.5q0 21 -4 35t-13.5 22.5t-26 12t-41.5 3.5z" />
    <glyph glyph-name="uni2087" unicode="&#x2087;" horiz-adv-x="425" 
d="M27 262h375v-72l-227 -388h-113l234 383h-269v77z" />
    <glyph glyph-name="uni2088" unicode="&#x2088;" horiz-adv-x="425" 
d="M214 -207q-100 0 -146.5 30t-46.5 99q0 49 21.5 75t59.5 37q-34 12 -53.5 38t-19.5 74q0 67 45 96t140 29t140 -29t45 -96q0 -48 -19 -74t-53 -38q38 -11 59.5 -37t21.5 -75q0 -69 -47.5 -99t-146.5 -30zM214 70q51 0 70.5 13t19.5 48q0 32 -18.5 47t-71.5 15t-71 -15
t-19 -47q1 -35 20 -48t70 -13zM214 -130q55 0 76 15.5t21 49.5q0 38 -22.5 52.5t-74.5 14.5t-74.5 -14.5t-22.5 -52.5q0 -34 21 -49.5t76 -15.5z" />
    <glyph glyph-name="uni2089" unicode="&#x2089;" horiz-adv-x="425" 
d="M207 271q54 0 91.5 -13.5t60.5 -43t33 -74.5t10 -108q0 -66 -12 -111.5t-39 -74t-69.5 -41t-103.5 -12.5q-38 0 -67 4.5t-50 10.5v80q28 -7 52.5 -11t61.5 -4q36 0 61 5.5t40 19t22 37t8 60.5q-26 -10 -54.5 -16.5t-66.5 -6.5q-88 0 -124 37t-36 110q0 84 49 118t133 34z
M120 122q0 -42 18 -57.5t67 -15.5q30 0 54 5.5t47 13.5q-1 36 -6.5 60.5t-17 39.5t-30 21t-47.5 6q-43 0 -64 -15.5t-21 -57.5z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="736" 
d="M37 342h96q-1 14 -1 29v62q0 15 1 29h-96v89h105q11 70 35 120.5t63.5 83t95.5 48t132 15.5q66 0 115 -7.5t87 -16.5v-100q-46 11 -91 17.5t-107 6.5q-50 0 -86.5 -8.5t-62.5 -28t-42 -51.5t-24 -79h364v-89h-373q-1 -14 -1 -29v-62q0 -15 1 -29h373v-89h-364
q8 -47 24 -79.5t41.5 -52t62.5 -28t87 -8.5q62 0 107 6t91 17v-100q-38 -9 -87 -16.5t-115 -7.5q-76 0 -132.5 15.5t-96 48.5t-63 84t-34.5 121h-105v89z" />
    <glyph glyph-name="uni2113" unicode="&#x2113;" 
d="M432 -16q-48 0 -86.5 10t-66.5 33t-45.5 60.5t-23.5 92.5q-17 -12 -34.5 -24t-34.5 -23l-47 81q27 18 55.5 37.5t57.5 40.5v296q0 121 47 175.5t154 54.5q44 0 79.5 -9t60 -29.5t38 -53.5t13.5 -80q0 -50 -23 -100.5t-62.5 -100.5t-92 -98.5t-112.5 -94.5v-10
q0 -89 28.5 -126.5t98.5 -37.5q56 0 95.5 30t75.5 81l76 -61q-49 -71 -108 -107.5t-143 -36.5zM309 369q38 32 71.5 64.5t58.5 65.5t39.5 66.5t14.5 65.5q0 52 -22 72.5t-67 20.5q-52 0 -73.5 -30.5t-21.5 -95.5v-229z" />
    <glyph glyph-name="uni2116" unicode="&#x2116;" horiz-adv-x="1494" 
d="M115 802h142l407 -633v633h111v-802h-148l-401 624v-624h-111v802zM1152 182q-131 0 -196 59t-65 197t65 196.5t196 58.5t196 -58.5t65 -196.5t-65 -197t-196 -59zM1152 280q81 0 117 36.5t36 121.5q0 84 -36 120.5t-117 36.5t-116.5 -36.5t-35.5 -120.5
q0 -85 35.5 -121.5t116.5 -36.5zM918 104h469v-104h-469v104z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="941" 
d="M142 721h-108v81h308v-81h-108v-320h-92v320zM413 802h116l112 -230l113 230h115v-401h-92v252l-91 -181h-90l-91 181v-252h-92v401z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="941" 
d="M232 362v-224q39 -38 98 -55t140 -17q100 0 166 26.5t103 84.5h106q-42 -101 -133.5 -147t-241.5 -46q-108 0 -185.5 24t-127.5 75t-73.5 130t-23.5 188t23.5 188t73.5 130t127.5 75t185.5 24t185.5 -24t128 -75t74 -130t23.5 -188v-39h-649zM470 736q-81 0 -140 -17
t-98 -55v-224h477v224q-39 38 -98.5 55t-140.5 17z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" 
d="M67 280l304 304l66 -65l-187 -188h510v-101h-513l190 -190l-66 -65z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" 
d="M75 324l306 304l303 -304l-65 -66l-189 188v-446h-100v447l-189 -189z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" 
d="M334 29l210 209h-544v85h542l-208 208l54 53l304 -304l-304 -304z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" 
d="M338 82v477h84v-477l209 208l53 -53l-304 -304l-304 304l54 53z" />
    <glyph glyph-name="uni2196" unicode="&#x2196;" 
d="M119 532h430v-75h-295l385 -384l-60 -60l-385 384v-295h-75v430z" />
    <glyph glyph-name="uni2197" unicode="&#x2197;" 
d="M566 129v294l-384 -383l-60 60l385 384h-295v75h429v-430h-75z" />
    <glyph glyph-name="uni2198" unicode="&#x2198;" 
d="M641 0h-429v75h294l-384 384l60 60l384 -384v294h75v-429z" />
    <glyph glyph-name="uni2199" unicode="&#x2199;" 
d="M194 429v-294l384 384l60 -60l-383 -384h294v-75h-430v429h75z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="772" 
d="M389 -16q-154 0 -231.5 67.5t-77.5 223.5q0 77 19 131.5t55.5 89.5t89 51t119.5 16q44 0 87.5 -9.5t86.5 -25.5q-19 38 -47.5 73t-67 68.5t-87 66t-108.5 66.5h155q87 -47 146 -99.5t96 -112.5t53 -129.5t16 -151.5q0 -89 -19 -151t-56.5 -100.5t-94.5 -56t-134 -17.5z
M190 275q0 -106 47 -152t152 -46q51 0 88 12.5t60.5 40t34.5 71.5t11 108q0 66 -12 123q-42 17 -88 28t-97 11q-102 0 -149 -44.5t-47 -151.5z" />
    <glyph glyph-name="uni2206" unicode="&#x2206;" 
d="M295 802h172l277 -802h-728zM588 104l-196 594h-20l-199 -594h415z" />
    <glyph glyph-name="product" unicode="&#x220f;" 
d="M116 697h-76v105h680v-105h-76v-806h-111v806h-306v-806h-111v806z" />
    <glyph glyph-name="summation" unicode="&#x2211;" 
d="M72 -33l324 379l-320 381v75h613v-104h-450l296 -352l-298 -351h452v-104h-617v76z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M107 323h546v-87h-546v87z" />
    <glyph glyph-name="radical" unicode="&#x221a;" 
d="M42 559h228l119 -427l244 855h111l-284 -987h-143l-129 461h-146v98z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" 
d="M192 179q-34 0 -64 9t-52.5 31.5t-35.5 61.5t-13 99t13 99t35.5 61.5t52.5 31t64 8.5q54 0 98.5 -27.5t79.5 -79.5l13 -19l13 19q34 51 76.5 79t95.5 28q34 0 64 -8.5t52.5 -31t35.5 -61.5t13 -99t-13 -99t-35.5 -61.5t-52.5 -31.5t-64 -9q-54 0 -99.5 28.5t-80.5 80.5
l-12 17l-11 -17q-34 -51 -77 -80t-96 -29zM192 260q32 0 58 16.5t45 44.5l35 53l-44 66q-18 27 -40.5 43t-53.5 16q-36 0 -58.5 -28t-22.5 -91q0 -64 22.5 -92t58.5 -28zM472 321q19 -28 42 -44.5t54 -16.5q36 0 58.5 28t22.5 92q0 63 -22.5 91t-58.5 28q-32 0 -57.5 -15.5
t-44.5 -43.5l-37 -55z" />
    <glyph glyph-name="integral" unicode="&#x222b;" 
d="M80 -112q23 -2 45 -3t45 -1q35 0 57.5 5.5t36.5 18t21 32.5t10 50l64 619q5 52 17 91t36.5 65.5t64.5 39.5t101 13q28 0 52 -1.5t50 -4.5v-96q-23 2 -45 3t-45 1q-35 0 -57.5 -5.5t-36.5 -18t-21 -32.5t-10 -50l-64 -619q-5 -52 -17 -91t-36.5 -65.5t-64 -39.5
t-101.5 -13q-28 0 -52 2t-50 4v96z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M107 354q0 77 34 112.5t100 35.5q40 0 77 -15.5t71 -34t64 -34t55 -15.5q34 0 48.5 17.5t14.5 55.5v19h82v-28q0 -149 -131 -149q-40 0 -77 16t-70.5 34.5t-63.5 34.5t-56 16q-35 0 -50 -17t-15 -57v-19h-83v28zM107 102q0 77 34 112.5t100 35.5q40 0 77 -15.5t71 -34
t64 -34t55 -15.5q34 0 48.5 17.5t14.5 55.5v19h82v-28q0 -149 -131 -149q-40 0 -77 16t-70.5 34.5t-63.5 34.5t-56 16q-35 0 -50 -17t-15 -57v-19h-83v28z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M107 177h171l106 207h-277v88h322l44 87h101l-45 -87h124v-88h-169l-106 -207h275v-88h-320l-46 -89h-99l45 89h-126v88z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M107 431l546 151v-91l-463 -124v-9l463 -125v-91l-546 150v139zM107 88h546v-88h-546v88z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M107 233l463 125v9l-463 124v91l546 -151v-139l-546 -150v91zM107 88h546v-88h-546v88z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" 
d="M74 401l236 401h140l236 -401l-236 -401h-140zM380 87l188 314l-188 315l-189 -315z" />
    <glyph glyph-name="uni25CC" unicode="&#x25cc;" horiz-adv-x="659" 
d="M329 -10q-60 0 -112.5 23t-91.5 62t-62 91.5t-23 112.5t23 112.5t62 92t91.5 62.5t112.5 23t112.5 -23t92 -62.5t62.5 -92t23 -112.5t-23 -112.5t-62.5 -91.5t-92 -62t-112.5 -23zM329 559q-57 0 -108 -22t-89 -60t-60 -89t-22 -109q0 -57 22 -108t60 -89t89 -60t108 -22
q58 0 109 22t89 60t60 89t22 108q0 58 -22 109t-60 89t-89 60t-109 22z" />
    <glyph glyph-name="uniFEFF" unicode="&#xfeff;" horiz-adv-x="0" 
 />
    <glyph glyph-name="glyph1" horiz-adv-x="0" 
 />
    <glyph glyph-name="ampersand.1" horiz-adv-x="876" 
d="M585 128v-1q-21 -31 -44 -57t-52.5 -45t-67.5 -29.5t-89 -10.5q-134 0 -198 54.5t-64 179.5q0 102 45 160t148 71l-6 6q-23 23 -39 43.5t-25.5 40.5t-13.5 42t-4 48q0 46 14 81t45.5 59t81.5 36t122 12q62 0 112 -6.5t87 -14.5v-98q-42 8 -89 14t-98 6q-53 0 -85.5 -5.5
t-50.5 -17.5t-24 -31t-6 -46q0 -15 3 -26.5t10.5 -23.5t20.5 -27t34 -36l221 -223l136 201h120l-186 -272l211 -212h-141zM179 218q0 -76 36 -111.5t122 -35.5q29 0 50.5 5.5t39.5 17.5t34.5 31t34.5 45l19 28l-188 188q-43 -2 -71.5 -12.5t-45.5 -31t-24 -51.5t-7 -73z" />
    <glyph glyph-name="commaaccentbelow" horiz-adv-x="600" 
d="M303 -53h123l-152 -145h-99z" />
    <glyph glyph-name="commaturnedabove" horiz-adv-x="600" 
d="M289 632h-119l160 170h99z" />
    <glyph glyph-name="gravecomb.case" horiz-adv-x="0" 
d="M-179 987h121l116 -140h-104z" />
    <glyph glyph-name="acutecomb.case" horiz-adv-x="0" 
d="M59 987h120l-132 -140h-105z" />
    <glyph glyph-name="uni030B.case" horiz-adv-x="0" 
d="M-58 987h113l-121 -140h-96zM169 987h113l-121 -140h-96z" />
    <glyph glyph-name="uni0302.case" horiz-adv-x="0" 
d="M-66 987h133l138 -140h-96l-109 97l-109 -97h-96z" />
    <glyph glyph-name="uni030C.case" horiz-adv-x="0" 
d="M-205 987h96l109 -98l109 98h96l-138 -140h-133z" />
    <glyph glyph-name="uni0306.case" horiz-adv-x="0" 
d="M0 842q-57 0 -95 8t-61 25.5t-32.5 45t-9.5 66.5h81q0 -19 5 -33t18 -23t36 -13t58 -4t58 4t36 13t18 23t5 33h82q0 -39 -9.5 -66.5t-32.5 -45t-61 -25.5t-96 -8z" />
    <glyph glyph-name="uni030A.case" horiz-adv-x="0" 
d="M0 832q-72 0 -109 24t-37 83t37 83t109 24t109 -24t37 -83t-37 -83t-109 -24zM0 892q42 0 62.5 10.5t20.5 36.5t-20.5 36.5t-62.5 10.5t-62 -10.5t-20 -36.5q0 -27 20 -37t62 -10z" />
    <glyph glyph-name="tildecomb.case" horiz-adv-x="0" 
d="M-202 868q0 57 27.5 84t78.5 27q33 0 60 -9.5t49.5 -21.5t41.5 -21.5t37 -9.5q22 0 31.5 10.5t9.5 37.5v9h69v-15q0 -111 -106 -111q-33 0 -59.5 10t-49 21.5t-42 21.5t-37.5 10q-21 0 -30.5 -10.5t-9.5 -38.5v-10h-70v16z" />
    <glyph glyph-name="uni0307.case" horiz-adv-x="0" 
d="M-56 960h112v-85h-112v85z" />
    <glyph glyph-name="uni0308.case" horiz-adv-x="0" 
d="M-162 960h92v-85h-92v85zM70 960h92v-85h-92v85z" />
    <glyph glyph-name="uni0304.case" horiz-adv-x="0" 
d="M-183 949h366v-68h-366v68z" />
    <glyph glyph-name="uni030C.alt" horiz-adv-x="0" 
d="M130 802h105l-102 -255h-69z" />
    <glyph glyph-name="uni0327.1" horiz-adv-x="0" 
d="M-71 -198h-97l129 201h95z" />
    <glyph glyph-name="Ccedilla.1" horiz-adv-x="878" 
d="M418 -15q-178 8 -263 107.5t-85 308.5q0 110 23 188.5t70.5 129.5t119.5 75t171 24q156 0 246 -60t122 -192h-118q-26 78 -86.5 112t-163.5 34q-73 0 -124.5 -17t-83.5 -54.5t-47 -96.5t-15 -143t15 -143t47 -96.5t83.5 -54.5t124.5 -17q103 0 164 34.5t86 112.5h118
q-31 -127 -116 -188t-230 -65l-44 -42h16q65 0 91.5 -17t26.5 -59q0 -45 -28.5 -62.5t-97.5 -17.5q-24 0 -45 1.5t-40 6.5v48q20 -4 40 -6t43 -2q45 0 58 7t13 25q0 16 -13 23.5t-58 7.5q-20 0 -37 -1.5t-34 -3.5v44z" />
    <glyph glyph-name="germandbls.cap" horiz-adv-x="1653" 
d="M101 136q60 -17 136.5 -30t159.5 -13q68 0 113 6t72 20t38.5 37t11.5 57q0 35 -8 58t-32 38t-67 25t-114 20q-95 11 -157 26.5t-98 41.5t-50 67t-14 103q0 57 18 99.5t58.5 70.5t105 42t158.5 14q81 0 146 -10t126 -26v-110q-63 16 -129.5 26.5t-141.5 10.5
q-69 0 -113 -6t-69.5 -19.5t-35 -35t-9.5 -51.5q0 -33 8 -53.5t31 -34.5t66.5 -23.5t113.5 -19.5q96 -11 158 -27t98 -44t50.5 -70.5t14.5 -106.5q0 -62 -19 -106.5t-61 -73t-108 -41.5t-160 -13q-89 0 -164 12t-133 29v111zM928 136q60 -17 136.5 -30t159.5 -13q68 0 113 6
t72 20t38.5 37t11.5 57q0 35 -8 58t-32 38t-67 25t-114 20q-95 11 -157 26.5t-98 41.5t-50 67t-14 103q0 57 18 99.5t58.5 70.5t105 42t158.5 14q81 0 146 -10t126 -26v-110q-63 16 -129.5 26.5t-141.5 10.5q-69 0 -113 -6t-69.5 -19.5t-35 -35t-9.5 -51.5q0 -33 8 -53.5
t31 -34.5t66.5 -23.5t113.5 -19.5q96 -11 158 -27t98 -44t50.5 -70.5t14.5 -106.5q0 -62 -19 -106.5t-61 -73t-108 -41.5t-160 -13q-89 0 -164 12t-133 29v111z" />
    <glyph glyph-name="ccedilla.1" horiz-adv-x="649" 
d="M329 -15q-68 4 -118 23t-83 54.5t-49 89t-16 127.5q0 81 18.5 137t57 91.5t97.5 51.5t139 16q60 0 111.5 -7.5t96.5 -18.5v-94q-48 9 -97 15t-106 6q-58 0 -98 -10.5t-64 -34t-34.5 -61t-10.5 -91.5t10.5 -91t34.5 -60.5t64 -34t98 -10.5q60 0 111.5 5.5t101.5 14.5v-93
q-45 -11 -96.5 -18t-110.5 -8l-44 -42h16q65 0 91.5 -17t26.5 -59q0 -45 -28.5 -62.5t-97.5 -17.5q-24 0 -45 1.5t-40 6.5v48q20 -4 40 -6t43 -2q45 0 58 7t13 25q0 16 -13 23.5t-58 7.5q-20 0 -37 -1.5t-34 -3.5v44z" />
    <glyph glyph-name="i.trk" horiz-adv-x="303" 
d="M96 768h112v-98h-112v98zM98 559h107v-559h-107v559z" />
    <glyph glyph-name="exclamdown.case" horiz-adv-x="380" 
d="M251 802v-116h-122v116h122zM232 553l12 -363v-190h-109v190l13 363h84z" />
    <glyph glyph-name="questiondown.case" horiz-adv-x="657" 
d="M476 694h-121v116h121v-116zM471 424v-70h-119q-57 0 -95.5 -8t-61 -24.5t-32 -41.5t-9.5 -59q0 -33 9 -57.5t32.5 -40t65 -23t106.5 -7.5q67 0 131.5 7t119.5 20v-101q-51 -11 -115 -18.5t-136 -7.5q-90 0 -151 14t-98.5 42t-54 69t-16.5 95q0 58 16 100.5t50.5 70.5
t88 42t129.5 14h31l11 121h85z" />
    <glyph glyph-name="hyphen.case" horiz-adv-x="380" 
d="M32 352v100h316v-100h-316z" />
    <glyph glyph-name="guilsinglleft.case" horiz-adv-x="348" 
d="M202 633h110l-166 -232l166 -232h-109l-168 232z" />
    <glyph glyph-name="guilsinglright.case" horiz-adv-x="348" 
d="M146 170h-111l167 232l-167 232h109l168 -232z" />
    <glyph glyph-name="guillemotleft.case" horiz-adv-x="596" 
d="M202 633h110l-166 -232l166 -232h-109l-168 232zM450 633h110l-166 -232l166 -232h-109l-168 232z" />
    <glyph glyph-name="guillemotright.case" horiz-adv-x="596" 
d="M146 170h-111l167 232l-167 232h109l168 -232zM394 170h-111l167 232l-167 232h109l168 -232z" />
    <glyph glyph-name="parenleft.case" horiz-adv-x="384" 
d="M252 -73q-82 89 -127 204.5t-45 269.5q0 156 47 272.5t125 201.5h106q-78 -81 -124.5 -202.5t-46.5 -271.5q0 -149 46 -270t125 -204h-106z" />
    <glyph glyph-name="parenright.case" horiz-adv-x="384" 
d="M132 875q82 -89 127 -204.5t45 -269.5q0 -156 -47 -272.5t-125 -201.5h-106q78 81 124.5 202.5t46.5 271.5q0 149 -46 270t-125 204h106z" />
    <glyph glyph-name="braceleft.case" horiz-adv-x="384" 
d="M358 -73h-63q-46 0 -79.5 8.5t-56 28t-33.5 50.5t-11 77v171q0 26 -3.5 44t-13 29t-27 16t-44.5 5h-23v90h23q27 0 44.5 5t27 16t13 29t3.5 44v170q0 46 11 77t33.5 50.5t56 28t79.5 8.5h63v-90h-40q-30 0 -49.5 -4.5t-31 -14.5t-16.5 -26t-5 -39v-154q0 -61 -18.5 -95.5
t-58.5 -49.5q77 -29 77 -145v-154q0 -47 21.5 -66t80.5 -19h40v-90z" />
    <glyph glyph-name="braceright.case" horiz-adv-x="384" 
d="M269 91q0 -46 -11 -77t-33.5 -50.5t-56 -28t-79.5 -8.5h-63v90h36q31 0 51.5 4.5t32.5 14.5t17 26t5 40v154q0 60 18 95t58 50q-40 15 -58 49.5t-18 95.5v154q0 23 -5 39t-16.5 26t-31 14.5t-49.5 4.5h-40v90h63q46 0 79.5 -8.5t56 -28t33.5 -50.5t11 -77v-170
q0 -26 3.5 -44t13 -29t26.5 -16t44 -5h24v-90h-24q-27 0 -44 -5t-26.5 -16t-13 -29t-3.5 -44v-171z" />
    <glyph glyph-name="bracketleft.case" horiz-adv-x="384" 
d="M88 875h270v-90h-169v-768h169v-90h-270v948z" />
    <glyph glyph-name="bracketright.case" horiz-adv-x="384" 
d="M296 -73h-270v90h169v768h-169v90h270v-948z" />
    <glyph glyph-name="endash.case" 
d="M0 352v101h760v-101h-760z" />
    <glyph glyph-name="emdash.case" horiz-adv-x="1520" 
d="M0 352v101h1520v-101h-1520z" />
    <glyph glyph-name="bullet.case" horiz-adv-x="469" 
d="M235 270q-66 0 -100 30.5t-34 101.5q0 70 34 100.5t100 30.5q65 0 99 -30.5t34 -100.5q0 -71 -34 -101.5t-99 -30.5z" />
    <glyph glyph-name="periodcentered.case" horiz-adv-x="380" 
d="M129 459h122v-116h-122v116z" />
    <glyph glyph-name="at.case" horiz-adv-x="979" 
d="M491 -15q-111 0 -190.5 23.5t-130.5 73.5t-75 129t-24 190q0 110 25 188.5t76.5 129.5t130.5 75t188 24q108 0 186.5 -24t129.5 -75t75.5 -129.5t24.5 -188.5v-134q0 -87 -35 -125.5t-117 -38.5q-69 0 -106 27t-48 82q-29 -11 -64.5 -19t-77.5 -8q-83 0 -129.5 31
t-46.5 105q0 68 38 97t120 29h156v18q0 19 -5 32.5t-18 22.5t-35 13.5t-56 4.5q-45 0 -85.5 -3.5t-82.5 -9.5v84q23 4 42 6.5t38.5 4t41.5 2t51 0.5q59 0 99 -9t64 -27t34.5 -45.5t10.5 -65.5v-213q0 -39 11.5 -62t49.5 -23q19 0 30.5 6t18 17t8.5 27t2 35v139
q0 92 -18.5 155t-58.5 102.5t-101 57t-147 17.5t-147.5 -17.5t-101 -57t-58 -102.5t-18.5 -155q0 -91 19 -154t59 -102t101.5 -56.5t145.5 -17.5q17 0 39 1t39 3v-85q-17 -3 -38 -4t-40 -1zM488 262q31 0 58.5 6.5t50.5 15.5v90h-122q-51 0 -72 -12t-21 -43
q0 -30 24.5 -43.5t81.5 -13.5z" />
    <glyph glyph-name="dollar.weight" horiz-adv-x="736" 
d="M87 166q55 -14 127 -26.5t150 -12.5q56 0 93 5.5t59.5 18.5t32 33.5t9.5 51.5q0 30 -7.5 50t-28.5 33t-59 21.5t-100 16.5q-85 11 -140 25t-87 37.5t-45 60t-13 91.5q0 46 12.5 81t41.5 60t76 39t117 17v93h89v-93q60 -2 110.5 -10.5t97.5 -20.5v-100q-57 14 -117.5 23.5
t-131.5 9.5q-56 0 -92.5 -4.5t-58 -15t-30 -27t-8.5 -41.5q0 -30 7 -49.5t27.5 -32t58 -20.5t98.5 -16q86 -11 142 -26t88.5 -40t45.5 -63.5t13 -95.5q0 -50 -14 -87.5t-44 -62.5t-77.5 -39t-114.5 -17v-93h-89v93q-69 2 -130 12.5t-108 24.5v96z" />
    <glyph glyph-name="cent.weight" horiz-adv-x="736" 
d="M453 802v-105q108 -5 192 -24v-100q-57 11 -116 16t-128 5q-60 0 -101.5 -10.5t-66.5 -33t-36 -59t-11 -89.5q0 -52 11 -88.5t36 -59t66.5 -33t101.5 -10.5q72 0 133 5t121 16v-100q-45 -11 -95 -16t-107 -7v-109h-88v109q-150 5 -220.5 73t-70.5 220q0 77 17.5 131.5
t53 90t90.5 53t130 20.5v105h88z" />
    <glyph glyph-name="zeroslash" horiz-adv-x="736" 
d="M368 -16q-86 0 -147 24t-100 74.5t-57 129.5t-18 189t18 189t57 129.5t100 74.5t147 24t147 -24t100 -74.5t57 -129.5t18 -189t-18 -189t-57 -129.5t-100 -74.5t-147 -24zM157 401q0 -59 4.5 -105t15.5 -81l335 454q-25 24 -60.5 35t-83.5 11q-58 0 -98.5 -16.5
t-65.5 -53.5t-36 -97t-11 -147zM368 87q59 0 99 16.5t65 53.5t36 97t11 147q0 59 -4.5 105t-15.5 81l-335 -454q25 -24 60.5 -35t83.5 -11z" />
    <glyph glyph-name="dollar.lt" 
d="M99 166q48 -12 109 -23.5t129 -14.5v234q-73 11 -121 25.5t-76 38t-39 58.5t-11 87q0 46 12.5 81t41.5 60t76 39t117 17v93h89v-93q60 -2 110.5 -10.5t97.5 -20.5v-100q-48 12 -99 21t-109 11v-210q74 -11 122 -26.5t76.5 -40.5t40 -62t11.5 -91q0 -50 -14 -87.5
t-44 -62.5t-77.5 -39t-114.5 -17v-93h-89v93q-69 2 -130 12.5t-108 24.5v96zM196 582q0 -27 5.5 -45t20.5 -30.5t42.5 -20.5t72.5 -15v198q-41 -2 -68.5 -7.5t-43.5 -16t-22.5 -26t-6.5 -37.5zM570 236q0 27 -5.5 45.5t-21.5 31t-44 21.5t-73 16v-222q41 2 68.5 9t44.5 20
t24 32.5t7 46.5z" />
    <glyph glyph-name="dollar.lt.weight" 
d="M99 166q55 -14 127 -26.5t150 -12.5q56 0 93 5.5t59.5 18.5t32 33.5t9.5 51.5q0 30 -7.5 50t-28.5 33t-59 21.5t-100 16.5q-85 11 -140 25t-87 37.5t-45 60t-13 91.5q0 46 12.5 81t41.5 60t76 39t117 17v93h89v-93q60 -2 110.5 -10.5t97.5 -20.5v-100q-57 14 -117.5 23.5
t-131.5 9.5q-56 0 -92.5 -4.5t-58 -15t-30 -27t-8.5 -41.5q0 -30 7 -49.5t27.5 -32t58 -20.5t98.5 -16q86 -11 142 -26t88.5 -40t45.5 -63.5t13 -95.5q0 -50 -14 -87.5t-44 -62.5t-77.5 -39t-114.5 -17v-93h-89v93q-69 2 -130 12.5t-108 24.5v96z" />
    <glyph glyph-name="Euro.lt" 
d="M49 342h96q-1 14 -1 29v62q0 15 1 29h-96v89h105q11 70 35 120.5t63.5 83t95.5 48t132 15.5q66 0 115 -7.5t87 -16.5v-100q-46 11 -91 17.5t-107 6.5q-50 0 -86.5 -8.5t-62.5 -28t-42 -51.5t-24 -79h364v-89h-373q-1 -14 -1 -29v-62q0 -15 1 -29h373v-89h-364
q8 -47 24 -79.5t41.5 -52t62.5 -28t87 -8.5q62 0 107 6t91 17v-100q-38 -9 -87 -16.5t-115 -7.5q-76 0 -132.5 15.5t-96 48.5t-63 84t-34.5 121h-105v89z" />
    <glyph glyph-name="cent.lt" 
d="M465 802v-105q56 -2 105.5 -7.5t94.5 -16.5v-100q-48 9 -97 13.5t-103 6.5v-381q57 1 108 5.5t102 14.5v-100q-48 -11 -99.5 -16t-110.5 -7v-109h-88v109q-77 3 -133.5 20.5t-93 52.5t-54.5 89.5t-18 130.5q0 77 18 131.5t54.5 90t93 53t133.5 20.5v105h88zM190 402
q0 -48 9.5 -83t31.5 -58t58 -34.5t88 -14.5v381q-52 -3 -88 -15t-58 -35t-31.5 -58t-9.5 -83z" />
    <glyph glyph-name="cent.lt.weight" 
d="M465 802v-105q108 -5 192 -24v-100q-57 11 -116 16t-128 5q-60 0 -101.5 -10.5t-66.5 -33t-36 -59t-11 -89.5q0 -52 11 -88.5t36 -59t66.5 -33t101.5 -10.5q72 0 133 5t121 16v-100q-45 -11 -95 -16t-107 -7v-109h-88v109q-150 5 -220.5 73t-70.5 220q0 77 17.5 131.5
t53 90t90.5 53t130 20.5v105h88z" />
    <glyph glyph-name="sterling.lt" 
d="M123 96q55 0 75 25t20 84v154h-151v89h151v150q0 56 14 97t45.5 68.5t81.5 41t123 13.5q66 0 116.5 -8t88.5 -16v-100q-42 10 -87 16.5t-102 6.5q-50 0 -83.5 -7.5t-54 -22.5t-29 -38t-8.5 -55v-146h328v-89h-328v-155q0 -69 -36 -108h423v-96h-643v96h56z" />
    <glyph glyph-name="yen.lt" 
d="M88 342h237v120h-237v89h164l-211 251h124l213 -251h4l214 251h123l-211 -251h164v-89h-238v-120h238v-89h-238v-253h-109v253h-237v89z" />
    <glyph glyph-name="florin.lt" 
d="M90 448h230v149q0 56 12 97.5t39 69t71 41t109 13.5q31 0 56.5 -1.5t54.5 -4.5v-99q-26 3 -50 3.5t-49 0.5q-38 0 -64 -5.5t-42 -19t-23 -35.5t-7 -54v-155h235v-89h-235v-352q0 -56 -11.5 -97.5t-38.5 -69t-70.5 -41t-106.5 -13.5q-30 0 -55.5 1.5t-54.5 4.5v99
q25 -3 49 -3.5t49 -0.5q38 0 63 5.5t40.5 19t22 35.5t6.5 54v358h-230v89z" />
    <glyph glyph-name="zeroslash.lt" 
d="M380 -16q-86 0 -147 24t-100 74.5t-57 129.5t-18 189t18 189t57 129.5t100 74.5t147 24t147 -24t100 -74.5t57 -129.5t18 -189t-18 -189t-57 -129.5t-100 -74.5t-147 -24zM169 401q0 -59 4.5 -105t15.5 -81l335 454q-25 24 -60.5 35t-83.5 11q-58 0 -98.5 -16.5
t-65.5 -53.5t-36 -97t-11 -147zM380 87q59 0 99 16.5t65 53.5t36 97t11 147q0 59 -4.5 105t-15.5 81l-335 -454q25 -24 60.5 -35t83.5 -11z" />
    <glyph glyph-name="zero.lt" 
d="M380 -16q-86 0 -147 24t-100 74.5t-57 129.5t-18 189t18 189t57 129.5t100 74.5t147 24t147 -24t100 -74.5t57 -129.5t18 -189t-18 -189t-57 -129.5t-100 -74.5t-147 -24zM380 87q59 0 99 16.5t65 53.5t36 97t11 147t-11 147t-36 97t-65 53.5t-99 16.5q-58 0 -98.5 -16.5
t-65.5 -53.5t-36 -97t-11 -147t11 -147t36 -97t65.5 -53.5t98.5 -16.5z" />
    <glyph glyph-name="one.lt" 
d="M108 100h258v593l-246 -122l-45 84l296 147h103v-702h234v-100h-600v100z" />
    <glyph glyph-name="two.lt" 
d="M367 818q86 0 145 -12t95 -37.5t51.5 -64.5t15.5 -92q0 -44 -8.5 -75t-27.5 -55.5t-50.5 -46.5t-78.5 -49l-223 -127q-26 -15 -42 -26.5t-25 -23.5t-12 -27t-3 -38v-44h479v-100h-588v145q0 39 6 67.5t22 51.5t42.5 43t66.5 43l229 131l50 30t31.5 25t16 29.5t4.5 41.5
q0 29 -8 49t-30.5 33.5t-62 19.5t-102.5 6q-64 0 -127 -7.5t-114 -18.5v103q51 11 109.5 18.5t138.5 7.5z" />
    <glyph glyph-name="three.lt" 
d="M84 100q57 -11 116 -18t137 -7q70 0 115 8.5t71.5 25.5t36.5 43t10 62q0 42 -10.5 69.5t-33.5 43.5t-59.5 22.5t-89.5 6.5h-196v101h196q48 0 82 5.5t55.5 20.5t31.5 41t10 67q0 36 -9.5 62t-33 42.5t-63.5 24.5t-102 8q-75 0 -133.5 -7t-112.5 -18v89q46 11 107.5 18.5
t141.5 7.5q86 0 146 -12t98 -38.5t55 -67.5t17 -98q0 -88 -34 -133.5t-95 -63.5q69 -16 106.5 -62t37.5 -138q0 -58 -18.5 -100t-59.5 -69t-105.5 -39.5t-157.5 -12.5q-82 0 -145.5 7.5t-111.5 18.5v90z" />
    <glyph glyph-name="four.lt" 
d="M659 456v-456h-107v179h-504v85l302 538h115l-293 -522h380v176h107z" />
    <glyph glyph-name="five.lt" 
d="M83 112q57 -12 117.5 -18.5t138.5 -6.5q67 0 111 8.5t69.5 26.5t35.5 45.5t10 64.5q0 39 -10.5 66.5t-34.5 44.5t-62 24.5t-93 7.5h-248v427h521v-102h-413v-223h156q80 0 136 -14.5t91.5 -44.5t51.5 -76t16 -110q0 -67 -18 -114.5t-58 -77t-103.5 -43t-153.5 -13.5
q-84 0 -147.5 7.5t-112.5 18.5v102z" />
    <glyph glyph-name="six.lt" 
d="M391 -16q-92 0 -154 22.5t-100 72t-54 129t-16 193.5t20 193t64 129t114 72.5t170 22.5q63 0 109 -7.5t84 -18.5v-102q-43 11 -86.5 18t-103.5 7q-72 0 -121 -13t-79.5 -44.5t-44 -84t-14.5 -130.5q51 20 107 33.5t133 13.5q73 0 124.5 -15t83.5 -46t47 -78t15 -111
q0 -72 -21 -121t-59.5 -79t-93.5 -43t-124 -13zM396 390q-67 0 -116 -11.5t-101 -29.5q2 -77 13 -128.5t35.5 -82t65 -43.5t100.5 -13q45 0 79.5 7.5t58 25t35.5 47t12 73.5q0 43 -8.5 72.5t-29.5 48t-56 26.5t-88 8z" />
    <glyph glyph-name="seven.lt" 
d="M68 802h624v-84l-418 -718h-130l420 700h-496v102z" />
    <glyph glyph-name="eight.lt" 
d="M380 -16q-85 0 -145.5 13t-99 40.5t-56.5 69.5t-18 100q0 90 38.5 135.5t108.5 62.5q-62 18 -97 63t-35 132q0 56 17 97t54 68t94.5 40t138.5 13q80 0 137.5 -13t94.5 -40t54.5 -68t17.5 -97q0 -87 -35 -132t-97 -63q70 -17 108.5 -62.5t38.5 -135.5q0 -58 -18 -100
t-56.5 -69.5t-99 -40.5t-145.5 -13zM380 457q53 0 89.5 6t59.5 20t33.5 39t10.5 62q0 33 -9 57.5t-31 41t-59 24.5t-94 8t-94 -8t-59 -24.5t-31 -41t-9 -57.5q0 -37 10.5 -61.5t33.5 -39t59.5 -20.5t89.5 -6zM380 87q60 0 100 8.5t63.5 25t33.5 41.5t10 59q0 39 -11.5 65
t-36.5 41t-64 21.5t-95 6.5q-55 0 -94.5 -6.5t-64.5 -21.5t-36.5 -41t-11.5 -65q0 -34 10 -59t33.5 -41.5t63.5 -25t100 -8.5z" />
    <glyph glyph-name="nine.lt" 
d="M365 818q92 0 154 -22.5t100 -72t54 -129t16 -193.5t-20 -193t-64 -129t-114 -72.5t-170 -22.5q-63 0 -109 7.5t-84 18.5v102q22 -6 43 -10.5t43.5 -7.5t48 -5t55.5 -2q72 0 121 13.5t79.5 45t44 83.5t14.5 130q-51 -20 -107 -33.5t-133 -13.5q-73 0 -124.5 15t-83.5 46
t-47 78t-15 111q0 72 21 121t59.5 79t93.5 43t124 13zM178 567q0 -43 8.5 -72.5t29.5 -48t56 -26.5t88 -8q67 0 116 11.5t101 29.5q-2 77 -13 128t-35.5 82t-65 44t-100.5 13q-45 0 -79.5 -7.5t-58 -25t-35.5 -47t-12 -73.5z" />
    <glyph glyph-name="dollar.ot" 
d="M99 176q50 -15 111.5 -25t126.5 -13v183q-74 11 -122 24t-75.5 33.5t-38.5 51t-11 76.5q0 41 12.5 71.5t41.5 51t76 31.5t117 14v87h89v-87q57 -2 109 -10.5t99 -19.5v-100q-48 13 -99.5 21t-108.5 10v-162q74 -11 122 -24.5t76.5 -34.5t40 -52t11.5 -78q0 -46 -14 -79.5
t-44 -56t-77.5 -34.5t-114.5 -15v-99h-89v99q-67 3 -129 13t-109 24v100zM192 509q0 -20 5 -33t20 -22t43.5 -15.5t76.5 -13.5v150q-43 -1 -71.5 -5t-44.5 -12t-22.5 -20t-6.5 -29zM574 219q0 21 -5.5 35t-21 24t-44.5 17t-77 14v-171q84 3 116 21t32 60z" />
    <glyph glyph-name="dollar.ot.weight" 
d="M99 176q58 -17 133 -28t150 -11q55 0 92 4t59 13.5t31.5 25t9.5 39.5t-7 39t-28 24.5t-60 17t-104 16.5q-86 11 -141 23.5t-87 33.5t-44.5 52.5t-12.5 80.5q0 41 12.5 71.5t41.5 51t76 31.5t117 14v87h89v-87q57 -2 109 -10.5t99 -19.5v-100q-57 15 -121 23.5t-133 8.5
q-57 0 -93.5 -3.5t-57.5 -11t-29 -20.5t-8 -32q0 -22 6.5 -36t26.5 -23t59 -16t103 -16q86 -11 142 -24.5t88.5 -34.5t45.5 -53t13 -82q0 -46 -14 -79.5t-44 -56t-77.5 -34.5t-114.5 -15v-99h-89v99q-67 3 -129 13t-109 24v100z" />
    <glyph glyph-name="Euro.ot" 
d="M49 300h96q-1 12 -1 25v53q0 13 1 26h-96v89h107q12 59 36 101.5t63.5 70t95 40.5t129.5 13q66 0 115 -7.5t87 -16.5v-100q-46 11 -91 17.5t-107 6.5q-49 0 -85.5 -6.5t-63 -21t-43.5 -38.5t-27 -59h368v-89h-381q-1 -12 -1 -25v-54q0 -13 1 -25h381v-88h-368
q19 -70 70 -98.5t149 -28.5q62 0 107 6t91 17v-100q-38 -9 -87 -16.5t-115 -7.5q-149 0 -225.5 54.5t-99.5 173.5h-106v88z" />
    <glyph glyph-name="Euro.ot.weight" 
d="M49 399h96q4 84 24 144.5t60 99t101.5 57t149.5 18.5q66 0 115 -7.5t87 -16.5v-100q-46 11 -91 17.5t-107 6.5q-63 0 -106 -11t-70 -36.5t-40 -67.5t-16 -104h381v-89h-381q3 -64 15.5 -107t39.5 -69.5t70.5 -37.5t106.5 -11q62 0 107 6t91 17v-100q-38 -9 -87 -16.5
t-115 -7.5q-88 0 -150.5 18.5t-102 58.5t-59.5 101.5t-23 147.5h-96v89z" />
    <glyph glyph-name="cent.ot" 
d="M465 702v-80q112 -3 200 -24v-100q-48 9 -97 13.5t-103 6.5v-331q112 1 210 19v-100q-47 -11 -99 -16.5t-111 -7.5v-82h-88v81q-154 5 -226.5 68t-72.5 203q0 71 18 121t54.5 82.5t93 48.5t133.5 19v79h88zM190 352q0 -42 9.5 -72t31.5 -50t58 -30.5t88 -12.5v331
q-52 -2 -88 -12.5t-58 -30.5t-31.5 -50.5t-9.5 -72.5z" />
    <glyph glyph-name="cent.ot.weight" 
d="M465 702v-80q112 -3 200 -24v-100q-60 11 -120.5 16t-131.5 5q-63 0 -105.5 -9t-69 -29t-37.5 -51.5t-11 -77.5q0 -45 11 -77t37.5 -51.5t69 -28.5t105.5 -9q74 0 137 4.5t125 15.5v-100q-47 -11 -99 -16.5t-111 -7.5v-82h-88v81q-154 5 -226.5 68t-72.5 203q0 71 18 121
t54.5 82.5t93 48.5t133.5 19v79h88z" />
    <glyph glyph-name="sterling.ot" 
d="M123 89q55 0 75 25t20 84v112h-151v89h151v122q0 55 14 96.5t45.5 69t81.5 41t123 13.5q66 0 116 -8t89 -16v-100q-42 11 -87 17.5t-102 6.5q-50 0 -83.5 -7.5t-54 -22.5t-29 -38.5t-8.5 -55.5v-118h328v-89h-328v-113q0 -72 -44 -108h431v-89h-643v89h56z" />
    <glyph glyph-name="yen.ot" 
d="M88 300h237v104h-237v89h160l-207 209h124l214 -209h2l215 209h123l-207 -209h160v-89h-238v-104h238v-88h-238v-212h-109v212h-237v88z" />
    <glyph glyph-name="florin.ot" 
d="M90 399h230v112q0 56 12 97.5t39 69t71 41t109 13.5q31 0 56.5 -1.5t54.5 -3.5v-100q-26 3 -50 4t-49 1q-38 0 -64 -6t-42 -19.5t-23 -35.5t-7 -54v-118h235v-89h-235v-303q0 -56 -11.5 -97.5t-38.5 -69t-70.5 -41t-106.5 -13.5q-30 0 -55.5 1.5t-54.5 4.5v99
q25 -3 49 -3.5t49 -0.5q38 0 63 5.5t40.5 19t22 35.5t6.5 54v309h-230v89z" />
    <glyph glyph-name="zeroslash.ot" 
d="M380 -16q-86 0 -147 21t-100 65.5t-57 114t-18 166.5t18.5 166.5t57.5 114t100 65.5t146 21q86 0 147 -21t100 -65.5t57 -114t18 -166.5t-18 -166.5t-57 -114t-100 -65.5t-147 -21zM169 351q0 -93 18 -152l330 384q-24 18 -58 26t-79 8q-58 0 -98.5 -14t-65.5 -45.5
t-36 -82t-11 -124.5zM380 85q59 0 99 14t65 45.5t36 82t11 124.5q0 96 -19 152l-329 -384q24 -18 58 -26t79 -8z" />
    <glyph glyph-name="zero.ot" 
d="M380 -16q-86 0 -147 21t-100 65.5t-57 114t-18 166.5t18.5 166.5t57.5 114t100 65.5t146 21q86 0 147 -21t100 -65.5t57 -114t18 -166.5t-18 -166.5t-57 -114t-100 -65.5t-147 -21zM380 85q59 0 99 14t65 45.5t36 82t11 124.5t-11 124.5t-36 82t-65 45.5t-99 14
q-58 0 -98.5 -14t-65.5 -45.5t-36 -82t-11 -124.5t11 -124.5t36 -82t65.5 -45.5t98.5 -14z" />
    <glyph glyph-name="one.ot" 
d="M108 89h258v504l-246 -122l-45 84l296 147h103v-613h234v-89h-600v89z" />
    <glyph glyph-name="two.ot" 
d="M361 718q83 0 140.5 -10t93.5 -32.5t52 -57t16 -84.5q0 -35 -6.5 -63t-24 -52t-48.5 -45t-81 -44l-223 -102q-26 -12 -42 -22t-24.5 -21.5t-11 -26t-2.5 -35.5v-34h471v-89h-580v117q0 38 5.5 66.5t21 52t42 42.5t68.5 37l234 104q32 15 50 27.5t27 25.5t11 26.5t2 30.5
q0 24 -8.5 40.5t-30.5 27.5t-59 16t-93 5q-74 0 -141.5 -7.5t-121.5 -18.5v100q54 11 116 18.5t147 7.5z" />
    <glyph glyph-name="three.ot" 
d="M84 0q57 -11 116 -18t137 -7q70 0 115 8.5t71.5 25.5t36.5 43t10 62q0 42 -10.5 69.5t-33.5 43.5t-59.5 22.5t-89.5 6.5h-196v101h196q48 0 82 5.5t55.5 20.5t31.5 41t10 67q0 36 -9.5 62t-33 42.5t-63.5 24.5t-102 8q-75 0 -133.5 -7t-112.5 -18v89q46 11 107.5 18.5
t141.5 7.5q86 0 146 -12t98 -38.5t55 -67.5t17 -98q0 -88 -34 -133.5t-95 -63.5q69 -16 106.5 -62t37.5 -138q0 -58 -18.5 -100t-59.5 -69t-105.5 -39.5t-157.5 -12.5q-82 0 -145.5 7.5t-111.5 18.5v90z" />
    <glyph glyph-name="four.ot" 
d="M659 356v-456h-107v179h-504v85l302 538h115l-293 -522h380v176h107z" />
    <glyph glyph-name="five.ot" 
d="M83 12q57 -12 117.5 -18.5t138.5 -6.5q67 0 111 8.5t69.5 26.5t35.5 45.5t10 64.5q0 39 -10.5 66.5t-34.5 44.5t-62 24.5t-93 7.5h-248v427h521v-102h-413v-223h156q80 0 136 -14.5t91.5 -44.5t51.5 -76t16 -110q0 -67 -18 -114.5t-58 -77t-103.5 -43t-153.5 -13.5
q-84 0 -147.5 7.5t-112.5 18.5v102z" />
    <glyph glyph-name="six.ot" 
d="M391 -16q-92 0 -154 22.5t-100 72t-54 129t-16 193.5t20 193t64 129t114 72.5t170 22.5q63 0 109 -7.5t84 -18.5v-102q-43 11 -86.5 18t-103.5 7q-72 0 -121 -13t-79.5 -44.5t-44 -84t-14.5 -130.5q51 20 107 33.5t133 13.5q73 0 124.5 -15t83.5 -46t47 -78t15 -111
q0 -72 -21 -121t-59.5 -79t-93.5 -43t-124 -13zM396 390q-67 0 -116 -11.5t-101 -29.5q2 -77 13 -128.5t35.5 -82t65 -43.5t100.5 -13q45 0 79.5 7.5t58 25t35.5 47t12 73.5q0 43 -8.5 72.5t-29.5 48t-56 26.5t-88 8z" />
    <glyph glyph-name="seven.ot" 
d="M68 702h624v-84l-418 -718h-130l420 700h-496v102z" />
    <glyph glyph-name="eight.ot" 
d="M380 -16q-85 0 -145.5 13t-99 40.5t-56.5 69.5t-18 100q0 90 38.5 135.5t108.5 62.5q-62 18 -97 63t-35 132q0 56 17 97t54 68t94.5 40t138.5 13q80 0 137.5 -13t94.5 -40t54.5 -68t17.5 -97q0 -87 -35 -132t-97 -63q70 -17 108.5 -62.5t38.5 -135.5q0 -58 -18 -100
t-56.5 -69.5t-99 -40.5t-145.5 -13zM380 457q53 0 89.5 6t59.5 20t33.5 39t10.5 62q0 33 -9 57.5t-31 41t-59 24.5t-94 8t-94 -8t-59 -24.5t-31 -41t-9 -57.5q0 -37 10.5 -61.5t33.5 -39t59.5 -20.5t89.5 -6zM380 87q60 0 100 8.5t63.5 25t33.5 41.5t10 59q0 39 -11.5 65
t-36.5 41t-64 21.5t-95 6.5q-55 0 -94.5 -6.5t-64.5 -21.5t-36.5 -41t-11.5 -65q0 -34 10 -59t33.5 -41.5t63.5 -25t100 -8.5z" />
    <glyph glyph-name="nine.ot" 
d="M365 718q92 0 154 -22.5t100 -72t54 -129t16 -193.5t-20 -193t-64 -129t-114 -72.5t-170 -22.5q-63 0 -109 7.5t-84 18.5v102q22 -6 43 -10.5t43.5 -7.5t48 -5t55.5 -2q72 0 121 13.5t79.5 45t44 83.5t14.5 130q-51 -20 -107 -33.5t-133 -13.5q-73 0 -124.5 15t-83.5 46
t-47 78t-15 111q0 72 21 121t59.5 79t93.5 43t124 13zM178 467q0 -43 8.5 -72.5t29.5 -48t56 -26.5t88 -8q67 0 116 11.5t101 29.5q-2 77 -13 128t-35.5 82t-65 44t-100.5 13q-45 0 -79.5 -7.5t-58 -25t-35.5 -47t-12 -73.5z" />
    <glyph glyph-name="dollar.op" horiz-adv-x="736" 
d="M87 176q50 -15 111.5 -25t126.5 -13v183q-74 11 -122 24t-75.5 33.5t-38.5 51t-11 76.5q0 41 12.5 71.5t41.5 51t76 31.5t117 14v87h89v-87q57 -2 109 -10.5t99 -19.5v-100q-48 13 -99.5 21t-108.5 10v-162q74 -11 122 -24.5t76.5 -34.5t40 -52t11.5 -78q0 -46 -14 -79.5
t-44 -56t-77.5 -34.5t-114.5 -15v-99h-89v99q-67 3 -129 13t-109 24v100zM180 509q0 -20 5 -33t20 -22t43.5 -15.5t76.5 -13.5v150q-43 -1 -71.5 -5t-44.5 -12t-22.5 -20t-6.5 -29zM562 219q0 21 -5.5 35t-21 24t-44.5 17t-77 14v-171q84 3 116 21t32 60z" />
    <glyph glyph-name="dollar.op.weight" horiz-adv-x="736" 
d="M87 176q58 -17 133 -28t150 -11q55 0 92 4t59 13.5t31.5 25t9.5 39.5t-7 39t-28 24.5t-60 17t-104 16.5q-86 11 -141 23.5t-87 33.5t-44.5 52.5t-12.5 80.5q0 41 12.5 71.5t41.5 51t76 31.5t117 14v87h89v-87q57 -2 109 -10.5t99 -19.5v-100q-57 15 -121 23.5t-133 8.5
q-57 0 -93.5 -3.5t-57.5 -11t-29 -20.5t-8 -32q0 -22 6.5 -36t26.5 -23t59 -16t103 -16q86 -11 142 -24.5t88.5 -34.5t45.5 -53t13 -82q0 -46 -14 -79.5t-44 -56t-77.5 -34.5t-114.5 -15v-99h-89v99q-67 3 -129 13t-109 24v100z" />
    <glyph glyph-name="Euro.op" horiz-adv-x="736" 
d="M37 300h96q-1 12 -1 25v53q0 13 1 26h-96v89h107q12 59 36 101.5t63.5 70t95 40.5t129.5 13q66 0 115 -7.5t87 -16.5v-100q-46 11 -91 17.5t-107 6.5q-49 0 -85.5 -6.5t-63 -21t-43.5 -38.5t-27 -59h368v-89h-381q-1 -12 -1 -25v-54q0 -13 1 -25h381v-88h-368
q19 -70 70 -98.5t149 -28.5q62 0 107 6t91 17v-100q-38 -9 -87 -16.5t-115 -7.5q-149 0 -225.5 54.5t-99.5 173.5h-106v88z" />
    <glyph glyph-name="Euro.op.weight" horiz-adv-x="736" 
d="M37 399h96q4 84 24 144.5t60 99t101.5 57t149.5 18.5q66 0 115 -7.5t87 -16.5v-100q-46 11 -91 17.5t-107 6.5q-63 0 -106 -11t-70 -36.5t-40 -67.5t-16 -104h381v-89h-381q3 -64 15.5 -107t39.5 -69.5t70.5 -37.5t106.5 -11q62 0 107 6t91 17v-100q-38 -9 -87 -16.5
t-115 -7.5q-88 0 -150.5 18.5t-102 58.5t-59.5 101.5t-23 147.5h-96v89z" />
    <glyph glyph-name="cent.op" horiz-adv-x="736" 
d="M453 702v-80q112 -3 200 -24v-100q-48 9 -97 13.5t-103 6.5v-331q112 1 210 19v-100q-47 -11 -99 -16.5t-111 -7.5v-82h-88v81q-154 5 -226.5 68t-72.5 203q0 71 18 121t54.5 82.5t93 48.5t133.5 19v79h88zM178 352q0 -42 9.5 -72t31.5 -50t58 -30.5t88 -12.5v331
q-52 -2 -88 -12.5t-58 -30.5t-31.5 -50.5t-9.5 -72.5z" />
    <glyph glyph-name="cent.op.weight" horiz-adv-x="736" 
d="M453 702v-80q112 -3 200 -24v-100q-60 11 -120.5 16t-131.5 5q-63 0 -105.5 -9t-69 -29t-37.5 -51.5t-11 -77.5q0 -45 11 -77t37.5 -51.5t69 -28.5t105.5 -9q74 0 137 4.5t125 15.5v-100q-47 -11 -99 -16.5t-111 -7.5v-82h-88v81q-154 5 -226.5 68t-72.5 203q0 71 18 121
t54.5 82.5t93 48.5t133.5 19v79h88z" />
    <glyph glyph-name="sterling.op" horiz-adv-x="736" 
d="M111 89q55 0 75 25t20 84v112h-151v89h151v122q0 55 14 96.5t45.5 69t81.5 41t123 13.5q66 0 116 -8t89 -16v-100q-42 11 -87 17.5t-102 6.5q-50 0 -83.5 -7.5t-54 -22.5t-29 -38.5t-8.5 -55.5v-118h328v-89h-328v-113q0 -72 -44 -108h431v-89h-643v89h56z" />
    <glyph glyph-name="yen.op" horiz-adv-x="736" 
d="M76 300h237v104h-237v89h160l-207 209h124l214 -209h2l215 209h123l-207 -209h160v-89h-238v-104h238v-88h-238v-212h-109v212h-237v88z" />
    <glyph glyph-name="florin.op" horiz-adv-x="736" 
d="M78 399h230v112q0 56 12 97.5t39 69t71 41t109 13.5q31 0 56.5 -1.5t54.5 -3.5v-100q-26 3 -50 4t-49 1q-38 0 -64 -6t-42 -19.5t-23 -35.5t-7 -54v-118h235v-89h-235v-303q0 -56 -11.5 -97.5t-38.5 -69t-70.5 -41t-106.5 -13.5q-30 0 -55.5 1.5t-54.5 4.5v99
q25 -3 49 -3.5t49 -0.5q38 0 63 5.5t40.5 19t22 35.5t6.5 54v309h-230v89z" />
    <glyph glyph-name="zeroslash.op" horiz-adv-x="736" 
d="M368 -16q-86 0 -147 21t-100 65.5t-57 114t-18 166.5t18.5 166.5t57.5 114t100 65.5t146 21q86 0 147 -21t100 -65.5t57 -114t18 -166.5t-18 -166.5t-57 -114t-100 -65.5t-147 -21zM157 351q0 -93 18 -152l330 384q-24 18 -58 26t-79 8q-58 0 -98.5 -14t-65.5 -45.5
t-36 -82t-11 -124.5zM368 85q59 0 99 14t65 45.5t36 82t11 124.5q0 96 -19 152l-329 -384q24 -18 58 -26t79 -8z" />
    <glyph glyph-name="zero.op" horiz-adv-x="736" 
d="M368 -16q-86 0 -147 21t-100 65.5t-57 114t-18 166.5t18.5 166.5t57.5 114t100 65.5t146 21q86 0 147 -21t100 -65.5t57 -114t18 -166.5t-18 -166.5t-57 -114t-100 -65.5t-147 -21zM368 85q59 0 99 14t65 45.5t36 82t11 124.5t-11 124.5t-36 82t-65 45.5t-99 14
q-58 0 -98.5 -14t-65.5 -45.5t-36 -82t-11 -124.5t11 -124.5t36 -82t65.5 -45.5t98.5 -14z" />
    <glyph glyph-name="one.op" horiz-adv-x="572" 
d="M341 702h103v-702h-109v585l-257 -122l-49 92z" />
    <glyph glyph-name="two.op" horiz-adv-x="736" 
d="M349 718q83 0 140.5 -10t93.5 -32.5t52 -57t16 -84.5q0 -35 -6.5 -63t-24 -52t-48.5 -45t-81 -44l-223 -102q-26 -12 -42 -22t-24.5 -21.5t-11 -26t-2.5 -35.5v-34h471v-89h-580v117q0 38 5.5 66.5t21 52t42 42.5t68.5 37l234 104q32 15 50 27.5t27 25.5t11 26.5t2 30.5
q0 24 -8.5 40.5t-30.5 27.5t-59 16t-93 5q-74 0 -141.5 -7.5t-121.5 -18.5v100q54 11 116 18.5t147 7.5z" />
    <glyph glyph-name="three.op" horiz-adv-x="736" 
d="M72 0q57 -11 116 -18t137 -7q70 0 115 8.5t71.5 25.5t36.5 43t10 62q0 42 -10.5 69.5t-33.5 43.5t-59.5 22.5t-89.5 6.5h-196v101h196q48 0 82 5.5t55.5 20.5t31.5 41t10 67q0 36 -9.5 62t-33 42.5t-63.5 24.5t-102 8q-75 0 -133.5 -7t-112.5 -18v89q46 11 107.5 18.5
t141.5 7.5q86 0 146 -12t98 -38.5t55 -67.5t17 -98q0 -88 -34 -133.5t-95 -63.5q69 -16 106.5 -62t37.5 -138q0 -58 -18.5 -100t-59.5 -69t-105.5 -39.5t-157.5 -12.5q-82 0 -145.5 7.5t-111.5 18.5v90z" />
    <glyph glyph-name="four.op" horiz-adv-x="736" 
d="M647 356v-456h-107v179h-504v85l302 538h115l-293 -522h380v176h107z" />
    <glyph glyph-name="five.op" horiz-adv-x="736" 
d="M71 12q57 -12 117.5 -18.5t138.5 -6.5q67 0 111 8.5t69.5 26.5t35.5 45.5t10 64.5q0 39 -10.5 66.5t-34.5 44.5t-62 24.5t-93 7.5h-248v427h521v-102h-413v-223h156q80 0 136 -14.5t91.5 -44.5t51.5 -76t16 -110q0 -67 -18 -114.5t-58 -77t-103.5 -43t-153.5 -13.5
q-84 0 -147.5 7.5t-112.5 18.5v102z" />
    <glyph glyph-name="six.op" horiz-adv-x="736" 
d="M379 -16q-92 0 -154 22.5t-100 72t-54 129t-16 193.5t20 193t64 129t114 72.5t170 22.5q63 0 109 -7.5t84 -18.5v-102q-43 11 -86.5 18t-103.5 7q-72 0 -121 -13t-79.5 -44.5t-44 -84t-14.5 -130.5q51 20 107 33.5t133 13.5q73 0 124.5 -15t83.5 -46t47 -78t15 -111
q0 -72 -21 -121t-59.5 -79t-93.5 -43t-124 -13zM384 390q-67 0 -116 -11.5t-101 -29.5q2 -77 13 -128.5t35.5 -82t65 -43.5t100.5 -13q45 0 79.5 7.5t58 25t35.5 47t12 73.5q0 43 -8.5 72.5t-29.5 48t-56 26.5t-88 8z" />
    <glyph glyph-name="seven.op" horiz-adv-x="736" 
d="M56 702h624v-84l-418 -718h-130l420 700h-496v102z" />
    <glyph glyph-name="eight.op" horiz-adv-x="736" 
d="M368 -16q-85 0 -145.5 13t-99 40.5t-56.5 69.5t-18 100q0 90 38.5 135.5t108.5 62.5q-62 18 -97 63t-35 132q0 56 17 97t54 68t94.5 40t138.5 13q80 0 137.5 -13t94.5 -40t54.5 -68t17.5 -97q0 -87 -35 -132t-97 -63q70 -17 108.5 -62.5t38.5 -135.5q0 -58 -18 -100
t-56.5 -69.5t-99 -40.5t-145.5 -13zM368 457q53 0 89.5 6t59.5 20t33.5 39t10.5 62q0 33 -9 57.5t-31 41t-59 24.5t-94 8t-94 -8t-59 -24.5t-31 -41t-9 -57.5q0 -37 10.5 -61.5t33.5 -39t59.5 -20.5t89.5 -6zM368 87q60 0 100 8.5t63.5 25t33.5 41.5t10 59q0 39 -11.5 65
t-36.5 41t-64 21.5t-95 6.5q-55 0 -94.5 -6.5t-64.5 -21.5t-36.5 -41t-11.5 -65q0 -34 10 -59t33.5 -41.5t63.5 -25t100 -8.5z" />
    <glyph glyph-name="nine.op" horiz-adv-x="736" 
d="M357 718q92 0 154 -22.5t100 -72t54 -129t16 -193.5t-20 -193t-64 -129t-114 -72.5t-170 -22.5q-63 0 -109 7.5t-84 18.5v102q22 -6 43 -10.5t43.5 -7.5t48 -5t55.5 -2q72 0 121 13.5t79.5 45t44 83.5t14.5 130q-51 -20 -107 -33.5t-133 -13.5q-73 0 -124.5 15t-83.5 46
t-47 78t-15 111q0 72 21 121t59.5 79t93.5 43t124 13zM170 467q0 -43 8.5 -72.5t29.5 -48t56 -26.5t88 -8q67 0 116 11.5t101 29.5q-2 77 -13 128t-35.5 82t-65 44t-100.5 13q-45 0 -79.5 -7.5t-58 -25t-35.5 -47t-12 -73.5z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="425" 
d="M213 333q-102 0 -147 57.5t-45 181.5t45 181.5t147 57.5q104 0 149.5 -57.5t45.5 -181.5t-45.5 -181.5t-149.5 -57.5zM213 412q29 0 47.5 8t30 26.5t16 49t4.5 76.5t-4.5 76.5t-16 49t-30 26.5t-47.5 8q-27 0 -45.5 -8t-30 -26.5t-16 -49t-4.5 -76.5t4.5 -76.5t16 -49
t30 -26.5t45.5 -8z" />
    <glyph glyph-name="one.numr" horiz-adv-x="337" 
d="M191 802h91v-460h-93v368l-138 -66l-39 72z" />
    <glyph glyph-name="two.numr" horiz-adv-x="425" 
d="M204 811q101 0 144.5 -29.5t43.5 -96.5q0 -26 -5.5 -44t-17.5 -32.5t-30.5 -27t-45.5 -27.5l-124 -68q-22 -13 -28.5 -21.5t-6.5 -29.5v-16h259v-77h-353v95q0 22 4 38t13.5 29.5t26 25t40.5 24.5l129 70q25 14 34 23.5t9 32.5q0 14 -4 24t-15.5 16t-30.5 8.5t-49 2.5
q-36 0 -75 -5t-68 -10v80q30 6 66 10.5t84 4.5z" />
    <glyph glyph-name="three.numr" horiz-adv-x="425" 
d="M37 426q35 -8 68 -11.5t78 -3.5q36 0 58.5 3t36 10.5t18.5 19.5t5 29q0 38 -18.5 51t-68.5 13h-123v77h123q44 0 62.5 11.5t18.5 48.5q0 17 -4.5 28.5t-16.5 18.5t-32 10t-52 3q-46 0 -78 -4t-67 -11v77q25 5 62.5 10t85.5 5q104 0 150.5 -27.5t46.5 -95.5
q0 -50 -19.5 -76t-54.5 -38q40 -11 61 -36.5t21 -76.5q0 -71 -49.5 -99.5t-160.5 -28.5q-49 0 -87 4.5t-64 10.5v78z" />
    <glyph glyph-name="four.numr" horiz-adv-x="425" 
d="M390 604v-262h-93v93h-278v72l165 295h99l-165 -292h179v94h93z" />
    <glyph glyph-name="five.numr" horiz-adv-x="425" 
d="M36 428q35 -7 68.5 -11t80.5 -4q35 0 57.5 3.5t35.5 11.5t18 21t5 32q0 37 -20.5 51.5t-75.5 14.5h-152v255h318v-77h-225v-102h74q49 0 82.5 -8.5t54.5 -26t30.5 -44t9.5 -62.5q0 -43 -12 -71.5t-37 -46t-64.5 -24.5t-95.5 -7q-48 0 -87 4.5t-65 10.5v80z" />
    <glyph glyph-name="six.numr" horiz-adv-x="425" 
d="M218 333q-54 0 -91.5 13.5t-60 43t-32.5 74.5t-10 108q0 66 12 111.5t39 74t69.5 41t102.5 12.5q38 0 67.5 -4.5t50.5 -10.5v-80q-28 7 -52.5 11t-61.5 4q-36 0 -61 -5.5t-40 -19t-22 -37t-8 -60.5q26 10 54.5 16.5t66.5 6.5q88 0 124 -37t36 -110q0 -84 -49 -118
t-134 -34zM220 555q-29 0 -53 -5.5t-47 -13.5q1 -36 6.5 -60.5t16.5 -39.5t30 -21t47 -6q43 0 64 15.5t21 57.5q0 21 -4 35t-13.5 22.5t-26 12t-41.5 3.5z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="425" 
d="M27 802h375v-72l-227 -388h-113l234 383h-269v77z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="425" 
d="M214 333q-100 0 -146.5 30t-46.5 99q0 49 21.5 75t59.5 37q-34 12 -53.5 38t-19.5 74q0 67 45 96t140 29t140 -29t45 -96q0 -48 -19 -74t-53 -38q38 -11 59.5 -37t21.5 -75q0 -69 -47.5 -99t-146.5 -30zM214 610q51 0 70.5 13t19.5 48q0 32 -18.5 47t-71.5 15t-71 -15
t-19 -47q1 -35 20 -48t70 -13zM214 410q55 0 76 15.5t21 49.5q0 38 -22.5 52.5t-74.5 14.5t-74.5 -14.5t-22.5 -52.5q0 -34 21 -49.5t76 -15.5z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="425" 
d="M207 811q54 0 91.5 -13.5t60.5 -43t33 -74.5t10 -108q0 -66 -12 -111.5t-39 -74t-69.5 -41t-103.5 -12.5q-38 0 -67 4.5t-50 10.5v80q28 -7 52.5 -11t61.5 -4q36 0 61 5.5t40 19t22 37t8 60.5q-26 -10 -54.5 -16.5t-66.5 -6.5q-88 0 -124 37t-36 110q0 84 49 118t133 34z
M120 662q0 -42 18 -57.5t67 -15.5q30 0 54 5.5t47 13.5q-1 36 -6.5 60.5t-17 39.5t-30 21t-47.5 6q-43 0 -64 -15.5t-21 -57.5z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="429" 
d="M213 -9q-102 0 -147 57.5t-45 181.5t45 181.5t147 57.5q104 0 149.5 -57.5t45.5 -181.5t-45.5 -181.5t-149.5 -57.5zM213 70q29 0 47.5 8t30 26.5t16 49t4.5 76.5t-4.5 76.5t-16 49t-30 26.5t-47.5 8q-27 0 -45.5 -8t-30 -26.5t-16 -49t-4.5 -76.5t4.5 -76.5t16 -49
t30 -26.5t45.5 -8z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="344" 
d="M191 460h91v-460h-93v368l-138 -66l-39 72z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="429" 
d="M204 469q101 0 144.5 -29.5t43.5 -96.5q0 -26 -5.5 -44t-17.5 -32.5t-30.5 -27t-45.5 -27.5l-124 -68q-22 -13 -28.5 -21.5t-6.5 -29.5v-16h259v-77h-353v95q0 22 4 38t13.5 29.5t26 25t40.5 24.5l129 70q25 14 34 23.5t9 32.5q0 14 -4 24t-15.5 16t-30.5 8.5t-49 2.5
q-36 0 -75 -5t-68 -10v80q30 6 66 10.5t84 4.5z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="429" 
d="M37 84q35 -8 68 -11.5t78 -3.5q36 0 58.5 3t36 10.5t18.5 19.5t5 29q0 38 -18.5 51t-68.5 13h-123v77h123q44 0 62.5 11.5t18.5 48.5q0 17 -4.5 28.5t-16.5 18.5t-32 10t-52 3q-46 0 -78 -4t-67 -11v77q25 5 62.5 10t85.5 5q104 0 150.5 -27.5t46.5 -95.5
q0 -50 -19.5 -76t-54.5 -38q40 -11 61 -36.5t21 -76.5q0 -71 -49.5 -99.5t-160.5 -28.5q-49 0 -87 4.5t-64 10.5v78z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="429" 
d="M390 262v-262h-93v93h-278v72l165 295h99l-165 -292h179v94h93z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="429" 
d="M36 86q35 -7 68.5 -11t80.5 -4q35 0 57.5 3.5t35.5 11.5t18 21t5 32q0 37 -20.5 51.5t-75.5 14.5h-152v255h318v-77h-225v-102h74q49 0 82.5 -8.5t54.5 -26t30.5 -44t9.5 -62.5q0 -43 -12 -71.5t-37 -46t-64.5 -24.5t-95.5 -7q-48 0 -87 4.5t-65 10.5v80z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="429" 
d="M218 -9q-54 0 -91.5 13.5t-60 43t-32.5 74.5t-10 108q0 66 12 111.5t39 74t69.5 41t102.5 12.5q38 0 67.5 -4.5t50.5 -10.5v-80q-28 7 -52.5 11t-61.5 4q-36 0 -61 -5.5t-40 -19t-22 -37t-8 -60.5q26 10 54.5 16.5t66.5 6.5q88 0 124 -37t36 -110q0 -84 -49 -118
t-134 -34zM220 213q-29 0 -53 -5.5t-47 -13.5q1 -36 6.5 -60.5t16.5 -39.5t30 -21t47 -6q43 0 64 15.5t21 57.5q0 21 -4 35t-13.5 22.5t-26 12t-41.5 3.5z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="429" 
d="M27 460h375v-72l-227 -388h-113l234 383h-269v77z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="429" 
d="M214 -9q-100 0 -146.5 30t-46.5 99q0 49 21.5 75t59.5 37q-34 12 -53.5 38t-19.5 74q0 67 45 96t140 29t140 -29t45 -96q0 -48 -19 -74t-53 -38q38 -11 59.5 -37t21.5 -75q0 -69 -47.5 -99t-146.5 -30zM214 268q51 0 70.5 13t19.5 48q0 32 -18.5 47t-71.5 15t-71 -15
t-19 -47q1 -35 20 -48t70 -13zM214 68q55 0 76 15.5t21 49.5q0 38 -22.5 52.5t-74.5 14.5t-74.5 -14.5t-22.5 -52.5q0 -34 21 -49.5t76 -15.5z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="429" 
d="M207 469q54 0 91.5 -13.5t60.5 -43t33 -74.5t10 -108q0 -66 -12 -111.5t-39 -74t-69.5 -41t-103.5 -12.5q-38 0 -67 4.5t-50 10.5v80q28 -7 52.5 -11t61.5 -4q36 0 61 5.5t40 19t22 37t8 60.5q-26 -10 -54.5 -16.5t-66.5 -6.5q-88 0 -124 37t-36 110q0 84 49 118t133 34z
M120 320q0 -42 18 -57.5t67 -15.5q30 0 54 5.5t47 13.5q-1 36 -6.5 60.5t-17 39.5t-30 21t-47.5 6q-43 0 -64 -15.5t-21 -57.5z" />
    <hkern u1="&#x23;" g2="nine.op" k="10" />
    <hkern u1="&#x23;" g2="four.op" k="80" />
    <hkern u1="&#x23;" u2="&#x37;" k="-30" />
    <hkern u1="&#x23;" u2="&#x34;" k="60" />
    <hkern u1="&#x28;" u2="&#x237;" k="-50" />
    <hkern u1="&#x28;" u2="j" k="-50" />
    <hkern u1="&#x2a;" u2="X" k="32" />
    <hkern u1="&#x2a;" u2="&#x2f;" k="110" />
    <hkern u1="&#x2f;" u2="&#xdf;" k="20" />
    <hkern u1="&#x2f;" u2="x" k="20" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="120" />
    <hkern u1="&#x31;" u2="&#x20ac;" k="4" />
    <hkern u1="&#x31;" u2="&#x192;" k="20" />
    <hkern u1="&#x31;" u2="&#xb0;" k="20" />
    <hkern u1="&#x31;" u2="&#x33;" k="20" />
    <hkern u1="&#x31;" u2="&#x31;" k="60" />
    <hkern u1="&#x32;" u2="&#x192;" k="60" />
    <hkern u1="&#x32;" u2="&#xa5;" k="8" />
    <hkern u1="&#x32;" u2="&#x37;" k="10" />
    <hkern u1="&#x32;" u2="&#x34;" k="10" />
    <hkern u1="&#x32;" u2="&#x33;" k="12" />
    <hkern u1="&#x32;" u2="&#x32;" k="10" />
    <hkern u1="&#x32;" u2="&#x31;" k="10" />
    <hkern u1="&#x33;" u2="&#x3e;" k="30" />
    <hkern u1="&#x33;" u2="&#x39;" k="10" />
    <hkern u1="&#x33;" u2="&#x37;" k="10" />
    <hkern u1="&#x33;" u2="&#x34;" k="-4" />
    <hkern u1="&#x33;" u2="&#x33;" k="20" />
    <hkern u1="&#x33;" u2="&#x32;" k="10" />
    <hkern u1="&#x33;" u2="&#x31;" k="20" />
    <hkern u1="&#x34;" u2="&#x37;" k="12" />
    <hkern u1="&#x34;" u2="&#x33;" k="24" />
    <hkern u1="&#x34;" u2="&#x31;" k="104" />
    <hkern u1="&#x35;" u2="&#xb0;" k="30" />
    <hkern u1="&#x35;" u2="&#x39;" k="10" />
    <hkern u1="&#x35;" u2="&#x37;" k="30" />
    <hkern u1="&#x35;" u2="&#x35;" k="10" />
    <hkern u1="&#x35;" u2="&#x33;" k="30" />
    <hkern u1="&#x35;" u2="&#x32;" k="20" />
    <hkern u1="&#x35;" u2="&#x31;" k="70" />
    <hkern u1="&#x37;" u2="&#x192;" k="70" />
    <hkern u1="&#x37;" u2="&#xb0;" k="-20" />
    <hkern u1="&#x37;" u2="&#xa3;" k="50" />
    <hkern u1="&#x37;" u2="&#x3e;" k="90" />
    <hkern u1="&#x37;" u2="&#x3c;" k="140" />
    <hkern u1="&#x37;" u2="&#x35;" k="20" />
    <hkern u1="&#x37;" u2="&#x34;" k="100" />
    <hkern u1="&#x37;" u2="&#x33;" k="10" />
    <hkern u1="&#x37;" u2="&#x32;" k="40" />
    <hkern u1="&#x37;" u2="&#x23;" k="90" />
    <hkern u1="&#x3c;" g2="seven.op" k="10" />
    <hkern u1="&#x3c;" u2="&#x37;" k="40" />
    <hkern u1="&#x3c;" u2="&#x31;" k="90" />
    <hkern u1="&#x3e;" g2="seven.op" k="110" />
    <hkern u1="&#x3e;" u2="&#x37;" k="100" />
    <hkern u1="&#x3f;" u2="X" k="30" />
    <hkern u1="&#x40;" u2="X" k="20" />
    <hkern u1="&#x40;" u2="V" k="50" />
    <hkern u1="B" g2="bullet.case" k="10" />
    <hkern u1="B" u2="&#x2122;" k="40" />
    <hkern u1="B" u2="&#xae;" k="18" />
    <hkern u1="B" u2="v" k="14" />
    <hkern u1="B" u2="X" k="20" />
    <hkern u1="B" u2="V" k="30" />
    <hkern u1="F" u2="&#xbf;" k="200" />
    <hkern u1="F" u2="&#xb0;" k="-20" />
    <hkern u1="F" u2="x" k="20" />
    <hkern u1="F" u2="v" k="10" />
    <hkern u1="F" u2="&#x3f;" k="-40" />
    <hkern u1="F" u2="&#x2f;" k="114" />
    <hkern u1="P" g2="at.case" k="-20" />
    <hkern u1="P" g2="bullet.case" k="-20" />
    <hkern u1="P" u2="v" k="-8" />
    <hkern u1="P" u2="X" k="54" />
    <hkern u1="P" u2="&#x2f;" k="80" />
    <hkern u1="P" u2="&#x2a;" k="-40" />
    <hkern u1="V" g2="at.case" k="40" />
    <hkern u1="V" g2="bullet.case" k="60" />
    <hkern u1="V" g2="questiondown.case" k="88" />
    <hkern u1="V" u2="&#x2022;" k="70" />
    <hkern u1="V" u2="&#xdf;" k="30" />
    <hkern u1="V" u2="&#xbf;" k="140" />
    <hkern u1="V" u2="&#xa9;" k="30" />
    <hkern u1="V" u2="&#xa1;" k="50" />
    <hkern u1="V" u2="x" k="40" />
    <hkern u1="V" u2="v" k="40" />
    <hkern u1="V" u2="V" k="-8" />
    <hkern u1="V" u2="&#x40;" k="50" />
    <hkern u1="V" u2="&#x2f;" k="80" />
    <hkern u1="V" u2="&#x23;" k="82" />
    <hkern u1="X" g2="at.case" k="70" />
    <hkern u1="X" g2="bullet.case" k="110" />
    <hkern u1="X" u2="&#x2022;" k="40" />
    <hkern u1="X" u2="&#xae;" k="14" />
    <hkern u1="X" u2="&#xa9;" k="40" />
    <hkern u1="X" u2="x" k="-4" />
    <hkern u1="X" u2="v" k="50" />
    <hkern u1="X" u2="&#x40;" k="20" />
    <hkern u1="X" u2="&#x2a;" k="32" />
    <hkern u1="\" u2="&#xdf;" k="20" />
    <hkern u1="\" u2="v" k="48" />
    <hkern u1="\" u2="V" k="80" />
    <hkern u1="v" u2="&#x2022;" k="20" />
    <hkern u1="v" u2="&#xbf;" k="24" />
    <hkern u1="v" u2="v" k="-10" />
    <hkern u1="v" u2="X" k="50" />
    <hkern u1="v" u2="V" k="40" />
    <hkern u1="v" u2="&#x2f;" k="48" />
    <hkern u1="x" u2="&#x2122;" k="40" />
    <hkern u1="x" u2="&#x2022;" k="40" />
    <hkern u1="x" u2="x" k="-8" />
    <hkern u1="x" u2="\" k="20" />
    <hkern u1="x" u2="X" k="-4" />
    <hkern u1="x" u2="V" k="40" />
    <hkern u1="&#xa1;" u2="V" k="50" />
    <hkern u1="&#xa3;" u2="&#x31;" k="24" />
    <hkern u1="&#xa5;" u2="&#x34;" k="14" />
    <hkern u1="&#xa5;" u2="&#x32;" k="24" />
    <hkern u1="&#xa5;" u2="&#x31;" k="12" />
    <hkern u1="&#xa9;" u2="X" k="40" />
    <hkern u1="&#xa9;" u2="V" k="30" />
    <hkern u1="&#xae;" u2="X" k="14" />
    <hkern u1="&#xb0;" g2="seven.op" k="-20" />
    <hkern u1="&#xb0;" g2="four.op" k="110" />
    <hkern u1="&#xb0;" u2="&#x37;" k="-10" />
    <hkern u1="&#xb0;" u2="&#x34;" k="80" />
    <hkern u1="&#xb0;" u2="&#x32;" k="20" />
    <hkern u1="&#xbf;" u2="&#xdf;" k="50" />
    <hkern u1="&#xbf;" u2="x" k="44" />
    <hkern u1="&#xbf;" u2="v" k="64" />
    <hkern u1="&#xbf;" u2="X" k="40" />
    <hkern u1="&#xbf;" u2="V" k="130" />
    <hkern u1="&#xbf;" u2="&#x237;" k="-100" />
    <hkern u1="&#xbf;" u2="j" k="-90" />
    <hkern u1="&#xde;" g2="at.case" k="-30" />
    <hkern u1="&#xde;" g2="bullet.case" k="-20" />
    <hkern u1="&#xde;" g2="parenleft.case" k="-30" />
    <hkern u1="&#xde;" u2="v" k="-8" />
    <hkern u1="&#xde;" u2="X" k="74" />
    <hkern u1="&#xde;" u2="V" k="30" />
    <hkern u1="&#xde;" u2="&#x2f;" k="50" />
    <hkern u1="&#xde;" u2="&#x2a;" k="-10" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="30" />
    <hkern u1="&#xdf;" u2="&#x2022;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xb0;" k="-10" />
    <hkern u1="&#xdf;" u2="&#xae;" k="20" />
    <hkern u1="&#xdf;" u2="x" k="10" />
    <hkern u1="&#xdf;" u2="v" k="10" />
    <hkern u1="&#xdf;" u2="\" k="20" />
    <hkern u1="&#xdf;" u2="X" k="12" />
    <hkern u1="&#xdf;" u2="V" k="30" />
    <hkern u1="&#xdf;" u2="&#x2f;" k="20" />
    <hkern u1="&#xf0;" u2="&#xae;" k="4" />
    <hkern u1="&#xf0;" u2="x" k="28" />
    <hkern u1="&#xf0;" u2="v" k="20" />
    <hkern u1="&#xf0;" u2="X" k="30" />
    <hkern u1="&#xf0;" u2="V" k="35" />
    <hkern u1="&#xf0;" u2="&#x2a;" k="4" />
    <hkern u1="&#x13d;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x13d;" u2="&#x21a;" k="80" />
    <hkern u1="&#x13d;" u2="&#x17d;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x17b;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x179;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x178;" k="80" />
    <hkern u1="&#x13d;" u2="&#x176;" k="80" />
    <hkern u1="&#x13d;" u2="&#x166;" k="80" />
    <hkern u1="&#x13d;" u2="&#x164;" k="80" />
    <hkern u1="&#x13d;" u2="&#x162;" k="80" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="80" />
    <hkern u1="&#x13d;" u2="Z" k="-20" />
    <hkern u1="&#x13d;" u2="Y" k="80" />
    <hkern u1="&#x13d;" u2="V" k="60" />
    <hkern u1="&#x13d;" u2="T" k="80" />
    <hkern u1="&#x192;" u2="&#x37;" k="20" />
    <hkern u1="&#x192;" u2="&#x34;" k="44" />
    <hkern u1="&#x192;" u2="&#x32;" k="30" />
    <hkern u1="&#x192;" u2="&#x31;" k="28" />
    <hkern u1="&#x2022;" u2="x" k="40" />
    <hkern u1="&#x2022;" u2="v" k="20" />
    <hkern u1="&#x2022;" u2="X" k="40" />
    <hkern u1="&#x2022;" u2="V" k="70" />
    <hkern u1="&#x2044;" g2="seven.dnom" k="-20" />
    <hkern u1="&#x20ac;" u2="&#x31;" k="4" />
    <hkern g1="questiondown.case" u2="V" k="74" />
    <hkern g1="bullet.case" u2="X" k="110" />
    <hkern g1="bullet.case" u2="V" k="60" />
    <hkern g1="at.case" u2="X" k="70" />
    <hkern g1="at.case" u2="V" k="35" />
    <hkern g1="sterling.op" g2="nine.op" k="10" />
    <hkern g1="sterling.op" g2="three.op" k="20" />
    <hkern g1="sterling.op" g2="one.op" k="20" />
    <hkern g1="yen.op" g2="nine.op" k="10" />
    <hkern g1="yen.op" g2="four.op" k="20" />
    <hkern g1="florin.op" g2="nine.op" k="10" />
    <hkern g1="florin.op" g2="seven.op" k="30" />
    <hkern g1="florin.op" g2="five.op" k="30" />
    <hkern g1="florin.op" g2="four.op" k="34" />
    <hkern g1="florin.op" g2="three.op" k="20" />
    <hkern g1="florin.op" g2="two.op" k="20" />
    <hkern g1="florin.op" g2="one.op" k="30" />
    <hkern g1="one.op" g2="three.op" k="30" />
    <hkern g1="one.op" g2="one.op" k="30" />
    <hkern g1="two.op" g2="nine.op" k="6" />
    <hkern g1="two.op" g2="seven.op" k="18" />
    <hkern g1="two.op" g2="three.op" k="24" />
    <hkern g1="three.op" g2="nine.op" k="2" />
    <hkern g1="three.op" g2="one.op" k="20" />
    <hkern g1="three.op" g2="florin.op" k="20" />
    <hkern g1="three.op" g2="yen.op" k="20" />
    <hkern g1="four.op" g2="nine.op" k="10" />
    <hkern g1="four.op" g2="seven.op" k="22" />
    <hkern g1="four.op" g2="one.op" k="110" />
    <hkern g1="four.op" u2="&#xb0;" k="70" />
    <hkern g1="five.op" g2="nine.op" k="26" />
    <hkern g1="five.op" g2="seven.op" k="40" />
    <hkern g1="five.op" g2="three.op" k="24" />
    <hkern g1="five.op" g2="one.op" k="50" />
    <hkern g1="five.op" g2="florin.op" k="10" />
    <hkern g1="five.op" u2="&#xb0;" k="10" />
    <hkern g1="five.op" u2="&#x23;" k="-30" />
    <hkern g1="seven.op" g2="five.op" k="20" />
    <hkern g1="seven.op" g2="four.op" k="80" />
    <hkern g1="seven.op" g2="florin.op" k="70" />
    <hkern g1="seven.op" u2="&#xb0;" k="-20" />
    <hkern g1="seven.op" u2="&#x3e;" k="30" />
    <hkern g1="seven.op" u2="&#x3c;" k="70" />
    <hkern g1="nine.op" g2="nine.op" k="14" />
    <hkern g1="nine.op" g2="seven.op" k="50" />
    <hkern g1="nine.op" g2="five.op" k="8" />
    <hkern g1="nine.op" g2="three.op" k="18" />
    <hkern g1="nine.op" g2="one.op" k="20" />
    <hkern g1="nine.op" u2="&#xb0;" k="20" />
    <hkern g1="four.numr" u2="&#x2044;" k="-30" />
    <hkern g1="seven.numr" u2="&#x2044;" k="60" />
    <hkern g1="seven.op"
	g2="period,ellipsis"
	k="180" />
    <hkern g1="seven.op"
	g2="zeroslash.op,zero.op"
	k="10" />
    <hkern g1="seven.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="190" />
    <hkern g1="seven.op"
	g2="plus,divide,minus"
	k="90" />
    <hkern g1="seven.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="30" />
    <hkern g1="seven.op"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="30" />
    <hkern g1="F"
	g2="period,ellipsis"
	k="130" />
    <hkern g1="F"
	g2="AE,AEacute"
	k="110" />
    <hkern g1="F"
	g2="J,Jcircumflex"
	k="78" />
    <hkern g1="F"
	g2="comma,quotesinglbase,quotedblbase"
	k="130" />
    <hkern g1="F"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="F"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="F"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-10" />
    <hkern g1="F"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-16" />
    <hkern g1="F"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="F"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="F"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="12" />
    <hkern g1="F"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="F"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="12" />
    <hkern g1="F"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="36" />
    <hkern g1="F"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="10" />
    <hkern g1="F"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="12" />
    <hkern g1="F"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="X"
	k="100" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="V"
	k="60" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="88" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="P"
	g2="period,ellipsis"
	k="100" />
    <hkern g1="P"
	g2="AE,AEacute"
	k="120" />
    <hkern g1="P"
	g2="J,Jcircumflex"
	k="52" />
    <hkern g1="P"
	g2="guilsinglright.case,guillemotright.case"
	k="-10" />
    <hkern g1="P"
	g2="guillemotright,guilsinglright"
	k="-20" />
    <hkern g1="P"
	g2="comma,quotesinglbase,quotedblbase"
	k="170" />
    <hkern g1="P"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="P"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-30" />
    <hkern g1="P"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="P"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="34" />
    <hkern g1="P"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-4" />
    <hkern g1="P"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-40" />
    <hkern g1="P"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-16" />
    <hkern g1="P"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-8" />
    <hkern g1="P"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="-10" />
    <hkern g1="P"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="64" />
    <hkern g1="P"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-10" />
    <hkern g1="P"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="8" />
    <hkern g1="P"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-8" />
    <hkern g1="P"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-15" />
    <hkern g1="P"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-4" />
    <hkern g1="six,six.op"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="six,six.op"
	g2="zeroslash.op,zero.op"
	k="2" />
    <hkern g1="six,six.op"
	g2="degree"
	k="40" />
    <hkern g1="six,six.op"
	g2="three.op"
	k="60" />
    <hkern g1="six,six.op"
	g2="five.op"
	k="20" />
    <hkern g1="six,six.op"
	g2="nine.op"
	k="20" />
    <hkern g1="six,six.op"
	g2="two"
	k="20" />
    <hkern g1="six,six.op"
	g2="three"
	k="42" />
    <hkern g1="six,six.op"
	g2="seven"
	k="70" />
    <hkern g1="six,six.op"
	g2="one"
	k="64" />
    <hkern g1="six,six.op"
	g2="five"
	k="20" />
    <hkern g1="six,six.op"
	g2="quotedbl,quotesingle"
	k="70" />
    <hkern g1="six,six.op"
	g2="nine"
	k="40" />
    <hkern g1="six,six.op"
	g2="Euro"
	k="4" />
    <hkern g1="six,six.op"
	g2="florin"
	k="20" />
    <hkern g1="six,six.op"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="six,six.op"
	g2="yen"
	k="18" />
    <hkern g1="six,six.op"
	g2="seven.op"
	k="40" />
    <hkern g1="six,six.op"
	g2="one.op"
	k="50" />
    <hkern g1="six,six.op"
	g2="ordfeminine,ordmasculine"
	k="40" />
    <hkern g1="six,six.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="70" />
    <hkern g1="six,six.op"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="six,six.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-8" />
    <hkern g1="six,six.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="Thorn"
	g2="period,ellipsis"
	k="60" />
    <hkern g1="Thorn"
	g2="AE,AEacute"
	k="60" />
    <hkern g1="Thorn"
	g2="J,Jcircumflex"
	k="30" />
    <hkern g1="Thorn"
	g2="guilsinglright.case,guillemotright.case"
	k="-20" />
    <hkern g1="Thorn"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="Thorn"
	g2="guilsinglleft.case,guillemotleft.case"
	k="-30" />
    <hkern g1="Thorn"
	g2="comma,quotesinglbase,quotedblbase"
	k="100" />
    <hkern g1="Thorn"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="Thorn"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-60" />
    <hkern g1="Thorn"
	g2="hyphen,periodcentered,endash,emdash"
	k="-30" />
    <hkern g1="Thorn"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="30" />
    <hkern g1="Thorn"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="70" />
    <hkern g1="Thorn"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="Thorn"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-8" />
    <hkern g1="Thorn"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="Thorn"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-12" />
    <hkern g1="Thorn"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-8" />
    <hkern g1="Thorn"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="30" />
    <hkern g1="Thorn"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-10" />
    <hkern g1="Thorn"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-8" />
    <hkern g1="Thorn"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-15" />
    <hkern g1="Thorn"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-8" />
    <hkern g1="guillemotright,guilsinglright"
	g2="X"
	k="30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="V"
	k="70" />
    <hkern g1="guillemotright,guilsinglright"
	g2="v"
	k="14" />
    <hkern g1="guillemotright,guilsinglright"
	g2="x"
	k="44" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="100" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="106" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="guillemotright,guilsinglright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="8" />
    <hkern g1="two"
	g2="dollar,dollar.weight"
	k="10" />
    <hkern g1="two"
	g2="cent,cent.weight"
	k="30" />
    <hkern g1="two"
	g2="plus,divide,minus"
	k="60" />
    <hkern g1="two"
	g2="zero,six,zeroslash,six.op"
	k="10" />
    <hkern g1="two"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="10" />
    <hkern g1="V"
	g2="period,ellipsis"
	k="140" />
    <hkern g1="V"
	g2="AE,AEacute"
	k="90" />
    <hkern g1="V"
	g2="J,Jcircumflex"
	k="50" />
    <hkern g1="V"
	g2="guilsinglright.case,guillemotright.case"
	k="34" />
    <hkern g1="V"
	g2="guillemotright,guilsinglright"
	k="50" />
    <hkern g1="V"
	g2="guilsinglleft.case,guillemotleft.case"
	k="60" />
    <hkern g1="V"
	g2="guillemotleft,guilsinglleft"
	k="70" />
    <hkern g1="V"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="V"
	g2="comma,quotesinglbase,quotedblbase"
	k="160" />
    <hkern g1="V"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="V"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="80" />
    <hkern g1="V"
	g2="hyphen,periodcentered,endash,emdash"
	k="70" />
    <hkern g1="V"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-4" />
    <hkern g1="V"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-4" />
    <hkern g1="V"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-4" />
    <hkern g1="V"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="V"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="V"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="V"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="40" />
    <hkern g1="V"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="80" />
    <hkern g1="V"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="30" />
    <hkern g1="V"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="V"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="50" />
    <hkern g1="V"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="50" />
    <hkern g1="V"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="70" />
    <hkern g1="V"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="40" />
    <hkern g1="V"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="V"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="70" />
    <hkern g1="asterisk"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="asterisk"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="asterisk"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="8" />
    <hkern g1="asterisk"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="asterisk"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="66" />
    <hkern g1="asterisk"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="asterisk"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="three.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="three.op"
	g2="cent.op,cent.op.weight"
	k="20" />
    <hkern g1="three.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="three.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="uni2075"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2075"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="germandbls"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="germandbls"
	g2="quotedbl,quotesingle"
	k="8" />
    <hkern g1="germandbls"
	g2="comma,quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="germandbls"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="germandbls"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="germandbls"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="germandbls"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="62" />
    <hkern g1="germandbls"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="germandbls"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="germandbls"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="germandbls"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="2" />
    <hkern g1="germandbls"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="14" />
    <hkern g1="germandbls"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="germandbls"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="5" />
    <hkern g1="germandbls"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="5" />
    <hkern g1="dollar.op,dollar.op.weight"
	g2="nine.op"
	k="10" />
    <hkern g1="dollar.op,dollar.op.weight"
	g2="one.op"
	k="70" />
    <hkern g1="five.op"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="five.op"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="five.op"
	g2="cent.op,cent.op.weight"
	k="30" />
    <hkern g1="five.op"
	g2="ordfeminine,ordmasculine"
	k="30" />
    <hkern g1="five.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="five.op"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="five.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="five.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="30" />
    <hkern g1="dcaron,lcaron"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="dcaron,lcaron"
	g2="X"
	k="-120" />
    <hkern g1="dcaron,lcaron"
	g2="V"
	k="-150" />
    <hkern g1="dcaron,lcaron"
	g2="v"
	k="-30" />
    <hkern g1="dcaron,lcaron"
	g2="x"
	k="-20" />
    <hkern g1="dcaron,lcaron"
	g2="parenright"
	k="-110" />
    <hkern g1="dcaron,lcaron"
	g2="germandbls"
	k="-30" />
    <hkern g1="dcaron,lcaron"
	g2="asterisk"
	k="-92" />
    <hkern g1="dcaron,lcaron"
	g2="bracketright,braceright"
	k="-110" />
    <hkern g1="dcaron,lcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-100" />
    <hkern g1="dcaron,lcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-120" />
    <hkern g1="dcaron,lcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-120" />
    <hkern g1="dcaron,lcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-140" />
    <hkern g1="dcaron,lcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-20" />
    <hkern g1="dcaron,lcaron"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-50" />
    <hkern g1="dcaron,lcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-50" />
    <hkern g1="dcaron,lcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-30" />
    <hkern g1="dcaron,lcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="8" />
    <hkern g1="dcaron,lcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="-40" />
    <hkern g1="dcaron,lcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="dcaron,lcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-90" />
    <hkern g1="dcaron,lcaron"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-40" />
    <hkern g1="dcaron,lcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-40" />
    <hkern g1="dcaron,lcaron"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="-80" />
    <hkern g1="dcaron,lcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="24" />
    <hkern g1="dcaron,lcaron"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,uni2116"
	k="-80" />
    <hkern g1="four"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="four"
	g2="cent,cent.weight"
	k="8" />
    <hkern g1="four"
	g2="ordfeminine,ordmasculine"
	k="40" />
    <hkern g1="four"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="one"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="one"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="one"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="one"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="20" />
    <hkern g1="seven"
	g2="period,ellipsis"
	k="160" />
    <hkern g1="seven"
	g2="dollar,dollar.weight"
	k="30" />
    <hkern g1="seven"
	g2="cent,cent.weight"
	k="30" />
    <hkern g1="seven"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="seven"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="seven"
	g2="comma,quotesinglbase,quotedblbase"
	k="180" />
    <hkern g1="seven"
	g2="plus,divide,minus"
	k="160" />
    <hkern g1="seven"
	g2="zero,six,zeroslash,six.op"
	k="6" />
    <hkern g1="seven"
	g2="hyphen,periodcentered,endash,emdash"
	k="60" />
    <hkern g1="seven"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="100" />
    <hkern g1="five"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="five"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="five"
	g2="ordfeminine,ordmasculine"
	k="30" />
    <hkern g1="five"
	g2="comma,quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="five"
	g2="hyphen,periodcentered,endash,emdash"
	k="-8" />
    <hkern g1="onesuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="onesuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="two"
	k="20" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="seven"
	k="-10" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="four"
	k="80" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="four.op"
	k="110" />
    <hkern g1="nine.op"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="nine.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="nine.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="nine.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="B"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="B"
	g2="AE,AEacute"
	k="10" />
    <hkern g1="B"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="B"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="B"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="44" />
    <hkern g1="B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="B"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="B"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="12" />
    <hkern g1="B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="K,uni0136"
	g2="guilsinglright.case,guillemotright.case"
	k="90" />
    <hkern g1="K,uni0136"
	g2="degree"
	k="18" />
    <hkern g1="K,uni0136"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="K,uni0136"
	g2="guilsinglleft.case,guillemotleft.case"
	k="134" />
    <hkern g1="K,uni0136"
	g2="v"
	k="100" />
    <hkern g1="K,uni0136"
	g2="guillemotleft,guilsinglleft"
	k="50" />
    <hkern g1="K,uni0136"
	g2="germandbls"
	k="20" />
    <hkern g1="K,uni0136"
	g2="asterisk"
	k="24" />
    <hkern g1="K,uni0136"
	g2="at.case"
	k="90" />
    <hkern g1="K,uni0136"
	g2="bullet"
	k="100" />
    <hkern g1="K,uni0136"
	g2="bullet.case"
	k="120" />
    <hkern g1="K,uni0136"
	g2="copyright"
	k="70" />
    <hkern g1="K,uni0136"
	g2="registered"
	k="30" />
    <hkern g1="K,uni0136"
	g2="at"
	k="20" />
    <hkern g1="K,uni0136"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="140" />
    <hkern g1="K,uni0136"
	g2="hyphen,periodcentered,endash,emdash"
	k="110" />
    <hkern g1="K,uni0136"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="K,uni0136"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="70" />
    <hkern g1="K,uni0136"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="52" />
    <hkern g1="K,uni0136"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="60" />
    <hkern g1="K,uni0136"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="100" />
    <hkern g1="K,uni0136"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="K,uni0136"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="34" />
    <hkern g1="K,uni0136"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="75" />
    <hkern g1="K,uni0136"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="40" />
    <hkern g1="K,uni0136"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="60" />
    <hkern g1="K,uni0136"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="50" />
    <hkern g1="K,uni0136"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="74" />
    <hkern g1="K,uni0136"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="20" />
    <hkern g1="K,uni0136"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="70" />
    <hkern g1="threesuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="threesuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="three"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="three"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="three"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="three"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="X"
	g2="AE,AEacute"
	k="-4" />
    <hkern g1="X"
	g2="guilsinglright.case,guillemotright.case"
	k="70" />
    <hkern g1="X"
	g2="guilsinglleft.case,guillemotleft.case"
	k="100" />
    <hkern g1="X"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="X"
	g2="z,zacute,zdotaccent,zcaron"
	k="12" />
    <hkern g1="X"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="90" />
    <hkern g1="X"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="X"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="12" />
    <hkern g1="X"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="X"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="58" />
    <hkern g1="X"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="X"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="50" />
    <hkern g1="X"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="X"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-4" />
    <hkern g1="X"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="X"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="X"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="30" />
    <hkern g1="X"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="X"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="50" />
    <hkern g1="X"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="40" />
    <hkern g1="uni2077"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2077"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="twosuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="twosuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="V"
	k="50" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="106" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="4" />
    <hkern g1="eth"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="eth"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="eth"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="eth"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="eth"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="eth"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="eth"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="30" />
    <hkern g1="eth"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="56" />
    <hkern g1="eth"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="22" />
    <hkern g1="eth"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="eth"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="eth"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="eth"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="5" />
    <hkern g1="eth"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="12" />
    <hkern g1="eth"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="eth"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-2" />
    <hkern g1="eth"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="2" />
    <hkern g1="two.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="two.op"
	g2="plus,divide,minus"
	k="30" />
    <hkern g1="period,ellipsis"
	g2="zeroslash.op,zero.op"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="V"
	k="140" />
    <hkern g1="period,ellipsis"
	g2="three.op"
	k="10" />
    <hkern g1="period,ellipsis"
	g2="five.op"
	k="30" />
    <hkern g1="period,ellipsis"
	g2="nine.op"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="seven"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="one"
	k="208" />
    <hkern g1="period,ellipsis"
	g2="quotedbl,quotesingle"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="v"
	k="60" />
    <hkern g1="period,ellipsis"
	g2="seven.op"
	k="120" />
    <hkern g1="period,ellipsis"
	g2="one.op"
	k="210" />
    <hkern g1="period,ellipsis"
	g2="asterisk"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="four"
	k="10" />
    <hkern g1="period,ellipsis"
	g2="eight,eight.op"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="period,ellipsis"
	g2="zero,six,zeroslash,six.op"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="150" />
    <hkern g1="period,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="180" />
    <hkern g1="period,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="period,ellipsis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="50" />
    <hkern g1="period,ellipsis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="colon,semicolon"
	g2="V"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="seven"
	k="20" />
    <hkern g1="colon,semicolon"
	g2="eight,eight.op"
	k="4" />
    <hkern g1="colon,semicolon"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="100" />
    <hkern g1="colon,semicolon"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="quotedbl,quotesingle"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="quotedbl,quotesingle"
	g2="zeroslash.op,zero.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="three.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="five.op"
	k="30" />
    <hkern g1="quotedbl,quotesingle"
	g2="nine.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="two"
	k="30" />
    <hkern g1="quotedbl,quotesingle"
	g2="three"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="x"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="four"
	k="70" />
    <hkern g1="quotedbl,quotesingle"
	g2="four.op"
	k="130" />
    <hkern g1="quotedbl,quotesingle"
	g2="slash"
	k="180" />
    <hkern g1="quotedbl,quotesingle"
	g2="two.op"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="quotedbl,quotesingle"
	g2="eight,eight.op"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="60" />
    <hkern g1="quotedbl,quotesingle"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="18" />
    <hkern g1="quotedbl,quotesingle"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="x"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="x"
	g2="guillemotleft,guilsinglleft"
	k="44" />
    <hkern g1="x"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-4" />
    <hkern g1="x"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="x"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="x"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="98" />
    <hkern g1="x"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="x"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="10" />
    <hkern g1="x"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-2" />
    <hkern g1="x"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="5" />
    <hkern g1="x"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-8" />
    <hkern g1="x"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="4" />
    <hkern g1="x"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="x"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="x"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="15" />
    <hkern g1="x"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="25" />
    <hkern g1="slash"
	g2="period,ellipsis"
	k="130" />
    <hkern g1="slash"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="slash"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="slash"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="slash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="slash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="slash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="slash"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="slash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="slash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="slash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="slash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="24" />
    <hkern g1="slash"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="eight,eight.op"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="eight,eight.op"
	g2="three.op"
	k="18" />
    <hkern g1="eight,eight.op"
	g2="nine.op"
	k="2" />
    <hkern g1="eight,eight.op"
	g2="one"
	k="10" />
    <hkern g1="eight,eight.op"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="eight,eight.op"
	g2="colon,semicolon"
	k="4" />
    <hkern g1="eight,eight.op"
	g2="cent.op,cent.op.weight"
	k="4" />
    <hkern g1="eight,eight.op"
	g2="numbersign"
	k="-10" />
    <hkern g1="eight,eight.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="eight,eight.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="cent.op,cent.op.weight"
	g2="three.op"
	k="20" />
    <hkern g1="cent.op,cent.op.weight"
	g2="one.op"
	k="10" />
    <hkern g1="v"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="v"
	g2="guillemotleft,guilsinglleft"
	k="14" />
    <hkern g1="v"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="v"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="v"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="v"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="v"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="86" />
    <hkern g1="v"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="v"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-2" />
    <hkern g1="v"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-4" />
    <hkern g1="v"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-2" />
    <hkern g1="v"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-8" />
    <hkern g1="v"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="v"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="v"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="15" />
    <hkern g1="v"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-2" />
    <hkern g1="v"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="15" />
    <hkern g1="cent,cent.weight"
	g2="seven"
	k="10" />
    <hkern g1="cent,cent.weight"
	g2="one"
	k="4" />
    <hkern g1="uni2074"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2074"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="zeroslash.op,zero.op"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="zeroslash.op,zero.op"
	g2="three.op"
	k="40" />
    <hkern g1="zeroslash.op,zero.op"
	g2="five.op"
	k="8" />
    <hkern g1="zeroslash.op,zero.op"
	g2="nine.op"
	k="2" />
    <hkern g1="zeroslash.op,zero.op"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="zeroslash.op,zero.op"
	g2="seven.op"
	k="20" />
    <hkern g1="zeroslash.op,zero.op"
	g2="one.op"
	k="4" />
    <hkern g1="zeroslash.op,zero.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="70" />
    <hkern g1="zeroslash.op,zero.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="X"
	k="70" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="V"
	k="40" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="76" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="uni2076"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2076"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="four.op"
	g2="quotedbl,quotesingle"
	k="60" />
    <hkern g1="four.op"
	g2="ordfeminine,ordmasculine"
	k="50" />
    <hkern g1="four.op"
	g2="plus,divide,minus"
	k="40" />
    <hkern g1="four.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="four.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="uni2078"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2078"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="plus,divide,minus"
	g2="three.op"
	k="50" />
    <hkern g1="plus,divide,minus"
	g2="five.op"
	k="30" />
    <hkern g1="plus,divide,minus"
	g2="two"
	k="20" />
    <hkern g1="plus,divide,minus"
	g2="three"
	k="50" />
    <hkern g1="plus,divide,minus"
	g2="seven"
	k="110" />
    <hkern g1="plus,divide,minus"
	g2="one"
	k="156" />
    <hkern g1="plus,divide,minus"
	g2="seven.op"
	k="140" />
    <hkern g1="plus,divide,minus"
	g2="four.op"
	k="30" />
    <hkern g1="plus,divide,minus"
	g2="zero,six,zeroslash,six.op"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="zero,nine,zeroslash"
	g2="two"
	k="10" />
    <hkern g1="zero,nine,zeroslash"
	g2="three"
	k="26" />
    <hkern g1="zero,nine,zeroslash"
	g2="seven"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="one"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="zero,nine,zeroslash"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="hyphen,periodcentered,endash,emdash"
	k="-8" />
    <hkern g1="zero,nine,zeroslash"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-8" />
    <hkern g1="zero,nine,zeroslash"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="20" />
    <hkern g1="f,longs,f_f"
	g2="period,ellipsis"
	k="100" />
    <hkern g1="f,longs,f_f"
	g2="V"
	k="-22" />
    <hkern g1="f,longs,f_f"
	g2="degree"
	k="-36" />
    <hkern g1="f,longs,f_f"
	g2="quotedbl,quotesingle"
	k="-28" />
    <hkern g1="f,longs,f_f"
	g2="v"
	k="-30" />
    <hkern g1="f,longs,f_f"
	g2="x"
	k="-10" />
    <hkern g1="f,longs,f_f"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="f,longs,f_f"
	g2="asterisk"
	k="-50" />
    <hkern g1="f,longs,f_f"
	g2="bullet"
	k="20" />
    <hkern g1="f,longs,f_f"
	g2="registered"
	k="-44" />
    <hkern g1="f,longs,f_f"
	g2="slash"
	k="58" />
    <hkern g1="f,longs,f_f"
	g2="numbersign"
	k="8" />
    <hkern g1="f,longs,f_f"
	g2="comma,quotesinglbase,quotedblbase"
	k="100" />
    <hkern g1="f,longs,f_f"
	g2="backslash"
	k="-66" />
    <hkern g1="f,longs,f_f"
	g2="question"
	k="-30" />
    <hkern g1="f,longs,f_f"
	g2="trademark"
	k="-36" />
    <hkern g1="f,longs,f_f"
	g2="questiondown"
	k="36" />
    <hkern g1="f,longs,f_f"
	g2="z,zacute,zdotaccent,zcaron"
	k="-2" />
    <hkern g1="f,longs,f_f"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-20" />
    <hkern g1="f,longs,f_f"
	g2="hyphen,periodcentered,endash,emdash"
	k="20" />
    <hkern g1="f,longs,f_f"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-50" />
    <hkern g1="f,longs,f_f"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-36" />
    <hkern g1="f,longs,f_f"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-16" />
    <hkern g1="f,longs,f_f"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-6" />
    <hkern g1="f,longs,f_f"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-50" />
    <hkern g1="f,longs,f_f"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-22" />
    <hkern g1="f,longs,f_f"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-12" />
    <hkern g1="f,longs,f_f"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="f,longs,f_f"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-8" />
    <hkern g1="f,longs,f_f"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="f,longs,f_f"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-16" />
    <hkern g1="f,longs,f_f"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-14" />
    <hkern g1="uni2070,uni2079"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2070,uni2079"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="X"
	k="8" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="V"
	k="120" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="three.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="five.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="nine.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="seven"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="one"
	k="154" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="quotedbl,quotesingle"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="v"
	k="40" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="seven.op"
	k="90" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="one.op"
	k="160" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="asterisk"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="zero,six,zeroslash,six.op"
	k="10" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="130" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="180" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="70" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="60" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="30" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="J,Jcircumflex"
	k="14" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="X"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="V"
	k="24" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="bullet"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="bullet.case"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="registered"
	k="14" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="trademark"
	k="40" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="42" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="2" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="11" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-5" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="zeroslash.op,zero.op"
	k="70" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="X"
	k="6" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="V"
	k="-8" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="three.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="five.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="nine.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="two"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="v"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="one.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="four"
	k="110" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="four.op"
	k="160" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="slash"
	k="150" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="two.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="eight,eight.op"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="z,zacute,zdotaccent,zcaron"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="zero,six,zeroslash,six.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="12" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="70" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="period,ellipsis"
	k="130" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="X"
	k="54" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="V"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="degree"
	k="-20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="v"
	k="-4" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="germandbls"
	k="4" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="asterisk"
	k="-20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="bullet"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="registered"
	k="-10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="slash"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="140" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="trademark"
	k="40" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="questiondown"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="24" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="104" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="35" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-8" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-4" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="15" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-8" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-2" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="15" />
    <hkern g1="Euro"
	g2="zero,six,zeroslash,six.op"
	k="10" />
    <hkern g1="questiondown"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="questiondown"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="140" />
    <hkern g1="questiondown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="170" />
    <hkern g1="questiondown"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="110" />
    <hkern g1="questiondown"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="58" />
    <hkern g1="questiondown"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="questiondown"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="34" />
    <hkern g1="questiondown"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="questiondown"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="30" />
    <hkern g1="questiondown"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="64" />
    <hkern g1="questiondown"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="40" />
    <hkern g1="questiondown"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="54" />
    <hkern g1="questiondown"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="60" />
    <hkern g1="questiondown"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="60" />
    <hkern g1="questiondown"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="50" />
    <hkern g1="questiondown"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="50" />
    <hkern g1="questiondown"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,uni2116"
	k="54" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="X"
	k="90" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="V"
	k="80" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="100" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="150" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-10" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="V"
	k="60" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="bullet"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="numbersign"
	k="-8" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="backslash"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="question"
	k="18" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="trademark"
	k="50" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-4" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="130" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="2" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="zeroslash.op,zero.op"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="X"
	k="50" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="V"
	k="70" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="three.op"
	k="60" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="nine.op"
	k="-10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="seven"
	k="70" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="one"
	k="152" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="v"
	k="10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="x"
	k="50" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="seven.op"
	k="100" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="one.op"
	k="20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="four"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="eight,eight.op"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="zero,six,zeroslash,six.op"
	k="-12" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="90" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="130" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-4" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-8" />
    <hkern g1="bullet.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="60" />
    <hkern g1="bullet.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="90" />
    <hkern g1="bullet.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="100" />
    <hkern g1="bullet.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="bullet.case"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="bullet.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="period,ellipsis"
	k="-8" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="J,Jcircumflex"
	k="-8" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="v"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="x"
	k="-4" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="bullet.case"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="numbersign"
	k="-20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="question"
	k="-24" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="-4" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="60" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-4" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="-4" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="numbersign"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="numbersign"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-10" />
    <hkern g1="numbersign"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-16" />
    <hkern g1="numbersign"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-8" />
    <hkern g1="numbersign"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="40" />
    <hkern g1="numbersign"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="86" />
    <hkern g1="numbersign"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="numbersign"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="40" />
    <hkern g1="questiondown.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="questiondown.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="questiondown.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="70" />
    <hkern g1="questiondown.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="questiondown.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-20" />
    <hkern g1="questiondown.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="questiondown.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="degree"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-10" />
    <hkern g1="degree"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-30" />
    <hkern g1="degree"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-10" />
    <hkern g1="degree"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-20" />
    <hkern g1="degree"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="degree"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="90" />
    <hkern g1="degree"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="degree"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="50" />
    <hkern g1="registered"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="registered"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="registered"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-4" />
    <hkern g1="registered"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="registered"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="90" />
    <hkern g1="registered"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="40" />
    <hkern g1="copyright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="copyright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="64" />
    <hkern g1="copyright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="copyright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="AE,AEacute"
	k="150" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="J,Jcircumflex"
	k="68" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="X"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="V"
	k="-4" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guillemotright,guilsinglright"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="degree"
	k="-30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guilsinglleft.case,guillemotleft.case"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="v"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="x"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guillemotleft,guilsinglleft"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="germandbls"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="at.case"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bullet"
	k="130" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bullet.case"
	k="90" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="copyright"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="registered"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="at"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="slash"
	k="140" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="numbersign"
	k="70" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bracketright,braceright"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="backslash"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="question"
	k="-50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="trademark"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="questiondown"
	k="160" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="z,zacute,zdotaccent,zcaron"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-4" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="hyphen,periodcentered,endash,emdash"
	k="90" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-12" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="exclamdown"
	k="70" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="questiondown.case"
	k="36" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-4" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="40" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="42" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="90" />
    <hkern g1="backslash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="140" />
    <hkern g1="backslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="114" />
    <hkern g1="backslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="60" />
    <hkern g1="backslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="34" />
    <hkern g1="backslash"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="40" />
    <hkern g1="backslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="28" />
    <hkern g1="backslash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="backslash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="backslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="backslash"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="40" />
    <hkern g1="backslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="64" />
    <hkern g1="backslash"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="AE,AEacute"
	k="90" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex"
	k="28" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="V"
	k="-4" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guilsinglright.case,guillemotright.case"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotright,guilsinglright"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guilsinglleft.case,guillemotleft.case"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="colon,semicolon"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="germandbls"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="at.case"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet.case"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="copyright"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="registered"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="at"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="slash"
	k="60" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="numbersign"
	k="60" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="90" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="trademark"
	k="-20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="questiondown"
	k="86" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,periodcentered,endash,emdash"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-4" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="exclamdown"
	k="24" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="45" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="45" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="X"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="V"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="degree"
	k="-10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="v"
	k="-2" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="bullet"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="slash"
	k="34" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="backslash"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="question"
	k="6" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="questiondown"
	k="12" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="50" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="80" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-2" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-4" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="5" />
    <hkern g1="bullet"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="130" />
    <hkern g1="bullet"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="150" />
    <hkern g1="bullet"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="bullet"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="bullet"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="bullet"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="bullet"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="exclamdown"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="70" />
    <hkern g1="exclamdown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="104" />
    <hkern g1="exclamdown"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="24" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="period,ellipsis"
	k="180" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="AE,AEacute"
	k="166" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J,Jcircumflex"
	k="54" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="V"
	k="-4" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guilsinglright.case,guillemotright.case"
	k="76" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotright,guilsinglright"
	k="106" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guilsinglleft.case,guillemotleft.case"
	k="88" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="v"
	k="86" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="x"
	k="98" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotleft,guilsinglleft"
	k="106" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="colon,semicolon"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="germandbls"
	k="54" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="asterisk"
	k="8" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at.case"
	k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="bullet"
	k="150" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="bullet.case"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="copyright"
	k="64" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at"
	k="110" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="slash"
	k="114" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="numbersign"
	k="110" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="180" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="questiondown"
	k="220" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="114" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="150" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="130" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-8" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="exclamdown"
	k="104" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="questiondown.case"
	k="24" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="72" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="92" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="128" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="48" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="108" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="116" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="116" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="124" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="66" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="52" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="8" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="136" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="period,ellipsis"
	k="-4" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="V"
	k="50" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="bullet"
	k="22" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="numbersign"
	k="-8" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="backslash"
	k="20" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="question"
	k="12" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="40" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="110" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-2" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="-2" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-4" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="2" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="X"
	k="50" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="V"
	k="40" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="degree"
	k="-20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="v"
	k="-8" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="x"
	k="-2" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="guillemotleft,guilsinglleft"
	k="8" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="bullet"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="at"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="slash"
	k="58" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="numbersign"
	k="18" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="100" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="backslash"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="trademark"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="questiondown"
	k="42" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="92" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-4" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-2" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-4" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="25" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-2" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="15" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="period,ellipsis"
	k="50" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="J,Jcircumflex"
	k="8" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="X"
	k="40" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="V"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="guillemotright,guilsinglright"
	k="-10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="x"
	k="2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="asterisk"
	k="-20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="at.case"
	k="-10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="slash"
	k="50" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="backslash"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="trademark"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="questiondown"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="64" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-9" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-4" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="V"
	k="60" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="guillemotright,guilsinglright"
	k="4" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="x"
	k="-4" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="bullet"
	k="50" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="copyright"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="backslash"
	k="40" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="trademark"
	k="70" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="hyphen,periodcentered,endash,emdash"
	k="70" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="54" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="102" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-4" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="25" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="40" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="15" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="25" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="40" />
    <hkern g1="at.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="at.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="80" />
    <hkern g1="at.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="at.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="30" />
    <hkern g1="at.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="at"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="at"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="110" />
    <hkern g1="at"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="at"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="period,ellipsis"
	k="-20" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="AE,AEacute"
	k="-16" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="J,Jcircumflex"
	k="-30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="X"
	k="-12" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="V"
	k="96" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="degree"
	k="130" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="quotedbl,quotesingle"
	k="170" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="guilsinglleft.case,guillemotleft.case"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="v"
	k="50" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="asterisk"
	k="170" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="at.case"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="bullet"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="bullet.case"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="registered"
	k="150" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="numbersign"
	k="-40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="comma,quotesinglbase,quotedblbase"
	k="-30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="backslash"
	k="118" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="question"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="trademark"
	k="180" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="z,zacute,zdotaccent,zcaron"
	k="-10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-12" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="100" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="hyphen,periodcentered,endash,emdash"
	k="-30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="170" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="144" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="142" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="45" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="-8" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-8" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="26" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="42" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="X"
	k="18" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="V"
	k="70" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="degree"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="v"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="x"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="germandbls"
	k="2" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="backslash"
	k="30" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="trademark"
	k="60" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="90" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="128" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="4" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="4" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="4" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="three.op"
	k="30" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="seven"
	k="130" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="one"
	k="110" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="seven.op"
	k="50" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="zero,six,zeroslash,six.op"
	k="20" />
    <hkern g1="bracketleft,braceleft"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="X"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="V"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="v"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="x"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="asterisk"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="bullet.case"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="registered"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="slash"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="backslash"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="trademark"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="16" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="50" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="2" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="X"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="V"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="degree"
	k="-20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="guillemotleft,guilsinglleft"
	k="-10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="slash"
	k="54" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="backslash"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="trademark"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="hyphen,periodcentered,endash,emdash"
	k="-40" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="12" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="40" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-4" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="6" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="V"
	k="60" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="registered"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="numbersign"
	k="-20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="trademark"
	k="20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="z,zacute,zdotaccent,zcaron"
	k="-2" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="30" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="104" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="-2" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-2" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="AE,AEacute"
	k="-12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="J,Jcircumflex"
	k="-4" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="X"
	k="-4" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="V"
	k="80" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guilsinglright.case,guillemotright.case"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="degree"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="quotedbl,quotesingle"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guilsinglleft.case,guillemotleft.case"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="v"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="x"
	k="-8" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="asterisk"
	k="66" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="at.case"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="bullet"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="bullet.case"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="copyright"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="registered"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="numbersign"
	k="-8" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="backslash"
	k="80" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="question"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="trademark"
	k="100" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="100" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="108" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="45" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-8" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="6" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="f,longs,f_f,f_f_i,f_f_l,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="trademark"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="question"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="74" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="X"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="V"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="degree"
	k="30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="v"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="x"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="asterisk"
	k="30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="registered"
	k="34" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="numbersign"
	k="-30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="backslash"
	k="50" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="trademark"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="136" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-2" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-4" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="AE,AEacute"
	k="-20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="J,Jcircumflex"
	k="-10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="v"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="x"
	k="-2" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="-8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute"
	k="12" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="period,ellipsis"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="J,Jcircumflex"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="X"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="x"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="slash"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="hyphen,periodcentered,endash,emdash"
	k="-4" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="2" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="2" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="16" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="2" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="period,ellipsis"
	k="50" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="J,Jcircumflex"
	k="22" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="X"
	k="40" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="V"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="guillemotright,guilsinglright"
	k="4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="degree"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="x"
	k="15" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="asterisk"
	k="-10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="at.case"
	k="-10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="slash"
	k="64" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="72" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="backslash"
	k="24" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="trademark"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="2" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="42" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="52" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-2" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="5" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-15" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-2" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="X"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="V"
	k="70" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="degree"
	k="50" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="v"
	k="15" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="x"
	k="25" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="asterisk"
	k="30" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="registered"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="numbersign"
	k="-20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="comma,quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="trademark"
	k="50" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="hyphen,periodcentered,endash,emdash"
	k="-8" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="90" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="136" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="2" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="2" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-2" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="c,d,e,g,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,oslashacute,ccedilla.1"
	k="-2" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="V"
	k="40" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="trademark"
	k="30" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-4" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="116" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni0237"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="V"
	k="55" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="degree"
	k="30" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="v"
	k="10" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="asterisk"
	k="20" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="registered"
	k="50" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="backslash"
	k="40" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="trademark"
	k="80" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-4" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="85" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="116" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,aringacute,f_h,f_f_h"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="d,i,j,l,igrave,iacute,icircumflex,idieresis,dcroat,itilde,imacron,ibreve,iogonek,ij,jcircumflex,lacute,uni013C,lslash,f_f_i,f_f_l,i.trk,f_j,fl,f_f_j"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="8" />
  </font>
</defs></svg>

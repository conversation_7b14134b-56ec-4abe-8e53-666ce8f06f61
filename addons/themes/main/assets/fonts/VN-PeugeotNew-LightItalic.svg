<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Wed Jun 23 16:34:08 2021
 By <PERSON>eksey,,,
Copyright (c) 2019 by Peugeot. All rights reserved.
</metadata>
<defs>
<font id="VN-PeugeotNew-LightItalic" horiz-adv-x="760" >
  <font-face 
    font-family="VN-Peugeot New"
    font-weight="300"
    font-style="italic"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 0 0 0 0 0 0 0 0"
    ascent="802"
    descent="-198"
    x-height="550"
    cap-height="802"
    bbox="-253 -266 1553 1243"
    underline-thickness="52"
    underline-position="-224"
    slope="-12"
    unicode-range="U+0020-FEFF"
  />
<missing-glyph horiz-adv-x="584" 
d="M109 802h584l-212 -1000h-584zM302 195l33 85q54 0 91.5 10t62.5 28t39 43.5t21 56.5q5 23 5 43q0 58 -47.5 96t-190.5 38q-45 0 -96 -5.5t-91 -13.5l-26 -124q48 9 95.5 14.5t89.5 5.5q40 0 63.5 -2t35 -7.5t13 -12t1.5 -10.5q0 -7 -2 -15q-3 -12 -7 -21t-13.5 -14
t-25.5 -7.5t-42 -2.5h-109l-15 -70l-5 -115h120zM278 0l33 153h-154l-33 -153h154z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="693" 
d="M63 0l106 502h-61l7 36l64 12l16 76q11 50 27.5 86.5t43 60t63.5 34.5t89 11q39 0 79 -6l-10 -49q-17 2 -34 3.5t-34 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-16 -78h266l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q49 0 98 -6l-10 -49q-21 2 -42 3.5t-44 1.5
q-38 0 -65.5 -7.5t-47.5 -24t-33 -43t-21 -65.5l-16 -78h202l-10 -48h-202l-106 -502h-57l106 502h-266l-106 -502h-57z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="921" 
d="M59 0l107 502h-61l7 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q40 0 80 -6l-10 -49q-17 2 -34 3.5t-35 1.5q-37 0 -65 -7.5t-47.5 -24t-32.5 -43t-21 -65.5l-17 -78h266l16 76q11 50 27 86.5t41.5 60t62 34.5t87.5 11q33 0 61 -6l-10 -49q-12 2 -24 3.5
t-26 1.5q-36 0 -63 -7.5t-46 -24t-32 -43t-21 -65.5l-17 -78h324l-117 -550h-57l107 502h-267l-106 -502h-56l106 502h-266l-106 -502h-57zM867 782h64l-14 -69h-64z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="933" 
d="M59 0l107 502h-60l6 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q40 0 80 -6l-10 -49q-17 2 -34 3.5t-34 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-17 -78h267l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q40 0 77 -6l-10 -49q-16 2 -32 3.5t-33 1.5
q-38 0 -65.5 -7.5t-47.5 -24t-33 -43t-21 -65.5l-17 -78h183l-10 -48h-183l-106 -502h-57l107 502h-267l-106 -502h-57zM886 802h57l-170 -802h-57z" />
    <glyph glyph-name="f_b" unicode="fb" horiz-adv-x="1047" 
d="M59 0l107 502h-61l7 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q39 0 78 -6l-10 -49q-17 2 -33 3.5t-33 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-17 -78h184l-11 -48h-183l-106 -502h-57zM563 802h57l-60 -285q44 19 96.5 34t103.5 15
q66 0 114.5 -17t77.5 -54t34 -74.5t5 -56.5q0 -46 -13 -105q-31 -145 -105.5 -210t-205.5 -65q-58 0 -113 16.5t-100 37.5l-19 -38h-42zM748 514q-56 0 -108 -15.5t-91 -33.5l-81 -379q37 -20 84.5 -35t107.5 -15q56 0 99 13t74.5 41t53 72t34.5 105q11 51 11 91
q0 13 -3 42.5t-25.5 58.5t-61.5 42t-94 13z" />
    <glyph glyph-name="f_h" unicode="fh" horiz-adv-x="1067" 
d="M59 0l107 502h-61l7 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q39 0 78 -6l-10 -49q-17 2 -33 3.5t-33 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-17 -78h184l-11 -48h-183l-106 -502h-57zM563 802h57l-61 -289q50 22 111.5 37.5t122.5 15.5
q120 0 161 -45.5t41 -102.5q0 -24 -6 -51l-78 -367h-57l77 367q5 22 5 40q0 41 -28 73.5t-127 32.5q-66 0 -125.5 -16.5t-108.5 -37.5l-97 -459h-57z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="599" 
d="M59 0l107 502h-60l6 36l64 12l16 76q11 50 27 86.5t41.5 60t62 34.5t87.5 11q33 0 61 -6l-10 -49q-13 2 -24.5 3.5t-25.5 1.5q-36 0 -63 -7.5t-46 -24t-32 -43t-21 -65.5l-17 -78h324l-118 -550h-56l107 502h-267l-106 -502h-57zM544 782h65l-14 -69h-66z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="599" 
d="M59 0l107 502h-61l7 36l64 12l16 76q11 50 27 86.5t41.5 60t62 34.5t87.5 11q33 0 61 -6l-10 -49q-13 2 -24.5 3.5t-25.5 1.5q-36 0 -63 -7.5t-46 -24t-32 -43t-21 -65.5l-17 -78h323l-125 -592q-10 -46 -25 -78.5t-36.5 -53.5t-52 -30.5t-72.5 -9.5q-20 0 -44.5 1
t-41.5 4l11 50q14 -2 35.5 -3t35.5 -1q57 0 88 26.5t45 94.5l115 544h-266l-106 -502h-57zM543 782h66l-14 -69h-66z" />
    <glyph glyph-name="f_k" unicode="fk" horiz-adv-x="975" 
d="M59 0l107 502h-61l7 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q39 0 78 -6l-9 -49q-17 2 -33.5 3.5t-33.5 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-17 -78h184l-11 -48h-183l-106 -502h-57zM563 802h57l-100 -474l374 222h86l-398 -236l295 -314
h-74l-287 311l-66 -311h-57z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="610" 
d="M59 0l107 502h-61l7 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q39 0 78 -6l-10 -49q-17 2 -33 3.5t-33 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-17 -78h184l-11 -48h-183l-106 -502h-57zM564 802h57l-171 -802h-57z" />
    <glyph glyph-name="f_f_b" unicode="ffb" horiz-adv-x="1371" 
d="M59 0l107 502h-60l6 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q40 0 80 -6l-9 -49q-17 2 -34 3.5t-35 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-17 -78h267l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q40 0 77 -6l-9 -49q-17 2 -33 3.5t-33 1.5
q-38 0 -65.5 -7.5t-47.5 -24t-33 -43t-21 -65.5l-17 -78h183l-10 -48h-183l-106 -502h-57l107 502h-267l-106 -502h-57zM886 802h57l-60 -285q43 19 96 34t104 15q66 0 114.5 -17t77.5 -54t33.5 -74.5t4.5 -56.5q0 -46 -12 -105q-15 -72 -41 -124t-64.5 -85.5t-89.5 -49.5
t-117 -16q-57 0 -112.5 16.5t-99.5 37.5l-19 -38h-42zM1071 514q-57 0 -109 -15.5t-90 -33.5l-81 -379q37 -20 84.5 -35t107.5 -15q111 0 173 54t87 177q11 51 11 90q0 14 -3 43.5t-25 58.5t-61.5 42t-93.5 13z" />
    <glyph glyph-name="f_f_h" unicode="ffh" horiz-adv-x="1391" 
d="M59 0l107 502h-60l6 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q40 0 80 -6l-10 -49q-17 2 -34 3.5t-34 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-17 -78h267l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q40 0 77 -6l-10 -49q-16 2 -32 3.5t-33 1.5
q-38 0 -65.5 -7.5t-47.5 -24t-33 -43t-21 -65.5l-17 -78h183l-10 -48h-183l-106 -502h-57l107 502h-267l-106 -502h-57zM886 802h57l-61 -289q50 22 111 37.5t122 15.5q121 0 161.5 -45.5t40.5 -102.5q0 -24 -6 -51l-78 -367h-57l78 367q5 21 5 40q0 41 -28.5 73.5
t-126.5 32.5q-66 0 -125.5 -16.5t-108.5 -37.5l-97 -459h-57z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="921" 
d="M59 0l107 502h-61l7 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q40 0 80 -6l-10 -49q-17 2 -34 3.5t-34 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-17 -78h266l16 76q11 50 27 86.5t41.5 60t62 34.5t87.5 11q33 0 61 -6l-10 -49l-24 3q-6 1 -12 1.5
t-13 0.5q-37 0 -64 -7.5t-46 -24t-32 -43t-21 -65.5l-17 -78h324l-126 -592q-10 -46 -24.5 -78.5t-36.5 -53.5t-52.5 -30.5t-72.5 -9.5q-20 0 -44 1t-41 4l11 51q14 -2 35.5 -3t35.5 -1q56 0 87.5 26t45.5 94l115 544h-267l-106 -502h-56l106 502h-266l-106 -502h-57z
M867 782h64l-14 -69h-64z" />
    <glyph glyph-name="f_f_k" unicode="ffk" horiz-adv-x="1297" 
d="M59 0l107 502h-60l6 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q40 0 80 -6l-9 -44q-17 2 -34 3.5t-35 1.5q-37 0 -65 -7.5t-47.5 -25t-32.5 -45t-22 -67.5l-17 -78h267l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q40 0 77 -6l-9 -44q-17 2 -33 3.5
t-33 1.5q-38 0 -65.5 -7.5t-47 -25t-32.5 -45t-22 -67.5l-17 -78h183l-10 -48h-183l-106 -502h-57l107 502h-267l-106 -502h-57zM886 802h57l-99 -468l370 216h88l-397 -234l298 -316h-75l-289 314l-66 -314h-57z" />
    <glyph glyph-name=".notdef" horiz-adv-x="584" 
d="M109 802h584l-212 -1000h-584zM302 195l33 85q54 0 91.5 10t62.5 28t39 43.5t21 56.5q5 23 5 43q0 58 -47.5 96t-190.5 38q-45 0 -96 -5.5t-91 -13.5l-26 -124q48 9 95.5 14.5t89.5 5.5q40 0 63.5 -2t35 -7.5t13 -12t1.5 -10.5q0 -7 -2 -15q-3 -12 -7 -21t-13.5 -14
t-25.5 -7.5t-42 -2.5h-109l-15 -70l-5 -115h120zM278 0l33 153h-154l-33 -153h154z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni000D" horiz-adv-x="321" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="285" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="380" 
d="M237 632l36 170h58l-36 -170l-90 -394h-46zM109 75h77l-16 -75h-76z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="550" 
d="M272 802h63l-64 -267h-49zM460 802h63l-64 -267h-49z" />
    <glyph glyph-name="numbersign" unicode="#" 
d="M168 244h-126l25 45h126l123 224h-125l25 45h125l135 244h55l-134 -244h196l134 244h55l-135 -244h127l-25 -45h-127l-123 -224h126l-26 -45h-125l-135 -244h-54l134 244h-196l-134 -244h-56zM444 289l124 224h-196l-124 -224h196z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="690" 
d="M58 125q38 -12 96 -24t127 -15l65 302q-66 12 -109 27t-66 38.5t-26 46.5t-3 34q0 30 9 69q9 43 28 74t52 51.5t81 30t114 9.5l20 92h50l-20 -93q59 -3 108.5 -12.5t84.5 -21.5l-9 -54q-37 12 -88.5 21.5t-106.5 12.5l-60 -279q66 -12 109 -28t66.5 -41t26 -48t2.5 -33
q0 -32 -10 -76q-10 -48 -29 -81t-51 -54t-80.5 -30.5t-118.5 -9.5l-20 -93l-50 -1l20 95q-74 3 -129 14t-93 25zM205 596q-7 -29 -7 -51q0 -7 2 -22.5t19 -32t50.5 -27t87.5 -20.5l58 271q-53 -1 -90 -8t-61.5 -21t-38.5 -36t-20 -53zM544 214q7 33 7 57q0 6 -1.5 22.5
t-18.5 34.5t-50.5 29.5t-86.5 21.5l-63 -294q56 1 93.5 8.5t62 23t37.5 39.5t20 58z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="845" 
d="M255 481q-85 0 -114.5 33.5t-29.5 82.5q0 26 7 60q18 87 62.5 124t127.5 37q85 0 114.5 -33.5t29.5 -81.5q0 -27 -7 -61q-18 -87 -62 -124t-128 -37zM142 0h-59l676 802h60zM259 521q60 0 90.5 29.5t45.5 98.5q6 28 6 50q0 32 -18 55.5t-78 23.5q-61 0 -92 -30t-45 -99
q-5 -27 -5 -48q0 -32 17.5 -56t78.5 -24zM593 -16q-84 0 -114 33.5t-30 82.5q0 26 7 59q18 88 62.5 125t127.5 37q85 0 114.5 -33.5t29.5 -81.5q0 -27 -7 -61q-18 -87 -62 -124t-128 -37zM597 24q60 0 90.5 29.5t45.5 98.5q6 28 6 49q0 32 -18 55.5t-78 23.5
q-61 0 -92 -29.5t-45 -98.5q-5 -27 -5 -48q0 -32 17.5 -56t78.5 -24z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="751" 
d="M673 35l-52 -34l-368 384q-35 -10 -60.5 -25t-44 -36t-31 -48t-21.5 -60q-10 -34 -10 -60q0 -7 1 -13q2 -34 26.5 -52.5t58.5 -27.5t70.5 -10.5t60.5 -1.5q50 0 102.5 6.5t97.5 29.5l34 -37q-57 -32 -121.5 -41t-128.5 -9q-33 0 -75.5 2.5t-81.5 14t-68 35.5t-35 66
q-1 8 -1 17q0 34 12 77q18 63 54 115t99 77q8 3 17.5 4.5t18.5 3.5q-12 13 -24.5 25.5t-23 26t-17.5 30t-8 36.5v5q0 30 11 72q9 28 21.5 53t30.5 48l14 15q26 24 56.5 39t63.5 23.5t68 12t69 4.5q21 0 45 -2.5t47.5 -8t45.5 -14.5t38 -23q8 -7 13 -15l10 -16q3 -8 5.5 -15
t3.5 -16q1 -10 1 -20q0 -30 -10 -65q-8 -26 -15.5 -44.5t-18 -33t-25.5 -27.5t-38 -27q-35 -22 -88.5 -43t-142.5 -35l-28 31q35 6 76.5 15t80.5 23.5t70 33.5t46 44q7 13 12.5 26.5t10.5 30.5q9 31 9 54q0 6 -1 12q-2 18 -10.5 30.5t-21 21.5t-28 14.5t-30.5 9.5
q-17 3 -31.5 5t-39.5 3q-45 -1 -85 -7.5t-73 -22.5t-57 -44.5t-37 -75.5q-8 -26 -8 -51q0 -2 0.5 -17t16.5 -36q8 -11 18 -20.5t20 -19.5l300 -314q3 4 18 24.5t33 54t35 79.5t24 101h49q-3 -38 -17 -84q-12 -40 -37 -94t-71 -117z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="380" 
d="M272 802h63l-64 -267h-49z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="342" 
d="M170 -146q-27 38 -48 86.5t-30.5 103.5t-9.5 115q0 11 1 53t19 128q29 140 94.5 257t167.5 205h65q-100 -84 -170 -206t-101 -268q-20 -95 -20 -178q0 -47 12 -129t78 -167h-58z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="342" 
d="M195 802q27 -38 48 -86.5t31 -104.5t10 -119q0 -8 -1 -49t-19 -127q-29 -140 -95 -257t-168 -205h-65q101 84 170.5 206t100.5 268q20 95 20 178q0 47 -12 129t-78 167h58z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="386" 
d="M261 586l-117 -147l-35 31l127 134l-143 45l18 44l141 -61l30 170h48l-45 -172l157 63l13 -47l-166 -45l74 -136l-43 -23z" />
    <glyph glyph-name="plus" unicode="+" 
d="M133 300h227l53 250h53l-54 -250h229l-11 -49h-228l-54 -251h-52l53 251h-227z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="380" 
d="M157 85h66l-146 -269h-57z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="380" 
d="M41 250l10 52h292l-11 -52h-291z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="380" 
d="M110 75h76l-15 -75h-76z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="349" 
d="M393 802h59l-472 -948h-58z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="690" 
d="M446 818q76 0 129 -21.5t81 -71.5t29.5 -95t1.5 -53q0 -77 -22 -186q-23 -108 -52.5 -185.5t-72 -126.5t-101 -72t-139.5 -23q-76 0 -129 21.5t-81 71.5t-29.5 95t-1.5 53q0 77 22 186q23 108 52.5 185.5t72 126.5t101 72t139.5 23zM304 40q65 0 112.5 19.5t83 62.5
t60.5 111.5t46 167.5t21.5 135t0.5 40q0 64 -19 105.5t-60.5 61t-106.5 19.5t-112.5 -19.5t-83 -62.5t-60.5 -111.5t-46 -167.5t-21.5 -135t-0.5 -40q0 -64 19 -105.5t61 -61t106 -19.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="525" 
d="M442 802h53l-170 -802h-58l155 734l-286 -127l-20 52z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="690" 
d="M441 818q77 0 127.5 -13t78 -39.5t32.5 -53.5t5 -44q0 -27 -7 -58q-8 -34 -18.5 -58t-28.5 -44t-47 -39t-75 -45l-274 -150q-34 -19 -56 -33.5t-35.5 -30t-21 -35t-13.5 -48.5l-16 -75h477l-11 -52h-536l30 138q8 35 18 59.5t27.5 44.5t43 38t63.5 39l283 156
q38 21 61 35.5t36.5 29.5t20.5 32.5t12 42.5t5 45q0 12 -3 31t-24 37t-60.5 26.5t-101.5 8.5q-63 0 -123 -7.5t-108 -18.5l11 56q48 11 102 18t126 7z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="690" 
d="M31 60q104 -26 238 -26q74 0 122.5 9.5t78.5 29t46 50.5t26 73t10 72q0 8 -2 29t-23 42.5t-60.5 30.5t-99.5 9h-174l10 53h171q53 0 93 6.5t67.5 22t45 42.5t25.5 68q8 39 8 69q0 10 -2 33t-24 47.5t-63 36t-104 11.5q-66 0 -121.5 -6.5t-103.5 -18.5l10 50q47 11 101 18
t122 7q153 0 200 -50t47 -121q0 -35 -9 -78q-16 -79 -60 -117t-109 -49q69 -14 91.5 -53.5t22.5 -82.5q0 -36 -11 -82q-12 -52 -33 -90t-58.5 -63t-95.5 -36.5t-144 -11.5q-81 0 -140.5 7t-107.5 19z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="690" 
d="M628 456l-97 -456h-58l41 195h-493l10 47l417 560h68l-412 -554h422l44 208h58z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="690" 
d="M30 65q50 -13 112.5 -19.5t134.5 -6.5q68 0 113.5 11t75 33t45 54t24.5 75q7 35 7 62q0 15 -4 39.5t-28 46.5t-65.5 31.5t-101.5 9.5h-206l85 401h468l-11 -53h-409l-63 -295h145q73 0 125.5 -13t83.5 -42.5t37 -62t6 -53.5q0 -35 -9 -78q-12 -57 -34 -98.5t-60 -69
t-93.5 -40.5t-135.5 -13q-84 0 -145 7t-108 19z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="690" 
d="M322 -16q-87 0 -143.5 23t-84 73.5t-27.5 131.5t25 199q26 116 58.5 194t78.5 125t109 67.5t149 20.5q66 0 115 -7.5t91 -19.5l-11 -54q-41 11 -89 18.5t-107 7.5q-72 0 -124.5 -16.5t-91 -56t-66 -105t-48.5 -163.5q24 10 49 18.5t53 15t60 10t70 3.5q60 0 108 -14
t79 -44.5t38.5 -64.5t7.5 -58q0 -32 -8 -70q-13 -66 -37 -110.5t-59.5 -72t-83.5 -39.5t-111 -12zM378 416q-38 0 -69 -3.5t-58.5 -9.5t-53 -14.5t-52.5 -18.5q-19 -93 -19.5 -125.5t-0.5 -33.5q0 -62 21 -101t65 -56t114 -17q48 0 86 8.5t66 30.5t47 58.5t31 92.5
q6 30 6 54q0 18 -5.5 43.5t-29 48t-61 33t-87.5 10.5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="690" 
d="M173 802h570l-10 -46l-573 -756h-75l577 749h-500z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="690" 
d="M289 -16q-153 0 -203 47t-50 116q0 29 7 62q20 94 66.5 140t121.5 60q-27 8 -48 22.5t-33 37.5t-13.5 43t-1.5 27q0 28 8 63q11 54 32 94.5t57.5 67.5t91.5 40.5t133 13.5q145 0 191 -45.5t46 -111.5q0 -29 -7 -64q-19 -90 -61 -134.5t-110 -58.5q64 -14 87.5 -51.5
t23.5 -82.5q0 -31 -8 -69q-12 -54 -34 -95t-60 -68t-95.5 -40.5t-140.5 -13.5zM379 432q58 0 100 7t71 25.5t47.5 49.5t28.5 79q7 33 7 60q0 7 -1.5 26.5t-21 41t-58.5 32t-102 10.5q-62 0 -105.5 -10.5t-72.5 -32t-45.5 -53.5t-25.5 -74q-7 -32 -7 -56q0 -13 3.5 -33.5
t25 -39t60 -25.5t96.5 -7zM296 40q66 0 112.5 10.5t77 31.5t48 53t26.5 75q7 32 7 57q0 14 -4 36t-27 41t-65 26.5t-103 7.5t-105.5 -7.5t-76 -26.5t-51.5 -51.5t-31 -82.5q-6 -31 -6 -56q0 -10 2 -30.5t23.5 -41.5t63.5 -31.5t109 -10.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="690" 
d="M421 818q87 0 143.5 -23t84 -73.5t28 -92t0.5 -42.5q0 -80 -26 -196q-25 -115 -58 -193.5t-79 -125.5t-109 -67.5t-149 -20.5q-66 0 -115 7.5t-91 19.5l11 54q42 -11 89.5 -18.5t107.5 -7.5q72 0 124 16.5t90.5 56t66 105t48.5 163.5q-24 -10 -49 -18.5t-53 -15t-60 -10
t-70 -3.5q-60 0 -108 14t-78.5 44.5t-38.5 64.5t-8 59q0 31 8 69q14 66 37.5 110.5t59 72t84 39.5t110.5 12zM189 575q-6 -29 -6 -53q0 -19 5.5 -44.5t29 -48t61 -33t86.5 -10.5q38 0 69.5 3.5t59 9.5t53 14.5t51.5 18.5q19 93 19.5 125.5t0.5 33.5q0 62 -21 101t-65 56
t-114 17q-48 0 -85.5 -8.5t-65.5 -30.5t-47 -58.5t-31 -92.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="380" 
d="M220 549h76l-15 -75h-76zM119 75h76l-15 -75h-76z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="361" 
d="M211 549h76l-15 -75h-76zM138 85h66l-146 -269h-57z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M133 309l556 210l-11 -55l-504 -189l423 -191l-11 -51l-467 210z" />
    <glyph glyph-name="equal" unicode="=" 
d="M166 457h508l-11 -50h-507zM100 146h508l-11 -50h-508z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M631 243l-557 -210l12 55l504 190l-423 190l10 51l468 -210z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="602" 
d="M212 376l9 42h97q58 0 98.5 8.5t68 27.5t44 49.5t25.5 74.5q7 36 7 65q0 10 -2 32t-24 44.5t-63.5 33t-106.5 10.5q-60 0 -117 -7.5t-105 -18.5l11 54q47 11 101 18t120 7q147 0 196 -48.5t49 -121.5q0 -32 -8 -71q-11 -55 -32.5 -94t-55.5 -63.5t-83 -35.5t-116 -11h-56
l-33 -133h-48zM141 75h75l-16 -75h-75z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="931" 
d="M401 68q-37 0 -67 7.5t-49 25t-24 38t-5 35.5q0 19 5 42q13 59 51.5 83.5t106.5 24.5h203l11 43q4 17 4 32q0 7 -2 19.5t-17.5 25t-45.5 18.5t-78 6q-43 0 -79 -4.5t-72 -9.5l11 48q18 3 34.5 5t34 4t36.5 3t42 1q57 0 95.5 -8.5t59.5 -27t25 -38.5t4 -34q0 -19 -5 -43
l-48 -224q-4 -20 -4.5 -30t-0.5 -13q0 -13 4.5 -24.5t17 -18.5t36.5 -7t39 7t24.5 18t15 25.5t8.5 30.5l33 160q16 77 16 136q0 20 -4.5 64t-40 87.5t-97 62.5t-148.5 19q-93 0 -158.5 -19t-110 -58t-72 -98t-44.5 -139q-20 -97 -20 -168q0 -19 4 -68t40 -97.5t99 -69
t150 -20.5q16 0 38 1t38 3l-12 -48q-15 -3 -38 -4t-40 -1q-94 0 -164.5 24t-112.5 79.5t-48 112.5t-6 82q0 77 22 179q19 90 49.5 157t81.5 111.5t126 66.5t182 22q96 0 167 -23.5t113 -73.5t49 -102t7 -81q0 -61 -16 -137l-35 -162q-5 -25 -13.5 -47.5t-23 -39t-38 -26.5
t-58.5 -10q-66 0 -91 26.5t-25 66.5q0 6 1 12q-29 -15 -71.5 -27.5t-99.5 -12.5zM412 112q51 0 94 12.5t74 26.5l32 131h-181q-54 0 -80.5 -15.5t-35.5 -57.5q-3 -16 -3 -30q0 -29 20 -48t80 -19z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="781" 
d="M449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68zM578 315l-73 437h-21l-256 -437h350z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="794" 
d="M236 802h303q63 0 110.5 -10.5t76 -36t35 -54.5t6.5 -49q0 -31 -9 -69q-18 -85 -62 -126.5t-106 -53.5q68 -14 92.5 -54.5t24.5 -86.5q0 -32 -9 -71q-13 -59 -35.5 -96.5t-57.5 -58.5t-82.5 -28.5t-109.5 -7.5h-347zM285 747l-67 -315h273q44 0 78 7t59.5 25t42.5 48.5
t27 77.5q6 30 6 54q0 12 -3.5 32.5t-24.5 38.5t-56 25t-83 7h-252zM206 377l-69 -322h276q50 0 87.5 5.5t64.5 21.5t45 45t28 77q8 34 8 61q0 11 -3 32.5t-21.5 41.5t-51.5 29t-80 9h-284z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="825" 
d="M535 818q137 0 209.5 -61t78.5 -177h-63q-8 91 -64 136t-166 45q-79 0 -137.5 -19.5t-101.5 -62.5t-73 -111.5t-51 -166.5q-19 -88 -19 -153q0 -7 1.5 -45t29.5 -80.5t81.5 -62t134.5 -19.5q104 0 172.5 41.5t115.5 140.5h66q-28 -63 -62.5 -108.5t-77.5 -74.5t-97 -42.5
t-121 -13.5q-96 0 -161.5 24t-101 75.5t-38.5 98.5t-3 62q0 72 20 166q24 110 59.5 187t87 126.5t121 72t160.5 22.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="852" 
d="M66 0l170 802h270q99 0 166 -23t103 -72.5t40 -97t4 -66.5q0 -66 -18 -151q-23 -106 -56.5 -180.5t-83 -121.5t-119 -68.5t-164.5 -21.5h-312zM137 55h249q77 0 135 18t100.5 59.5t71 108t48.5 160.5q17 81 17 140q0 11 -2 48.5t-30.5 79t-82.5 60t-137 18.5h-221z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="727" 
d="M236 802h517l-11 -55h-457l-68 -316h428l-12 -55h-427l-69 -321h458l-12 -55h-517z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="691" 
d="M236 802h508l-12 -55h-447l-69 -321h417l-11 -55h-417l-79 -371h-60z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="853" 
d="M539 818q139 0 212.5 -61t79.5 -177h-63q-8 91 -65 136t-169 45q-79 0 -139 -19.5t-103.5 -62.5t-73.5 -111.5t-51 -166.5q-18 -87 -18 -150q0 -9 1.5 -47.5t29.5 -81t82.5 -62t136.5 -19.5q72 0 125.5 13t92.5 43t64 79t40 121l11 53h-272l12 55h332l-25 -114
q-18 -84 -48.5 -142.5t-77 -95t-110.5 -53t-148 -16.5q-96 0 -162 24t-102 75.5t-39.5 100t-3.5 65.5q0 70 19 161q24 110 59.5 187t87.5 126.5t122 72t163 22.5z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="866" 
d="M236 802h60l-78 -370h500l78 370h60l-171 -802h-59l80 377h-500l-80 -377h-60z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="307" 
d="M236 802h60l-170 -802h-60z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="364" 
d="M19 -14q-19 0 -33 2t-28 5l10 52q23 -4 50 -4q37 0 61.5 7.5t41 25t26.5 46t19 69.5l130 613h60l-131 -617q-12 -54 -26.5 -92t-38 -62t-57.5 -34.5t-84 -10.5z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="727" 
d="M236 802h59l-76 -358l500 358h86l-526 -379l360 -423h-76l-350 417l-89 -417h-58z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="650" 
d="M236 802h60l-159 -752h429l-10 -50h-490z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="988" 
d="M234 802h86l160 -544h14l391 544h94l-170 -802h-60l153 725l-382 -531h-84l-157 535l-155 -729h-60z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="876" 
d="M234 802h76l342 -727l155 727h60l-171 -802h-77l-340 728l-155 -728h-60z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="878" 
d="M393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q97 0 163 -24t101.5 -76t39 -100t3.5 -64q0 -71 -20 -163q-23 -110 -59 -187.5t-88.5 -126t-123 -71t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5
t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="776" 
d="M236 802h283q78 0 134.5 -15.5t89.5 -50t39 -71t6 -57.5q0 -41 -11 -92q-16 -73 -40.5 -121t-61.5 -76t-89 -39t-122 -11h-281l-57 -269h-60zM285 747l-90 -423h268q56 0 97.5 7.5t71.5 29t50.5 61t33.5 102.5q10 45 10 80q0 14 -3.5 41t-28.5 53t-69 37.5t-108 11.5
h-232z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="878" 
d="M393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q97 0 163 -24t101.5 -76t39 -100t3.5 -64q0 -71 -20 -163q-26 -124 -67.5 -205.5t-104.5 -129.5l52 -95l-47 -27l-52 93q-86 -43 -215 -43zM398 41
q55 0 100 8.5t83 28.5l-85 153l48 28l82 -151q51 42 84.5 113.5t56.5 179.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="805" 
d="M236 802h283q81 0 137.5 -15t89 -48t38 -68.5t5.5 -57.5q0 -39 -10 -89q-13 -60 -32.5 -101.5t-47 -69.5t-64.5 -42.5t-86 -20.5l112 -290h-67l-104 286h-303l-61 -286h-60zM285 747l-86 -406h267q56 0 97 7t71 27.5t50.5 58.5t33.5 99q9 41 9 73q0 15 -4 42t-29 52
t-69.5 36t-109.5 11h-230z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="766" 
d="M65 83q50 -16 125.5 -28.5t166.5 -12.5q59 0 100.5 8t70 26t45.5 46.5t26 68.5q7 31 7 56q0 7 -1.5 25t-22.5 40t-63 39t-113 32q-83 17 -134.5 38.5t-77 51.5t-27.5 55t-2 34q0 34 9 76q10 42 29 75.5t53.5 56.5t87.5 35t132 12q92 0 155.5 -10t114.5 -25l-12 -59
q-50 16 -116 26t-148 10q-61 0 -103 -8t-69 -24t-41.5 -39.5t-21.5 -55.5q-7 -34 -7 -60q0 -6 1 -23.5t20.5 -39t60 -37.5t107.5 -30q87 -18 140.5 -39t80.5 -51t29 -55t2 -35q0 -34 -9 -78q-12 -53 -33.5 -91t-57.5 -62t-89 -35.5t-128 -11.5q-100 0 -174 11t-125 28z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="728" 
d="M435 747h-297l11 55h655l-12 -55h-297l-159 -747h-60z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="844" 
d="M392 -16q-178 0 -233 60.5t-55 146.5q0 44 12 98l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h60l-112 -527q-16 -77 -41 -132t-64.5 -90.5t-96.5 -52t-138 -16.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="781" 
d="M147 802h60l126 -749h22l437 749h68l-473 -802h-107z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1214" 
d="M155 802h61l51 -749h18l376 749h113l60 -749h17l368 749h66l-395 -802h-113l-60 749h-16l-378 -749h-113z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="789" 
d="M-7 0l392 413l-207 389h69l181 -346l326 346h81l-373 -396l216 -406h-67l-193 366l-342 -366h-83z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="715" 
d="M338 318l-208 484h65l180 -427l362 427h74l-413 -485l-68 -317h-60z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="718" 
d="M-9 51l670 695h-489l13 56h573l-11 -51l-668 -695h555l-12 -56h-642z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="342" 
d="M205 802h228l-10 -49h-172l-182 -850h173l-11 -49h-227z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="349" 
d="M123 802h54l74 -948h-55z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="342" 
d="M159 -146h-226l10 49h171l181 850h-171l10 49h227z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M460 802h65l124 -510h-52l-111 458l-308 -458h-59z" />
    <glyph glyph-name="underscore" unicode="_" 
d="M-100 -198l11 52h760l-11 -52h-760z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="570" 
d="M300 802h64l94 -170h-51z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="700" 
d="M290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13
q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="700" 
d="M216 802h57l-61 -285q44 19 96.5 34t103.5 15q66 0 114.5 -17t77.5 -54t33.5 -74.5t4.5 -56.5q0 -46 -12 -105q-31 -145 -105.5 -210t-205.5 -65q-58 0 -113 16.5t-100 37.5l-19 -38h-42zM400 514q-56 0 -108 -15.5t-91 -33.5l-80 -379q36 -20 83.5 -35t107.5 -15
q56 0 99 13t74.5 41t53 72t34.5 105q11 51 11 91q0 13 -3 42.5t-25.5 58.5t-61.5 42t-94 13z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="599" 
d="M391 514q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106q-11 -51 -11 -91q0 -13 3 -42t25 -58t61.5 -42t94.5 -13q59 0 107 6t90 16l-10 -50q-35 -11 -85 -18t-106 -7q-69 0 -119.5 16.5t-80 53t-34.5 74.5t-5 58q0 47 13 106q15 72 41 123.5t65 85t91.5 49.5t121.5 16
q63 0 114 -7.5t87 -16.5l-9 -50q-35 8 -84.5 15t-108.5 7z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="700" 
d="M290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q57 0 111 -16t98 -36l61 288h57l-170 -802h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -90q0 -13 3 -42.5t25.5 -59t61.5 -42.5t94 -13
q56 0 107.5 15.5t90.5 34.5l81 379q-36 19 -83.5 34.5t-107.5 15.5q-56 0 -99 -13t-74.5 -41t-53 -72t-34.5 -106z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="653" 
d="M388 566q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-41 -11 -91.5 -18t-121.5 -7q-69 0 -119.5 16.5t-80 53t-34.5 74.5t-5 58q0 47 13 106
q31 143 106 208.5t210 65.5zM386 516q-102 0 -161.5 -47t-87.5 -154h410q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="370" 
d="M169 502h-62l8 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q50 0 99 -6l-10 -49q-21 2 -42.5 3.5t-44.5 1.5q-38 0 -65.5 -7.5t-47 -24t-32.5 -43t-21 -65.5l-17 -78h203l-11 -48h-202l-107 -502h-57z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="698" 
d="M289 -11q-65 0 -113.5 17t-77.5 53.5t-33.5 74t-4.5 56.5q0 46 12 104q31 143 105.5 207.5t204.5 64.5q59 0 113.5 -16t98.5 -37l19 37h42l-112 -526q-13 -63 -35.5 -106.5t-57.5 -71t-84.5 -40t-117.5 -12.5q-35 0 -63 1t-54 4t-51 7t-54 10l11 51q30 -6 55.5 -10t50 -7
t49.5 -4t55 -1q57 0 97.5 9.5t68.5 30.5t45.5 55t27.5 82l4 17q-44 -20 -97 -35t-104 -15zM130 286q-12 -52 -12 -92q0 -12 3 -40.5t25.5 -57.5t61.5 -42t93 -13q57 0 109 15.5t90 33.5l80 375q-37 19 -84.5 34t-106.5 15q-55 0 -98 -13t-74.5 -40.5t-52.5 -70.5t-34 -104z
" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="720" 
d="M216 802h57l-62 -289q51 22 112 37.5t121 15.5q121 0 161.5 -46t40.5 -104q0 -23 -5 -49l-79 -367h-56l77 367q5 21 5 40q0 41 -28.5 73.5t-126.5 32.5q-66 0 -125 -14t-107 -34l-99 -465h-57z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="263" 
d="M188 719h66l-15 -69h-65zM162 550h57l-117 -550h-56z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="263" 
d="M207 782h66l-15 -69h-65zM-167 -159q14 -2 35 -3t35 -1q57 0 88 26.5t45 94.5l126 592h57l-126 -592q-10 -46 -24.5 -78.5t-36.5 -53.5t-52.5 -30.5t-72.5 -9.5q-20 0 -44 1t-41 4z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="627" 
d="M216 802h57l-100 -468l371 216h86l-397 -234l298 -316h-74l-288 313l-67 -313h-57z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="263" 
d="M216 802h57l-171 -802h-57z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1140" 
d="M208 511q48 22 107 38.5t117 16.5q69 0 112 -18.5t63 -53.5q28 13 60 26t66 23t70 16.5t71 6.5q116 0 154.5 -45.5t38.5 -100.5q0 -25 -6 -53l-78 -367h-57l77 367q5 23 5 42q0 40 -25.5 72t-116.5 32q-69 0 -130.5 -19.5t-113.5 -42.5q3 -16 3 -33q0 -24 -6 -51
l-78 -367h-57l78 367q4 22 4 40q0 41 -25.5 73.5t-116.5 32.5q-63 0 -120 -17t-104 -37l-98 -459h-57l118 550h41z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="720" 
d="M207 511q51 23 113 39t124 16q121 0 162 -45t41 -103q0 -24 -6 -51l-79 -367h-56l77 367q5 21 5 40q0 41 -28.5 73.5t-126.5 32.5q-66 0 -125.5 -17t-108.5 -37l-97 -459h-57l117 550h42z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="690" 
d="M299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q70 0 121 -17t80.5 -53.5t35 -74t5.5 -57.5q0 -43 -12 -97q-16 -76 -42 -130t-65 -88t-92.5 -49.5t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5
t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="700" 
d="M162 550h42l3 -35q45 20 98.5 35.5t106.5 15.5q66 0 114.5 -17t77.5 -54t33.5 -74.5t4.5 -56.5q0 -46 -12 -105q-31 -145 -105.5 -210t-205.5 -65q-57 0 -111 16t-98 36l-50 -234h-57zM400 514q-56 0 -108 -15.5t-91 -33.5l-81 -379q37 -19 84.5 -34.5t107.5 -15.5
q56 0 99 13t74.5 41t53 72t34.5 105q11 51 11 91q0 13 -3 42.5t-25.5 58.5t-61.5 42t-94 13z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="700" 
d="M290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-160 -748h-56l49 232q-44 -20 -96.5 -35t-103.5 -15zM129 283q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5
t94 -13q56 0 108 15.5t91 33.5l81 380q-37 19 -84.5 34t-107.5 15q-111 0 -173 -53.5t-88 -177.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="389" 
d="M162 550h42l3 -38q45 20 97 33t110 13l-11 -54q-57 -1 -108 -13t-95 -29l-98 -462h-57z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="614" 
d="M32 61q40 -9 101.5 -18t126.5 -9q49 0 84 4t58.5 14.5t37 29t19.5 47.5q5 26 5 43q0 6 -1 18t-17 25.5t-49.5 22.5t-89.5 19q-67 11 -108 23t-62 31t-23 37t-2 28q0 23 6 54q8 38 24 64t45 42t74 23t110 7q66 0 118 -7.5t94 -18.5l-11 -49q-39 10 -87 17.5t-114 7.5
q-52 0 -87.5 -4.5t-58 -15t-34.5 -28t-18 -43.5q-4 -22 -4 -38q0 -5 1 -16.5t17 -23.5t49.5 -20.5t88.5 -17.5q67 -11 109 -23.5t63 -32.5t23 -39t2 -27q0 -27 -8 -64q-8 -39 -26 -65.5t-48 -43t-74 -23.5t-105 -7q-72 0 -135 8.5t-104 20.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="433" 
d="M247 -11q-107 0 -134.5 35t-27.5 81q0 26 7 59l72 338h-61l8 36l63 12l28 129h57l-27 -129h202l-11 -48h-202l-72 -339q-5 -25 -5 -45q0 -33 19 -55t94 -22q39 0 76 6l-11 -52q-18 -2 -36.5 -4t-38.5 -2z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="720" 
d="M278 -16q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 459h57l-117 -550h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="647" 
d="M91 550h59l122 -505l337 505h65l-365 -550h-86z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="980" 
d="M93 550h58l57 -507l294 507h89l76 -506l274 507l65 -1l-299 -550h-89l-77 507l-294 -507h-89zM541 512l3 1h-9zM664 36l6 1h-2zM212 36l-4 1l-1 -1h5z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="626" 
d="M-23 0l306 286l-171 264h62l147 -229l241 229h73l-287 -270l182 -280h-63l-158 244l-258 -244h-74z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="646" 
d="M-34 -161q30 -3 57 -3q30 0 54.5 5t44.5 16t37.5 29t35.5 44l49 73h-25l-128 547h57l116 -498h13l333 498h63l-421 -629q-25 -38 -48.5 -64t-49.5 -41.5t-57 -22.5t-71 -7q-18 0 -35.5 1t-34.5 4z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="625" 
d="M18 41l492 461h-382l11 48h466l-9 -41l-493 -461h406l-10 -48h-489z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="342" 
d="M231 -144h-22q-46 0 -77.5 8.5t-48.5 28t-19 39t-2 28.5q0 27 8 64l36 168q7 31 7.5 42.5t0.5 12.5q0 20 -8 32.5t-26 18.5t-48 6h-23l10 49h24q30 0 50 6t33.5 19t22.5 35t15 53l44 211q14 69 52 97t117 28h55l-9 -49h-43q-57 0 -82.5 -18t-34.5 -62l-43 -203
q-14 -64 -36 -98t-64 -47q33 -13 40.5 -36t7.5 -42q0 -25 -8 -59l-37 -167q-7 -34 -7 -55q0 -2 0.5 -14t11.5 -24.5t33 -17.5t56 -5h25z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="307" 
d="M238 802h56l-202 -948h-55z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="342" 
d="M158 -19q-7 -35 -19.5 -59t-32.5 -38.5t-48.5 -21t-68.5 -6.5h-55l10 48h41q58 0 83.5 18t34.5 63l43 203q14 63 36 97t63 47q-32 14 -39 37.5t-7 42.5q0 25 7 58l37 166q7 32 7 54q0 2 -0.5 15t-11 25.5t-32.5 17.5t-56 5h-26l11 49h23q46 0 77 -8.5t48 -28t19 -39.5
t2 -28q0 -28 -8 -65l-35 -167q-7 -31 -7.5 -42.5t-0.5 -12.5q0 -20 7.5 -33t25.5 -19t48 -6h23l-10 -49h-23q-30 0 -50.5 -5.5t-34 -19t-22.5 -34.5t-15 -53z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M84 261q16 79 52.5 111.5t96.5 32.5q36 0 65 -14t53 -34.5t46 -45t44 -45t46 -34.5t53 -14q44 0 67.5 27.5t35.5 88.5l13 62h46l-16 -81q-15 -76 -52 -109.5t-97 -33.5q-36 0 -64.5 14t-53 34.5t-46 45t-44 45t-46.5 34.5t-53 14q-44 0 -67.5 -27t-35.5 -89l-13 -63h-46z
" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="285" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="380" 
d="M263 475h-77l16 75h76zM135 -82l-36 -170h-58l36 170l90 394h46z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="690" 
d="M512 802l-23 -109q57 -2 106.5 -8.5t91.5 -15.5l-12 -54q-44 9 -93.5 15t-104.5 8l-100 -471q63 1 117 7t99 15l-11 -53q-45 -11 -100 -17.5t-116 -7.5l-24 -111h-51l24 112q-142 10 -184.5 71t-42.5 137q0 48 13 107q16 75 44 126t69.5 82t97 44.5t127.5 14.5l23 108h50
zM161 419q-11 -55 -11 -97q0 -5 1 -30.5t20 -56t57.5 -47t98.5 -20.5l100 470q-61 -1 -105.5 -13.5t-76 -38.5t-52 -67t-32.5 -100z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="690" 
d="M63 51q58 0 84.5 25t37.5 77l48 227h-152l10 48h152l42 199q11 48 28.5 84t47.5 60t74 35.5t106 11.5q67 0 116.5 -7.5t89.5 -17.5l-11 -53q-40 11 -87.5 17.5t-106.5 6.5q-50 0 -84.5 -9t-58 -27t-37 -43.5t-20.5 -58.5l-42 -198h328l-10 -48h-328l-48 -223
q-8 -38 -23.5 -64t-41.5 -42h436l-11 -51h-602l11 51h52z" />
    <glyph glyph-name="currency" unicode="&#xa4;" 
d="M89 63l128 110q-13 25 -13 62q0 26 7 58q17 80 52 121l-79 108l35 26l78 -105q44 28 120 28q35 0 63 -7.5t47 -24.5l129 111l30 -34l-132 -114q9 -22 9 -53q0 -27 -7 -60q-9 -42 -22 -72.5t-33 -51.5l81 -108l-38 -29l-79 108q-43 -25 -115 -25q-68 0 -105 28l-126 -110z
M354 154q65 0 101 32t52 105q6 29 6 52q0 34 -18 59t-83 25q-64 0 -100.5 -31.5t-52.5 -104.5q-6 -29 -6 -51q0 -35 18.5 -60.5t82.5 -25.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="690" 
d="M317 279h-239l10 49h240l31 147h-240l11 48h202l-185 280h64l182 -276l306 275h70l-306 -279h204l-10 -48h-239l-31 -147h239l-10 -49h-240l-59 -279h-59z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="307" 
d="M160 432l78 370h56l-79 -370h-55zM116 224h55l-79 -370h-55z" />
    <glyph glyph-name="section" unicode="&#xa7;" 
d="M50 -118q39 -14 96.5 -27.5t127.5 -13.5q58 0 98 6.5t66 21t40.5 38t21.5 57.5q6 29 6 52q0 4 -0.5 18.5t-15 34.5t-43.5 36t-77 31l-120 37q-31 9 -57.5 22.5t-43.5 35.5t-20.5 43.5t-3.5 34.5q0 23 6 53q9 47 40.5 80.5t82.5 56.5q-30 27 -33.5 52t-3.5 39q0 29 7 66
q9 43 27 73.5t49 50t77 28.5t110 9q69 0 122 -10t95 -22l-11 -52q-38 13 -89 21.5t-116 8.5q-52 0 -88.5 -6t-61 -20t-38.5 -36t-21 -54q-6 -26 -6 -46q0 -5 1 -19t15.5 -32.5t43.5 -33.5t77 -30l120 -37q32 -9 58 -22.5t43 -35.5t21 -44t4 -36q0 -23 -6 -52
q-10 -47 -42.5 -80.5t-84.5 -56.5q32 -29 36 -55.5t4 -42.5q0 -30 -8 -69q-9 -45 -28 -77t-51.5 -52.5t-81.5 -30t-119 -9.5q-75 0 -135 13t-102 28zM400 183q57 -18 94 -39q42 20 67 45.5t33 65.5q6 24 6 43q0 9 -2.5 24.5t-13.5 32t-28.5 26.5t-39.5 17l-136 40
q-30 9 -53.5 18.5t-42.5 20.5q-41 -20 -65.5 -46t-32.5 -64q-5 -24 -5 -43q0 -8 2 -24t13 -32.5t29 -26.5t39 -16z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="570" 
d="M255 754h55l-14 -68h-55zM465 754h55l-14 -68h-55z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="878" 
d="M99 437q21 100 55 172t86 118.5t125 68.5t173 22q98 0 167.5 -24t109 -78t44 -107t4.5 -74q0 -74 -20 -170q-21 -100 -55.5 -172t-87 -118.5t-125 -68.5t-171.5 -22t-168.5 24t-108.5 78t-43.5 107t-4.5 74q0 74 20 170zM793 372q20 90 20 157q0 16 -3.5 61t-36 92
t-92 68t-145.5 21t-150 -19t-110.5 -60.5t-77 -106t-49.5 -155.5q-20 -92 -20 -160q0 -14 3 -58.5t36 -91.5t92.5 -68t145.5 -21t149.5 19t110 60.5t77.5 106t50 155.5zM417 143q-54 0 -92 14.5t-60 46.5t-25 63t-3 44q0 43 12 98q29 133 93.5 191.5t172.5 58.5
q85 0 132 -35t49 -116h-56q-5 54 -34.5 77.5t-92.5 23.5q-41 0 -74 -11.5t-58.5 -36.5t-44 -64.5t-30.5 -95.5q-10 -50 -10 -87q0 -46 22.5 -83.5t101.5 -37.5q64 0 104.5 23.5t67.5 77.5h60q-18 -40 -40 -68.5t-50.5 -47t-64 -27t-80.5 -8.5z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="410" 
d="M248 481q-38 0 -66.5 10t-45.5 32t-19.5 43.5t-2.5 32.5q0 27 7 61q17 85 61 121.5t119 36.5q33 0 64 -8.5t58 -21.5l13 21h36l-68 -319h-35l-5 19q-27 -12 -57 -20t-59 -8zM171 653q-6 -29 -6 -51q0 -34 19 -57t77 -23q29 0 55.5 7.5t49.5 17.5l44 204
q-20 11 -45.5 18.5t-55.5 7.5q-60 0 -92 -28.5t-46 -95.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="516" 
d="M256 509h64l-218 -235l117 -229h-61l-118 232zM466 509h64l-218 -235l117 -229h-61l-118 232z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M166 457h508l-77 -361h-50l66 311h-457z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="439" 
d="M105 628q11 50 28 86t43 59t63 34t86 11t84 -12t54.5 -39t21.5 -53t2 -36q0 -37 -10 -86q-11 -50 -28 -86t-43.5 -59.5t-63 -34.5t-85.5 -11t-84 12.5t-54.5 39.5t-21.5 53t-2 36q0 37 10 86zM448 595q10 45 10 78q0 7 -1.5 29t-17 45.5t-44.5 33.5t-71 10
q-85 0 -128.5 -39t-61.5 -128q-10 -45 -10 -78q0 -7 1.5 -29t17.5 -45.5t44.5 -33.5t70.5 -10q84 0 127.5 39t62.5 128zM231 733h87q46 0 65.5 -16t19.5 -44q0 -14 -4 -31q-8 -34 -24.5 -50t-48.5 -21l35 -84h-33l-34 83h-67l-18 -83h-31zM255 704l-23 -107h67q32 0 48 10.5
t21 38.5q3 13 3 22q0 18 -12.5 27t-41.5 9h-62z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="570" 
d="M241 741h286l-8 -41h-287z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="400" 
d="M255 481q-85 0 -114.5 33.5t-29.5 82.5q0 26 7 60q18 87 62.5 124t127.5 37q85 0 114.5 -33.5t29.5 -81.5q0 -27 -7 -61q-18 -87 -62 -124t-128 -37zM259 521q60 0 90.5 29.5t45.5 98.5q6 28 6 50q0 32 -18 55.5t-78 23.5q-61 0 -92 -30t-45 -99q-5 -27 -5 -48
q0 -32 17.5 -56t78.5 -24z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M148 369h228l39 181h49l-39 -181h230l-10 -49h-230l-39 -182h-50l39 182h-228zM79 50h508l-10 -50h-508z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="398" 
d="M344 1009q90 0 118 -26t28 -64q0 -15 -4 -33q-5 -21 -11 -35t-17 -25.5t-29 -23t-46 -26.5l-151 -82q-17 -10 -28 -17.5t-17.5 -15.5t-10.5 -18t-7 -24l-8 -37h265l-9 -42h-316l18 87q4 19 10 33t16 25.5t25 21.5t37 22l157 85q20 11 32.5 19t20 16t11 16.5t6.5 21.5
q2 12 2 22q0 25 -17.5 40.5t-82.5 15.5q-36 0 -71 -4.5t-64 -10.5l9 45q29 6 60.5 10t73.5 4z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="398" 
d="M109 590q58 -16 137 -16q40 0 66 4.5t42 14.5t24.5 26t13.5 38q5 20 5 36q0 26 -16.5 42.5t-78.5 16.5h-106l8 42h104q56 0 83.5 14.5t35.5 57.5q4 18 4 33q0 28 -17.5 47.5t-86.5 19.5q-39 0 -70.5 -3.5t-61.5 -11.5l9 44q27 6 59 10t74 4q92 0 120.5 -28.5t28.5 -70.5
q0 -20 -5 -44q-9 -45 -33 -66.5t-61 -29.5q38 -10 50 -33t12 -46q0 -20 -6 -45q-7 -31 -20 -52.5t-35.5 -35.5t-57 -20.5t-85.5 -6.5q-48 0 -83 4t-63 12z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="570" 
d="M428 802h70l-179 -170h-57z" />
    <glyph glyph-name="uni00B5" unicode="&#xb5;" 
d="M187 550h57l-80 -377q-3 -16 -3 -31q0 -43 28.5 -74.5t116.5 -31.5q66 0 124 14t106 35l99 465h57l-117 -550h-42l-3 38q-50 -23 -110 -38.5t-118 -15.5q-66 0 -105.5 21t-56.5 57l-55 -260h-57z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" 
d="M330 339q-56 0 -96 12t-62.5 41.5t-25 59t-2.5 41.5q0 42 12 100t32.5 98t50.5 64.5t71 35.5t95 11h316l-201 -948h-55l190 898h-182l-190 -898h-56z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="380" 
d="M119 314h76l-15 -75h-76z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="570" 
d="M214 1h43l-81 -70q6 1 12 1h12q54 0 72.5 -15t18.5 -41q0 -10 -2 -21q-4 -18 -11 -31.5t-20.5 -21.5t-35 -12t-54.5 -4q-24 0 -43.5 2.5t-33.5 6.5l8 32q30 -8 71 -8q47 0 62.5 9t19.5 32q2 7 2 12q0 12 -9 21t-56 9q-19 0 -37 -1.5t-31 -3.5l7 29z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="304" 
d="M341 1000h49l-98 -460h-51l86 403l-156 -70l-17 44z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="402" 
d="M255 481q-85 0 -114.5 33.5t-29.5 82.5q0 26 7 60q18 87 62.5 124t127.5 37q85 0 114.5 -33.5t29.5 -81.5q0 -27 -7 -61q-18 -87 -62 -124t-128 -37zM259 521q60 0 90.5 29.5t45.5 98.5q6 28 6 50q0 32 -18 55.5t-78 23.5q-61 0 -92 -30t-45 -99q-5 -27 -5 -48
q0 -32 17.5 -56t78.5 -24z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="516" 
d="M52 44h-63l217 234l-116 230h60l119 -232zM262 44h-63l217 234l-116 230h60l119 -232z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="878" 
d="M299 802h49l-98 -460h-51l86 403l-156 -70l-17 44zM728 802h61l-678 -802h-59zM827 262l-55 -262h-51l22 106h-277l9 40l233 314h58l-232 -312h218l24 114h51z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="878" 
d="M299 802h49l-98 -460h-51l86 403l-156 -70l-17 44zM728 802h61l-678 -802h-59zM710 469q90 0 118 -26t28 -64q0 -15 -4 -33q-5 -21 -11 -35t-17 -25.5t-29 -23t-46 -26.5l-151 -82q-17 -10 -28 -17.5t-17.5 -15.5t-10.5 -18t-7 -24l-8 -37h265l-9 -42h-316l18 87
q4 19 10 33t16 25.5t25 21.5t37 22l157 85q20 11 32.5 19t20 16t11 16.5t6.5 21.5q2 12 2 22q0 25 -17.5 40.5t-82.5 15.5q-36 0 -71 -4.5t-64 -10.5l9 45q29 6 60.5 10t73.5 4z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="972" 
d="M67 392q58 -16 137 -16q40 0 66 4.5t42 14.5t24.5 26t13.5 38q5 20 5 36q0 26 -16.5 42.5t-78.5 16.5h-106l8 42h104q56 0 83.5 14.5t35.5 57.5q4 18 4 33q0 28 -17.5 47.5t-86.5 19.5q-39 0 -70.5 -3.5t-61.5 -11.5l9 44q27 6 59 10t74 4q92 0 120.5 -28.5t28.5 -70.5
q0 -20 -5 -44q-9 -45 -33 -66.5t-61 -29.5q38 -10 50 -33t12 -46q0 -20 -6 -45q-7 -31 -20 -52.5t-35.5 -35.5t-57 -20.5t-85.5 -6.5q-48 0 -83 4t-63 12zM821 802h61l-678 -802h-59zM921 262l-55 -262h-51l22 106h-277l9 40l233 314h58l-232 -312h218l24 114h51z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="602" 
d="M461 475h-75l16 75h75zM390 174l-9 -42h-97q-58 0 -98.5 -8.5t-68 -27.5t-44 -49.5t-25.5 -74.5q-7 -36 -7 -65q0 -10 2 -32t24 -44.5t63.5 -33t106.5 -10.5q60 0 117 7.5t105 18.5l-11 -54q-47 -11 -101 -18t-120 -7q-147 0 -196 48.5t-49 121.5q0 32 8 71
q11 56 32.5 94.5t55.5 63t83 35.5t116 11h56l33 133h48z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="781" 
d="M401 987h63l79 -140h-53zM449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68zM578 315l-73 437h-21l-256 -437h350z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="781" 
d="M619 987h72l-149 -140h-62zM449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68zM578 315l-73 437h-21l-256 -437h350z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="781" 
d="M507 987h73l105 -140h-50l-93 112l-141 -112h-59zM449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68zM578 315l-73 437h-21l-256 -437h350z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="781" 
d="M344 888q10 43 32 64.5t63 21.5q28 0 51.5 -12.5t44.5 -28t41 -28t40 -12.5q25 0 38 13t18 36l5 27h41l-6 -30q-8 -41 -31 -63.5t-64 -22.5q-28 0 -52 13t-45 28t-40.5 28t-39.5 13q-25 0 -37.5 -13t-18.5 -37l-6 -27h-40zM449 802h107l134 -802h-60l-43 261h-390
l-153 -261h-68zM578 315l-73 437h-21l-256 -437h350z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="781" 
d="M400 949h56l-13 -63h-57zM614 949h56l-14 -63h-56zM449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68zM578 315l-73 437h-21l-256 -437h350z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="781" 
d="M449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68zM578 315l-73 437h-21l-256 -437h350z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1198" 
d="M591 261h-355l-221 -261h-74l681 802h602l-12 -55h-458l-67 -316h427l-12 -55h-427l-69 -326h458l-11 -50h-518zM602 316l92 431h-45l-366 -431h319z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="825" 
d="M535 818q137 0 209.5 -61t78.5 -177h-63q-8 91 -64 136t-166 45q-79 0 -137.5 -19.5t-101.5 -62.5t-73 -111.5t-51 -166.5q-19 -88 -19 -153q0 -7 1.5 -45t29.5 -80.5t81.5 -62t134.5 -19.5q104 0 172.5 41.5t115.5 140.5h66q-28 -62 -61.5 -107t-75.5 -74t-94.5 -43
t-117.5 -15l-146 -182h-58l149 183q-83 6 -139 33t-85.5 78.5t-31 95t-1.5 53.5q0 72 19 165q24 110 59.5 187t87 126.5t121 72t160.5 22.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="727" 
d="M399 987h63l79 -140h-53zM236 802h517l-11 -55h-457l-68 -316h428l-12 -55h-427l-69 -321h458l-12 -55h-517z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="727" 
d="M617 987h72l-149 -140h-62zM236 802h517l-11 -55h-457l-68 -316h428l-12 -55h-427l-69 -321h458l-12 -55h-517z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="727" 
d="M505 987h73l105 -140h-50l-93 112l-141 -112h-59zM236 802h517l-11 -55h-457l-68 -316h428l-12 -55h-427l-69 -321h458l-12 -55h-517z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="727" 
d="M398 949h56l-13 -63h-57zM612 949h56l-14 -63h-56zM236 802h517l-11 -55h-457l-68 -316h428l-12 -55h-427l-69 -321h458l-12 -55h-517z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="307" 
d="M164 987h63l79 -140h-53zM236 802h60l-170 -802h-60z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="307" 
d="M382 987h72l-149 -140h-62zM236 802h60l-170 -802h-60z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="307" 
d="M270 987h73l105 -140h-50l-93 112l-141 -112h-59zM236 802h60l-170 -802h-60z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="307" 
d="M163 949h56l-13 -63h-57zM377 949h56l-14 -63h-56zM236 802h60l-170 -802h-60z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="852" 
d="M34 431h123l79 371h270q99 0 166 -23t103 -72.5t40 -97t4 -66.5q0 -66 -18 -151q-23 -106 -56.5 -180.5t-83 -121.5t-119 -68.5t-164.5 -21.5h-312l80 376h-123zM137 55h249q77 0 135 18t100.5 59.5t71 108t48.5 160.5q17 81 17 140q0 11 -2 48.5t-30.5 79t-82.5 60
t-137 18.5h-221l-68 -316h224l-11 -55h-224z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="876" 
d="M391 888q10 43 32 64.5t63 21.5q28 0 51.5 -12.5t44.5 -28t41 -28t40 -12.5q25 0 38 13t18 36l5 27h41l-6 -30q-8 -41 -31 -63.5t-64 -22.5q-28 0 -52 13t-45 28t-40.5 28t-39.5 13q-25 0 -37.5 -13t-18.5 -37l-6 -27h-40zM234 802h76l342 -727l155 727h60l-171 -802h-77
l-340 728l-155 -728h-60z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="878" 
d="M449 987h63l79 -140h-53zM393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q97 0 163 -24t101.5 -76t39 -100t3.5 -64q0 -71 -20 -163q-23 -110 -59 -187.5t-88.5 -126t-123 -71t-163.5 -22.5zM398 41
q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="878" 
d="M667 987h72l-149 -140h-62zM393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q97 0 163 -24t101.5 -76t39 -100t3.5 -64q0 -71 -20 -163q-23 -110 -59 -187.5t-88.5 -126t-123 -71t-163.5 -22.5zM398 41
q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="878" 
d="M555 987h73l105 -140h-50l-93 112l-141 -112h-59zM393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q97 0 163 -24t101.5 -76t39 -100t3.5 -64q0 -71 -20 -163q-23 -110 -59 -187.5t-88.5 -126t-123 -71
t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="878" 
d="M392 888q10 43 32 64.5t63 21.5q28 0 51.5 -12.5t44.5 -28t41 -28t40 -12.5q25 0 38 13t18 36l5 27h41l-6 -30q-8 -41 -31 -63.5t-64 -22.5q-28 0 -52 13t-45 28t-40.5 28t-39.5 13q-25 0 -37.5 -13t-18.5 -37l-6 -27h-40zM393 -16q-96 0 -162 24t-101.5 76t-39 100
t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q97 0 163 -24t101.5 -76t39 -100t3.5 -64q0 -71 -20 -163q-23 -110 -59 -187.5t-88.5 -126t-123 -71t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5
t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="878" 
d="M448 949h56l-13 -63h-57zM662 949h56l-14 -63h-56zM393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q97 0 163 -24t101.5 -76t39 -100t3.5 -64q0 -71 -20 -163q-23 -110 -59 -187.5t-88.5 -126t-123 -71
t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M89 60l256 220l-159 216l40 34l159 -216l254 218l32 -39l-256 -220l162 -220l-41 -33l-161 219l-254 -218z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="878" 
d="M24 21l92 86q-28 56 -28 142q0 71 19 162q24 110 59.5 187.5t88 126t122.5 71t163 22.5q177 0 250 -82l89 82l31 -37l-93 -85q29 -56 29 -143q0 -71 -19 -162q-23 -110 -59 -187.5t-88.5 -126t-123 -71t-163.5 -22.5q-88 0 -149.5 19.5t-99.5 62.5l-89 -82zM167 401
q-19 -85 -19 -148q0 -60 17 -101l579 541q-30 35 -82 51.5t-127 16.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5zM398 41q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q17 84 17.5 116t0.5 36q0 56 -16 97l-578 -542q29 -35 80.5 -51t126.5 -16z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="844" 
d="M432 987h63l79 -140h-53zM392 -16q-178 0 -233 60.5t-55 146.5q0 44 12 98l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h60l-112 -527q-16 -77 -41 -132t-64.5 -90.5t-96.5 -52
t-138 -16.5z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="844" 
d="M650 987h72l-149 -140h-62zM392 -16q-178 0 -233 60.5t-55 146.5q0 44 12 98l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h60l-112 -527q-16 -77 -41 -132t-64.5 -90.5t-96.5 -52
t-138 -16.5z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="844" 
d="M538 987h73l105 -140h-50l-93 112l-141 -112h-59zM392 -16q-178 0 -233 60.5t-55 146.5q0 44 12 98l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h60l-112 -527q-16 -77 -41 -132
t-64.5 -90.5t-96.5 -52t-138 -16.5z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="844" 
d="M431 949h56l-13 -63h-57zM645 949h56l-14 -63h-56zM392 -16q-178 0 -233 60.5t-55 146.5q0 44 12 98l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h60l-112 -527q-16 -77 -41 -132
t-64.5 -90.5t-96.5 -52t-138 -16.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="715" 
d="M587 987h72l-149 -140h-62zM338 318l-208 484h65l180 -427l362 427h74l-413 -485l-68 -317h-60z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="776" 
d="M296 802l-28 -133h223q78 0 134.5 -15.5t89.5 -50t39.5 -71t6.5 -59.5q0 -40 -11 -90q-16 -73 -40.5 -120t-62 -74.5t-89 -38.5t-122.5 -11h-281l-29 -139h-60l170 802h60zM256 614l-89 -420h268q56 0 98 7.5t72 28.5t50.5 59.5t33.5 101.5q9 44 9 78q0 15 -3.5 42.5
t-28.5 53.5t-69 37.5t-108 11.5h-233z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="697" 
d="M352 -3q-40 0 -67 2t-54 8l11 51q26 -5 48.5 -7.5t58.5 -2.5q49 0 84 8t59.5 27.5t40 51.5t26.5 80q7 34 7 61q0 50 -30 86t-133 36h-89l10 48h88q49 0 84 8t59.5 26.5t40 48.5t24.5 73q6 26 6 49q0 49 -34.5 82.5t-140.5 33.5q-55 0 -93 -9.5t-63.5 -29.5t-40 -50.5
t-23.5 -71.5l-129 -606h-57l129 606q11 53 30.5 92.5t51.5 66t80.5 40t118.5 13.5q136 0 181.5 -46t45.5 -112q0 -29 -7 -63q-18 -81 -57.5 -123.5t-108.5 -54.5q33 -6 57.5 -21t39 -41.5t16 -49t1.5 -30.5q0 -33 -9 -75q-12 -57 -32.5 -96.5t-52 -63.5t-75 -34.5
t-102.5 -10.5z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="700" 
d="M294 802h64l94 -170h-51zM290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -91q0 -13 3 -42
t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="700" 
d="M561 794h70l-179 -170h-57zM290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -91q0 -13 3 -42
t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="700" 
d="M429 802h65l91 -170h-51l-76 137l-135 -137h-56zM290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283
q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="700" 
d="M258 684q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41zM290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57
q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35
t-107.5 15q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="700" 
d="M319 754h55l-14 -68h-55zM529 754h55l-14 -68h-55zM290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283
q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="700" 
d="M427 617q-30 0 -53.5 6t-38.5 19t-19 28.5t-4 27.5q0 13 3 29q12 56 49 79.5t99 23.5q30 0 53.5 -6t38.5 -19t19.5 -28.5t4.5 -27.5q0 -13 -3 -28q-13 -57 -50 -80.5t-99 -23.5zM429 654q44 0 70.5 15.5t34.5 53.5q2 12 2 22q0 22 -16 35t-59 13t-70 -15.5t-35 -53.5
q-2 -12 -2 -21q0 -23 16.5 -36t58.5 -13zM290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -91
q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1121" 
d="M255 -16q-109 0 -152 40.5t-43 100.5q0 26 7 57q9 39 26.5 65.5t45 42t65.5 22t87 6.5h248l11 51q6 25 6 46q0 9 -2.5 26.5t-21.5 36.5t-57 28.5t-100 9.5q-55 0 -103.5 -5t-97.5 -13l9 50q26 5 48.5 8t45.5 5.5t48 3.5t55 1q110 0 162 -29t60 -86q39 60 99 87.5
t148 27.5q137 0 177.5 -58t40.5 -129q0 -36 -8 -77l-6 -30h-466q-9 -46 -9 -82q0 -16 3.5 -45t26 -57t61.5 -40t94 -12q69 0 118.5 6.5t98.5 16.5l-9 -50q-41 -11 -91.5 -18t-121.5 -7q-74 0 -126 19t-81 62q-33 -19 -65 -34.5t-67.5 -25.5t-75.5 -15.5t-88 -5.5zM846 523
q-102 0 -161 -48t-88 -157h410q7 36 7 66q0 10 -2 34t-21.5 50.5t-55 40.5t-89.5 14zM262 31q42 0 78.5 4.5t69 13.5t62.5 22.5t61 30.5q-13 36 -13 85q0 39 8 85h-235q-81 0 -120 -22t-51 -77q-5 -23 -5 -43q0 -45 32.5 -72t112.5 -27z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="599" 
d="M391 514q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106q-11 -51 -11 -91q0 -13 3 -42t25 -58t61.5 -42t94.5 -13q59 0 107 6t90 16l-10 -50q-33 -10 -80.5 -17t-101.5 -8l-146 -182h-58l150 184q-57 5 -98.5 24.5t-65.5 56t-26.5 70.5t-2.5 48q0 47 12 107
q15 72 41 123.5t65 85t91.5 49.5t121.5 16q63 0 114 -7.5t87 -16.5l-9 -50q-35 8 -84.5 15t-108.5 7z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="653" 
d="M286 802h64l94 -170h-51zM388 566q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-41 -11 -91.5 -18t-121.5 -7q-69 0 -119.5 16.5t-80 53t-34.5 74.5
t-5 58q0 47 13 106q31 143 106 208.5t210 65.5zM386 516q-102 0 -161.5 -47t-87.5 -154h410q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="653" 
d="M552 794h70l-179 -170h-57zM388 566q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-41 -11 -91.5 -18t-121.5 -7q-69 0 -119.5 16.5t-80 53t-34.5 74.5
t-5 58q0 47 13 106q31 143 106 208.5t210 65.5zM386 516q-102 0 -161.5 -47t-87.5 -154h410q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="653" 
d="M421 802h65l91 -170h-51l-76 137l-135 -137h-56zM388 566q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-41 -11 -91.5 -18t-121.5 -7q-69 0 -119.5 16.5
t-80 53t-34.5 74.5t-5 58q0 47 13 106q31 143 106 208.5t210 65.5zM386 516q-102 0 -161.5 -47t-87.5 -154h410q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="653" 
d="M311 754h55l-14 -68h-55zM521 754h55l-14 -68h-55zM388 566q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-41 -11 -91.5 -18t-121.5 -7
q-69 0 -119.5 16.5t-80 53t-34.5 74.5t-5 58q0 47 13 106q31 143 106 208.5t210 65.5zM386 516q-102 0 -161.5 -47t-87.5 -154h410q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="263" 
d="M76 802h64l94 -170h-51zM162 550h57l-117 -550h-56z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="263" 
d="M342 794h70l-179 -170h-57zM162 550h57l-117 -550h-56z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="263" 
d="M211 802h65l91 -170h-51l-76 137l-135 -137h-56zM162 550h57l-117 -550h-56z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="263" 
d="M101 754h55l-14 -68h-55zM311 754h55l-14 -68h-55zM162 550h57l-117 -550h-56z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="683" 
d="M304 654l154 62q-24 23 -54.5 44t-66.5 42h76q54 -31 93 -66l170 68l10 -37l-149 -60q38 -39 60.5 -83.5t31 -90t8.5 -88.5q0 -14 -2 -51t-17 -107q-18 -82 -44.5 -139.5t-65 -94t-89.5 -53t-117 -16.5q-73 0 -124.5 16.5t-81 53t-33.5 73t-4 54.5q0 47 13 109
q30 143 106 209.5t207 66.5q51 0 100 -11.5t87 -28.5q-9 45 -29 84.5t-55 76.5l-172 -70zM561 297q18 85 18 154v19q-38 18 -85.5 31.5t-104.5 13.5q-53 0 -95.5 -13.5t-75 -42.5t-55 -74.5t-36.5 -110.5q-10 -49 -10 -87q0 -61 34 -106t152 -45q56 0 97 14t72 45.5t52.5 81
t36.5 120.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="720" 
d="M269 684q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41zM207 511q51 23 113 39t124 16q121 0 162 -45t41 -103
q0 -24 -6 -51l-79 -367h-56l77 367q5 21 5 40q0 41 -28.5 73.5t-126.5 32.5q-66 0 -125.5 -17t-108.5 -37l-97 -459h-57l117 550h42z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="690" 
d="M290 802h64l94 -170h-51zM299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q70 0 121 -17t80.5 -53.5t35 -74t5.5 -57.5q0 -43 -12 -97q-16 -76 -42 -130t-65 -88t-92.5 -49.5t-123.5 -15.5zM303 36
q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="690" 
d="M556 794h70l-179 -170h-57zM299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q70 0 121 -17t80.5 -53.5t35 -74t5.5 -57.5q0 -43 -12 -97q-16 -76 -42 -130t-65 -88t-92.5 -49.5t-123.5 -15.5zM303 36
q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="690" 
d="M425 802h65l91 -170h-51l-76 137l-135 -137h-56zM299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q70 0 121 -17t80.5 -53.5t35 -74t5.5 -57.5q0 -43 -12 -97q-16 -76 -42 -130t-65 -88t-92.5 -49.5
t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="690" 
d="M254 684q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41zM299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5
q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q70 0 121 -17t80.5 -53.5t35 -74t5.5 -57.5q0 -43 -12 -97q-16 -76 -42 -130t-65 -88t-92.5 -49.5t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42
t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="690" 
d="M315 754h55l-14 -68h-55zM525 754h55l-14 -68h-55zM299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q70 0 121 -17t80.5 -53.5t35 -74t5.5 -57.5q0 -43 -12 -97q-16 -76 -42 -130t-65 -88t-92.5 -49.5
t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M398 508h65l-13 -66h-66zM133 300h508l-11 -49h-508zM313 109h66l-14 -67h-66z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="690" 
d="M12 17l71 61q-24 43 -24 108q0 44 11 98q16 77 42.5 130.5t65.5 87t92 49t122 15.5q65 0 112.5 -14t78.5 -45l71 59l27 -33l-71 -61q20 -36 22 -65.5t2 -39.5q0 -44 -12 -100q-16 -76 -42 -130t-65 -88t-92.5 -49.5t-123.5 -15.5q-63 0 -111 14.5t-78 44.5l-71 -59z
M128 276q-11 -49 -11 -87q0 -42 13 -71l408 351q-45 45 -149 45q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5zM303 36q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 41 -13 70l-408 -351q45 -45 149 -45z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="720" 
d="M305 802h64l94 -170h-51zM278 -16q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 459h57l-117 -550h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="720" 
d="M572 794h70l-179 -170h-57zM278 -16q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 459h57l-117 -550h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="720" 
d="M440 802h65l91 -170h-51l-76 137l-135 -137h-56zM278 -16q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 459h57l-117 -550h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="720" 
d="M330 754h55l-14 -68h-55zM540 754h55l-14 -68h-55zM278 -16q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 459h57l-117 -550h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="646" 
d="M534 794h70l-179 -170h-57zM-34 -161q30 -3 57 -3q30 0 54.5 5t44.5 16t37.5 29t35.5 44l49 73h-25l-128 547h57l116 -498h13l333 498h63l-421 -629q-25 -38 -48.5 -64t-49.5 -41.5t-57 -22.5t-71 -7q-18 0 -35.5 1t-34.5 4z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="700" 
d="M216 802h57l-61 -285q44 19 96.5 34t103.5 15q66 0 114.5 -17t77.5 -54t33.5 -74.5t4.5 -56.5q0 -46 -12 -105q-31 -145 -105.5 -210t-205.5 -65q-57 0 -111 16t-98 36l-50 -234h-57zM400 513q-56 0 -108 -15.5t-91 -33.5l-80 -377q36 -20 83.5 -35t107.5 -15q56 0 99 13
t74.5 40.5t53 71.5t34.5 105q11 51 11 91q0 13 -3 42t-25.5 58t-61.5 42t-94 13z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="646" 
d="M292 754h55l-14 -68h-55zM502 754h55l-14 -68h-55zM-34 -161q30 -3 57 -3q30 0 54.5 5t44.5 16t37.5 29t35.5 44l49 73h-25l-128 547h57l116 -498h13l333 498h63l-421 -629q-25 -38 -48.5 -64t-49.5 -41.5t-57 -22.5t-71 -7q-18 0 -35.5 1t-34.5 4z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="781" 
d="M372 933h319l-9 -39h-318zM449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68zM578 315l-73 437h-21l-256 -437h350z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="700" 
d="M306 741h286l-8 -41h-287zM290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -91q0 -13 3 -42
t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="781" 
d="M514 842q-46 0 -76 8t-46.5 25.5t-18.5 35t-2 25.5q0 23 6 51h46q-5 -24 -5 -42q0 -3 0.5 -14t11 -23.5t33 -18t58.5 -5.5t60.5 5.5t41 18t26 32t15.5 47.5h46q-8 -39 -20.5 -66.5t-34.5 -45t-56 -25.5t-85 -8zM449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68z
M578 315l-73 437h-21l-256 -437h350z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="700" 
d="M434 649q-86 0 -111.5 25t-25.5 62q0 20 6 45h46q-5 -22 -5 -38q0 -2 0.5 -11.5t10 -20t29.5 -15.5t53 -5t55.5 5t37 15.5t23 28t13.5 41.5h47q-8 -36 -20 -61t-32.5 -40.5t-51 -23t-75.5 -7.5zM290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104
q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15
q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="781" 
d="M449 802h107l134 -802h-26q-33 -23 -55 -39t-35.5 -29.5t-21 -26t-10.5 -27.5q-2 -8 -2 -15q0 -37 57 -37q22 0 38.5 2.5t29.5 4.5l-8 -38q-14 -5 -31 -7t-40 -2q-52 0 -72.5 18t-20.5 46q0 11 2 23q4 16 13 31.5t24.5 32t39 35t56.5 40.5l-41 249h-390l-153 -261h-68z
M578 315l-73 437h-21l-256 -437h350z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="700" 
d="M290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-12q-33 -23 -55 -39t-35.5 -29.5t-21 -26t-10.5 -27.5q-2 -8 -2 -15q0 -37 57 -37q22 0 38.5 2.5t29.5 4.5
l-8 -38q-14 -5 -31 -7t-40 -2q-52 0 -72.5 18t-20.5 46q0 11 2 23q8 34 39.5 66t98.5 76l-2 21q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13
t-74.5 -40.5t-53 -71.5t-34.5 -106z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="825" 
d="M655 987h72l-149 -140h-62zM535 818q137 0 209.5 -61t78.5 -177h-63q-8 91 -64 136t-166 45q-79 0 -137.5 -19.5t-101.5 -62.5t-73 -111.5t-51 -166.5q-19 -88 -19 -153q0 -7 1.5 -45t29.5 -80.5t81.5 -62t134.5 -19.5q104 0 172.5 41.5t115.5 140.5h66
q-28 -63 -62.5 -108.5t-77.5 -74.5t-97 -42.5t-121 -13.5q-96 0 -161.5 24t-101 75.5t-38.5 98.5t-3 62q0 72 20 166q24 110 59.5 187t87 126.5t121 72t160.5 22.5z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="599" 
d="M550 794h70l-179 -170h-57zM391 514q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106q-11 -51 -11 -91q0 -13 3 -42t25 -58t61.5 -42t94.5 -13q59 0 107 6t90 16l-10 -50q-35 -11 -85 -18t-106 -7q-69 0 -119.5 16.5t-80 53t-34.5 74.5t-5 58q0 47 13 106
q15 72 41 123.5t65 85t91.5 49.5t121.5 16q63 0 114 -7.5t87 -16.5l-9 -50q-35 8 -84.5 15t-108.5 7z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="825" 
d="M543 987h73l105 -140h-50l-93 112l-141 -112h-59zM535 818q137 0 209.5 -61t78.5 -177h-63q-8 91 -64 136t-166 45q-79 0 -137.5 -19.5t-101.5 -62.5t-73 -111.5t-51 -166.5q-19 -88 -19 -153q0 -7 1.5 -45t29.5 -80.5t81.5 -62t134.5 -19.5q104 0 172.5 41.5
t115.5 140.5h66q-28 -63 -62.5 -108.5t-77.5 -74.5t-97 -42.5t-121 -13.5q-96 0 -161.5 24t-101 75.5t-38.5 98.5t-3 62q0 72 20 166q24 110 59.5 187t87 126.5t121 72t160.5 22.5z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="599" 
d="M419 802h65l91 -170h-51l-76 137l-135 -137h-56zM391 514q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106q-11 -51 -11 -91q0 -13 3 -42t25 -58t61.5 -42t94.5 -13q59 0 107 6t90 16l-10 -50q-35 -11 -85 -18t-106 -7q-69 0 -119.5 16.5t-80 53t-34.5 74.5t-5 58
q0 47 13 106q15 72 41 123.5t65 85t91.5 49.5t121.5 16q63 0 114 -7.5t87 -16.5l-9 -50q-35 8 -84.5 15t-108.5 7z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="825" 
d="M538 949h66l-14 -63h-65zM535 818q137 0 209.5 -61t78.5 -177h-63q-8 91 -64 136t-166 45q-79 0 -137.5 -19.5t-101.5 -62.5t-73 -111.5t-51 -166.5q-19 -88 -19 -153q0 -7 1.5 -45t29.5 -80.5t81.5 -62t134.5 -19.5q104 0 172.5 41.5t115.5 140.5h66
q-28 -63 -62.5 -108.5t-77.5 -74.5t-97 -42.5t-121 -13.5q-96 0 -161.5 24t-101 75.5t-38.5 98.5t-3 62q0 72 20 166q24 110 59.5 187t87 126.5t121 72t160.5 22.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="599" 
d="M409 754h65l-14 -68h-65zM391 514q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106q-11 -51 -11 -91q0 -13 3 -42t25 -58t61.5 -42t94.5 -13q59 0 107 6t90 16l-10 -50q-35 -11 -85 -18t-106 -7q-69 0 -119.5 16.5t-80 53t-34.5 74.5t-5 58q0 47 13 106q15 72 41 123.5
t65 85t91.5 49.5t121.5 16q63 0 114 -7.5t87 -16.5l-9 -50q-35 8 -84.5 15t-108.5 7z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="825" 
d="M408 987h50l93 -112l142 112h58l-165 -140h-73zM535 818q137 0 209.5 -61t78.5 -177h-63q-8 91 -64 136t-166 45q-79 0 -137.5 -19.5t-101.5 -62.5t-73 -111.5t-51 -166.5q-19 -88 -19 -153q0 -7 1.5 -45t29.5 -80.5t81.5 -62t134.5 -19.5q104 0 172.5 41.5t115.5 140.5
h66q-28 -63 -62.5 -108.5t-77.5 -74.5t-97 -42.5t-121 -13.5q-96 0 -161.5 24t-101 75.5t-38.5 98.5t-3 62q0 72 20 166q24 110 59.5 187t87 126.5t121 72t160.5 22.5z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="599" 
d="M293 802h51l75 -137l136 137h56l-163 -170h-65zM391 514q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106q-11 -51 -11 -91q0 -13 3 -42t25 -58t61.5 -42t94.5 -13q59 0 107 6t90 16l-10 -50q-35 -11 -85 -18t-106 -7q-69 0 -119.5 16.5t-80 53t-34.5 74.5t-5 58
q0 47 13 106q15 72 41 123.5t65 85t91.5 49.5t121.5 16q63 0 114 -7.5t87 -16.5l-9 -50q-35 8 -84.5 15t-108.5 7z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="852" 
d="M407 987h50l93 -112l142 112h58l-165 -140h-73zM66 0l170 802h270q99 0 166 -23t103 -72.5t40 -97t4 -66.5q0 -66 -18 -151q-23 -106 -56.5 -180.5t-83 -121.5t-119 -68.5t-164.5 -21.5h-312zM137 55h249q77 0 135 18t100.5 59.5t71 108t48.5 160.5q17 81 17 140
q0 11 -2 48.5t-30.5 79t-82.5 60t-137 18.5h-221z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="700" 
d="M290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q57 0 111 -16t98 -36l61 288h57l-170 -802h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM851 802h56l-146 -256h-37zM129 283q-11 -51 -11 -90q0 -13 3 -42.5
t25.5 -59t61.5 -42.5t94 -13q56 0 107.5 15.5t90.5 34.5l81 379q-36 19 -83.5 34.5t-107.5 15.5q-56 0 -99 -13t-74.5 -41t-53 -72t-34.5 -106z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="852" 
d="M34 431h123l79 371h270q99 0 166 -23t103 -72.5t40 -97t4 -66.5q0 -66 -18 -151q-23 -106 -56.5 -180.5t-83 -121.5t-119 -68.5t-164.5 -21.5h-312l80 376h-123zM137 55h249q77 0 135 18t100.5 59.5t71 108t48.5 160.5q17 81 17 140q0 11 -2 48.5t-30.5 79t-82.5 60
t-137 18.5h-221l-68 -316h224l-11 -55h-224z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="700" 
d="M290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q57 0 111 -16t98 -36l29 136h-224l10 47h224l22 105h57l-22 -105h103l-10 -47h-103l-138 -650h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283
q-11 -51 -11 -90q0 -13 3 -42.5t25.5 -59t61.5 -42.5t94 -13q56 0 107.5 15.5t90.5 34.5l81 379q-36 19 -83.5 34.5t-107.5 15.5q-56 0 -99 -13t-74.5 -41t-53 -72t-34.5 -106z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="727" 
d="M370 933h319l-9 -39h-318zM236 802h517l-11 -55h-457l-68 -316h428l-12 -55h-427l-69 -321h458l-12 -55h-517z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="653" 
d="M298 741h286l-8 -41h-287zM388 566q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-41 -11 -91.5 -18t-121.5 -7q-69 0 -119.5 16.5t-80 53t-34.5 74.5
t-5 58q0 47 13 106q31 143 106 208.5t210 65.5zM386 516q-102 0 -161.5 -47t-87.5 -154h410q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="727" 
d="M512 842q-46 0 -76 8t-46.5 25.5t-18.5 35t-2 25.5q0 23 6 51h46q-5 -24 -5 -42q0 -3 0.5 -14t11 -23.5t33 -18t58.5 -5.5t60.5 5.5t41 18t26 32t15.5 47.5h46q-8 -39 -20.5 -66.5t-34.5 -45t-56 -25.5t-85 -8zM236 802h517l-11 -55h-457l-68 -316h428l-12 -55h-427
l-69 -321h458l-12 -55h-517z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="653" 
d="M426 649q-86 0 -111.5 25t-25.5 62q0 20 6 45h46q-5 -22 -5 -38q0 -2 0.5 -11.5t10 -20t29.5 -15.5t53 -5t55.5 5t37 15.5t23 28t13.5 41.5h47q-8 -36 -20 -61t-32.5 -40.5t-51 -23t-75.5 -7.5zM388 566q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467
q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-41 -11 -91.5 -18t-121.5 -7q-69 0 -119.5 16.5t-80 53t-34.5 74.5t-5 58q0 47 13 106q31 143 106 208.5t210 65.5zM386 516q-102 0 -161.5 -47t-87.5 -154h410
q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="727" 
d="M500 949h66l-14 -63h-65zM236 802h517l-11 -55h-457l-68 -316h428l-12 -55h-427l-69 -321h458l-12 -55h-517z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="653" 
d="M411 754h65l-14 -68h-65zM388 566q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-41 -11 -91.5 -18t-121.5 -7q-69 0 -119.5 16.5t-80 53t-34.5 74.5
t-5 58q0 47 13 106q31 143 106 208.5t210 65.5zM386 516q-102 0 -161.5 -47t-87.5 -154h410q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="727" 
d="M236 802h517l-11 -55h-457l-68 -316h428l-12 -55h-427l-69 -321h458l-12 -55h-37q-33 -23 -55 -39t-35.5 -29.5t-21 -26t-10.5 -27.5q-2 -8 -2 -15q0 -37 57 -37q22 0 38.5 2.5t29.5 4.5l-8 -38q-14 -5 -31 -7t-40 -2q-52 0 -72.5 18t-20.5 46q0 11 2 23q7 31 33 59.5
t83 67.5h-427z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="653" 
d="M350 -122q-2 -8 -2 -15q0 -37 57 -37q22 0 38.5 2.5t29.5 4.5l-8 -38q-14 -5 -31 -7t-40 -2q-52 0 -72.5 18t-20.5 46q0 11 2 23q6 29 29 55t71 61q-23 -2 -49 -3.5t-56 -1.5q-69 0 -119.5 16.5t-80 53t-34.5 74.5t-5 58q0 47 13 106q31 143 106 208.5t210 65.5
q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-9 -2 -19 -4.5t-20 -4.5q-33 -23 -55 -39t-35.5 -29.5t-21 -26t-10.5 -27.5zM386 516q-102 0 -161.5 -47
t-87.5 -154h410q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="727" 
d="M370 987h50l93 -112l142 112h58l-165 -140h-73zM236 802h517l-11 -55h-457l-68 -316h428l-12 -55h-427l-69 -321h458l-12 -55h-517z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="653" 
d="M295 802h51l75 -137l136 137h56l-163 -170h-65zM388 566q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-41 -11 -91.5 -18t-121.5 -7q-69 0 -119.5 16.5
t-80 53t-34.5 74.5t-5 58q0 47 13 106q31 143 106 208.5t210 65.5zM386 516q-102 0 -161.5 -47t-87.5 -154h410q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="853" 
d="M548 987h73l105 -140h-50l-93 112l-141 -112h-59zM539 818q139 0 212.5 -61t79.5 -177h-63q-8 91 -65 136t-169 45q-79 0 -139 -19.5t-103.5 -62.5t-73.5 -111.5t-51 -166.5q-18 -87 -18 -150q0 -9 1.5 -47.5t29.5 -81t82.5 -62t136.5 -19.5q72 0 125.5 13t92.5 43t64 79
t40 121l11 53h-272l12 55h332l-25 -114q-18 -84 -48.5 -142.5t-77 -95t-110.5 -53t-148 -16.5q-96 0 -162 24t-102 75.5t-39.5 100t-3.5 65.5q0 70 19 161q24 110 59.5 187t87.5 126.5t122 72t163 22.5z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="698" 
d="M439 802h65l91 -170h-51l-76 137l-135 -137h-56zM289 -11q-65 0 -113.5 17t-77.5 53.5t-33.5 74t-4.5 56.5q0 46 12 104q31 143 105.5 207.5t204.5 64.5q59 0 113.5 -16t98.5 -37l19 37h42l-112 -526q-13 -63 -35.5 -106.5t-57.5 -71t-84.5 -40t-117.5 -12.5q-35 0 -63 1
t-54 4t-51 7t-54 10l11 51q30 -6 55.5 -10t50 -7t49.5 -4t55 -1q57 0 97.5 9.5t68.5 30.5t45.5 55t27.5 82l4 17q-44 -20 -97 -35t-104 -15zM130 286q-12 -52 -12 -92q0 -12 3 -40.5t25.5 -57.5t61.5 -42t93 -13q57 0 109 15.5t90 33.5l80 375q-37 19 -84.5 34t-106.5 15
q-55 0 -98 -13t-74.5 -40.5t-52.5 -70.5t-34 -104z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="853" 
d="M555 842q-46 0 -76 8t-46.5 25.5t-18.5 35t-2 25.5q0 23 6 51h46q-5 -24 -5 -42q0 -3 0.5 -14t11 -23.5t33 -18t58.5 -5.5t60.5 5.5t41 18t26 32t15.5 47.5h46q-8 -39 -20.5 -66.5t-34.5 -45t-56 -25.5t-85 -8zM539 818q139 0 212.5 -61t79.5 -177h-63q-8 91 -65 136
t-169 45q-79 0 -139 -19.5t-103.5 -62.5t-73.5 -111.5t-51 -166.5q-18 -87 -18 -150q0 -9 1.5 -47.5t29.5 -81t82.5 -62t136.5 -19.5q72 0 125.5 13t92.5 43t64 79t40 121l11 53h-272l12 55h332l-25 -114q-18 -84 -48.5 -142.5t-77 -95t-110.5 -53t-148 -16.5q-96 0 -162 24
t-102 75.5t-39.5 100t-3.5 65.5q0 70 19 161q24 110 59.5 187t87.5 126.5t122 72t163 22.5z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="698" 
d="M444 649q-86 0 -111.5 25t-25.5 62q0 20 6 45h46q-5 -22 -5 -38q0 -2 0.5 -11.5t10 -20t29.5 -15.5t53 -5t55.5 5t37 15.5t23 28t13.5 41.5h47q-8 -36 -20 -61t-32.5 -40.5t-51 -23t-75.5 -7.5zM289 -11q-65 0 -113.5 17t-77.5 53.5t-33.5 74t-4.5 56.5q0 46 12 104
q31 143 105.5 207.5t204.5 64.5q59 0 113.5 -16t98.5 -37l19 37h42l-112 -526q-13 -63 -35.5 -106.5t-57.5 -71t-84.5 -40t-117.5 -12.5q-35 0 -63 1t-54 4t-51 7t-54 10l11 51q30 -6 55.5 -10t50 -7t49.5 -4t55 -1q57 0 97.5 9.5t68.5 30.5t45.5 55t27.5 82l4 17
q-44 -20 -97 -35t-104 -15zM130 286q-12 -52 -12 -92q0 -12 3 -40.5t25.5 -57.5t61.5 -42t93 -13q57 0 109 15.5t90 33.5l80 375q-37 19 -84.5 34t-106.5 15q-55 0 -98 -13t-74.5 -40.5t-52.5 -70.5t-34 -104z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="853" 
d="M543 949h66l-14 -63h-65zM539 818q139 0 212.5 -61t79.5 -177h-63q-8 91 -65 136t-169 45q-79 0 -139 -19.5t-103.5 -62.5t-73.5 -111.5t-51 -166.5q-18 -87 -18 -150q0 -9 1.5 -47.5t29.5 -81t82.5 -62t136.5 -19.5q72 0 125.5 13t92.5 43t64 79t40 121l11 53h-272
l12 55h332l-25 -114q-18 -84 -48.5 -142.5t-77 -95t-110.5 -53t-148 -16.5q-96 0 -162 24t-102 75.5t-39.5 100t-3.5 65.5q0 70 19 161q24 110 59.5 187t87.5 126.5t122 72t163 22.5z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="698" 
d="M429 754h65l-14 -68h-65zM289 -11q-65 0 -113.5 17t-77.5 53.5t-33.5 74t-4.5 56.5q0 46 12 104q31 143 105.5 207.5t204.5 64.5q59 0 113.5 -16t98.5 -37l19 37h42l-112 -526q-13 -63 -35.5 -106.5t-57.5 -71t-84.5 -40t-117.5 -12.5q-35 0 -63 1t-54 4t-51 7t-54 10
l11 51q30 -6 55.5 -10t50 -7t49.5 -4t55 -1q57 0 97.5 9.5t68.5 30.5t45.5 55t27.5 82l4 17q-44 -20 -97 -35t-104 -15zM130 286q-12 -52 -12 -92q0 -12 3 -40.5t25.5 -57.5t61.5 -42t93 -13q57 0 109 15.5t90 33.5l80 375q-37 19 -84.5 34t-106.5 15q-55 0 -98 -13
t-74.5 -40.5t-52.5 -70.5t-34 -104z" />
    <glyph glyph-name="uni0122" unicode="&#x122;" horiz-adv-x="853" 
d="M539 818q139 0 212.5 -61t79.5 -177h-63q-8 91 -65 136t-169 45q-79 0 -139 -19.5t-103.5 -62.5t-73.5 -111.5t-51 -166.5q-18 -87 -18 -150q0 -9 1.5 -47.5t29.5 -81t82.5 -62t136.5 -19.5q72 0 125.5 13t92.5 43t64 79t40 121l11 53h-272l12 55h332l-25 -114
q-18 -84 -48.5 -142.5t-77 -95t-110.5 -53t-148 -16.5q-96 0 -162 24t-102 75.5t-39.5 100t-3.5 65.5q0 70 19 161q24 110 59.5 187t87.5 126.5t122 72t163 22.5zM335 -53h74l-161 -145h-58z" />
    <glyph glyph-name="uni0123" unicode="&#x123;" horiz-adv-x="698" 
d="M469 632h-69l178 170h57zM289 -11q-65 0 -113.5 17t-77.5 53.5t-33.5 74t-4.5 56.5q0 46 12 104q31 143 105.5 207.5t204.5 64.5q59 0 113.5 -16t98.5 -37l19 37h42l-112 -526q-13 -63 -35.5 -106.5t-57.5 -71t-84.5 -40t-117.5 -12.5q-35 0 -63 1t-54 4t-51 7t-54 10
l11 51q30 -6 55.5 -10t50 -7t49.5 -4t55 -1q57 0 97.5 9.5t68.5 30.5t45.5 55t27.5 82l4 17q-44 -20 -97 -35t-104 -15zM130 286q-12 -52 -12 -92q0 -12 3 -40.5t25.5 -57.5t61.5 -42t93 -13q57 0 109 15.5t90 33.5l80 375q-37 19 -84.5 34t-106.5 15q-55 0 -98 -13
t-74.5 -40.5t-52.5 -70.5t-34 -104z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="866" 
d="M549 987h73l105 -140h-50l-93 112l-141 -112h-59zM236 802h60l-78 -370h500l78 370h60l-171 -802h-59l80 377h-500l-80 -377h-60z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="720" 
d="M476 987h73l105 -140h-50l-93 112l-141 -112h-59zM216 802h57l-62 -289q51 22 112 37.5t121 15.5q121 0 161.5 -46t40.5 -104q0 -23 -5 -49l-79 -367h-56l77 367q5 21 5 40q0 41 -28.5 73.5t-126.5 32.5q-66 0 -125 -14t-107 -34l-99 -465h-57z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="866" 
d="M191 592h-123l12 55h123l33 155h60l-33 -155h500l33 155h60l-33 -155h123l-12 -55h-123l-126 -592h-59l80 377h-500l-80 -377h-60zM718 432l33 160h-500l-33 -160h500z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="720" 
d="M184 650h-103l10 47h103l22 105h57l-22 -105h223l-10 -47h-223l-30 -137q51 22 112 37.5t121 15.5q121 0 161.5 -46t40.5 -104q0 -23 -5 -49l-79 -367h-56l77 367q5 21 5 40q0 41 -28.5 73.5t-126.5 32.5q-66 0 -125 -14t-107 -34l-99 -465h-57z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="307" 
d="M107 888q10 43 32 64.5t63 21.5q28 0 51.5 -12.5t44.5 -28t41 -28t40 -12.5q25 0 38 13t18 36l5 27h41l-6 -30q-8 -41 -31 -63.5t-64 -22.5q-28 0 -52 13t-45 28t-40.5 28t-39.5 13q-25 0 -37.5 -13t-18.5 -37l-6 -27h-40zM236 802h60l-170 -802h-60z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="263" 
d="M40 684q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41zM162 550h57l-117 -550h-56z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="307" 
d="M135 933h319l-9 -39h-318zM236 802h60l-170 -802h-60z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="263" 
d="M88 741h286l-8 -41h-287zM162 550h57l-117 -550h-56z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="307" 
d="M277 842q-46 0 -76 8t-46.5 25.5t-18.5 35t-2 25.5q0 23 6 51h46q-5 -24 -5 -42q0 -3 0.5 -14t11 -23.5t33 -18t58.5 -5.5t60.5 5.5t41 18t26 32t15.5 47.5h46q-8 -39 -20.5 -66.5t-34.5 -45t-56 -25.5t-85 -8zM236 802h60l-170 -802h-60z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="263" 
d="M216 649q-86 0 -111.5 25t-25.5 62q0 20 6 45h46q-5 -22 -5 -38q0 -2 0.5 -11.5t10 -20t29.5 -15.5t53 -5t55.5 5t37 15.5t23 28t13.5 41.5h47q-8 -36 -20 -61t-32.5 -40.5t-51 -23t-75.5 -7.5zM162 550h57l-117 -550h-56z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="307" 
d="M-11 -122q-2 -8 -2 -15q0 -37 57 -37q22 0 38.5 2.5t29.5 4.5l-8 -38q-14 -5 -31 -7t-40 -2q-52 0 -72.5 18t-20.5 46q0 11 2 23q7 32 35.5 62t89.5 72l169 795h60l-170 -802h-15q-33 -23 -55 -39t-35.5 -29.5t-21 -26t-10.5 -27.5z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="263" 
d="M207 782h65l-14 -69h-65zM-33 -122q-2 -8 -2 -15q0 -37 57 -37q22 0 38.5 2.5t29.5 4.5l-8 -38q-14 -5 -31 -7t-40 -2q-52 0 -72.5 18t-20.5 46q0 11 2 23q7 32 36 62.5t92 72.5l114 542h57l-117 -550h-13q-33 -23 -55 -39t-35.5 -29.5t-21 -26t-10.5 -27.5z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="307" 
d="M265 949h66l-14 -63h-65zM236 802h60l-170 -802h-60z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="263" 
d="M162 550h57l-117 -550h-56z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="671" 
d="M236 802h60l-170 -802h-60zM326 -14q-19 0 -33 2t-28 5l10 52q23 -4 50 -4q37 0 61.5 7.5t41 25t26.5 46t19 69.5l130 613h60l-131 -617q-12 -54 -26.5 -92t-38 -62t-57.5 -34.5t-84 -10.5z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="525" 
d="M207 782h65l-14 -69h-65zM470 782h66l-15 -69h-65zM162 550h57l-117 -550h-56zM96 -159q14 -2 35 -3t35 -1q57 0 88 26.5t45 94.5l126 592h57l-126 -592q-10 -46 -24.5 -78.5t-36.5 -53.5t-52.5 -30.5t-72.5 -9.5q-20 0 -44 1t-41 4z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="364" 
d="M328 987h73l105 -140h-50l-93 112l-141 -112h-59zM19 -14q-19 0 -33 2t-28 5l10 52q23 -4 50 -4q37 0 61.5 7.5t41 25t26.5 46t19 69.5l130 613h60l-131 -617q-12 -54 -26.5 -92t-38 -62t-57.5 -34.5t-84 -10.5z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="263" 
d="M211 802h65l91 -170h-51l-76 137l-135 -137h-56zM-168 -164q14 -2 35.5 -3t35.5 -1q57 0 88 27.5t46 98.5l125 592h57l-125 -592q-10 -46 -25 -78.5t-37 -53.5t-52.5 -30.5t-72.5 -9.5q-20 0 -44 1t-41 4z" />
    <glyph glyph-name="uni0136" unicode="&#x136;" horiz-adv-x="727" 
d="M236 802h59l-76 -358l500 358h86l-526 -379l360 -423h-76l-350 417l-89 -417h-58zM266 -53h74l-161 -145h-58z" />
    <glyph glyph-name="uni0137" unicode="&#x137;" horiz-adv-x="627" 
d="M216 802h57l-100 -468l371 216h86l-397 -234l298 -316h-74l-288 313l-67 -313h-57zM210 -53h74l-161 -145h-58z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="627" 
d="M163 550h56l-46 -216l371 216h92l-399 -235l297 -315h-77l-288 313l-67 -313h-57z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="650" 
d="M599 987h72l-149 -140h-62zM236 802h60l-159 -752h429l-10 -50h-490z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="263" 
d="M360 987h72l-149 -140h-62zM216 802h57l-171 -802h-57z" />
    <glyph glyph-name="uni013B" unicode="&#x13b;" horiz-adv-x="650" 
d="M236 802h60l-159 -752h429l-10 -50h-490zM274 -53h74l-161 -145h-58z" />
    <glyph glyph-name="uni013C" unicode="&#x13c;" horiz-adv-x="263" 
d="M216 802h57l-171 -802h-57zM28 -53h74l-161 -145h-58z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="650" 
d="M236 802h60l-159 -752h429l-10 -50h-490zM454 802h56l-146 -256h-37z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="263" 
d="M216 802h57l-171 -802h-57zM415 802h56l-146 -256h-37z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="650" 
d="M236 802h60l-159 -752h429l-10 -50h-490zM461 466h76l-15 -75h-76z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="339" 
d="M216 802h57l-171 -802h-57zM272 314h76l-15 -75h-76z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="650" 
d="M139 345l-136 -61l14 64l136 61l83 393h60l-77 -363l244 109l-14 -64l-243 -109l-69 -325h429l-10 -50h-490z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="263" 
d="M105 280l-114 -51l11 53l114 51l100 469h57l-94 -440l114 51l-11 -54l-114 -51l-66 -308h-57z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="876" 
d="M666 987h72l-149 -140h-62zM234 802h76l342 -727l155 727h60l-171 -802h-77l-340 728l-155 -728h-60z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="720" 
d="M571 794h70l-179 -170h-57zM207 511q51 23 113 39t124 16q121 0 162 -45t41 -103q0 -24 -6 -51l-79 -367h-56l77 367q5 21 5 40q0 41 -28.5 73.5t-126.5 32.5q-66 0 -125.5 -17t-108.5 -37l-97 -459h-57l117 550h42z" />
    <glyph glyph-name="uni0145" unicode="&#x145;" horiz-adv-x="876" 
d="M234 802h76l342 -727l155 727h60l-171 -802h-77l-340 728l-155 -728h-60zM319 -53h74l-161 -145h-58z" />
    <glyph glyph-name="uni0146" unicode="&#x146;" horiz-adv-x="720" 
d="M207 511q51 23 113 39t124 16q121 0 162 -45t41 -103q0 -24 -6 -51l-79 -367h-56l77 367q5 21 5 40q0 41 -28.5 73.5t-126.5 32.5q-66 0 -125.5 -17t-108.5 -37l-97 -459h-57l117 550h42zM257 -53h74l-161 -145h-58z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="876" 
d="M419 987h50l93 -112l142 112h58l-165 -140h-73zM234 802h76l342 -727l155 727h60l-171 -802h-77l-340 728l-155 -728h-60z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="720" 
d="M314 802h51l75 -137l136 137h56l-163 -170h-65zM207 511q51 23 113 39t124 16q121 0 162 -45t41 -103q0 -24 -6 -51l-79 -367h-56l77 367q5 21 5 40q0 41 -28.5 73.5t-126.5 32.5q-66 0 -125.5 -17t-108.5 -37l-97 -459h-57l117 550h42z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="835" 
d="M309 802h66l-146 -269h-56zM323 511q51 23 113 39t124 16q121 0 162 -45t41 -103q0 -24 -6 -51l-79 -367h-56l77 367q5 21 5 40q0 41 -28.5 73.5t-126.5 32.5q-66 0 -125.5 -17t-108.5 -37l-97 -459h-57l117 550h42z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="876" 
d="M234 802h78l344 -709l150 709h60l-179 -845q-10 -47 -24.5 -79.5t-38 -53t-58 -29.5t-82.5 -9q-23 0 -36.5 1.5t-25.5 3.5l10 51q20 -4 51 -4q36 0 60 6t40.5 20t26.5 37t17 58l9 41l-356 735l-156 -735h-60z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="720" 
d="M293 -158q14 -2 35.5 -3t35.5 -1q57 0 88 26t45 94l86 409q5 23 5 42q0 42 -28.5 75.5t-126.5 33.5q-66 0 -125 -16.5t-107 -36.5l-99 -465h-57l117 550h42l3 -39q51 23 113 39t124 16q121 0 161.5 -46t40.5 -104q0 -23 -5 -49l-87 -409q-10 -46 -25 -78.5t-36.5 -53.5
t-52 -30.5t-72.5 -9.5q-20 0 -44.5 1t-41.5 4z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="878" 
d="M420 933h319l-9 -39h-318zM393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q97 0 163 -24t101.5 -76t39 -100t3.5 -64q0 -71 -20 -163q-23 -110 -59 -187.5t-88.5 -126t-123 -71t-163.5 -22.5zM398 41
q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="690" 
d="M302 741h286l-8 -41h-287zM299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q70 0 121 -17t80.5 -53.5t35 -74t5.5 -57.5q0 -43 -12 -97q-16 -76 -42 -130t-65 -88t-92.5 -49.5t-123.5 -15.5zM303 36
q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="878" 
d="M562 842q-46 0 -76 8t-46.5 25.5t-18.5 35t-2 25.5q0 23 6 51h46q-5 -24 -5 -42q0 -3 0.5 -14t11 -23.5t33 -18t58.5 -5.5t60.5 5.5t41 18t26 32t15.5 47.5h46q-8 -39 -20.5 -66.5t-34.5 -45t-56 -25.5t-85 -8zM393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64
q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q97 0 163 -24t101.5 -76t39 -100t3.5 -64q0 -71 -20 -163q-23 -110 -59 -187.5t-88.5 -126t-123 -71t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5
t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="690" 
d="M430 649q-86 0 -111.5 25t-25.5 62q0 20 6 45h46q-5 -22 -5 -38q0 -2 0.5 -11.5t10 -20t29.5 -15.5t53 -5t55.5 5t37 15.5t23 28t13.5 41.5h47q-8 -36 -20 -61t-32.5 -40.5t-51 -23t-75.5 -7.5zM299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97
q16 77 42.5 130.5t65.5 87t92 49t122 15.5q70 0 121 -17t80.5 -53.5t35 -74t5.5 -57.5q0 -43 -12 -97q-16 -76 -42 -130t-65 -88t-92.5 -49.5t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5
q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="878" 
d="M571 987h70l-145 -140h-59zM761 987h70l-145 -140h-59zM393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q97 0 163 -24t101.5 -76t39 -100t3.5 -64q0 -71 -20 -163q-23 -110 -59 -187.5t-88.5 -126t-123 -71
t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="690" 
d="M460 802h67l-170 -170h-54zM647 802h66l-170 -170h-55zM299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q70 0 121 -17t80.5 -53.5t35 -74t5.5 -57.5q0 -43 -12 -97q-16 -76 -42 -130t-65 -88t-92.5 -49.5
t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1273" 
d="M106 410q23 106 56.5 180.5t83.5 121.5t119.5 68.5t164.5 21.5h770l-12 -55h-458l-68 -318h428l-11 -50h-427l-70 -329h458l-11 -50h-727q-99 0 -166 23t-103.5 72.5t-40.5 97t-4 66.5q0 66 18 151zM622 50l148 697h-241q-82 0 -141.5 -18.5t-102 -59.5t-70.5 -107
t-48 -161q-18 -84 -18 -146q0 -9 1.5 -46.5t30 -79t83.5 -60.5t137 -19h221z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1149" 
d="M298 -16q-69 0 -119.5 17.5t-80 54t-34.5 74t-5 57.5q0 43 11 97q16 77 42 130.5t65 87t91 49t121 15.5q98 0 157 -36.5t73 -116.5q39 79 104.5 116t162.5 37q137 0 177.5 -57.5t40.5 -127.5q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t62 -39.5
t94.5 -12q35 0 63.5 1.5t54.5 4.5t50 7t48 10l-8 -50q-40 -11 -90 -18t-120 -7q-98 0 -156.5 35.5t-71.5 117.5q-39 -81 -104.5 -117t-167.5 -36zM883 516q-103 0 -162.5 -47t-87.5 -154h410q7 35 7 64q0 10 -2 33.5t-21 49.5t-54.5 40t-89.5 14zM303 36q58 0 101 13.5
t74 42.5t52 74.5t34 109.5q10 48 10 86q0 62 -36 107t-149 45q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-10 -49 -10 -87q0 -62 35.5 -107.5t149.5 -45.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="805" 
d="M631 987h72l-149 -140h-62zM236 802h283q81 0 137.5 -15t89 -48t38 -68.5t5.5 -57.5q0 -39 -10 -89q-13 -60 -32.5 -101.5t-47 -69.5t-64.5 -42.5t-86 -20.5l112 -290h-67l-104 286h-303l-61 -286h-60zM285 747l-86 -406h267q56 0 97 7t71 27.5t50.5 58.5t33.5 99
q9 41 9 73q0 15 -4 42t-29 52t-69.5 36t-109.5 11h-230z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="389" 
d="M447 794h70l-179 -170h-57zM162 550h42l3 -38q45 20 97 33t110 13l-11 -54q-57 -1 -108 -13t-95 -29l-98 -462h-57z" />
    <glyph glyph-name="uni0156" unicode="&#x156;" horiz-adv-x="805" 
d="M236 802h283q81 0 137.5 -15t89 -48t38 -68.5t5.5 -57.5q0 -39 -10 -89q-13 -60 -32.5 -101.5t-47 -69.5t-64.5 -42.5t-86 -20.5l112 -290h-67l-104 286h-303l-61 -286h-60zM285 747l-86 -406h267q56 0 97 7t71 27.5t50.5 58.5t33.5 99q9 41 9 73q0 15 -4 42t-29 52
t-69.5 36t-109.5 11h-230zM297 -53h74l-161 -145h-58z" />
    <glyph glyph-name="uni0157" unicode="&#x157;" horiz-adv-x="389" 
d="M162 550h42l3 -38q45 20 97 33t110 13l-11 -54q-57 -1 -108 -13t-95 -29l-98 -462h-57zM27 -53h74l-161 -145h-58z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="805" 
d="M384 987h50l93 -112l142 112h58l-165 -140h-73zM236 802h283q81 0 137.5 -15t89 -48t38 -68.5t5.5 -57.5q0 -39 -10 -89q-13 -60 -32.5 -101.5t-47 -69.5t-64.5 -42.5t-86 -20.5l112 -290h-67l-104 286h-303l-61 -286h-60zM285 747l-86 -406h267q56 0 97 7t71 27.5
t50.5 58.5t33.5 99q9 41 9 73q0 15 -4 42t-29 52t-69.5 36t-109.5 11h-230z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="389" 
d="M190 802h51l75 -137l136 137h56l-163 -170h-65zM162 550h42l3 -38q45 20 97 33t110 13l-11 -54q-57 -1 -108 -13t-95 -29l-98 -462h-57z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="766" 
d="M613 987h72l-149 -140h-62zM65 83q50 -16 125.5 -28.5t166.5 -12.5q59 0 100.5 8t70 26t45.5 46.5t26 68.5q7 31 7 56q0 7 -1.5 25t-22.5 40t-63 39t-113 32q-83 17 -134.5 38.5t-77 51.5t-27.5 55t-2 34q0 34 9 76q10 42 29 75.5t53.5 56.5t87.5 35t132 12
q92 0 155.5 -10t114.5 -25l-12 -59q-50 16 -116 26t-148 10q-61 0 -103 -8t-69 -24t-41.5 -39.5t-21.5 -55.5q-7 -34 -7 -60q0 -6 1 -23.5t20.5 -39t60 -37.5t107.5 -30q87 -18 140.5 -39t80.5 -51t29 -55t2 -35q0 -34 -9 -78q-12 -53 -33.5 -91t-57.5 -62t-89 -35.5
t-128 -11.5q-100 0 -174 11t-125 28z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="614" 
d="M518 794h70l-179 -170h-57zM32 61q40 -9 101.5 -18t126.5 -9q49 0 84 4t58.5 14.5t37 29t19.5 47.5q5 26 5 43q0 6 -1 18t-17 25.5t-49.5 22.5t-89.5 19q-67 11 -108 23t-62 31t-23 37t-2 28q0 23 6 54q8 38 24 64t45 42t74 23t110 7q66 0 118 -7.5t94 -18.5l-11 -49
q-39 10 -87 17.5t-114 7.5q-52 0 -87.5 -4.5t-58 -15t-34.5 -28t-18 -43.5q-4 -22 -4 -38q0 -5 1 -16.5t17 -23.5t49.5 -20.5t88.5 -17.5q67 -11 109 -23.5t63 -32.5t23 -39t2 -27q0 -27 -8 -64q-8 -39 -26 -65.5t-48 -43t-74 -23.5t-105 -7q-72 0 -135 8.5t-104 20.5z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="766" 
d="M501 987h73l105 -140h-50l-93 112l-141 -112h-59zM65 83q50 -16 125.5 -28.5t166.5 -12.5q59 0 100.5 8t70 26t45.5 46.5t26 68.5q7 31 7 56q0 7 -1.5 25t-22.5 40t-63 39t-113 32q-83 17 -134.5 38.5t-77 51.5t-27.5 55t-2 34q0 34 9 76q10 42 29 75.5t53.5 56.5
t87.5 35t132 12q92 0 155.5 -10t114.5 -25l-12 -59q-50 16 -116 26t-148 10q-61 0 -103 -8t-69 -24t-41.5 -39.5t-21.5 -55.5q-7 -34 -7 -60q0 -6 1 -23.5t20.5 -39t60 -37.5t107.5 -30q87 -18 140.5 -39t80.5 -51t29 -55t2 -35q0 -34 -9 -78q-12 -53 -33.5 -91t-57.5 -62
t-89 -35.5t-128 -11.5q-100 0 -174 11t-125 28z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="614" 
d="M387 802h65l91 -170h-51l-76 137l-135 -137h-56zM32 61q40 -9 101.5 -18t126.5 -9q49 0 84 4t58.5 14.5t37 29t19.5 47.5q5 26 5 43q0 6 -1 18t-17 25.5t-49.5 22.5t-89.5 19q-67 11 -108 23t-62 31t-23 37t-2 28q0 23 6 54q8 38 24 64t45 42t74 23t110 7q66 0 118 -7.5
t94 -18.5l-11 -49q-39 10 -87 17.5t-114 7.5q-52 0 -87.5 -4.5t-58 -15t-34.5 -28t-18 -43.5q-4 -22 -4 -38q0 -5 1 -16.5t17 -23.5t49.5 -20.5t88.5 -17.5q67 -11 109 -23.5t63 -32.5t23 -39t2 -27q0 -27 -8 -64q-8 -39 -26 -65.5t-48 -43t-74 -23.5t-105 -7
q-72 0 -135 8.5t-104 20.5z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="766" 
d="M65 83q50 -16 125.5 -28.5t166.5 -12.5q59 0 100.5 8t70 26t45.5 46.5t26 68.5q7 31 7 56q0 7 -1.5 25t-22.5 40t-63 39t-113 32q-83 17 -134.5 38.5t-77 51.5t-27.5 55t-2 34q0 34 9 76q10 42 29 75.5t53.5 56.5t87.5 35t132 12q92 0 155.5 -10t114.5 -25l-12 -59
q-50 16 -116 26t-148 10q-61 0 -103 -8t-69 -24t-41.5 -39.5t-21.5 -55.5q-7 -34 -7 -60q0 -6 1 -23.5t20.5 -39t60 -37.5t107.5 -30q87 -18 140.5 -39t80.5 -51t29 -55t2 -35q0 -34 -9 -78q-15 -67 -44.5 -107t-71.5 -60.5t-95 -26.5t-115 -6l-61 -53q6 1 12 1h12
q54 0 72.5 -15t18.5 -41q0 -10 -2 -21q-4 -18 -11 -31.5t-20.5 -21.5t-35 -12t-54.5 -4q-24 0 -43.5 2.5t-33.5 6.5l8 32q30 -8 71 -8q47 0 62.5 9t19.5 32q2 7 2 12q0 12 -9 21t-56 9q-19 0 -37 -1.5t-31 -3.5l7 29l68 59q-78 3 -137.5 13.5t-102.5 24.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="614" 
d="M32 61q40 -9 101.5 -18t126.5 -9q49 0 84 4t58.5 14.5t37 29t19.5 47.5q5 26 5 43q0 6 -1 18t-17 25.5t-49.5 22.5t-89.5 19q-67 11 -108 23t-62 31t-23 37t-2 28q0 23 6 54q8 38 24 64t45 42t74 23t110 7q66 0 118 -7.5t94 -18.5l-11 -49q-39 10 -87 17.5t-114 7.5
q-52 0 -87.5 -4.5t-58 -15t-34.5 -28t-18 -43.5q-4 -22 -4 -38q0 -5 1 -16.5t17 -23.5t49.5 -20.5t88.5 -17.5q67 -11 109 -23.5t63 -32.5t23 -39t2 -27q0 -27 -8 -64q-10 -49 -36 -77t-61 -41.5t-76.5 -17t-84.5 -3.5l-61 -53q6 1 12 1h12q54 0 72.5 -15t18.5 -41
q0 -10 -2 -21q-4 -18 -11 -31.5t-20.5 -21.5t-35 -12t-54.5 -4q-24 0 -43.5 2.5t-33.5 6.5l8 32q30 -8 71 -8q47 0 62.5 9t19.5 32q2 7 2 12q0 12 -9 21t-56 9q-19 0 -37 -1.5t-31 -3.5l7 29l68 59q-59 2 -109 10t-84 18z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="766" 
d="M366 987h50l93 -112l142 112h58l-165 -140h-73zM65 83q50 -16 125.5 -28.5t166.5 -12.5q59 0 100.5 8t70 26t45.5 46.5t26 68.5q7 31 7 56q0 7 -1.5 25t-22.5 40t-63 39t-113 32q-83 17 -134.5 38.5t-77 51.5t-27.5 55t-2 34q0 34 9 76q10 42 29 75.5t53.5 56.5t87.5 35
t132 12q92 0 155.5 -10t114.5 -25l-12 -59q-50 16 -116 26t-148 10q-61 0 -103 -8t-69 -24t-41.5 -39.5t-21.5 -55.5q-7 -34 -7 -60q0 -6 1 -23.5t20.5 -39t60 -37.5t107.5 -30q87 -18 140.5 -39t80.5 -51t29 -55t2 -35q0 -34 -9 -78q-12 -53 -33.5 -91t-57.5 -62t-89 -35.5
t-128 -11.5q-100 0 -174 11t-125 28z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="614" 
d="M261 802h51l75 -137l136 137h56l-163 -170h-65zM32 61q40 -9 101.5 -18t126.5 -9q49 0 84 4t58.5 14.5t37 29t19.5 47.5q5 26 5 43q0 6 -1 18t-17 25.5t-49.5 22.5t-89.5 19q-67 11 -108 23t-62 31t-23 37t-2 28q0 23 6 54q8 38 24 64t45 42t74 23t110 7q66 0 118 -7.5
t94 -18.5l-11 -49q-39 10 -87 17.5t-114 7.5q-52 0 -87.5 -4.5t-58 -15t-34.5 -28t-18 -43.5q-4 -22 -4 -38q0 -5 1 -16.5t17 -23.5t49.5 -20.5t88.5 -17.5q67 -11 109 -23.5t63 -32.5t23 -39t2 -27q0 -27 -8 -64q-8 -39 -26 -65.5t-48 -43t-74 -23.5t-105 -7
q-72 0 -135 8.5t-104 20.5z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="728" 
d="M290 0h-14l159 747h-297l11 55h655l-12 -55h-297l-159 -747h-3l-80 -69q6 1 12 1h12q54 0 72.5 -15t18.5 -41q0 -10 -2 -21q-4 -18 -11 -31.5t-20.5 -21.5t-35 -12t-54.5 -4q-24 0 -43.5 2.5t-33.5 6.5l8 32q30 -8 71 -8q47 0 62.5 9t19.5 32q2 7 2 12q0 12 -9 21t-56 9
q-19 0 -37 -1.5t-31 -3.5l7 29z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="433" 
d="M189 -7q-71 11 -87.5 44t-16.5 68q0 26 7 59l72 338h-61l8 36l63 12l28 129h57l-27 -129h202l-11 -48h-202l-72 -339q-5 -25 -5 -45q0 -33 19 -55t94 -22q39 0 76 6l-11 -52q-48 -6 -94 -6l-68 -58q6 1 12 1h12q54 0 72.5 -15t18.5 -41q0 -10 -2 -21q-4 -18 -11 -31.5
t-20.5 -21.5t-35 -12t-54.5 -4q-24 0 -43.5 2.5t-33.5 6.5l8 32q30 -8 71 -8q47 0 62.5 9t19.5 32q2 7 2 12q0 12 -9 21t-56 9q-19 0 -37 -1.5t-31 -3.5l7 29z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="728" 
d="M345 987h50l93 -112l142 112h58l-165 -140h-73zM435 747h-297l11 55h655l-12 -55h-297l-159 -747h-60z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="433" 
d="M426 855h56l-146 -256h-37zM247 -11q-107 0 -134.5 35t-27.5 81q0 26 7 59l72 338h-61l8 36l63 12l28 129h57l-27 -129h202l-11 -48h-202l-72 -339q-5 -25 -5 -45q0 -33 19 -55t94 -22q39 0 76 6l-11 -52q-18 -2 -36.5 -4t-38.5 -2z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="728" 
d="M354 366h-196l12 54h196l69 327h-297l11 55h655l-12 -55h-297l-69 -327h195l-12 -54h-195l-78 -366h-60z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="433" 
d="M247 -11q-107 0 -134.5 35t-27.5 81q0 26 7 59l20 94h-60l10 47h60l42 197h-61l8 36l63 12l28 129h57l-27 -129h202l-11 -48h-202l-42 -197h169l-10 -47h-169l-20 -95q-5 -25 -5 -45q0 -33 19 -55t94 -22q39 0 76 6l-11 -52q-18 -2 -36.5 -4t-38.5 -2z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="844" 
d="M375 888q10 43 32 64.5t63 21.5q28 0 51.5 -12.5t44.5 -28t41 -28t40 -12.5q25 0 38 13t18 36l5 27h41l-6 -30q-8 -41 -31 -63.5t-64 -22.5q-28 0 -52 13t-45 28t-40.5 28t-39.5 13q-25 0 -37.5 -13t-18.5 -37l-6 -27h-40zM392 -16q-178 0 -233 60.5t-55 146.5
q0 44 12 98l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h60l-112 -527q-16 -77 -41 -132t-64.5 -90.5t-96.5 -52t-138 -16.5z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="720" 
d="M269 684q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41zM278 -16q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367
h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 459h57l-117 -550h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="844" 
d="M403 933h319l-9 -39h-318zM392 -16q-178 0 -233 60.5t-55 146.5q0 44 12 98l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h60l-112 -527q-16 -77 -41 -132t-64.5 -90.5t-96.5 -52
t-138 -16.5z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="720" 
d="M317 741h286l-8 -41h-287zM278 -16q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 459h57l-117 -550h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="844" 
d="M545 842q-46 0 -76 8t-46.5 25.5t-18.5 35t-2 25.5q0 23 6 51h46q-5 -24 -5 -42q0 -3 0.5 -14t11 -23.5t33 -18t58.5 -5.5t60.5 5.5t41 18t26 32t15.5 47.5h46q-8 -39 -20.5 -66.5t-34.5 -45t-56 -25.5t-85 -8zM392 -16q-178 0 -233 60.5t-55 146.5q0 44 12 98l108 513
h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h60l-112 -527q-16 -77 -41 -132t-64.5 -90.5t-96.5 -52t-138 -16.5z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="720" 
d="M445 649q-86 0 -111.5 25t-25.5 62q0 20 6 45h46q-5 -22 -5 -38q0 -2 0.5 -11.5t10 -20t29.5 -15.5t53 -5t55.5 5t37 15.5t23 28t13.5 41.5h47q-8 -36 -20 -61t-32.5 -40.5t-51 -23t-75.5 -7.5zM278 -16q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367
q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 459h57l-117 -550h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="844" 
d="M392 -16q-178 0 -233 60.5t-55 146.5q0 44 12 98l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h60l-112 -527q-16 -77 -41 -132t-64.5 -90.5t-96.5 -52t-138 -16.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="720" 
d="M438 617q-30 0 -53.5 6t-38.5 19t-19 28.5t-4 27.5q0 13 3 29q12 56 49 79.5t99 23.5q30 0 53.5 -6t38.5 -19t19.5 -28.5t4.5 -27.5q0 -13 -3 -28q-13 -57 -50 -80.5t-99 -23.5zM440 654q44 0 70.5 15.5t34.5 53.5q2 12 2 22q0 22 -16 35t-59 13t-70 -15.5t-35 -53.5
q-2 -12 -2 -21q0 -23 16.5 -36t58.5 -13zM278 -16q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 459h57l-117 -550h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="844" 
d="M554 987h70l-145 -140h-59zM744 987h70l-145 -140h-59zM392 -16q-178 0 -233 60.5t-55 146.5q0 44 12 98l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h60l-112 -527
q-16 -77 -41 -132t-64.5 -90.5t-96.5 -52t-138 -16.5z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="720" 
d="M475 802h67l-170 -170h-54zM662 802h66l-170 -170h-55zM278 -16q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 459h57l-117 -550h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="844" 
d="M299 -122q-2 -8 -2 -15q0 -37 57 -37q22 0 38.5 2.5t29.5 4.5l-8 -38q-14 -5 -31 -7t-40 -2q-52 0 -72.5 18t-20.5 46q0 11 2 23q6 28 27.5 53.5t67.5 58.5q-152 9 -197 69.5t-45 137.5q0 44 11 97l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59
t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h60l-112 -527q-16 -77 -41 -132t-64 -90.5t-95 -52t-134 -16.5q-26 -18 -43.5 -32t-29 -26t-17.5 -23.5t-9 -24.5z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="720" 
d="M278 -16q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 459h57l-117 -550h-15q-33 -23 -55.5 -39.5t-36 -30t-21 -26t-10.5 -27.5q-2 -8 -2 -15q0 -37 57 -37q22 0 38.5 2.5t29.5 4.5
l-8 -38q-14 -5 -31 -7t-40 -2q-52 0 -72.5 18t-20.5 46q0 11 2 23q4 17 13.5 33t26 33t42 36t60.5 42l-2 23q-51 -22 -113 -38.5t-124 -16.5z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="1214" 
d="M719 987h73l105 -140h-50l-93 112l-141 -112h-59zM155 802h61l51 -749h18l376 749h113l60 -749h17l368 749h66l-395 -802h-113l-60 749h-16l-378 -749h-113z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="980" 
d="M566 802h65l91 -170h-51l-76 137l-135 -137h-56zM93 550h58l57 -507l294 507h89l76 -506l274 507l65 -1l-299 -550h-89l-77 507l-294 -507h-89zM541 512l3 1h-9zM664 36l6 1h-2zM212 36l-4 1l-1 -1h5z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="715" 
d="M475 987h73l105 -140h-50l-93 112l-141 -112h-59zM338 318l-208 484h65l180 -427l362 427h74l-413 -485l-68 -317h-60z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="646" 
d="M402 802h65l91 -170h-51l-76 137l-135 -137h-56zM-34 -161q30 -3 57 -3q30 0 54.5 5t44.5 16t37.5 29t35.5 44l49 73h-25l-128 547h57l116 -498h13l333 498h63l-421 -629q-25 -38 -48.5 -64t-49.5 -41.5t-57 -22.5t-71 -7q-18 0 -35.5 1t-34.5 4z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="715" 
d="M368 949h56l-13 -63h-57zM582 949h56l-14 -63h-56zM338 318l-208 484h65l180 -427l362 427h74l-413 -485l-68 -317h-60z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="718" 
d="M601 987h72l-149 -140h-62zM-9 51l670 695h-489l13 56h573l-11 -51l-668 -695h555l-12 -56h-642z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="625" 
d="M526 794h70l-179 -170h-57zM18 41l492 461h-382l11 48h466l-9 -41l-493 -461h406l-10 -48h-489z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="718" 
d="M484 949h66l-14 -63h-65zM-9 51l670 695h-489l13 56h573l-11 -51l-668 -695h555l-12 -56h-642z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="625" 
d="M384 754h65l-14 -68h-65zM18 41l492 461h-382l11 48h466l-9 -41l-493 -461h406l-10 -48h-489z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="718" 
d="M354 987h50l93 -112l142 112h58l-165 -140h-73zM-9 51l670 695h-489l13 56h573l-11 -51l-668 -695h555l-12 -56h-642z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="625" 
d="M268 802h51l75 -137l136 137h56l-163 -170h-65zM18 41l492 461h-382l11 48h466l-9 -41l-493 -461h406l-10 -48h-489z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="370" 
d="M-151 -159q14 -2 35 -3t36 -1q57 0 88.5 26.5t45.5 94.5l141 668q11 50 27.5 86.5t43 60t63.5 34.5t89 11q49 0 98 -6l-10 -49q-22 2 -43 3.5t-44 1.5q-37 0 -65 -7.5t-47.5 -24t-32.5 -43t-22 -65.5l-141 -668q-10 -47 -24.5 -80t-36.5 -54t-53 -30.5t-73 -9.5
q-20 0 -44 1t-42 4z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="690" 
d="M117 428h231l47 222q9 46 25.5 78t43 52t65 29t91.5 9q50 0 99 -6l-11 -52q-45 6 -91 6q-74 0 -112.5 -26t-52.5 -92l-47 -220h232l-10 -48h-232l-94 -443q-7 -36 -21.5 -64.5t-39.5 -47.5t-63 -29t-93 -10q-31 0 -55 1.5t-48 4.5l12 52q44 -6 90 -6q41 0 69.5 7
t46.5 19.5t28.5 31.5t15.5 43l94 441h-230z" />
    <glyph glyph-name="Ohorn" unicode="&#x1a0;" horiz-adv-x="878" 
d="M393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q81 0 138 -16h23q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29l-12 -6q64 -48 75 -109t11 -99q0 -71 -20 -164q-23 -110 -59 -187.5t-88.5 -126t-123 -71
t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5z" />
    <glyph glyph-name="ohorn" unicode="&#x1a1;" horiz-adv-x="690" 
d="M299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q35 0 65 -4t56 -13h15q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29q-8 -5 -17 -8q36 -36 43 -76.5t7 -65.5q0 -44 -11 -99q-16 -76 -42 -130t-65 -88
t-92.5 -49.5t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45z" />
    <glyph glyph-name="Uhorn" unicode="&#x1af;" horiz-adv-x="844" 
d="M392 -16q-178 0 -233 60.5t-55 146.5q0 44 12 98l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h24q72 9 72 95h62q0 -33 -8 -57q-8 -25 -21 -42t-33 -29q-19 -12 -47 -17l-101 -477
q-16 -77 -41 -132t-64.5 -90.5t-96.5 -52t-138 -16.5z" />
    <glyph glyph-name="uhorn" unicode="&#x1b0;" horiz-adv-x="720" 
d="M278 -16q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 458l-1 1h23q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29t-48 -17l-106 -500h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="781" 
d="M619 987h72l-149 -140h-62zM449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68zM578 315l-73 437h-21l-256 -437h350z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="700" 
d="M615 1047h70l-179 -170h-57zM427 617q-30 0 -53.5 6t-38.5 19t-19 28.5t-4 27.5q0 13 3 29q12 56 49 79.5t99 23.5q30 0 53.5 -6t38.5 -19t19.5 -28.5t4.5 -27.5q0 -13 -3 -28q-13 -57 -50 -80.5t-99 -23.5zM429 654q44 0 70.5 15.5t34.5 53.5q2 12 2 22q0 22 -16 35
t-59 13t-70 -15.5t-35 -53.5q-2 -12 -2 -21q0 -23 16.5 -36t58.5 -13zM290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16z
M129 283q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="1198" 
d="M854 987h72l-149 -140h-62zM591 261h-355l-221 -261h-74l681 802h602l-12 -55h-458l-67 -316h427l-12 -55h-427l-69 -326h458l-11 -50h-518zM602 316l92 431h-45l-366 -431h319z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="1121" 
d="M774 790h70l-179 -170h-57zM255 -16q-109 0 -152 40.5t-43 100.5q0 26 7 57q9 39 26.5 65.5t45 42t65.5 22t87 6.5h248l11 51q6 25 6 46q0 9 -2.5 26.5t-21.5 36.5t-57 28.5t-100 9.5q-55 0 -103.5 -5t-97.5 -13l9 50q26 5 48.5 8t45.5 5.5t48 3.5t55 1q110 0 162 -29
t60 -86q39 60 99 87.5t148 27.5q137 0 177.5 -58t40.5 -129q0 -36 -8 -77l-6 -30h-466q-9 -46 -9 -82q0 -16 3.5 -45t26 -57t61.5 -40t94 -12q69 0 118.5 6.5t98.5 16.5l-9 -50q-41 -11 -91.5 -18t-121.5 -7q-74 0 -126 19t-81 62q-33 -19 -65 -34.5t-67.5 -25.5
t-75.5 -15.5t-88 -5.5zM846 523q-102 0 -161 -48t-88 -157h410q7 36 7 66q0 10 -2 34t-21.5 50.5t-55 40.5t-89.5 14zM262 31q42 0 78.5 4.5t69 13.5t62.5 22.5t61 30.5q-13 36 -13 85q0 39 8 85h-235q-81 0 -120 -22t-51 -77q-5 -23 -5 -43q0 -45 32.5 -72t112.5 -27z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="878" 
d="M667 987h72l-149 -140h-62zM24 21l92 86q-28 56 -28 142q0 71 19 162q24 110 59.5 187.5t88 126t122.5 71t163 22.5q177 0 250 -82l89 82l31 -37l-93 -85q29 -56 29 -143q0 -71 -19 -162q-23 -110 -59 -187.5t-88.5 -126t-123 -71t-163.5 -22.5q-88 0 -149.5 19.5
t-99.5 62.5l-89 -82zM167 401q-19 -85 -19 -148q0 -60 17 -101l579 541q-30 35 -82 51.5t-127 16.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5zM398 41q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q17 84 17.5 116t0.5 36q0 56 -16 97l-578 -542
q29 -35 80.5 -51t126.5 -16z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="690" 
d="M556 794h70l-179 -170h-57zM12 17l71 61q-24 43 -24 108q0 44 11 98q16 77 42.5 130.5t65.5 87t92 49t122 15.5q65 0 112.5 -14t78.5 -45l71 59l27 -33l-71 -61q20 -36 22 -65.5t2 -39.5q0 -44 -12 -100q-16 -76 -42 -130t-65 -88t-92.5 -49.5t-123.5 -15.5
q-63 0 -111 14.5t-78 44.5l-71 -59zM128 276q-11 -49 -11 -87q0 -42 13 -71l408 351q-45 45 -149 45q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5zM303 36q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 41 -13 70l-408 -351q45 -45 149 -45z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="766" 
d="M65 83q50 -16 125.5 -28.5t166.5 -12.5q59 0 100.5 8t70 26t45.5 46.5t26 68.5q7 31 7 56q0 7 -1.5 25t-22.5 40t-63 39t-113 32q-83 17 -134.5 38.5t-77 51.5t-27.5 55t-2 34q0 34 9 76q10 42 29 75.5t53.5 56.5t87.5 35t132 12q92 0 155.5 -10t114.5 -25l-12 -59
q-50 16 -116 26t-148 10q-61 0 -103 -8t-69 -24t-41.5 -39.5t-21.5 -55.5q-7 -34 -7 -60q0 -6 1 -23.5t20.5 -39t60 -37.5t107.5 -30q87 -18 140.5 -39t80.5 -51t29 -55t2 -35q0 -34 -9 -78q-12 -53 -33.5 -91t-57.5 -62t-89 -35.5t-128 -11.5q-100 0 -174 11t-125 28z
M284 -53h74l-161 -145h-58z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="614" 
d="M32 61q40 -9 101.5 -18t126.5 -9q49 0 84 4t58.5 14.5t37 29t19.5 47.5q5 26 5 43q0 6 -1 18t-17 25.5t-49.5 22.5t-89.5 19q-67 11 -108 23t-62 31t-23 37t-2 28q0 23 6 54q8 38 24 64t45 42t74 23t110 7q66 0 118 -7.5t94 -18.5l-11 -49q-39 10 -87 17.5t-114 7.5
q-52 0 -87.5 -4.5t-58 -15t-34.5 -28t-18 -43.5q-4 -22 -4 -38q0 -5 1 -16.5t17 -23.5t49.5 -20.5t88.5 -17.5q67 -11 109 -23.5t63 -32.5t23 -39t2 -27q0 -27 -8 -64q-8 -39 -26 -65.5t-48 -43t-74 -23.5t-105 -7q-72 0 -135 8.5t-104 20.5zM206 -53h74l-161 -145h-58z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="728" 
d="M435 747h-297l11 55h655l-12 -55h-297l-159 -747h-60zM264 -53h74l-161 -145h-58z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="433" 
d="M247 -11q-107 0 -134.5 35t-27.5 81q0 26 7 59l72 338h-61l8 36l63 12l28 129h57l-27 -129h202l-11 -48h-202l-72 -339q-5 -25 -5 -45q0 -33 19 -55t94 -22q39 0 76 6l-11 -52q-18 -2 -36.5 -4t-38.5 -2zM171 -53h74l-161 -145h-58z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="263" 
d="M-168 -164q14 -2 35.5 -3t35.5 -1q57 0 88 27.5t46 98.5l125 592h57l-125 -592q-10 -46 -25 -78.5t-37 -53.5t-52.5 -30.5t-72.5 -9.5q-20 0 -44 1t-41 4z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="570" 
d="M366 802h65l91 -170h-51l-76 137l-135 -137h-56z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="570" 
d="M240 802h51l75 -137l136 137h56l-163 -170h-65z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="570" 
d="M370 649q-86 0 -111.5 25t-25.5 62q0 20 6 45h46q-5 -22 -5 -38q0 -2 0.5 -11.5t10 -20t29.5 -15.5t53 -5t55.5 5t37 15.5t23 28t13.5 41.5h47q-8 -36 -20 -61t-32.5 -40.5t-51 -23t-75.5 -7.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="570" 
d="M355 754h65l-14 -68h-65z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="570" 
d="M363 617q-30 0 -53.5 6t-38.5 19t-19 28.5t-4 27.5q0 13 3 29q12 56 49 79.5t99 23.5q30 0 53.5 -6t38.5 -19t19.5 -28.5t4.5 -27.5q0 -13 -3 -28q-13 -57 -50 -80.5t-99 -23.5zM365 654q44 0 70.5 15.5t34.5 53.5q2 12 2 22q0 22 -16 35t-59 13t-70 -15.5t-35 -53.5
q-2 -12 -2 -21q0 -23 16.5 -36t58.5 -13z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="570" 
d="M158 -122q-2 -8 -2 -15q0 -37 57 -37q22 0 38.5 2.5t29.5 4.5l-8 -38q-14 -5 -31 -7t-40 -2q-52 0 -72.5 18t-20.5 46q0 11 2 23q4 17 13.5 33t26.5 33.5t42.5 36.5t61.5 43l38 -10q-37 -25 -61.5 -43t-39.5 -32.5t-23 -27.5t-11 -28z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="570" 
d="M194 684q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="570" 
d="M333 802h67l-170 -170h-54zM520 802h66l-170 -170h-55z" />
    <glyph glyph-name="gravecomb" unicode="&#x300;" horiz-adv-x="0" 
d="M-55 802h64l94 -170h-51z" />
    <glyph glyph-name="acutecomb" unicode="&#x301;" horiz-adv-x="0" 
d="M213 802h70l-179 -170h-57z" />
    <glyph glyph-name="uni0302" unicode="&#x302;" horiz-adv-x="0" 
d="M80 802h65l91 -170h-51l-76 137l-135 -137h-56z" />
    <glyph glyph-name="tildecomb" unicode="&#x303;" horiz-adv-x="0" 
d="M-91 684q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41z" />
    <glyph glyph-name="uni0304" unicode="&#x304;" horiz-adv-x="0" 
d="M-43 741h286l-8 -41h-287z" />
    <glyph glyph-name="uni0306" unicode="&#x306;" horiz-adv-x="0" 
d="M85 649q-86 0 -111.5 25t-25.5 62q0 20 6 45h46q-5 -22 -5 -38q0 -2 0.5 -11.5t10 -20t29.5 -15.5t53 -5t55.5 5t37 15.5t23 28t13.5 41.5h47q-8 -36 -20 -61t-32.5 -40.5t-51 -23t-75.5 -7.5z" />
    <glyph glyph-name="uni0307" unicode="&#x307;" horiz-adv-x="0" 
d="M70 754h65l-14 -68h-65z" />
    <glyph glyph-name="uni0308" unicode="&#x308;" horiz-adv-x="0" 
d="M-30 754h55l-14 -68h-55zM180 754h55l-14 -68h-55z" />
    <glyph glyph-name="uni030A" unicode="&#x30a;" horiz-adv-x="0" 
d="M78 617q-30 0 -53.5 6t-38.5 19t-19 28.5t-4 27.5q0 13 3 29q12 56 49 79.5t99 23.5q30 0 53.5 -6t38.5 -19t19.5 -28.5t4.5 -27.5q0 -13 -3 -28q-13 -57 -50 -80.5t-99 -23.5zM80 654q44 0 70.5 15.5t34.5 53.5q2 12 2 22q0 22 -16 35t-59 13t-70 -15.5t-35 -53.5
q-2 -12 -2 -21q0 -23 16.5 -36t58.5 -13z" />
    <glyph glyph-name="uni030B" unicode="&#x30b;" horiz-adv-x="0" 
d="M115 802h67l-170 -170h-54zM302 802h66l-170 -170h-55z" />
    <glyph glyph-name="uni030C" unicode="&#x30c;" horiz-adv-x="0" 
d="M-46 802h51l75 -137l136 137h56l-163 -170h-65z" />
    <glyph glyph-name="uni0312" unicode="&#x312;" horiz-adv-x="0" 
d="M110 632h-69l178 170h57z" />
    <glyph glyph-name="uni0326" unicode="&#x326;" horiz-adv-x="0" 
d="M-102 -53h74l-161 -145h-58z" />
    <glyph glyph-name="uni0327" unicode="&#x327;" horiz-adv-x="0" 
d="M-75 1h43l-81 -70q6 1 12 1h12q54 0 72.5 -15t18.5 -41q0 -10 -2 -21q-4 -18 -11.5 -31.5t-21 -21.5t-34.5 -12t-54 -4q-24 0 -43.5 2.5t-33.5 6.5l8 32q30 -8 71 -8q24 0 38.5 2t23.5 7t13.5 12.5t6.5 19.5q2 8 2 15q0 1 -0.5 6t-7 10.5t-20 8t-37.5 2.5q-19 0 -37 -1.5
t-31 -3.5l7 29z" />
    <glyph glyph-name="uni0328" unicode="&#x328;" horiz-adv-x="0" 
d="M-159 -122q-2 -8 -2 -15q0 -37 57 -37q22 0 38.5 2.5t29.5 4.5l-8 -38q-14 -5 -31 -7t-40 -2q-52 0 -72.5 18t-20.5 46q0 11 2 23q4 17 13.5 33t26.5 33.5t42.5 36.5t61.5 43l38 -10q-37 -25 -61.5 -43t-39.5 -32.5t-23 -27.5t-11 -28z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" 
d="M30 52h157q-72 46 -85.5 114.5t-13.5 114.5q0 70 18 157q22 103 52 175t73.5 117.5t104 66.5t142.5 21q170 0 215.5 -75.5t45.5 -167.5q0 -69 -19 -159q-31 -148 -90 -235.5t-147 -128.5h153l-11 -52h-259l10 44q59 14 105 46t80.5 80t59 110.5t40.5 138.5q20 91 20 155
q0 1 -0.5 34.5t-23 75t-67 60.5t-112.5 19q-70 0 -121 -18.5t-88 -60t-62.5 -107t-45.5 -158.5q-16 -76 -17 -111t-1 -42q0 -54 13.5 -99t46 -77t85.5 -46l-10 -44h-259z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="739" 
d="M178 495h-75l12 55h628l-12 -55h-76l-105 -495h-58l105 495h-360l-105 -495h-58z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="1214" 
d="M613 987h63l79 -140h-53zM155 802h61l51 -749h18l376 749h113l60 -749h17l368 749h66l-395 -802h-113l-60 749h-16l-378 -749h-113z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="980" 
d="M431 802h64l94 -170h-51zM93 550h58l57 -507l294 507h89l76 -506l274 507l65 -1l-299 -550h-89l-77 507l-294 -507h-89zM541 512l3 1h-9zM664 36l6 1h-2zM212 36l-4 1l-1 -1h5z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="1214" 
d="M831 987h72l-149 -140h-62zM155 802h61l51 -749h18l376 749h113l60 -749h17l368 749h66l-395 -802h-113l-60 749h-16l-378 -749h-113z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="980" 
d="M697 794h70l-179 -170h-57zM93 550h58l57 -507l294 507h89l76 -506l274 507l65 -1l-299 -550h-89l-77 507l-294 -507h-89zM541 512l3 1h-9zM664 36l6 1h-2zM212 36l-4 1l-1 -1h5z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="1214" 
d="M612 949h56l-13 -63h-57zM826 949h56l-14 -63h-56zM155 802h61l51 -749h18l376 749h113l60 -749h17l368 749h66l-395 -802h-113l-60 749h-16l-378 -749h-113z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="980" 
d="M456 754h55l-14 -68h-55zM666 754h55l-14 -68h-55zM93 550h58l57 -507l294 507h89l76 -506l274 507l65 -1l-299 -550h-89l-77 507l-294 -507h-89zM541 512l3 1h-9zM664 36l6 1h-2zM212 36l-4 1l-1 -1h5z" />
    <glyph glyph-name="uni1EA0" unicode="&#x1ea0;" horiz-adv-x="781" 
d="M449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68zM578 315l-73 437h-21l-256 -437h350zM353 -110l-31 -116h-88l31 116h88z" />
    <glyph glyph-name="uni1EA1" unicode="&#x1ea1;" horiz-adv-x="700" 
d="M290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13
q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106zM324 -122l-31 -104h-76l30 104h77z" />
    <glyph glyph-name="uni1EA2" unicode="&#x1ea2;" horiz-adv-x="781" 
d="M515 938q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM449 802
h107l134 -802h-60l-43 261h-390l-153 -261h-68zM578 315l-73 437h-21l-256 -437h350z" />
    <glyph glyph-name="uni1EA3" unicode="&#x1ea3;" horiz-adv-x="700" 
d="M447 700q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM290 -16
q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13
q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106z" />
    <glyph glyph-name="uni1EA4" unicode="&#x1ea4;" horiz-adv-x="781" 
d="M507 987h73l105 -140h-50l-93 112l-141 -112h-59zM449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68zM578 315l-73 437h-21l-256 -437h350zM687 1200h70l-179 -170h-57z" />
    <glyph glyph-name="uni1EA5" unicode="&#x1ea5;" horiz-adv-x="700" 
d="M429 802h65l91 -170h-51l-76 137l-135 -137h-56zM290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283
q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106zM607 1015h70l-179 -170h-57z" />
    <glyph glyph-name="uni1EA6" unicode="&#x1ea6;" horiz-adv-x="781" 
d="M507 987h73l105 -140h-50l-93 112l-141 -112h-59zM449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68zM578 315l-73 437h-21l-256 -437h350zM421 1209h64l94 -170h-51z" />
    <glyph glyph-name="uni1EA7" unicode="&#x1ea7;" horiz-adv-x="700" 
d="M429 802h65l91 -170h-51l-76 137l-135 -137h-56zM290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283
q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106zM341 1024h64l94 -170h-51z" />
    <glyph glyph-name="uni1EA8" unicode="&#x1ea8;" horiz-adv-x="781" 
d="M651 1012q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM507 987
h73l105 -140h-50l-93 112l-141 -112h-59zM449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68zM578 315l-73 437h-21l-256 -437h350z" />
    <glyph glyph-name="uni1EA9" unicode="&#x1ea9;" horiz-adv-x="700" 
d="M562 813q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM429 802
h65l91 -170h-51l-76 137l-135 -137h-56zM290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -91
q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106z" />
    <glyph glyph-name="uni1EAA" unicode="&#x1eaa;" horiz-adv-x="781" 
d="M507 987h73l105 -140h-50l-93 112l-141 -112h-59zM449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68zM578 315l-73 437h-21l-256 -437h350zM385 1091q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33
q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41z" />
    <glyph glyph-name="uni1EAB" unicode="&#x1eab;" horiz-adv-x="700" 
d="M429 802h65l91 -170h-51l-76 137l-135 -137h-56zM290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283
q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106zM305 906q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41
l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41z" />
    <glyph glyph-name="uni1EAC" unicode="&#x1eac;" horiz-adv-x="781" 
d="M449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68zM578 315l-73 437h-21l-256 -437h350zM353 -110l-31 -116h-88l31 116h88zM524 1054h65l91 -170h-51l-76 137l-135 -137h-56z" />
    <glyph glyph-name="uni1EAD" unicode="&#x1ead;" horiz-adv-x="700" 
d="M290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13
q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106zM324 -122l-31 -104h-76l30 104h77zM429 802h65l91 -170h-51l-76 137l-135 -137h-56z" />
    <glyph glyph-name="uni1EAE" unicode="&#x1eae;" horiz-adv-x="781" 
d="M514 842q-46 0 -76 8t-46.5 25.5t-18.5 35t-2 25.5q0 23 6 51h46q-5 -24 -5 -42q0 -3 0.5 -14t11 -23.5t33 -18t58.5 -5.5t60.5 5.5t41 18t26 32t15.5 47.5h46q-8 -39 -20.5 -66.5t-34.5 -45t-56 -25.5t-85 -8zM449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68z
M578 315l-73 437h-21l-256 -437h350zM687 1200h70l-179 -170h-57z" />
    <glyph glyph-name="uni1EAF" unicode="&#x1eaf;" horiz-adv-x="700" 
d="M434 649q-86 0 -111.5 25t-25.5 62q0 20 6 45h46q-5 -22 -5 -38q0 -2 0.5 -11.5t10 -20t29.5 -15.5t53 -5t55.5 5t37 15.5t23 28t13.5 41.5h47q-8 -36 -20 -61t-32.5 -40.5t-51 -23t-75.5 -7.5zM290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104
q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15
q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106zM602 994h70l-179 -170h-57z" />
    <glyph glyph-name="uni1EB0" unicode="&#x1eb0;" horiz-adv-x="781" 
d="M514 842q-46 0 -76 8t-46.5 25.5t-18.5 35t-2 25.5q0 23 6 51h46q-5 -24 -5 -42q0 -3 0.5 -14t11 -23.5t33 -18t58.5 -5.5t60.5 5.5t41 18t26 32t15.5 47.5h46q-8 -39 -20.5 -66.5t-34.5 -45t-56 -25.5t-85 -8zM449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68z
M578 315l-73 437h-21l-256 -437h350zM421 1209h64l94 -170h-51z" />
    <glyph glyph-name="uni1EB1" unicode="&#x1eb1;" horiz-adv-x="700" 
d="M434 649q-86 0 -111.5 25t-25.5 62q0 20 6 45h46q-5 -22 -5 -38q0 -2 0.5 -11.5t10 -20t29.5 -15.5t53 -5t55.5 5t37 15.5t23 28t13.5 41.5h47q-8 -36 -20 -61t-32.5 -40.5t-51 -23t-75.5 -7.5zM290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104
q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15
q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106zM336 1003h64l94 -170h-51z" />
    <glyph glyph-name="uni1EB2" unicode="&#x1eb2;" horiz-adv-x="781" 
d="M542 1079q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM514 842
q-46 0 -76 8t-46.5 25.5t-18.5 35t-2 25.5q0 23 6 51h46q-5 -24 -5 -42q0 -3 0.5 -14t11 -23.5t33 -18t58.5 -5.5t60.5 5.5t41 18t26 32t15.5 47.5h46q-8 -39 -20.5 -66.5t-34.5 -45t-56 -25.5t-85 -8zM449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68zM578 315
l-73 437h-21l-256 -437h350z" />
    <glyph glyph-name="uni1EB3" unicode="&#x1eb3;" horiz-adv-x="700" 
d="M461 875q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM434 649
q-86 0 -111.5 25t-25.5 62q0 20 6 45h46q-5 -22 -5 -38q0 -2 0.5 -11.5t10 -20t29.5 -15.5t53 -5t55.5 5t37 15.5t23 28t13.5 41.5h47q-8 -36 -20 -61t-32.5 -40.5t-51 -23t-75.5 -7.5zM290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125
t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13
t-74.5 -40.5t-53 -71.5t-34.5 -106z" />
    <glyph glyph-name="uni1EB4" unicode="&#x1eb4;" horiz-adv-x="781" 
d="M514 842q-46 0 -76 8t-46.5 25.5t-18.5 35t-2 25.5q0 23 6 51h46q-5 -24 -5 -42q0 -3 0.5 -14t11 -23.5t33 -18t58.5 -5.5t60.5 5.5t41 18t26 32t15.5 47.5h46q-8 -39 -20.5 -66.5t-34.5 -45t-56 -25.5t-85 -8zM449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68z
M578 315l-73 437h-21l-256 -437h350zM385 1091q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41z" />
    <glyph glyph-name="uni1EB5" unicode="&#x1eb5;" horiz-adv-x="700" 
d="M434 649q-86 0 -111.5 25t-25.5 62q0 20 6 45h46q-5 -22 -5 -38q0 -2 0.5 -11.5t10 -20t29.5 -15.5t53 -5t55.5 5t37 15.5t23 28t13.5 41.5h47q-8 -36 -20 -61t-32.5 -40.5t-51 -23t-75.5 -7.5zM290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104
q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15
q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106zM300 885q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41z" />
    <glyph glyph-name="uni1EB6" unicode="&#x1eb6;" horiz-adv-x="781" 
d="M449 802h107l134 -802h-60l-43 261h-390l-153 -261h-68zM578 315l-73 437h-21l-256 -437h350zM353 -110l-31 -116h-88l31 116h88zM529 901q-86 0 -111.5 25t-25.5 62q0 20 6 45h46q-5 -22 -5 -38q0 -2 0.5 -11.5t10 -20t29.5 -15.5t53 -5t55.5 5t37 15.5t23 28t13.5 41.5
h47q-8 -36 -20 -61t-32.5 -40.5t-51 -23t-75.5 -7.5z" />
    <glyph glyph-name="uni1EB7" unicode="&#x1eb7;" horiz-adv-x="700" 
d="M290 -16q-66 0 -114.5 17t-77.5 54t-33.5 75t-4.5 57q0 46 12 104q15 73 41 125t64.5 85.5t89.5 49t116 15.5q58 0 113 -16.5t100 -37.5l19 38h42l-117 -550h-42l-3 36q-45 -20 -98.5 -36t-106.5 -16zM129 283q-11 -51 -11 -91q0 -13 3 -42t25.5 -58.5t61.5 -42.5t94 -13
q56 0 108 15.5t90 34.5l82 378q-37 20 -84.5 35t-107.5 15q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106zM324 -122l-31 -104h-76l30 104h77zM434 649q-86 0 -111.5 25t-25.5 62q0 20 6 45h46q-5 -22 -5 -38q0 -2 0.5 -11.5t10 -20t29.5 -15.5t53 -5t55.5 5t37 15.5
t23 28t13.5 41.5h47q-8 -36 -20 -61t-32.5 -40.5t-51 -23t-75.5 -7.5z" />
    <glyph glyph-name="uni1EB8" unicode="&#x1eb8;" horiz-adv-x="727" 
d="M236 802h517l-11 -55h-457l-68 -316h428l-12 -55h-427l-69 -321h458l-12 -55h-517zM353 -110l-31 -116h-88l31 116h88z" />
    <glyph glyph-name="uni1EB9" unicode="&#x1eb9;" horiz-adv-x="653" 
d="M388 566q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-41 -11 -91.5 -18t-121.5 -7q-69 0 -119.5 16.5t-80 53t-34.5 74.5t-5 58q0 47 13 106
q31 143 106 208.5t210 65.5zM386 516q-102 0 -161.5 -47t-87.5 -154h410q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14zM324 -122l-31 -104h-76l30 104h77z" />
    <glyph glyph-name="uni1EBA" unicode="&#x1eba;" horiz-adv-x="727" 
d="M515 938q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM236 802
h517l-11 -55h-457l-68 -316h428l-12 -55h-427l-69 -321h458l-12 -55h-517z" />
    <glyph glyph-name="uni1EBB" unicode="&#x1ebb;" horiz-adv-x="653" 
d="M423 689q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM388 566
q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-41 -11 -91.5 -18t-121.5 -7q-69 0 -119.5 16.5t-80 53t-34.5 74.5t-5 58q0 47 13 106q31 143 106 208.5
t210 65.5zM386 516q-102 0 -161.5 -47t-87.5 -154h410q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14z" />
    <glyph glyph-name="uni1EBC" unicode="&#x1ebc;" horiz-adv-x="727" 
d="M236 802h517l-11 -55h-457l-68 -316h428l-12 -55h-427l-69 -321h458l-12 -55h-517zM351 936q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5
q-50 0 -62 -63l-6 -32h-41z" />
    <glyph glyph-name="uni1EBD" unicode="&#x1ebd;" horiz-adv-x="653" 
d="M388 566q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-41 -11 -91.5 -18t-121.5 -7q-69 0 -119.5 16.5t-80 53t-34.5 74.5t-5 58q0 47 13 106
q31 143 106 208.5t210 65.5zM386 516q-102 0 -161.5 -47t-87.5 -154h410q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14zM250 684q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26
q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41z" />
    <glyph glyph-name="uni1EBE" unicode="&#x1ebe;" horiz-adv-x="727" 
d="M505 987h73l105 -140h-50l-93 112l-141 -112h-59zM236 802h517l-11 -55h-457l-68 -316h428l-12 -55h-427l-69 -321h458l-12 -55h-517zM685 1200h70l-179 -170h-57z" />
    <glyph glyph-name="uni1EBF" unicode="&#x1ebf;" horiz-adv-x="653" 
d="M421 802h65l91 -170h-51l-76 137l-135 -137h-56zM388 566q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-41 -11 -91.5 -18t-121.5 -7q-69 0 -119.5 16.5
t-80 53t-34.5 74.5t-5 58q0 47 13 106q31 143 106 208.5t210 65.5zM386 516q-102 0 -161.5 -47t-87.5 -154h410q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14zM598 1015h70l-179 -170h-57z" />
    <glyph glyph-name="uni1EC0" unicode="&#x1ec0;" horiz-adv-x="727" 
d="M505 987h73l105 -140h-50l-93 112l-141 -112h-59zM236 802h517l-11 -55h-457l-68 -316h428l-12 -55h-427l-69 -321h458l-12 -55h-517zM419 1209h64l94 -170h-51z" />
    <glyph glyph-name="uni1EC1" unicode="&#x1ec1;" horiz-adv-x="653" 
d="M421 802h65l91 -170h-51l-76 137l-135 -137h-56zM388 566q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-41 -11 -91.5 -18t-121.5 -7q-69 0 -119.5 16.5
t-80 53t-34.5 74.5t-5 58q0 47 13 106q31 143 106 208.5t210 65.5zM386 516q-102 0 -161.5 -47t-87.5 -154h410q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14zM332 1024h64l94 -170h-51z" />
    <glyph glyph-name="uni1EC2" unicode="&#x1ec2;" horiz-adv-x="727" 
d="M646 1010q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM505 987
h73l105 -140h-50l-93 112l-141 -112h-59zM236 802h517l-11 -55h-457l-68 -316h428l-12 -55h-427l-69 -321h458l-12 -55h-517z" />
    <glyph glyph-name="uni1EC3" unicode="&#x1ec3;" horiz-adv-x="653" 
d="M421 802h65l91 -170h-51l-76 137l-135 -137h-56zM388 566q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-41 -11 -91.5 -18t-121.5 -7q-69 0 -119.5 16.5
t-80 53t-34.5 74.5t-5 58q0 47 13 106q31 143 106 208.5t210 65.5zM386 516q-102 0 -161.5 -47t-87.5 -154h410q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14z" />
    <glyph glyph-name="uni1EC4" unicode="&#x1ec4;" horiz-adv-x="727" 
d="M505 987h73l105 -140h-50l-93 112l-141 -112h-59zM236 802h517l-11 -55h-457l-68 -316h428l-12 -55h-427l-69 -321h458l-12 -55h-517zM383 1091q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77
t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41z" />
    <glyph glyph-name="uni1EC5" unicode="&#x1ec5;" horiz-adv-x="653" 
d="M421 802h65l91 -170h-51l-76 137l-135 -137h-56zM388 566q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-41 -11 -91.5 -18t-121.5 -7q-69 0 -119.5 16.5
t-80 53t-34.5 74.5t-5 58q0 47 13 106q31 143 106 208.5t210 65.5zM386 516q-102 0 -161.5 -47t-87.5 -154h410q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14zM296 906q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41
l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41z" />
    <glyph glyph-name="uni1EC6" unicode="&#x1ec6;" horiz-adv-x="727" 
d="M236 802h517l-11 -55h-457l-68 -316h428l-12 -55h-427l-69 -321h458l-12 -55h-517zM353 -110l-31 -116h-88l31 116h88zM522 1054h65l91 -170h-51l-76 137l-135 -137h-56z" />
    <glyph glyph-name="uni1EC7" unicode="&#x1ec7;" horiz-adv-x="653" 
d="M388 566q137 0 178.5 -57t41.5 -128q0 -38 -9 -82l-6 -31h-467q-8 -44 -8 -79q0 -16 3.5 -45t26 -56.5t61.5 -39.5t94 -12q35 0 64 1.5t55.5 4.5t50.5 7.5t49 9.5l-11 -50q-41 -11 -91.5 -18t-121.5 -7q-69 0 -119.5 16.5t-80 53t-34.5 74.5t-5 58q0 47 13 106
q31 143 106 208.5t210 65.5zM386 516q-102 0 -161.5 -47t-87.5 -154h410q7 34 7 62q0 11 -2.5 35t-21.5 50t-54.5 40t-89.5 14zM324 -122l-31 -104h-76l30 104h77zM421 802h65l91 -170h-51l-76 137l-135 -137h-56z" />
    <glyph glyph-name="uni1EC8" unicode="&#x1ec8;" horiz-adv-x="307" 
d="M282 938q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM236 802
h60l-170 -802h-60z" />
    <glyph glyph-name="uni1EC9" unicode="&#x1ec9;" horiz-adv-x="263" 
d="M205 717q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM162 550
h57l-117 -550h-56z" />
    <glyph glyph-name="uni1ECA" unicode="&#x1eca;" horiz-adv-x="307" 
d="M236 802h60l-170 -802h-60zM106 -133l-24 -93h-71l25 93h70z" />
    <glyph glyph-name="uni1ECB" unicode="&#x1ecb;" horiz-adv-x="263" 
d="M188 719h66l-15 -69h-65zM162 550h57l-117 -550h-56zM12 -129h66l-15 -69h-65z" />
    <glyph glyph-name="uni1ECC" unicode="&#x1ecc;" horiz-adv-x="878" 
d="M393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q97 0 163 -24t101.5 -76t39 -100t3.5 -64q0 -71 -20 -163q-23 -110 -59 -187.5t-88.5 -126t-123 -71t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5
t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5zM353 -110l-31 -116h-88l31 116h88z" />
    <glyph glyph-name="uni1ECD" unicode="&#x1ecd;" horiz-adv-x="690" 
d="M299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q70 0 121 -17t80.5 -53.5t35 -74t5.5 -57.5q0 -43 -12 -97q-16 -76 -42 -130t-65 -88t-92.5 -49.5t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5
t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45zM298 -122l-31 -104h-76l30 104h77z" />
    <glyph glyph-name="uni1ECE" unicode="&#x1ece;" horiz-adv-x="878" 
d="M588 938q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM393 -16
q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q97 0 163 -24t101.5 -76t39 -100t3.5 -64q0 -71 -20 -163q-23 -110 -59 -187.5t-88.5 -126t-123 -71t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5t73.5 111.5
t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5z" />
    <glyph glyph-name="uni1ECF" unicode="&#x1ecf;" horiz-adv-x="690" 
d="M438 687q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM299 -16
q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q70 0 121 -17t80.5 -53.5t35 -74t5.5 -57.5q0 -43 -12 -97q-16 -76 -42 -130t-65 -88t-92.5 -49.5t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5t34 109.5
q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45z" />
    <glyph glyph-name="uni1ED0" unicode="&#x1ed0;" horiz-adv-x="878" 
d="M555 987h73l105 -140h-50l-93 112l-141 -112h-59zM393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q97 0 163 -24t101.5 -76t39 -100t3.5 -64q0 -71 -20 -163q-23 -110 -59 -187.5t-88.5 -126t-123 -71
t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5zM735 1200
h70l-179 -170h-57z" />
    <glyph glyph-name="uni1ED1" unicode="&#x1ed1;" horiz-adv-x="690" 
d="M425 802h65l91 -170h-51l-76 137l-135 -137h-56zM299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q70 0 121 -17t80.5 -53.5t35 -74t5.5 -57.5q0 -43 -12 -97q-16 -76 -42 -130t-65 -88t-92.5 -49.5
t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45zM603 1015h70l-179 -170h-57z" />
    <glyph glyph-name="uni1ED2" unicode="&#x1ed2;" horiz-adv-x="878" 
d="M555 987h73l105 -140h-50l-93 112l-141 -112h-59zM393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q97 0 163 -24t101.5 -76t39 -100t3.5 -64q0 -71 -20 -163q-23 -110 -59 -187.5t-88.5 -126t-123 -71
t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5zM469 1209
h64l94 -170h-51z" />
    <glyph glyph-name="uni1ED3" unicode="&#x1ed3;" horiz-adv-x="690" 
d="M425 802h65l91 -170h-51l-76 137l-135 -137h-56zM299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q70 0 121 -17t80.5 -53.5t35 -74t5.5 -57.5q0 -43 -12 -97q-16 -76 -42 -130t-65 -88t-92.5 -49.5
t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45zM337 1024h64l94 -170h-51z" />
    <glyph glyph-name="uni1ED4" unicode="&#x1ed4;" horiz-adv-x="878" 
d="M703 1006q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM555 987
h73l105 -140h-50l-93 112l-141 -112h-59zM393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q97 0 163 -24t101.5 -76t39 -100t3.5 -64q0 -71 -20 -163q-23 -110 -59 -187.5t-88.5 -126t-123 -71t-163.5 -22.5z
M398 41q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5z" />
    <glyph glyph-name="uni1ED5" unicode="&#x1ed5;" horiz-adv-x="690" 
d="M577 827q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM425 802
h65l91 -170h-51l-76 137l-135 -137h-56zM299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q70 0 121 -17t80.5 -53.5t35 -74t5.5 -57.5q0 -43 -12 -97q-16 -76 -42 -130t-65 -88t-92.5 -49.5t-123.5 -15.5zM303 36
q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45z" />
    <glyph glyph-name="uni1ED6" unicode="&#x1ed6;" horiz-adv-x="878" 
d="M555 987h73l105 -140h-50l-93 112l-141 -112h-59zM393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q97 0 163 -24t101.5 -76t39 -100t3.5 -64q0 -71 -20 -163q-23 -110 -59 -187.5t-88.5 -126t-123 -71
t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5zM433 1091
q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41z" />
    <glyph glyph-name="uni1ED7" unicode="&#x1ed7;" horiz-adv-x="690" 
d="M425 802h65l91 -170h-51l-76 137l-135 -137h-56zM299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q70 0 121 -17t80.5 -53.5t35 -74t5.5 -57.5q0 -43 -12 -97q-16 -76 -42 -130t-65 -88t-92.5 -49.5
t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45zM301 906q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5
t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41z" />
    <glyph glyph-name="uni1ED8" unicode="&#x1ed8;" horiz-adv-x="878" 
d="M393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q97 0 163 -24t101.5 -76t39 -100t3.5 -64q0 -71 -20 -163q-23 -110 -59 -187.5t-88.5 -126t-123 -71t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5
t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5zM353 -110l-31 -116h-88l31 116h88zM572 1054h65l91 -170
h-51l-76 137l-135 -137h-56z" />
    <glyph glyph-name="uni1ED9" unicode="&#x1ed9;" horiz-adv-x="690" 
d="M299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q70 0 121 -17t80.5 -53.5t35 -74t5.5 -57.5q0 -43 -12 -97q-16 -76 -42 -130t-65 -88t-92.5 -49.5t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5
t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45zM298 -122l-31 -104h-76l30 104h77zM425 802h65l91 -170h-51l-76 137l-135 -137h-56z" />
    <glyph glyph-name="uni1EDA" unicode="&#x1eda;" horiz-adv-x="878" 
d="M393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q81 0 138 -16h23q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29l-12 -6q64 -48 75 -109t11 -99q0 -71 -20 -164q-23 -110 -59 -187.5t-88.5 -126t-123 -71
t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5zM703 1045
h70l-179 -170h-57z" />
    <glyph glyph-name="uni1EDB" unicode="&#x1edb;" horiz-adv-x="690" 
d="M299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q35 0 65 -4t56 -13h15q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29q-8 -5 -17 -8q36 -36 43 -76.5t7 -65.5q0 -44 -11 -99q-16 -76 -42 -130t-65 -88
t-92.5 -49.5t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45zM556 793h70l-179 -170h-57z" />
    <glyph glyph-name="uni1EDC" unicode="&#x1edc;" horiz-adv-x="878" 
d="M393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q81 0 138 -16h23q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29l-12 -6q64 -48 75 -109t11 -99q0 -71 -20 -164q-23 -110 -59 -187.5t-88.5 -126t-123 -71
t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5zM437 1054
h64l94 -170h-51z" />
    <glyph glyph-name="uni1EDD" unicode="&#x1edd;" horiz-adv-x="690" 
d="M299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q35 0 65 -4t56 -13h15q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29q-8 -5 -17 -8q36 -36 43 -76.5t7 -65.5q0 -44 -11 -99q-16 -76 -42 -130t-65 -88
t-92.5 -49.5t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45zM290 802h64l94 -170h-51z" />
    <glyph glyph-name="uni1EDE" unicode="&#x1ede;" horiz-adv-x="878" 
d="M515 938q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM393 -16
q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q81 0 138 -16h23q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29l-12 -6q64 -48 75 -109t11 -99q0 -71 -20 -164q-23 -110 -59 -187.5t-88.5 -126t-123 -71
t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5z" />
    <glyph glyph-name="uni1EDF" unicode="&#x1edf;" horiz-adv-x="690" 
d="M412 694q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM299 -16
q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q35 0 65 -4t56 -13h15q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29q-8 -5 -17 -8q36 -36 43 -76.5t7 -65.5q0 -44 -11 -99q-16 -76 -42 -130t-65 -88t-92.5 -49.5
t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45z" />
    <glyph glyph-name="uni1EE0" unicode="&#x1ee0;" horiz-adv-x="878" 
d="M393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q81 0 138 -16h23q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29l-12 -6q64 -48 75 -109t11 -99q0 -71 -20 -164q-23 -110 -59 -187.5t-88.5 -126t-123 -71
t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5zM401 936
q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41z" />
    <glyph glyph-name="uni1EE1" unicode="&#x1ee1;" horiz-adv-x="690" 
d="M299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q35 0 65 -4t56 -13h15q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29q-8 -5 -17 -8q36 -36 43 -76.5t7 -65.5q0 -44 -11 -99q-16 -76 -42 -130t-65 -88
t-92.5 -49.5t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45zM254 684q11 52 37.5 77t66.5 25
q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41z" />
    <glyph glyph-name="uni1EE2" unicode="&#x1ee2;" horiz-adv-x="878" 
d="M393 -16q-96 0 -162 24t-101.5 76t-39 100t-3.5 64q0 70 20 163q24 110 59.5 187.5t88 126t122.5 71t163 22.5q81 0 138 -16h23q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29l-12 -6q64 -48 75 -109t11 -99q0 -71 -20 -164q-23 -110 -59 -187.5t-88.5 -126t-123 -71
t-163.5 -22.5zM398 41q81 0 141 19.5t103.5 62.5t73.5 111.5t51 166.5q18 86 18 149q0 9 -1.5 47.5t-29.5 81.5t-82.5 62.5t-136.5 19.5q-80 0 -139.5 -19.5t-103.5 -62.5t-74 -111.5t-51 -166.5q-18 -86 -18 -149q0 -9 1.5 -47.5t29.5 -81.5t82 -62.5t136 -19.5zM353 -110
l-31 -116h-88l31 116h88z" />
    <glyph glyph-name="uni1EE3" unicode="&#x1ee3;" horiz-adv-x="690" 
d="M299 -16q-69 0 -120 17t-80.5 53.5t-34.5 74t-5 58.5q0 43 11 97q16 77 42.5 130.5t65.5 87t92 49t122 15.5q35 0 65 -4t56 -13h15q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29q-8 -5 -17 -8q36 -36 43 -76.5t7 -65.5q0 -44 -11 -99q-16 -76 -42 -130t-65 -88
t-92.5 -49.5t-123.5 -15.5zM303 36q58 0 101 13.5t74 42.5t52 74.5t34 109.5q11 48 11 86q0 12 -3 40t-25 56.5t-61.5 42t-96.5 13.5q-58 0 -101 -13.5t-74 -42t-51.5 -74t-34.5 -108.5q-11 -49 -11 -88q0 -62 36 -107t150 -45zM294 -110l-31 -116h-88l31 116h88z" />
    <glyph glyph-name="uni1EE4" unicode="&#x1ee4;" horiz-adv-x="844" 
d="M392 -16q-178 0 -233 60.5t-55 146.5q0 44 12 98l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h60l-112 -527q-16 -77 -41 -132t-64.5 -90.5t-96.5 -52t-138 -16.5zM353 -110
l-31 -116h-88l31 116h88z" />
    <glyph glyph-name="uni1EE5" unicode="&#x1ee5;" horiz-adv-x="720" 
d="M278 -16q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 459h57l-117 -550h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5zM324 -122l-31 -104h-76l30 104h77z" />
    <glyph glyph-name="uni1EE6" unicode="&#x1ee6;" horiz-adv-x="844" 
d="M540 938q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM392 -16
q-178 0 -233 60.5t-55 146.5q0 44 12 98l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h60l-112 -527q-16 -77 -41 -132t-64.5 -90.5t-96.5 -52t-138 -16.5z" />
    <glyph glyph-name="uni1EE7" unicode="&#x1ee7;" horiz-adv-x="720" 
d="M437 684q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM278 -16
q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 459h57l-117 -550h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5z" />
    <glyph glyph-name="uni1EE8" unicode="&#x1ee8;" horiz-adv-x="844" 
d="M392 -16q-178 0 -233 60.5t-55 146.5q0 44 12 98l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h24q72 9 72 95h62q0 -33 -8 -57q-8 -25 -21 -42t-33 -29q-19 -12 -47 -17l-101 -477
q-16 -77 -41 -132t-64.5 -90.5t-96.5 -52t-138 -16.5zM686 1045h70l-179 -170h-57z" />
    <glyph glyph-name="uni1EE9" unicode="&#x1ee9;" horiz-adv-x="720" 
d="M278 -16q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 458l-1 1h23q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29t-48 -17l-106 -500h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5z
M571 793h70l-179 -170h-57z" />
    <glyph glyph-name="uni1EEA" unicode="&#x1eea;" horiz-adv-x="844" 
d="M392 -16q-178 0 -233 60.5t-55 146.5q0 44 12 98l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h24q72 9 72 95h62q0 -33 -8 -57q-8 -25 -21 -42t-33 -29q-19 -12 -47 -17l-101 -477
q-16 -77 -41 -132t-64.5 -90.5t-96.5 -52t-138 -16.5zM420 1054h64l94 -170h-51z" />
    <glyph glyph-name="uni1EEB" unicode="&#x1eeb;" horiz-adv-x="720" 
d="M278 -16q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 458l-1 1h23q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29t-48 -17l-106 -500h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5z
M305 802h64l94 -170h-51z" />
    <glyph glyph-name="uni1EEC" unicode="&#x1eec;" horiz-adv-x="844" 
d="M515 938q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM392 -16
q-178 0 -233 60.5t-55 146.5q0 44 12 98l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h24q72 9 72 95h62q0 -33 -8 -57q-8 -25 -21 -42t-33 -29q-19 -12 -47 -17l-101 -477
q-16 -77 -41 -132t-64.5 -90.5t-96.5 -52t-138 -16.5z" />
    <glyph glyph-name="uni1EED" unicode="&#x1eed;" horiz-adv-x="720" 
d="M442 697q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM278 -16
q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 458l-1 1h23q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29t-48 -17l-106 -500h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5z" />
    <glyph glyph-name="uni1EEE" unicode="&#x1eee;" horiz-adv-x="844" 
d="M392 -16q-178 0 -233 60.5t-55 146.5q0 44 12 98l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h24q72 9 72 95h62q0 -33 -8 -57q-8 -25 -21 -42t-33 -29q-19 -12 -47 -17l-101 -477
q-16 -77 -41 -132t-64.5 -90.5t-96.5 -52t-138 -16.5zM384 936q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41z" />
    <glyph glyph-name="uni1EEF" unicode="&#x1eef;" horiz-adv-x="720" 
d="M278 -16q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 458l-1 1h23q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29t-48 -17l-106 -500h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5z
M269 684q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41z" />
    <glyph glyph-name="uni1EF0" unicode="&#x1ef0;" horiz-adv-x="844" 
d="M392 -16q-178 0 -233 60.5t-55 146.5q0 44 12 98l108 513h61l-109 -513q-10 -50 -10 -89q0 -14 3 -43.5t29 -59t75.5 -42.5t123.5 -13q66 0 112.5 13t78 41t51.5 72.5t33 106.5l112 527h24q72 9 72 95h62q0 -33 -8 -57q-8 -25 -21 -42t-33 -29q-19 -12 -47 -17l-101 -477
q-16 -77 -41 -132t-64.5 -90.5t-96.5 -52t-138 -16.5zM353 -110l-31 -116h-88l31 116h88z" />
    <glyph glyph-name="uni1EF1" unicode="&#x1ef1;" horiz-adv-x="720" 
d="M278 -16q-121 0 -161.5 46t-40.5 104q0 23 5 49l79 367h56l-77 -367q-5 -21 -5 -39q0 -41 28.5 -74t126.5 -33q66 0 125.5 17t108.5 37l97 458l-1 1h23q72 9 72 95h62q0 -33 -8 -57.5t-21 -41.5t-33 -29t-48 -17l-106 -500h-42l-3 39q-51 -22 -113 -38.5t-124 -16.5z
M289 -110l-31 -116h-88l31 116h88z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="715" 
d="M369 987h63l79 -140h-53zM338 318l-208 484h65l180 -427l362 427h74l-413 -485l-68 -317h-60z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="646" 
d="M267 802h64l94 -170h-51zM-34 -161q30 -3 57 -3q30 0 54.5 5t44.5 16t37.5 29t35.5 44l49 73h-25l-128 547h57l116 -498h13l333 498h63l-421 -629q-25 -38 -48.5 -64t-49.5 -41.5t-57 -22.5t-71 -7q-18 0 -35.5 1t-34.5 4z" />
    <glyph glyph-name="uni1EF4" unicode="&#x1ef4;" horiz-adv-x="715" 
d="M338 318l-208 484h65l180 -427l362 427h74l-413 -485l-68 -317h-60zM324 -110l-31 -116h-76l30 116h77z" />
    <glyph glyph-name="uni1EF5" unicode="&#x1ef5;" horiz-adv-x="646" 
d="M-34 -161q30 -3 57 -3q30 0 54.5 5t44.5 16t37.5 29t35.5 44l49 73h-25l-128 547h57l116 -498h13l333 498h63l-421 -629q-25 -38 -48.5 -64t-49.5 -41.5t-57 -22.5t-71 -7q-18 0 -35.5 1t-34.5 4zM324 -122l-31 -104h-76l30 104h77z" />
    <glyph glyph-name="uni1EF6" unicode="&#x1ef6;" horiz-adv-x="715" 
d="M484 938q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM338 318
l-208 484h65l180 -427l362 427h74l-413 -485l-68 -317h-60z" />
    <glyph glyph-name="uni1EF7" unicode="&#x1ef7;" horiz-adv-x="646" 
d="M380 691q17 1 36.5 5t37.5 12.5t32.5 21t20.5 30.5q3 9 3 17q0 13 -7 25q-12 18 -47 18q-31 0 -57 -10t-50 -29q-3 5 -5.5 8t-3.5 5q-2 2 -4 5t-5 8q32 21 67 36q31 12 63 12h9q50 -3 72 -28q13 -14 13 -36q0 -14 -6 -32q-20 -43 -55 -64t-80 -29l-20 -51h-32zM-34 -161
q30 -3 57 -3q30 0 54.5 5t44.5 16t37.5 29t35.5 44l49 73h-25l-128 547h57l116 -498h13l333 498h63l-421 -629q-25 -38 -48.5 -64t-49.5 -41.5t-57 -22.5t-71 -7q-18 0 -35.5 1t-34.5 4z" />
    <glyph glyph-name="uni1EF8" unicode="&#x1ef8;" horiz-adv-x="715" 
d="M338 318l-208 484h65l180 -427l362 427h74l-413 -485l-68 -317h-60zM321 936q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63
l-6 -32h-41z" />
    <glyph glyph-name="uni1EF9" unicode="&#x1ef9;" horiz-adv-x="646" 
d="M-34 -161q30 -3 57 -3q30 0 54.5 5t44.5 16t37.5 29t35.5 44l49 73h-25l-128 547h57l116 -498h13l333 498h63l-421 -629q-25 -38 -48.5 -64t-49.5 -41.5t-57 -22.5t-71 -7q-18 0 -35.5 1t-34.5 4zM231 684q11 52 37.5 77t66.5 25q33 0 53.5 -15.5t37 -34.5t33.5 -34.5
t42 -15.5q47 0 61 65l6 30h41l-7 -33q-10 -51 -36.5 -77t-65.5 -26q-33 0 -53.5 15.5t-37 34t-33.5 34t-42 15.5q-50 0 -62 -63l-6 -32h-41z" />
    <glyph glyph-name="uni2000" unicode="&#x2000;" 
 />
    <glyph glyph-name="uni2001" unicode="&#x2001;" horiz-adv-x="1520" 
 />
    <glyph glyph-name="uni2002" unicode="&#x2002;" 
 />
    <glyph glyph-name="uni2003" unicode="&#x2003;" horiz-adv-x="1520" 
 />
    <glyph glyph-name="uni2004" unicode="&#x2004;" horiz-adv-x="507" 
 />
    <glyph glyph-name="uni2005" unicode="&#x2005;" horiz-adv-x="380" 
 />
    <glyph glyph-name="uni2006" unicode="&#x2006;" horiz-adv-x="253" 
 />
    <glyph glyph-name="uni2007" unicode="&#x2007;" horiz-adv-x="690" 
 />
    <glyph glyph-name="uni2008" unicode="&#x2008;" horiz-adv-x="380" 
 />
    <glyph glyph-name="uni2009" unicode="&#x2009;" horiz-adv-x="190" 
 />
    <glyph glyph-name="uni200A" unicode="&#x200a;" horiz-adv-x="152" 
 />
    <glyph glyph-name="uni200B" unicode="&#x200b;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni200C" unicode="&#x200c;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni200D" unicode="&#x200d;" horiz-adv-x="0" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" 
d="M-5 250l12 52h760l-12 -52h-760z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1520" 
d="M-5 250l12 52h1520l-12 -52h-1520z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="380" 
d="M238 533h-66l146 269h56z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="380" 
d="M309 802h66l-146 -269h-56z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="380" 
d="M166 85h66l-146 -269h-57z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="552" 
d="M238 533h-66l146 269h56zM429 533h-66l146 269h56z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="552" 
d="M309 802h66l-146 -269h-56zM500 802h66l-146 -269h-56z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="551" 
d="M357 86h66l-146 -269h-57zM166 85h66l-146 -269h-57z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" 
d="M378 400l20 95h-245l13 55h244l53 252h58l-53 -252h245l-11 -55h-245l-20 -95l-123 -546h-45z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" 
d="M83 161h244l71 334h-245l13 55h244l53 252h58l-53 -252h245l-11 -55h-246l-70 -334h245l-12 -55h-245l-53 -252h-59l53 252h-244z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="416" 
d="M196 177q-51 0 -69 20t-18 50q0 17 4 37q11 49 38.5 70t73.5 21q49 0 66.5 -20.5t17.5 -49.5q0 -17 -4 -37q-11 -49 -38 -70t-71 -21z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="758" 
d="M110 75h76l-15 -75h-76zM309 75h76l-15 -75h-76zM507 75h76l-15 -75h-76z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1235" 
d="M255 481q-85 0 -114.5 33.5t-29.5 82.5q0 26 7 60q18 87 62.5 124t127.5 37q85 0 114.5 -33.5t29.5 -81.5q0 -27 -7 -61q-18 -87 -62 -124t-128 -37zM142 0h-59l676 802h60zM259 521q60 0 90.5 29.5t45.5 98.5q6 28 6 50q0 32 -18 55.5t-78 23.5q-61 0 -92 -30t-45 -99
q-5 -27 -5 -48q0 -32 17.5 -56t78.5 -24zM593 -16q-84 0 -114 33.5t-30 82.5q0 26 7 59q18 88 62.5 125t127.5 37q85 0 114.5 -33.5t29.5 -81.5q0 -27 -7 -61q-18 -87 -62 -124t-128 -37zM983 -16q-84 0 -114 33.5t-30 82.5q0 26 7 59q18 88 62 125t127 37q85 0 115 -33.5
t30 -82.5q0 -26 -7 -60q-18 -87 -62 -124t-128 -37zM597 24q60 0 90.5 29.5t45.5 98.5q6 28 6 49q0 32 -18 55.5t-78 23.5q-61 0 -92 -29.5t-45 -98.5q-5 -27 -5 -48q0 -32 17.5 -56t78.5 -24zM987 24q60 0 90.5 29.5t45.5 98.5q6 28 6 49q0 32 -18 55.5t-78 23.5
q-61 0 -92 -29.5t-45 -98.5q-5 -27 -5 -48q0 -32 17.5 -56t78.5 -24z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="307" 
d="M256 509h64l-218 -235l117 -229h-61l-118 232z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="307" 
d="M52 44h-63l217 234l-116 230h60l119 -232z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="175" 
d="M423 802h61l-678 -802h-59z" />
    <glyph glyph-name="uni2060" unicode="&#x2060;" horiz-adv-x="0" 
 />
    <glyph glyph-name="uni2070" unicode="&#x2070;" horiz-adv-x="398" 
d="M347 1009q45 0 76.5 -13t48 -42t17.5 -55t1 -30q0 -45 -13 -107q-13 -61 -29.5 -104.5t-41 -71.5t-59 -41.5t-82.5 -13.5q-46 0 -77.5 13t-48 42t-17.5 55t-1 30q0 45 13 107q13 61 30 104.5t41.5 71.5t59 41.5t82.5 13.5zM268 575q34 0 59 10t43 33.5t31.5 60.5t24.5 91
q12 54 12.5 74.5t0.5 23.5q0 33 -9.5 55t-31 32t-55.5 10t-58.5 -10t-42.5 -33.5t-31.5 -60.5t-24.5 -91q-12 -54 -12.5 -74.5t-0.5 -24.5q0 -32 9 -54t30.5 -32t55.5 -10z" />
    <glyph glyph-name="uni2074" unicode="&#x2074;" horiz-adv-x="398" 
d="M461 802l-55 -262h-51l22 106h-277l9 40l233 314h58l-232 -312h218l24 114h51z" />
    <glyph glyph-name="uni2075" unicode="&#x2075;" horiz-adv-x="398" 
d="M108 591q29 -8 65.5 -12t77.5 -4q36 0 60.5 5.5t40 16.5t24 28t13.5 40q3 18 3 32q0 32 -20 49.5t-83 17.5h-126l51 236h279l-9 -42h-228l-32 -152h74q88 0 118 -28t30 -72q0 -21 -5 -47q-7 -35 -21 -59.5t-37.5 -39.5t-56.5 -22t-80 -7q-48 0 -85 4.5t-63 11.5z" />
    <glyph glyph-name="uni2076" unicode="&#x2076;" horiz-adv-x="398" 
d="M278 531q-52 0 -85.5 13.5t-50 42.5t-17 53.5t-0.5 25.5q0 46 14 111q15 65 32.5 109.5t43 71.5t61.5 39t86 12q41 0 71 -4.5t54 -11.5l-10 -44q-24 7 -52 11.5t-62 4.5q-38 0 -65 -8.5t-46 -29t-32 -54t-24 -83.5q26 11 54.5 18t67.5 7q36 0 64.5 -8.5t46 -27t22 -38.5
t4.5 -34q0 -19 -5 -41q-16 -76 -57 -105.5t-115 -29.5zM307 772q-38 0 -65.5 -6.5t-55.5 -17.5q-10 -49 -10.5 -66.5t-0.5 -18.5q0 -32 11 -52t34 -29t59 -9q51 0 79 21t40 79q3 17 3 31q0 31 -21 49.5t-73 18.5z" />
    <glyph glyph-name="uni2077" unicode="&#x2077;" horiz-adv-x="398" 
d="M187 1000h336l-9 -40l-318 -420h-64l324 418h-279z" />
    <glyph glyph-name="uni2078" unicode="&#x2078;" horiz-adv-x="398" 
d="M261 531q-91 0 -122 27t-31 68q0 17 4 37q11 51 36.5 77t67.5 35q-31 11 -42 30.5t-11 41.5q0 18 5 40q6 31 19 54t34.5 38t53.5 22.5t77 7.5q85 0 114.5 -26.5t29.5 -66.5q0 -16 -4 -35q-11 -50 -34.5 -75.5t-61.5 -35.5q35 -9 48 -30t13 -47q0 -17 -4 -39
q-7 -31 -20 -54t-35.5 -38.5t-56 -23t-80.5 -7.5zM311 792q30 0 51.5 4t36.5 13.5t24.5 26t14.5 41.5q4 16 4 29q0 24 -15 42t-80 18q-32 0 -54.5 -5.5t-37 -16.5t-23.5 -27.5t-14 -39.5q-3 -16 -3 -29q0 -27 17.5 -41.5t78.5 -14.5zM265 574q34 0 58 5.5t40 16.5t25 28
t14 40q4 17 4 30q0 29 -20 44.5t-84 15.5q-63 0 -94.5 -18.5t-42.5 -71.5q-4 -16 -4 -30q0 -5 1.5 -16t13 -22t33 -16.5t56.5 -5.5z" />
    <glyph glyph-name="uni2079" unicode="&#x2079;" horiz-adv-x="398" 
d="M332 1009q52 0 85.5 -13.5t50 -42.5t17 -53.5t0.5 -25.5q0 -46 -14 -111q-15 -65 -32.5 -109.5t-43.5 -71.5t-61.5 -39t-86.5 -12q-40 0 -70.5 4.5t-53.5 11.5l9 44q25 -7 53 -11.5t62 -4.5q38 0 64.5 8.5t45.5 28.5t32.5 53.5t23.5 83.5q-25 -11 -53.5 -17.5t-67.5 -6.5
q-73 0 -105 30t-32 76q0 20 5 43q16 76 56.5 105.5t115.5 29.5zM212 867q-3 -17 -3 -31q0 -31 21 -49.5t73 -18.5q38 0 65.5 6.5t54.5 17.5q10 49 10.5 66.5t0.5 18.5q0 32 -11 52t-34 29t-59 9q-51 0 -78.5 -21t-39.5 -79z" />
    <glyph glyph-name="uni2080" unicode="&#x2080;" horiz-adv-x="399" 
d="M191 271q45 0 76.5 -13t48 -42t17.5 -55t1 -30q0 -45 -13 -107q-13 -61 -29.5 -104.5t-41 -71.5t-59 -41.5t-82.5 -13.5q-46 0 -77.5 13t-48 42t-17.5 55t-1 30q0 45 13 107q13 61 30 104.5t41.5 71.5t59 41.5t82.5 13.5zM112 -163q34 0 59 10t43 33.5t31.5 60.5t24.5 91
q12 54 12.5 74.5t0.5 23.5q0 33 -9.5 55t-31 32t-55.5 10t-58.5 -10t-42.5 -33.5t-31.5 -60.5t-24.5 -91q-12 -54 -12.5 -74.5t-0.5 -24.5q0 -32 9 -54t30.5 -32t55.5 -10z" />
    <glyph glyph-name="uni2081" unicode="&#x2081;" horiz-adv-x="311" 
d="M185 262h49l-98 -460h-51l86 403l-156 -70l-17 44z" />
    <glyph glyph-name="uni2082" unicode="&#x2082;" horiz-adv-x="399" 
d="M188 271q90 0 118 -26t28 -64q0 -15 -4 -33q-5 -21 -11 -35t-17 -25.5t-29 -23t-46 -26.5l-151 -82q-17 -10 -28 -17.5t-17.5 -15.5t-10.5 -18t-7 -24l-8 -37h265l-9 -42h-316l18 87q4 19 10 33t16 25.5t25 21.5t37 22l157 85q20 11 32.5 19t20 16t11 16.5t6.5 21.5
q2 12 2 22q0 25 -17.5 40.5t-82.5 15.5q-36 0 -71 -4.5t-64 -10.5l9 45q29 6 60.5 10t73.5 4z" />
    <glyph glyph-name="uni2083" unicode="&#x2083;" horiz-adv-x="399" 
d="M-47 -148q58 -16 137 -16q40 0 66 4.5t42 14.5t24.5 26t13.5 38q5 20 5 36q0 26 -16.5 42.5t-78.5 16.5h-106l8 42h104q56 0 83.5 14.5t35.5 57.5q4 18 4 33q0 28 -17.5 47.5t-86.5 19.5q-39 0 -70.5 -3.5t-61.5 -11.5l9 44q27 6 59 10t74 4q92 0 120.5 -28.5t28.5 -70.5
q0 -20 -5 -44q-9 -45 -33 -66.5t-61 -29.5q38 -10 50 -33t12 -46q0 -20 -6 -45q-7 -31 -20 -52.5t-35.5 -35.5t-57 -20.5t-85.5 -6.5q-48 0 -83 4t-63 12z" />
    <glyph glyph-name="uni2084" unicode="&#x2084;" horiz-adv-x="399" 
d="M305 64l-55 -262h-51l22 106h-277l9 40l233 314h58l-232 -312h218l24 114h51z" />
    <glyph glyph-name="uni2085" unicode="&#x2085;" horiz-adv-x="399" 
d="M-48 -147q29 -8 65.5 -12t77.5 -4q36 0 60.5 5.5t40 16.5t24 28t13.5 40q3 18 3 32q0 32 -20 49.5t-83 17.5h-126l51 236h279l-9 -42h-228l-32 -152h74q88 0 118 -28t30 -72q0 -21 -5 -47q-7 -35 -21 -59.5t-37.5 -39.5t-56.5 -22t-80 -7q-48 0 -85 4.5t-63 11.5z" />
    <glyph glyph-name="uni2086" unicode="&#x2086;" horiz-adv-x="399" 
d="M122 -207q-52 0 -85.5 13.5t-50 42.5t-17 53.5t-0.5 25.5q0 46 14 111q15 65 32.5 109.5t43 71.5t61.5 39t86 12q41 0 71 -4.5t54 -11.5l-10 -44q-24 7 -52 11.5t-62 4.5q-38 0 -65 -8.5t-46 -29t-32 -54t-24 -83.5q26 11 54.5 18t67.5 7q36 0 64.5 -8.5t46 -27t22 -38.5
t4.5 -34q0 -19 -5 -41q-16 -76 -57 -105.5t-115 -29.5zM151 34q-38 0 -65.5 -6.5t-55.5 -17.5q-10 -49 -10.5 -66.5t-0.5 -18.5q0 -32 11 -52t34 -29t59 -9q51 0 79 21t40 79q3 17 3 31q0 31 -21 49.5t-73 18.5z" />
    <glyph glyph-name="uni2087" unicode="&#x2087;" horiz-adv-x="399" 
d="M31 262h336l-9 -40l-318 -420h-64l324 418h-279z" />
    <glyph glyph-name="uni2088" unicode="&#x2088;" horiz-adv-x="399" 
d="M105 -207q-91 0 -122 27t-31 68q0 17 4 37q11 51 36.5 77t67.5 35q-31 11 -42 30.5t-11 41.5q0 18 5 40q6 31 19 54t34.5 38t53.5 22.5t77 7.5q85 0 114.5 -26.5t29.5 -66.5q0 -16 -4 -35q-11 -50 -34.5 -75.5t-61.5 -35.5q35 -9 48 -30t13 -47q0 -17 -4 -39
q-7 -31 -20 -54t-35.5 -38.5t-56 -23t-80.5 -7.5zM155 54q30 0 51.5 4t36.5 13.5t24.5 26t14.5 41.5q4 16 4 29q0 24 -15 42t-80 18q-32 0 -54.5 -5.5t-37 -16.5t-23.5 -27.5t-14 -39.5q-3 -16 -3 -29q0 -27 17.5 -41.5t78.5 -14.5zM109 -164q34 0 58 5.5t40 16.5t25 28
t14 40q4 17 4 30q0 29 -20 44.5t-84 15.5q-63 0 -94.5 -18.5t-42.5 -71.5q-4 -16 -4 -30q0 -5 1.5 -16t13 -22t33 -16.5t56.5 -5.5z" />
    <glyph glyph-name="uni2089" unicode="&#x2089;" horiz-adv-x="399" 
d="M176 271q52 0 85.5 -13.5t50 -42.5t17 -53.5t0.5 -25.5q0 -46 -14 -111q-15 -65 -32.5 -109.5t-43.5 -71.5t-61.5 -39t-86.5 -12q-40 0 -70.5 4.5t-53.5 11.5l9 44q25 -7 53 -11.5t62 -4.5q38 0 64.5 8.5t45.5 28.5t32.5 53.5t23.5 83.5q-25 -11 -53.5 -17.5t-67.5 -6.5
q-36 0 -64.5 8.5t-46 27t-22 38.5t-4.5 34q0 19 5 41q16 76 56.5 105.5t115.5 29.5zM56 129q-3 -17 -3 -31q0 -31 21 -49.5t73 -18.5q38 0 65.5 6.5t54.5 17.5q10 49 10.5 66.5t0.5 18.5q0 32 -11 52t-34 29t-59 9q-51 0 -78.5 -21t-39.5 -79z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="690" 
d="M51 328h94q5 37 12.5 73.5t15.5 73.5h-91l11 48h93q23 80 54 136t74.5 91.5t101.5 51.5t135 16q62 0 103 -7t76 -18l-11 -53q-37 11 -77 17.5t-94 6.5q-64 0 -112.5 -13.5t-84 -42.5t-61 -74.5t-44.5 -110.5h384l-10 -48h-387q-9 -36 -16.5 -73t-12.5 -74h385l-11 -49
h-381q-3 -30 -3 -55q0 -32 6 -68t29.5 -64.5t64 -41t99.5 -12.5q54 0 97 6.5t85 17.5l-12 -53q-37 -11 -78 -18t-97 -7q-72 0 -122 16.5t-80.5 52t-39.5 81.5t-9 92q0 25 2 53h-99z" />
    <glyph glyph-name="uni2113" unicode="&#x2113;" 
d="M382 -16q-44 0 -77 12t-53.5 39t-24.5 56.5t-4 48.5q0 31 6 70q-23 -17 -45.5 -32.5t-45.5 -30.5l-27 45q32 22 65.5 45.5t68.5 48.5l74 349q11 53 27.5 88t40 56t54 30t70.5 9q38 0 67 -9t46.5 -29t20.5 -41.5t3 -32.5q0 -27 -8 -62q-10 -46 -42 -94.5t-79.5 -98
t-105.5 -99t-121 -96.5l-3 -15q-12 -54 -12 -93q0 -41 19 -76.5t89 -35.5q59 0 104.5 34.5t88.5 90.5l38 -34q-52 -70 -106 -106.5t-128 -36.5zM309 333q50 38 96 77.5t83 79.5t62.5 79.5t34.5 78.5q6 28 6 48q0 5 -1 19t-11.5 27.5t-28.5 18.5t-43 5q-57 0 -88.5 -30.5
t-45.5 -100.5z" />
    <glyph glyph-name="uni2116" unicode="&#x2116;" horiz-adv-x="1390" 
d="M234 802h77l302 -708l150 708h63l-170 -802h-79l-300 707l-149 -707h-64zM869 412q14 64 37 108t55 71.5t74.5 40t95.5 12.5q125 0 163.5 -48t38.5 -115q0 -39 -10 -89q-13 -64 -36 -108t-55 -72t-74.5 -40.5t-95.5 -12.5q-124 0 -163.5 48.5t-39.5 117.5q0 38 10 87z
M1267 401q8 40 8 71q0 48 -26 83.5t-120 35.5q-86 0 -134 -44.5t-69 -144.5q-8 -40 -8 -72q0 -48 25.5 -83.5t120.5 -35.5q85 0 133 45t70 145zM821 55h404l-11 -55h-405z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="878" 
d="M255 756h-114l7 46h281l-8 -46h-114l-75 -355h-52zM509 802h65l75 -269l189 269h69l-85 -401h-51l69 325l-169 -238h-60l-68 239l-69 -326h-50z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="878" 
d="M205 381l-54 -252q29 -53 86.5 -75.5t148.5 -22.5q116 0 193.5 35t128.5 119h58q-30 -56 -68.5 -94.5t-85.5 -62t-104 -34t-124 -10.5q-97 0 -164 23.5t-104 74.5t-41 99.5t-4 67.5q0 68 19 158q24 113 63.5 191.5t95.5 127t131 70.5t169 22q96 0 162.5 -23.5t103.5 -74
t41.5 -100t4.5 -69.5q0 -67 -18 -154l-3 -16h-635zM547 771q-91 0 -160.5 -24.5t-120.5 -78.5l-52 -243h517l52 249q-29 51 -87 74t-149 23z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" 
d="M94 276l294 294l36 -36l-231 -231h567v-54h-568l232 -232l-36 -36z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" 
d="M380 617l294 -294l-35 -36l-232 231v-518h-54v519l-232 -232l-36 36z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" 
d="M340 12l240 240h-580v48h580l-240 239l31 31l295 -294l-295 -295z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" 
d="M117 259l239 -239v530h48v-530l240 239l30 -31l-294 -294l-294 294z" />
    <glyph glyph-name="uni2196" unicode="&#x2196;" 
d="M136 514h416v-44h-337l409 -410l-34 -34l-410 409v-337h-44v416z" />
    <glyph glyph-name="uni2197" unicode="&#x2197;" 
d="M580 134v338l-409 -409l-35 34l410 410h-338v43h416v-416h-44z" />
    <glyph glyph-name="uni2198" unicode="&#x2198;" 
d="M624 0h-416v44h337l-409 410l35 34l409 -409v337h44v-416z" />
    <glyph glyph-name="uni2199" unicode="&#x2199;" 
d="M180 416v-338l410 410l34 -34l-409 -410h337v-44h-416v416h44z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="728" 
d="M640 287q-18 -82 -44.5 -139.5t-64.5 -94t-89 -53t-117 -16.5q-73 0 -124.5 16.5t-81 53t-34 74t-4.5 55.5q0 47 13 107q30 141 106 206.5t207 65.5q52 0 101 -12t87 -29q-15 84 -71 151t-164 130h75q78 -46 126.5 -97.5t72.5 -113.5t24.5 -100t0.5 -40q0 -73 -19 -164z
M583 297q10 48 14.5 84t4.5 64q0 11 -1 22q-37 18 -84.5 31.5t-104.5 13.5q-106 0 -170.5 -55t-91.5 -183q-10 -49 -10 -87q0 -61 34 -107t151 -46q56 0 97.5 14.5t72 46t52 81.5t36.5 121z" />
    <glyph glyph-name="uni2206" unicode="&#x2206;" 
d="M438 802h109l111 -802h-674zM588 55l-92 700h-20l-393 -700h505z" />
    <glyph glyph-name="product" unicode="&#x220f;" 
d="M244 747h-75l11 55h624l-11 -55h-76l-181 -856h-59l181 856h-355l-181 -856h-60z" />
    <glyph glyph-name="summation" unicode="&#x2211;" 
d="M30 -70l415 425l-233 408l8 39h551l-11 -55h-471l229 -400l-390 -401h462l-12 -55h-556z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M133 300h508l-11 -49h-508z" />
    <glyph glyph-name="radical" unicode="&#x221a;" 
d="M122 550h190l34 -480l453 917h61l-487 -987h-78l-36 497h-148z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" 
d="M410 426l30 41q37 51 80.5 76.5t89.5 25.5q31 0 57 -9.5t42.5 -32t19 -46.5t2.5 -35q0 -32 -9 -75q-11 -54 -29 -89t-40.5 -55.5t-49 -28t-56.5 -7.5q-48 0 -81.5 27.5t-53.5 75.5l-16 39l-28 -38q-38 -51 -81.5 -77.5t-90.5 -26.5q-31 0 -57 9.5t-42 32t-19 47t-3 36.5
q0 32 9 73q12 57 30.5 92.5t42 55t49.5 26t53 6.5q49 0 81 -27t53 -75zM347 447q-15 34 -35.5 55t-55.5 21q-20 0 -39.5 -7t-36 -23.5t-29.5 -44t-22 -68.5q-7 -34 -7 -59q0 -34 17 -59t62 -25q35 0 68 20t60 58l47 64zM458 315q15 -35 36 -56.5t56 -21.5q44 0 77.5 31.5
t49.5 111.5q7 34 7 58q0 5 -1 20.5t-10.5 32.5t-26.5 24.5t-40 7.5q-71 0 -128 -77l-48 -66z" />
    <glyph glyph-name="integral" unicode="&#x222b;" 
d="M308 -39q-14 -46 -30 -79t-40 -54.5t-59 -32t-86 -10.5q-29 0 -52.5 1.5t-46.5 4.5l10 52q44 -6 88 -6q39 0 65 6.5t43 21t29 37.5t22 57l213 684q14 45 30.5 78t40.5 54.5t59 32t87 10.5q28 0 51 -1.5t48 -4.5l-10 -50q-23 2 -44.5 3.5t-43.5 1.5q-39 0 -65 -6.5
t-43.5 -20.5t-29 -37.5t-22.5 -57.5z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M148 371q12 60 42.5 89t86.5 29q42 0 75.5 -19t64 -42.5t60.5 -42.5t63 -19q38 0 56.5 19t26.5 58l9 38h47l-9 -44q-12 -59 -42.5 -88.5t-87.5 -29.5q-42 0 -75.5 19.5t-64 42.5t-60 42.5t-62.5 19.5q-38 0 -56 -19.5t-27 -57.5l-9 -39h-47zM94 119q12 60 42.5 89
t87.5 29q41 0 75 -19t64.5 -42.5t60 -42.5t62.5 -19q39 0 57 19t26 58l9 38h48l-10 -44q-12 -59 -42 -88.5t-87 -29.5q-42 0 -76 19.5t-64.5 42.5t-60 42.5t-62.5 19.5q-38 0 -56 -19.5t-27 -57.5l-8 -39h-48z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M100 146h159l187 261h-290l10 50h315l67 93h59l-67 -93h134l-11 -50h-158l-187 -261h290l-11 -50h-315l-69 -97l-59 1l69 96h-134z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M149 376l546 183l-11 -54l-490 -164l-2 -9l420 -164l-10 -49l-469 182zM79 50h508l-10 -50h-508z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M641 301l-547 -182l12 53l490 164l2 9l-421 164l10 50l470 -183zM79 50h508l-10 -50h-508z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" 
d="M142 401l319 401h62l149 -401l-319 -401h-62zM331 56l280 349l-128 341l-279 -348z" />
    <glyph glyph-name="uni25CC" unicode="&#x25cc;" horiz-adv-x="650" 
d="M325 -10q-59 0 -111 22.5t-90.5 61t-61 90.5t-22.5 111t22.5 111t61 90.5t90.5 61t111 22.5t111 -22.5t90.5 -61t61 -90.5t22.5 -111t-22.5 -111t-61 -90.5t-90.5 -61t-111 -22.5zM325 550q-57 0 -107 -21.5t-87.5 -59t-59 -87.5t-21.5 -107t21.5 -107t59 -87.5t87.5 -59
t107 -21.5t107 21.5t87.5 59t59 87.5t21.5 107t-21.5 107t-59 87.5t-87.5 59t-107 21.5z" />
    <glyph glyph-name="uniFEFF" unicode="&#xfeff;" horiz-adv-x="0" 
 />
    <glyph glyph-name="glyph1" horiz-adv-x="0" 
 />
    <glyph glyph-name="ampersand.1" horiz-adv-x="823" 
d="M539 160l-32 -38q-26 -31 -52 -56.5t-55.5 -43.5t-64.5 -28t-80 -10q-56 0 -97.5 14t-66 44t-28.5 61t-4 47q0 37 10 83q21 100 79.5 155t171.5 61q-17 23 -31 45t-22 47.5t-9 43t-1 22.5q0 26 6 56q8 36 22.5 65t42.5 49t72.5 30.5t113.5 10.5q68 0 117 -8t82 -17
l-11 -52q-36 8 -81 15t-106 7q-54 0 -88.5 -7t-56.5 -21t-32.5 -35t-16.5 -49q-5 -23 -5 -43q0 -4 0.5 -17t7 -34t19 -41.5t29.5 -43.5l172 -228l205 240h65l-238 -281l153 -203h-68zM126 221q-8 -39 -8 -69q0 -47 25 -83t118 -36q32 0 58 7t49.5 21t46.5 37t50 54l43 50
l-158 208q-53 -1 -91 -12.5t-64 -35t-42.5 -58.5t-26.5 -83z" />
    <glyph glyph-name="f.1" horiz-adv-x="370" 
d="M-151 -159q14 -2 35 -3t36 -1q57 0 88.5 26.5t45.5 94.5l115 544h-62l8 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63.5 34.5t89.5 11q49 0 98 -6l-10 -49q-22 2 -43 3.5t-44 1.5q-38 0 -65.5 -7.5t-47 -24t-32.5 -43t-21 -65.5l-17 -78h203l-11 -48h-202l-115 -542
q-10 -46 -24.5 -79t-36.5 -54t-53 -31t-73 -10q-20 0 -44 1t-42 4z" />
    <glyph glyph-name="commaaccentbelow" horiz-adv-x="570" 
d="M237 -53h74l-161 -145h-58z" />
    <glyph glyph-name="commaturnedabove" horiz-adv-x="570" 
d="M331 632h-69l178 170h57z" />
    <glyph glyph-name="gravecomb.case" horiz-adv-x="0" 
d="M10 987h63l79 -140h-53z" />
    <glyph glyph-name="acutecomb.case" horiz-adv-x="0" 
d="M228 987h72l-149 -140h-62z" />
    <glyph glyph-name="uni030B.case" horiz-adv-x="0" 
d="M132 987h70l-145 -140h-59zM322 987h70l-145 -140h-59z" />
    <glyph glyph-name="uni0302.case" horiz-adv-x="0" 
d="M116 987h73l105 -140h-50l-93 112l-141 -112h-59z" />
    <glyph glyph-name="uni030C.case" horiz-adv-x="0" 
d="M-19 987h50l93 -112l142 112h58l-165 -140h-73z" />
    <glyph glyph-name="uni0306.case" horiz-adv-x="0" 
d="M123 842q-91 0 -117 27.5t-26 68.5q0 22 6 49h46q-5 -24 -5 -42q0 -3 0.5 -14t11 -23.5t33 -18t58.5 -5.5t60.5 5.5t41 18t26 32t15.5 47.5h46q-8 -39 -20.5 -66.5t-34.5 -45t-56 -25.5t-85 -8z" />
    <glyph glyph-name="uni030A.case" horiz-adv-x="0" 
d="M120 832q-28 0 -50.5 5.5t-37.5 18.5t-19.5 28.5t-4.5 26.5q0 14 4 30q11 53 48 76.5t103 23.5q28 0 51 -5.5t38 -18.5t19 -28t4 -25q0 -14 -4 -31q-11 -54 -48 -77.5t-103 -23.5zM126 869q44 0 70.5 15t34.5 53q2 11 2 20q0 22 -16.5 34.5t-59.5 12.5q-44 0 -70.5 -15
t-34.5 -52q-2 -11 -2 -20q0 -22 16.5 -35t59.5 -13z" />
    <glyph glyph-name="tildecomb.case" horiz-adv-x="0" 
d="M-47 888q10 43 32 64.5t63 21.5q28 0 51.5 -12.5t44.5 -28t41 -28t40 -12.5q25 0 38 13t18 36l5 27h41l-6 -30q-8 -41 -31 -63.5t-64 -22.5q-28 0 -52 13t-45 28t-40.5 28t-39.5 13q-25 0 -37.5 -13t-18.5 -37l-6 -27h-40z" />
    <glyph glyph-name="uni0307.case" horiz-adv-x="0" 
d="M111 949h66l-14 -63h-65z" />
    <glyph glyph-name="uni0308.case" horiz-adv-x="0" 
d="M9 949h56l-13 -63h-57zM223 949h56l-14 -63h-56z" />
    <glyph glyph-name="uni0304.case" horiz-adv-x="0" 
d="M-19 933h319l-9 -39h-318z" />
    <glyph glyph-name="uni030C.alt" horiz-adv-x="0" 
d="M255 802h56l-146 -256h-37z" />
    <glyph glyph-name="uni0327.1" horiz-adv-x="0" 
d="M-182 -198h-58l162 199h56z" />
    <glyph glyph-name="Ccedilla.1" horiz-adv-x="825" 
d="M343 -14q-165 11 -210.5 89.5t-45.5 170.5q0 71 20 164q24 110 59.5 187t87 126.5t121 72t160.5 22.5q137 0 209.5 -61t78.5 -177h-63q-8 91 -64 136t-166 45q-79 0 -137.5 -19.5t-101.5 -62.5t-73 -111.5t-51 -166.5q-19 -88 -19 -153q0 -7 1.5 -45t29.5 -80.5t81.5 -62
t134.5 -19.5q104 0 172.5 41.5t115.5 140.5h66q-28 -64 -62.5 -109.5t-78.5 -74t-99 -42t-125 -13.5l-61 -53q6 1 12 1h12q54 0 72.5 -15t18.5 -41q0 -10 -2 -21q-4 -18 -11 -31.5t-20.5 -21.5t-35 -12t-54.5 -4q-24 0 -43.5 2.5t-33.5 6.5l8 32q30 -8 71 -8q47 0 62.5 9
t19.5 32q2 7 2 12q0 12 -9 21t-56 9q-19 0 -37 -1.5t-31 -3.5l7 29z" />
    <glyph glyph-name="germandbls.cap" horiz-adv-x="1530" 
d="M65 83q50 -16 125.5 -28.5t166.5 -12.5q59 0 100.5 8t70 26t45.5 46.5t26 68.5q7 31 7 56q0 7 -1.5 25t-22.5 40t-63 39t-113 32q-83 17 -134.5 38.5t-77 51.5t-27.5 55t-2 34q0 34 9 76q10 42 29 75.5t53.5 56.5t87.5 35t132 12q92 0 155.5 -10t114.5 -25l-12 -59
q-50 16 -116 26t-148 10q-61 0 -103 -8t-69 -24t-41.5 -39.5t-21.5 -55.5q-7 -34 -7 -60q0 -6 1 -23.5t20.5 -39t60 -37.5t107.5 -30q87 -18 140.5 -39t80.5 -51t29 -55t2 -35q0 -34 -9 -78q-12 -53 -33.5 -91t-57.5 -62t-89 -35.5t-128 -11.5q-100 0 -174 11t-125 28z
M830 83q50 -16 125.5 -28.5t166.5 -12.5q59 0 100.5 8t70 26t45.5 46.5t26 68.5q7 31 7 56q0 7 -1.5 25t-22.5 40t-63 39t-113 32q-83 17 -134.5 38.5t-77 51.5t-27.5 55t-2 34q0 34 9 76q10 42 29 75.5t53.5 56.5t87.5 35t132 12q92 0 155.5 -10t114.5 -25l-12 -59
q-50 16 -116 26t-148 10q-61 0 -103 -8t-69 -24t-41.5 -39.5t-21.5 -55.5q-7 -34 -7 -60q0 -6 1 -23.5t20.5 -39t60 -37.5t107.5 -30q87 -18 140.5 -39t80.5 -51t29 -55t2 -35q0 -34 -9 -78q-12 -53 -33.5 -91t-57.5 -62t-89 -35.5t-128 -11.5q-100 0 -174 11t-125 28z" />
    <glyph glyph-name="ccedilla.1" horiz-adv-x="599" 
d="M252 -14q-57 5 -98 25t-64.5 56.5t-26 70.5t-2.5 47q0 47 12 107q15 72 41 123.5t65 85t91.5 49.5t121.5 16q63 0 114 -7.5t87 -16.5l-9 -50q-35 8 -84.5 15t-108.5 7q-56 0 -99 -13t-74.5 -40.5t-53 -71.5t-34.5 -106q-11 -51 -11 -91q0 -13 3 -42t25 -58t61.5 -42
t94.5 -13q59 0 107 6t90 16l-10 -50q-48 -14 -98 -19.5t-100 -5.5l-61 -53q6 1 12 1h12q54 0 72.5 -15t18.5 -41q0 -10 -2 -21q-4 -18 -11 -31.5t-20.5 -21.5t-35 -12t-54.5 -4q-24 0 -43.5 2.5t-33.5 6.5l8 32q30 -8 71 -8q47 0 62.5 9t19.5 32q2 7 2 12q0 12 -9 21t-56 9
q-19 0 -37 -1.5t-31 -3.5l7 29z" />
    <glyph glyph-name="i.trk" horiz-adv-x="263" 
d="M201 754h65l-14 -68h-65zM162 550h57l-117 -550h-56z" />
    <glyph glyph-name="f_b.1" horiz-adv-x="1047" 
d="M-154 -159q14 -2 35 -3t36 -1q57 0 88.5 26.5t45.5 94.5l115 544h-61l7 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q39 0 78 -6l-10 -49q-17 2 -33 3.5t-33 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-16 -78h183l-11 -48h-183l-114 -542
q-10 -46 -24.5 -79t-36.5 -54t-53 -31t-74 -10q-20 0 -43.5 1t-41.5 4zM563 802h57l-60 -285q44 19 96.5 34t103.5 15q66 0 114.5 -17t77.5 -54t34 -74.5t5 -56.5q0 -46 -13 -105q-31 -145 -105.5 -210t-205.5 -65q-58 0 -113 16.5t-100 37.5l-19 -38h-42zM748 514
q-56 0 -108 -15.5t-91 -33.5l-81 -379q37 -20 84.5 -35t107.5 -15q56 0 99 13t74.5 41t53 72t34.5 105q11 51 11 91q0 13 -3 42.5t-25.5 58.5t-61.5 42t-94 13z" />
    <glyph glyph-name="f_f.1" horiz-adv-x="693" 
d="M172 -159q14 -2 35 -3t36 -1q57 0 88 26.5t46 94.5l115 544h-267l-114 -542q-10 -46 -24.5 -79t-36.5 -54t-53 -31t-73 -10q-20 0 -44 1t-42 4l11 50q14 -2 35 -3t36 -1q57 0 88.5 26.5t45.5 94.5l115 544h-61l7 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63.5 34.5
t89.5 11q39 0 79 -6l-10 -49q-17 2 -34 3.5t-34 1.5q-37 0 -65 -7.5t-47.5 -24t-32.5 -43t-22 -65.5l-16 -78h266l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q50 0 98 -6l-9 -49q-22 3 -43 4t-44 1q-38 0 -65.5 -7.5t-47.5 -24t-33 -43t-21 -65.5l-16 -78h202l-10 -48
h-203l-114 -542q-10 -46 -24.5 -79t-36.5 -54t-53 -31t-74 -10q-20 0 -43.5 1t-41.5 4z" />
    <glyph glyph-name="f_h.1" horiz-adv-x="1067" 
d="M-154 -159q14 -2 35 -3t36 -1q57 0 88.5 26.5t45.5 94.5l115 544h-61l7 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q39 0 78 -6l-10 -49q-17 2 -33 3.5t-33 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-16 -78h183l-11 -48h-183l-114 -542
q-10 -46 -24.5 -79t-36.5 -54t-53 -31t-74 -10q-20 0 -43.5 1t-41.5 4zM563 802h57l-61 -289q50 22 111.5 37.5t122.5 15.5q120 0 161 -45.5t41 -102.5q0 -24 -6 -51l-78 -367h-57l77 367q5 22 5 40q0 41 -28 73.5t-127 32.5q-66 0 -125 -14.5t-108 -34.5l-98 -464h-57z" />
    <glyph glyph-name="fi.1" horiz-adv-x="599" 
d="M-154 -159q14 -2 35 -3t36 -1q57 0 88.5 26.5t45.5 94.5l115 544h-62l8 36l64 12l16 76q11 50 27 86.5t42 60t62.5 34.5t87.5 11q32 0 60 -6l-10 -49q-12 2 -23.5 3.5t-25.5 1.5q-37 0 -64 -7.5t-46 -24t-32 -43t-21 -65.5l-16 -78h323l-118 -550h-56l107 502h-267
l-114 -542q-10 -46 -24.5 -79t-36.5 -54t-53 -31t-74 -10q-20 0 -43.5 1t-41.5 4zM544 782h65l-14 -69h-66z" />
    <glyph glyph-name="f_j.1" horiz-adv-x="599" 
d="M168 -164q14 -2 35.5 -3t35.5 -1q57 0 88 27.5t46 98.5l115 544h-266l-114 -542q-10 -46 -24.5 -79t-36.5 -54t-53 -31t-74 -10q-20 0 -43.5 1t-41.5 4l11 50q14 -2 35 -3t36 -1q57 0 88.5 26.5t45.5 94.5l115 544h-61l7 36l64 12l16 76q11 50 27 86.5t42 60t62.5 34.5
t87.5 11q30 0 60 -6l-10 -49q-12 2 -23.5 3.5t-25.5 1.5q-37 0 -64 -7.5t-46 -24t-32 -43t-21 -65.5l-16 -78h322l-125 -592q-10 -46 -25 -78.5t-36.5 -53.5t-52 -30.5t-72.5 -9.5q-20 0 -44.5 1t-41.5 4zM543 782h65l-13 -69h-66z" />
    <glyph glyph-name="f_k.1" horiz-adv-x="975" 
d="M-154 -159q14 -2 35 -3t36 -1q57 0 88.5 26.5t45.5 94.5l115 544h-61l7 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q39 0 78 -6l-10 -49q-17 2 -33 3.5t-33 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-16 -78h183l-11 -48h-183l-114 -542
q-10 -46 -24.5 -79t-36.5 -54t-53 -31t-74 -10q-20 0 -43.5 1t-41.5 4zM564 802h57l-99 -468l370 216h86l-397 -234l298 -316h-73l-289 314l-66 -314h-57z" />
    <glyph glyph-name="fl.1" horiz-adv-x="610" 
d="M-154 -159q14 -2 35 -3t36 -1q57 0 88.5 26.5t45.5 94.5l115 544h-61l7 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q39 0 78 -6l-10 -49q-17 2 -33 3.5t-33 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-16 -78h183l-11 -48h-183l-114 -542
q-10 -46 -24.5 -79t-36.5 -54t-53 -31t-74 -10q-20 0 -43.5 1t-41.5 4zM564 802h57l-171 -802h-57z" />
    <glyph glyph-name="f_f_b.1" horiz-adv-x="1371" 
d="M168 -159q14 -2 35.5 -3t36.5 -1q57 0 88 26.5t45 94.5l116 544h-267l-114 -542q-10 -46 -24.5 -79t-36.5 -54t-53 -31t-74 -10q-20 0 -43.5 1t-41.5 4l11 50q14 -2 35 -3t36 -1q57 0 88.5 26.5t45.5 94.5l115 544h-61l7 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5
t89 11q40 0 79 -6l-9 -49q-17 2 -34 3.5t-34 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-16 -78h266l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q40 0 77 -6l-9 -49q-17 2 -33 3.5t-33 1.5q-38 0 -65.5 -7.5t-47.5 -24t-33 -43t-21 -65.5l-16 -78h182l-10 -48h-183
l-114 -542q-10 -46 -25 -79t-37 -54t-53 -31t-73 -10q-20 0 -43.5 1t-41.5 4zM886 802h57l-61 -285q45 19 97 34t103 15q66 0 115 -17t78 -54t33.5 -74.5t4.5 -56.5q0 -46 -12 -105q-15 -72 -41 -124t-64.5 -85.5t-89.5 -49.5t-117 -16q-58 0 -113.5 16.5t-98.5 37.5
l-19 -38h-42zM1071 514q-57 0 -109.5 -15.5t-90.5 -33.5l-80 -379q36 -20 84 -35t108 -15q111 0 173 54t87 177q11 51 11 90q0 14 -3 43.5t-25 58.5t-61.5 42t-93.5 13z" />
    <glyph glyph-name="f_f_h.1" horiz-adv-x="1391" 
d="M168 -159q14 -2 35.5 -3t36.5 -1q57 0 88 26.5t45 94.5l116 544h-267l-114 -542q-10 -46 -24.5 -79t-36.5 -54t-53 -31t-74 -10q-20 0 -43.5 1t-41.5 4l11 50q14 -2 35 -3t36 -1q57 0 88.5 26.5t45.5 94.5l115 544h-61l7 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5
t89 11q40 0 79 -6l-9 -49q-17 2 -34 3.5t-34 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-16 -78h266l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q22 0 43 -1t42 -5l-10 -49q-18 3 -36 4t-37 1q-38 0 -65.5 -7.5t-47.5 -24t-33 -43t-21 -65.5l-16 -78h189l-10 -48
h-190l-114 -542q-10 -46 -25 -79t-37 -54t-53 -31t-73 -10q-20 0 -43.5 1t-41.5 4zM886 802h57l-61 -289q50 22 111 37.5t122 15.5q121 0 161.5 -45.5t40.5 -102.5q0 -24 -6 -51l-78 -367h-57l78 367q4 22 4 41q0 11 -2.5 29.5t-20 37t-50 28.5t-81.5 10q-66 0 -125.5 -14.5
t-107.5 -34.5l-98 -464h-57z" />
    <glyph glyph-name="f_f_i.1" horiz-adv-x="921" 
d="M168 -159q14 -2 35.5 -3t36.5 -1q57 0 88 26.5t45 94.5l116 544h-267l-114 -542q-10 -46 -24.5 -79t-36.5 -54t-53 -31t-74 -10q-20 0 -43.5 1t-41.5 4l11 50q14 -2 35 -3t36 -1q57 0 88.5 26.5t45.5 94.5l115 544h-61l7 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5
t89 11q40 0 79 -6l-9 -49q-17 2 -34 3.5t-34 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-16 -78h266l16 76q11 50 27 86.5t41.5 60t62.5 34.5t88 11q31 0 59 -6l-9 -49q-12 2 -24 3.5t-25 1.5q-37 0 -64 -7.5t-46 -24t-32 -43t-21 -65.5l-16 -78h322l-117 -550h-57
l107 502h-266l-114 -542q-10 -46 -25 -79t-37 -54t-53 -31t-73 -10q-20 0 -43.5 1t-41.5 4zM867 782h64l-14 -69h-64z" />
    <glyph glyph-name="f_f_j.1" horiz-adv-x="921" 
d="M168 -159q14 -2 35.5 -3t36.5 -1q57 0 88 26.5t45 94.5l116 544h-267l-114 -542q-10 -46 -24.5 -79t-36.5 -54t-53 -31t-74 -10q-20 0 -43.5 1t-41.5 4l11 50q14 -2 35 -3t36 -1q57 0 88.5 26.5t45.5 94.5l115 544h-61l7 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5
t89 11q40 0 79 -6l-9 -49q-17 2 -34 3.5t-34 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-16 -78h266l16 76q11 50 27 86.5t41.5 60t62.5 34.5t88 11q32 0 60 -6l-10 -49q-12 2 -24 3.5t-25 1.5q-37 0 -64 -7.5t-46 -24t-32 -43t-21 -65.5l-16 -78h322l-126 -592
q-10 -46 -24.5 -78.5t-36.5 -53.5t-52.5 -30.5t-72.5 -9.5q-20 0 -44 1t-41 4l11 50q14 -2 35 -3t35 -1q57 0 88 26.5t45 94.5l116 544h-266l-114 -542q-10 -46 -25 -79t-37 -54t-53 -31t-73 -10q-20 0 -43.5 1t-41.5 4zM866 782h65l-14 -69h-65z" />
    <glyph glyph-name="f_f_k.1" horiz-adv-x="1297" 
d="M168 -159q14 -2 35.5 -3t36.5 -1q57 0 88 26.5t45 94.5l116 544h-267l-114 -542q-10 -46 -24.5 -79t-36.5 -54t-53 -31t-74 -10q-20 0 -43.5 1t-41.5 4l11 50q14 -2 35 -3t36 -1q57 0 88.5 26.5t45.5 94.5l115 544h-61l7 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5
t89 11q40 0 79 -6l-9 -49q-17 2 -34 3.5t-34 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-16 -78h266l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q20 0 39 -1t38 -5l-10 -49q-16 2 -32 3.5t-33 1.5q-38 0 -65.5 -7.5t-47.5 -24t-33 -43t-21 -65.5l-16 -78h182
l-10 -48h-183l-114 -542q-10 -46 -25 -79t-37 -54t-53 -31t-73 -10q-20 0 -43.5 1t-41.5 4zM886 802h57l-99 -468l370 216h86l-397 -234l298 -316h-73l-289 314l-66 -314h-57z" />
    <glyph glyph-name="f_f_l.1" horiz-adv-x="933" 
d="M168 -159q14 -2 35.5 -3t36.5 -1q57 0 88 26.5t45 94.5l116 544h-267l-114 -542q-10 -46 -24.5 -79t-36.5 -54t-53 -31t-74 -10q-20 0 -43.5 1t-41.5 4l11 50q14 -2 35 -3t36 -1q57 0 88.5 26.5t45.5 94.5l115 544h-61l7 36l64 12l16 76q11 50 27.5 86.5t42.5 60t63 34.5
t89 11q40 0 79 -6l-9 -49q-17 2 -34 3.5t-34 1.5q-37 0 -65 -7.5t-48 -24t-33 -43t-21 -65.5l-16 -78h266l16 76q11 50 27.5 86.5t42.5 60t63 34.5t89 11q40 0 77 -6l-9 -49q-17 2 -33 3.5t-33 1.5q-38 0 -65.5 -7.5t-47.5 -24t-33 -43t-21 -65.5l-16 -78h182l-10 -48h-183
l-114 -542q-10 -46 -25 -79t-37 -54t-53 -31t-73 -10q-20 0 -43.5 1t-41.5 4zM886 802h57l-170 -802h-57z" />
    <glyph glyph-name="exclamdown.case" horiz-adv-x="380" 
d="M316 727h-77l16 75h76zM188 170l-36 -170h-58l36 170l90 394h46z" />
    <glyph glyph-name="questiondown.case" horiz-adv-x="602" 
d="M516 734h-75l16 75h75zM445 433l-9 -42h-97q-58 0 -98.5 -8.5t-68 -27.5t-44 -49.5t-25.5 -74.5q-7 -36 -7 -65q0 -10 2 -32t24 -44.5t63.5 -33t106.5 -10.5q60 0 117 7.5t105 18.5l-11 -54q-47 -11 -101 -18t-120 -7q-147 0 -196 48.5t-49 121.5q0 32 8 71
q11 56 32.5 94.5t55.5 63t83 35.5t116 11h56l33 133h48z" />
    <glyph glyph-name="hyphen.case" horiz-adv-x="380" 
d="M67 375l10 52h292l-11 -52h-291z" />
    <glyph glyph-name="guilsinglleft.case" horiz-adv-x="307" 
d="M282 633h64l-218 -235l117 -229h-61l-118 232z" />
    <glyph glyph-name="guilsinglright.case" horiz-adv-x="307" 
d="M78 169h-63l217 234l-116 230h60l119 -232z" />
    <glyph glyph-name="guillemotleft.case" horiz-adv-x="516" 
d="M282 633h64l-218 -235l117 -229h-61l-118 232zM492 633h64l-218 -235l117 -229h-61l-118 232z" />
    <glyph glyph-name="guillemotright.case" horiz-adv-x="516" 
d="M78 169h-63l217 234l-116 230h60l119 -232zM288 169h-63l217 234l-116 230h60l119 -232z" />
    <glyph glyph-name="parenleft.case" horiz-adv-x="342" 
d="M185 -73q-27 38 -48 86.5t-30.5 103.5t-9.5 115q0 11 1 53t19 128q29 140 94.5 257t167.5 205h65q-100 -84 -170 -206t-101 -268q-20 -95 -20 -178q0 -47 12 -129t78 -167h-58z" />
    <glyph glyph-name="parenright.case" horiz-adv-x="342" 
d="M210 875q27 -38 48 -86.5t31 -104.5t10 -119q0 -8 -1 -49t-19 -127q-29 -140 -95 -257t-168 -205h-65q101 84 170.5 206t100.5 268q20 95 20 178q0 47 -12 129t-78 167h58z" />
    <glyph glyph-name="braceleft.case" horiz-adv-x="342" 
d="M246 -72h-22q-46 0 -77.5 8.5t-48.5 28t-19 39t-2 28.5q0 27 8 64l36 168q7 31 7.5 42.5t0.5 12.5q0 20 -8 32.5t-26 18.5t-48 6h-23l10 49h24q30 0 50 6t33.5 19t22.5 35t15 53l44 211q14 69 52 97t117 28h55l-9 -49h-43q-57 0 -82.5 -18t-34.5 -62l-43 -203
q-14 -64 -36 -98t-64 -47q33 -13 40.5 -36t7.5 -42q0 -25 -8 -59l-37 -167q-7 -34 -7 -55q0 -2 0.5 -14t11.5 -24.5t33 -17.5t56 -5h25z" />
    <glyph glyph-name="braceright.case" horiz-adv-x="342" 
d="M173 53q-7 -35 -19.5 -59t-32.5 -38.5t-48.5 -21t-68.5 -6.5h-55l10 48h41q58 0 83.5 18t34.5 63l43 203q14 63 36 97t63 47q-32 14 -39 37.5t-7 42.5q0 25 7 58l37 166q7 32 7 54q0 2 -0.5 15t-11 25.5t-32.5 17.5t-56 5h-26l11 49h23q46 0 77 -8.5t48 -28t19 -39.5
t2 -28q0 -28 -8 -65l-35 -167q-7 -31 -7.5 -42.5t-0.5 -12.5q0 -20 7.5 -33t25.5 -19t48 -6h23l-10 -49h-23q-30 0 -50.5 -5.5t-34 -19t-22.5 -34.5t-15 -53z" />
    <glyph glyph-name="bracketleft.case" horiz-adv-x="342" 
d="M220 875h228l-10 -49h-172l-182 -850h173l-11 -49h-227z" />
    <glyph glyph-name="bracketright.case" horiz-adv-x="342" 
d="M174 -73h-226l10 49h171l181 850h-171l10 49h227z" />
    <glyph glyph-name="endash.case" 
d="M21 375l12 52h760l-12 -52h-760z" />
    <glyph glyph-name="emdash.case" horiz-adv-x="1520" 
d="M21 375l12 52h1520l-12 -52h-1520z" />
    <glyph glyph-name="bullet.case" horiz-adv-x="416" 
d="M222 302q-51 0 -69 20t-18 50q0 17 4 37q11 49 38.5 70t73.5 21q49 0 66.5 -20.5t17.5 -49.5q0 -17 -4 -37q-11 -49 -38 -70t-71 -21z" />
    <glyph glyph-name="periodcentered.case" horiz-adv-x="380" 
d="M187 439h76l-15 -75h-76z" />
    <glyph glyph-name="at.case" horiz-adv-x="931" 
d="M429 -16q-94 0 -164.5 24t-112.5 79.5t-48 112.5t-6 82q0 77 22 179q19 90 49.5 157t81.5 111.5t126 66.5t182 22q96 0 167 -23.5t113 -73.5t49 -102t7 -81q0 -61 -16 -137l-35 -162q-5 -25 -13.5 -47.5t-23 -39t-38 -26.5t-58.5 -10q-66 0 -91 26.5t-25 66.5q0 6 1 12
q-29 -15 -71.5 -27.5t-99.5 -12.5q-37 0 -67 7.5t-49 25t-24 38t-5 35.5q0 19 5 42q13 59 51.5 83.5t106.5 24.5h203l11 43q4 17 4 32q0 7 -2 19.5t-17.5 25t-45.5 18.5t-78 6q-43 0 -79 -4.5t-72 -9.5l11 48q18 3 34.5 5t34 4t36.5 3t42 1q57 0 95.5 -8.5t59.5 -27
t25 -38.5t4 -34q0 -19 -5 -43l-48 -224q-4 -20 -4.5 -30t-0.5 -13q0 -13 4.5 -24.5t17 -18.5t36.5 -7t39 7t24.5 18t15 25.5t8.5 30.5l33 160q16 77 16 136q0 20 -4.5 64t-40 87.5t-97 62.5t-148.5 19q-93 0 -158.5 -19t-110 -58t-72 -98t-44.5 -139q-20 -97 -20 -168
q0 -19 4 -68t40 -97.5t99 -69t150 -20.5q16 0 38 1t38 3l-12 -48q-15 -3 -38 -4t-40 -1zM436 225q51 0 94 12.5t74 26.5l32 131h-181q-54 0 -80.5 -15.5t-35.5 -57.5q-3 -16 -3 -30q0 -29 20 -48t80 -19z" />
    <glyph glyph-name="dollar.weight" horiz-adv-x="690" 
d="M58 125q43 -14 109.5 -27t147.5 -13q61 0 101.5 7t66.5 22.5t40 40t21 59.5q7 34 7 58q0 8 -2 25.5t-22.5 35.5t-59.5 29t-101 22q-72 13 -119 27.5t-72.5 38t-28.5 47t-3 36.5q0 30 8 70q9 43 28 74t52 51.5t81 30t114 9.5l20 92h50l-20 -93q59 -3 108.5 -12.5
t84.5 -21.5l-9 -54q-43 14 -105.5 24.5t-126.5 10.5q-57 0 -96.5 -6.5t-65.5 -21t-40 -36.5t-21 -54q-6 -30 -6 -52q0 -8 2 -24.5t22 -33t59 -27t101 -21.5q72 -13 120 -28.5t73.5 -40.5t28.5 -49.5t3 -36.5q0 -32 -9 -75q-10 -48 -29 -81t-51 -54t-80.5 -30.5t-118.5 -9.5
l-20 -93l-50 -1l20 95q-74 3 -129 14t-93 25z" />
    <glyph glyph-name="cent.weight" horiz-adv-x="690" 
d="M512 802l-23 -109q56 -2 104.5 -8.5t90.5 -15.5l-12 -54q-50 11 -107 17t-120 6q-64 0 -111.5 -11t-81 -37t-55 -67.5t-33.5 -103.5q-11 -54 -11 -96q0 -10 2 -38t25.5 -58.5t69 -45t117.5 -14.5q65 0 121 6t102 16l-11 -53q-44 -11 -98.5 -17.5t-115.5 -7.5l-23 -111
h-51l24 112q-141 10 -182.5 71t-41.5 136q0 48 13 108q16 75 43.5 125.5t69 81.5t96.5 45t126 15l23 108h50z" />
    <glyph glyph-name="zeroslash" horiz-adv-x="690" 
d="M446 818q76 0 129 -21.5t81 -71.5t29.5 -95t1.5 -53q0 -77 -22 -186q-23 -108 -52.5 -185.5t-72 -126.5t-101 -72t-139.5 -23q-76 0 -129 21.5t-81 71.5t-29.5 95t-1.5 53q0 77 22 186q23 108 52.5 185.5t72 126.5t101 72t139.5 23zM140 401q-17 -79 -19.5 -117.5
t-2.5 -55.5q0 -37 6 -67l478 534q-22 35 -61.5 51t-98.5 16q-65 0 -112.5 -19.5t-83 -62.5t-60.5 -111.5t-46 -167.5zM304 40q65 0 112.5 19.5t83 62.5t60.5 111.5t46 167.5q17 79 19.5 118t2.5 55q0 38 -6 67l-478 -533q21 -36 60.5 -52t99.5 -16z" />
    <glyph glyph-name="dollar.lt" 
d="M93 125q38 -12 96 -24t127 -15l65 302q-66 12 -109 27t-66 38.5t-26 46.5t-3 34q0 30 9 69q9 43 28 74t52 51.5t81 30t114 9.5l20 92h50l-20 -93q59 -3 108.5 -12.5t84.5 -21.5l-9 -54q-37 12 -88.5 21.5t-106.5 12.5l-60 -279q66 -12 109 -28t66.5 -41t26 -48t2.5 -33
q0 -32 -10 -76q-10 -48 -29 -81t-51 -54t-80.5 -30.5t-118.5 -9.5l-20 -93l-50 -1l20 95q-74 3 -129 14t-93 25zM240 596q-7 -29 -7 -51q0 -7 2 -22.5t19 -32t50.5 -27t87.5 -20.5l58 271q-53 -1 -90 -8t-61.5 -21t-38.5 -36t-20 -53zM579 214q7 33 7 57q0 6 -1.5 22.5
t-18.5 34.5t-50.5 29.5t-86.5 21.5l-63 -294q56 1 93.5 8.5t62 23t37.5 39.5t20 58z" />
    <glyph glyph-name="dollar.lt.weight" 
d="M93 125q43 -14 109.5 -27t147.5 -13q61 0 101.5 7t66.5 22.5t40 40t21 59.5q7 34 7 58q0 8 -2 25.5t-22.5 35.5t-59.5 29t-101 22q-72 13 -119 27.5t-72.5 38t-28.5 47t-3 36.5q0 30 8 70q9 43 28 74t52 51.5t81 30t114 9.5l20 92h50l-20 -93q59 -3 108.5 -12.5
t84.5 -21.5l-9 -54q-43 14 -105.5 24.5t-126.5 10.5q-57 0 -96.5 -6.5t-65.5 -21t-40 -36.5t-21 -54q-6 -30 -6 -52q0 -8 2 -24.5t22 -33t59 -27t101 -21.5q72 -13 120 -28.5t73.5 -40.5t28.5 -49.5t3 -36.5q0 -32 -9 -75q-10 -48 -29 -81t-51 -54t-80.5 -30.5t-118.5 -9.5
l-20 -93l-50 -1l20 95q-74 3 -129 14t-93 25z" />
    <glyph glyph-name="Euro.lt" 
d="M86 328h94q5 37 12.5 73.5t15.5 73.5h-91l11 48h93q23 80 54 136t74.5 91.5t101.5 51.5t135 16q62 0 103 -7t76 -18l-11 -53q-37 11 -77 17.5t-94 6.5q-64 0 -112.5 -13.5t-84 -42.5t-61 -74.5t-44.5 -110.5h384l-10 -48h-387q-9 -36 -16.5 -73t-12.5 -74h385l-11 -49
h-381q-3 -30 -3 -55q0 -32 6 -68t29.5 -64.5t64 -41t99.5 -12.5q54 0 97 6.5t85 17.5l-12 -53q-37 -11 -78 -18t-97 -7q-72 0 -122 16.5t-80.5 52t-39.5 81.5t-9 92q0 25 2 53h-99z" />
    <glyph glyph-name="cent.lt" 
d="M547 802l-23 -109q57 -2 106.5 -8.5t91.5 -15.5l-12 -54q-44 9 -93.5 15t-104.5 8l-100 -471q63 1 117 7t99 15l-11 -53q-45 -11 -100 -17.5t-116 -7.5l-24 -111h-51l24 112q-142 10 -184.5 71t-42.5 137q0 48 13 107q16 75 44 126t69.5 82t97 44.5t127.5 14.5l23 108h50
zM196 419q-11 -55 -11 -97q0 -5 1 -30.5t20 -56t57.5 -47t98.5 -20.5l100 470q-61 -1 -105.5 -13.5t-76 -38.5t-52 -67t-32.5 -100z" />
    <glyph glyph-name="cent.lt.weight" 
d="M547 802l-23 -109q56 -2 104.5 -8.5t90.5 -15.5l-12 -54q-50 11 -107 17t-120 6q-64 0 -111.5 -11t-81 -37t-55 -67.5t-33.5 -103.5q-11 -54 -11 -96q0 -10 2 -38t25.5 -58.5t69 -45t117.5 -14.5q65 0 121 6t102 16l-11 -53q-44 -11 -98.5 -17.5t-115.5 -7.5l-23 -111
h-51l24 112q-141 10 -182.5 71t-41.5 136q0 48 13 108q16 75 43.5 125.5t69 81.5t96.5 45t126 15l23 108h50z" />
    <glyph glyph-name="sterling.lt" 
d="M98 51q58 0 84.5 25t37.5 77l48 227h-152l10 48h152l42 199q11 48 28.5 84t47.5 60t74 35.5t106 11.5q67 0 116.5 -7.5t89.5 -17.5l-11 -53q-40 11 -87.5 17.5t-106.5 6.5q-50 0 -84.5 -9t-58 -27t-37 -43.5t-20.5 -58.5l-42 -198h328l-10 -48h-328l-48 -223
q-8 -38 -23.5 -64t-41.5 -42h436l-11 -51h-602l11 51h52z" />
    <glyph glyph-name="yen.lt" 
d="M352 279h-239l10 49h240l31 147h-240l11 48h202l-185 280h64l182 -276l306 275h70l-306 -279h204l-10 -48h-239l-31 -147h239l-10 -49h-240l-59 -279h-59z" />
    <glyph glyph-name="florin.lt" 
d="M152 428h231l47 222q9 46 25.5 78t43 52t65 29t91.5 9q50 0 99 -6l-11 -52q-45 6 -91 6q-74 0 -112.5 -26t-52.5 -92l-47 -220h232l-10 -48h-232l-94 -443q-7 -36 -21.5 -64.5t-39.5 -47.5t-63 -29t-93 -10q-31 0 -55 1.5t-48 4.5l12 52q44 -6 90 -6q41 0 69.5 7
t46.5 19.5t28.5 31.5t15.5 43l94 441h-230z" />
    <glyph glyph-name="zeroslash.lt" 
d="M481 818q76 0 129 -21.5t81 -71.5t29.5 -95t1.5 -53q0 -77 -22 -186q-23 -108 -52.5 -185.5t-72 -126.5t-101 -72t-139.5 -23q-76 0 -129 21.5t-81 71.5t-29.5 95t-1.5 53q0 77 22 186q23 108 52.5 185.5t72 126.5t101 72t139.5 23zM175 401q-17 -79 -19.5 -117.5
t-2.5 -55.5q0 -37 6 -67l478 534q-22 35 -61.5 51t-98.5 16q-65 0 -112.5 -19.5t-83 -62.5t-60.5 -111.5t-46 -167.5zM339 40q65 0 112.5 19.5t83 62.5t60.5 111.5t46 167.5q17 79 19.5 118t2.5 55q0 38 -6 67l-478 -533q21 -36 60.5 -52t99.5 -16z" />
    <glyph glyph-name="zero.lt" 
d="M481 818q76 0 129 -21.5t81 -71.5t29.5 -95t1.5 -53q0 -77 -22 -186q-23 -108 -52.5 -185.5t-72 -126.5t-101 -72t-139.5 -23q-76 0 -129 21.5t-81 71.5t-29.5 95t-1.5 53q0 77 22 186q23 108 52.5 185.5t72 126.5t101 72t139.5 23zM339 40q65 0 112.5 19.5t83 62.5
t60.5 111.5t46 167.5t21.5 135t0.5 40q0 64 -19 105.5t-60.5 61t-106.5 19.5t-112.5 -19.5t-83 -62.5t-60.5 -111.5t-46 -167.5t-21.5 -135t-0.5 -40q0 -64 19 -105.5t61 -61t106 -19.5z" />
    <glyph glyph-name="one.lt" 
d="M86 53h258l145 682l-285 -126l-17 52l321 141h53l-159 -749h235l-11 -53h-550z" />
    <glyph glyph-name="two.lt" 
d="M483 818q77 0 127.5 -13t78 -39.5t32.5 -53.5t5 -44q0 -27 -7 -58q-8 -34 -18.5 -58t-28.5 -44t-47 -39t-75 -45l-274 -150q-34 -19 -56 -33.5t-35.5 -30t-21 -35t-13.5 -48.5l-16 -75h477l-11 -52h-536l30 138q8 35 18 59.5t27.5 44.5t43 38t63.5 39l283 156
q38 21 61 35.5t36.5 29.5t20.5 32.5t12 42.5t5 45q0 12 -3 31t-24 37t-60.5 26.5t-101.5 8.5q-63 0 -123 -7.5t-108 -18.5l11 56q48 11 102 18t126 7z" />
    <glyph glyph-name="three.lt" 
d="M66 60q104 -26 238 -26q74 0 122.5 9.5t78.5 29t46 50.5t26 73t10 72q0 8 -2 29t-23 42.5t-60.5 30.5t-99.5 9h-174l10 53h171q53 0 93 6.5t67.5 22t45 42.5t25.5 68q8 39 8 69q0 10 -2 33t-24 47.5t-63 36t-104 11.5q-66 0 -121.5 -6.5t-103.5 -18.5l10 50q47 11 101 18
t122 7q153 0 200 -50t47 -121q0 -35 -9 -78q-16 -79 -60 -117t-109 -49q69 -14 91.5 -53.5t22.5 -82.5q0 -36 -11 -82q-12 -52 -33 -90t-58.5 -63t-95.5 -36.5t-144 -11.5q-81 0 -140.5 7t-107.5 19z" />
    <glyph glyph-name="four.lt" 
d="M663 456l-97 -456h-58l41 195h-493l10 47l417 560h68l-412 -554h422l44 208h58z" />
    <glyph glyph-name="five.lt" 
d="M65 65q50 -13 112.5 -19.5t134.5 -6.5q68 0 113.5 11t75 33t45 54t24.5 75q7 35 7 62q0 15 -4 39.5t-28 46.5t-65.5 31.5t-101.5 9.5h-206l85 401h468l-11 -53h-409l-63 -295h145q73 0 125.5 -13t83.5 -42.5t37 -62t6 -53.5q0 -35 -9 -78q-12 -57 -34 -98.5t-60 -69
t-93.5 -40.5t-135.5 -13q-84 0 -145 7t-108 19z" />
    <glyph glyph-name="six.lt" 
d="M357 -16q-87 0 -143.5 23t-84 73.5t-27.5 131.5t25 199q26 116 58.5 194t78.5 125t109 67.5t149 20.5q66 0 115 -7.5t91 -19.5l-11 -54q-41 11 -89 18.5t-107 7.5q-72 0 -124.5 -16.5t-91 -56t-66 -105t-48.5 -163.5q24 10 49 18.5t53 15t60 10t70 3.5q60 0 108 -14
t79 -44.5t38.5 -64.5t7.5 -58q0 -32 -8 -70q-13 -66 -37 -110.5t-59.5 -72t-83.5 -39.5t-111 -12zM413 416q-38 0 -69 -3.5t-58.5 -9.5t-53 -14.5t-52.5 -18.5q-19 -93 -19.5 -125.5t-0.5 -33.5q0 -62 21 -101t65 -56t114 -17q48 0 86 8.5t66 30.5t47 58.5t31 92.5
q6 30 6 54q0 18 -5.5 43.5t-29 48t-61 33t-87.5 10.5z" />
    <glyph glyph-name="seven.lt" 
d="M208 802h570l-10 -46l-573 -756h-75l577 749h-500z" />
    <glyph glyph-name="eight.lt" 
d="M324 -16q-153 0 -203 47t-50 116q0 29 7 62q20 94 66.5 140t121.5 60q-27 8 -48 22.5t-33 37.5t-13.5 43t-1.5 27q0 28 8 63q11 54 32 94.5t57.5 67.5t91.5 40.5t133 13.5q145 0 191 -45.5t46 -111.5q0 -29 -7 -64q-19 -90 -61 -134.5t-110 -58.5q64 -14 87.5 -51.5
t23.5 -82.5q0 -31 -8 -69q-12 -54 -34 -95t-60 -68t-95.5 -40.5t-140.5 -13.5zM414 432q58 0 100 7t71 25.5t47.5 49.5t28.5 79q7 33 7 60q0 7 -1.5 26.5t-21 41t-58.5 32t-102 10.5q-62 0 -105.5 -10.5t-72.5 -32t-45.5 -53.5t-25.5 -74q-7 -32 -7 -56q0 -13 3.5 -33.5
t25 -39t60 -25.5t96.5 -7zM331 40q66 0 112.5 10.5t77 31.5t48 53t26.5 75q7 32 7 57q0 14 -4 36t-27 41t-65 26.5t-103 7.5t-105.5 -7.5t-76 -26.5t-51.5 -51.5t-31 -82.5q-6 -31 -6 -56q0 -10 2 -30.5t23.5 -41.5t63.5 -31.5t109 -10.5z" />
    <glyph glyph-name="nine.lt" 
d="M456 818q87 0 143.5 -23t84 -73.5t28 -92t0.5 -42.5q0 -80 -26 -196q-25 -115 -58 -193.5t-79 -125.5t-109 -67.5t-149 -20.5q-66 0 -115 7.5t-91 19.5l11 54q42 -11 89.5 -18.5t107.5 -7.5q72 0 124 16.5t90.5 56t66 105t48.5 163.5q-24 -10 -49 -18.5t-53 -15t-60 -10
t-70 -3.5q-60 0 -108 14t-78.5 44.5t-38.5 64.5t-8 59q0 31 8 69q14 66 37.5 110.5t59 72t84 39.5t110.5 12zM224 575q-6 -29 -6 -53q0 -19 5.5 -44.5t29 -48t61 -33t86.5 -10.5q38 0 69.5 3.5t59 9.5t53 14.5t51.5 18.5q19 93 19.5 125.5t0.5 33.5q0 62 -21 101t-65 56
t-114 17q-48 0 -85.5 -8.5t-65.5 -30.5t-47 -58.5t-31 -92.5z" />
    <glyph glyph-name="dollar.ot" 
d="M97 135q38 -13 96.5 -25t124.5 -14l54 251q-70 12 -114 25.5t-66.5 35t-24.5 41t-2 27.5q0 27 8 64q8 36 25.5 61t49 41.5t79 24.5t114.5 9l18 84h51l-18 -85q57 -3 107 -13t85 -21l-10 -53q-37 12 -87 21.5t-106 12.5l-49 -229q69 -12 112.5 -28t66 -39t25 -44t2.5 -31
q0 -26 -7 -59q-9 -45 -28.5 -74t-52.5 -46t-80.5 -23.5t-112.5 -6.5l-22 -102l-50 -1l22 104q-66 2 -125 12.5t-97 24.5zM227 532q-5 -24 -5 -41q0 -6 1.5 -19t18 -27t50.5 -24t91 -20l47 222q-53 -1 -89.5 -6t-60 -16t-36 -28t-17.5 -41zM576 200q5 24 5 41q0 7 -1.5 20.5
t-18.5 29.5t-51 27t-90 21l-52 -244q51 1 87.5 5.5t61.5 16.5t39 32t20 51z" />
    <glyph glyph-name="dollar.ot.weight" 
d="M97 135q21 -7 49 -14t61 -13t69 -9.5t73 -3.5q57 0 97 4t66.5 15.5t41.5 32t22 53.5q5 22 5 40q0 8 -2.5 23.5t-22 31t-59 27t-104.5 22.5q-77 12 -125 25.5t-72.5 35t-27.5 42.5t-3 32q0 26 8 61q8 36 25.5 61t49 41.5t79 24.5t114.5 9l18 84h51l-18 -85q57 -3 107 -13
t85 -21l-10 -53q-43 14 -102.5 24.5t-125.5 10.5q-58 0 -97 -5t-64.5 -15.5t-38.5 -28t-19 -42.5q-5 -24 -5 -41q0 -8 2 -21.5t21.5 -28t58.5 -24t105 -20.5q77 -12 125 -28t73 -39.5t28 -45t3 -33.5q0 -26 -7 -59q-9 -45 -28.5 -74t-52.5 -46t-80.5 -23.5t-112.5 -6.5
l-22 -102l-50 -1l22 104q-66 2 -125 12.5t-97 24.5z" />
    <glyph glyph-name="Euro.ot" 
d="M76 281h94q7 70 26 141h-90l10 48h93q21 66 50.5 113t72 77t100.5 44t136 14q31 0 55.5 -2t45.5 -5.5t39 -8t36 -9.5l-12 -54q-35 11 -75.5 18t-94.5 7q-64 0 -111 -11t-81.5 -34.5t-58 -60t-40.5 -88.5h383l-10 -48h-387q-10 -35 -17 -70.5t-11 -70.5h385l-10 -48h-380
q-1 -13 -1 -25q0 -38 8.5 -70.5t34 -56t67.5 -33.5t102 -10q52 0 96 7t79 18l-12 -53q-32 -11 -74 -18.5t-96 -7.5q-71 0 -122 14t-83 44t-45 73t-13 95q0 11 1 23h-100z" />
    <glyph glyph-name="Euro.ot.weight" 
d="M96 378h90q19 92 47.5 156.5t72.5 105t108 59.5t154 19q31 0 55.5 -2t45.5 -5.5t39 -8t36 -9.5l-12 -54q-35 11 -75.5 18t-94.5 7q-77 0 -129.5 -16t-88.5 -50.5t-58 -88.5t-39 -131h387l-10 -48h-387q-14 -75 -14 -127q0 -5 1 -35.5t24 -65t69 -49.5t118 -15q52 0 96 7
t79 18l-12 -53q-32 -11 -74 -18.5t-96 -7.5q-82 0 -138 19t-86.5 60.5t-34.5 83t-4 61.5q0 54 12 122h-90z" />
    <glyph glyph-name="cent.ot" 
d="M526 702l-17 -80q62 -2 111 -8.5t86 -16.5l-11 -54q-38 9 -88 15t-110 8l-90 -427q63 1 117 7t99 15l-12 -54q-45 -10 -99.5 -16.5t-116.5 -7.5l-18 -83h-51l18 84q-67 4 -115.5 21.5t-77 52.5t-33 70t-4.5 54q0 41 10 91q15 70 42 117.5t68.5 76t97 41.5t127.5 14l17 80
h50zM184 365q-9 -43 -9 -77q0 -58 33.5 -100.5t147.5 -47.5l91 426q-60 -2 -104.5 -14t-76 -36t-51.5 -61.5t-31 -89.5z" />
    <glyph glyph-name="cent.ot.weight" 
d="M526 702l-17 -80q62 -2 111 -8.5t86 -16.5l-11 -54q-42 10 -99 16.5t-125 6.5q-67 0 -115.5 -11t-83 -35t-55.5 -62t-33 -93q-9 -44 -9 -78q0 -14 3.5 -42t28.5 -55t70 -39t111 -12q69 0 128 6t107 16l-12 -54q-45 -10 -99.5 -16.5t-117.5 -7.5l-17 -83h-51l18 84
q-67 3 -115.5 21t-77 52.5t-33 69.5t-4.5 54q0 41 10 92q15 70 42 117.5t68.5 76t97 41.5t127.5 14l17 80h50z" />
    <glyph glyph-name="sterling.ot" 
d="M97 48q58 0 85 26t38 81l37 175h-152l10 48h152l42 196q9 44 27.5 76.5t50.5 55t79 33.5t113 11q61 0 107 -8t81 -17l-11 -54q-36 11 -79.5 18t-97.5 7t-92 -9t-63 -25.5t-39 -39t-20 -50.5l-41 -194h328l-10 -48h-328l-36 -171q-8 -41 -24.5 -68t-45.5 -43h439l-10 -48
h-602l10 48h52z" />
    <glyph glyph-name="yen.ot" 
d="M113 281h239l30 141h-239l10 48h197l-189 233h63l194 -232l297 231h68l-289 -232h197l-10 -48h-240l-30 -141h240l-10 -48h-240l-49 -233h-59l49 233h-239z" />
    <glyph glyph-name="florin.ot" 
d="M141 378h230l36 168q11 53 28.5 89.5t44.5 59t65 33t90 10.5q30 0 54 -1.5t48 -4.5l-11 -51q-23 2 -45.5 3.5t-45.5 1.5q-39 0 -67 -7.5t-48.5 -24.5t-33.5 -44t-22 -67l-35 -165h233l-11 -48h-232l-75 -352q-11 -52 -28 -88.5t-43.5 -59.5t-63 -33.5t-87.5 -10.5
q-33 0 -57.5 1.5t-48.5 4.5l12 52q44 -6 90 -6q40 0 68 7.5t47.5 24.5t32 44t21.5 67l74 349h-230z" />
    <glyph glyph-name="zeroslash.ot" 
d="M452 718q83 0 137.5 -21t83.5 -66t31 -86t2 -52q0 -64 -17 -150q-21 -99 -50.5 -167.5t-71 -111t-97 -61.5t-128.5 -19q-82 0 -137 21t-84 66t-31.5 86t-2.5 53q0 64 18 149q21 99 50.5 167.5t71.5 111t97.5 61.5t127.5 19zM165 351q-14 -66 -16 -97t-2 -41q0 -37 8 -65
l461 457q-46 58 -169 58q-60 0 -105 -17t-78.5 -54t-57 -96.5t-41.5 -144.5zM347 39q60 0 105 17t78.5 54t57 96.5t41.5 144.5q18 80 18 138q0 38 -8 66l-461 -457q23 -31 64.5 -45t104.5 -14z" />
    <glyph glyph-name="zero.ot" 
d="M452 718q83 0 137.5 -21t83.5 -66t31 -86t2 -52q0 -64 -17 -150q-21 -99 -50.5 -167.5t-71 -111t-97 -61.5t-128.5 -19q-82 0 -137 21t-84 66t-31.5 86t-2.5 53q0 64 18 149q21 99 50.5 167.5t71.5 111t97.5 61.5t127.5 19zM347 39q60 0 105 17t78.5 54t57 96.5
t41.5 144.5q18 84 18 142q0 1 -0.5 31.5t-22.5 68t-65.5 54t-111.5 16.5q-60 0 -105 -17t-78.5 -54t-57 -96.5t-41.5 -144.5q-18 -83 -18 -141q0 -2 0.5 -32.5t22.5 -68t66 -54t111 -16.5z" />
    <glyph glyph-name="one.ot" 
d="M85 48h258l125 589l-286 -127l-16 51l321 141h53l-139 -654h236l-11 -48h-550z" />
    <glyph glyph-name="two.ot" 
d="M452 718q134 0 187 -38t53 -101q0 -21 -5 -45q-5 -25 -14 -46t-25.5 -40t-42 -37t-63.5 -37l-278 -141q-34 -18 -55.5 -32t-34 -28.5t-19 -33t-12.5 -45.5l-10 -46h463l-11 -48h-521l20 92q8 37 18 63t28 47t45 39.5t69 39.5l281 144q30 16 48.5 29.5t29.5 27t16 27.5
t8 31q3 16 3 30q0 44 -39.5 69t-146.5 25q-68 0 -130.5 -7t-113.5 -18l11 54q51 11 108.5 18t132.5 7z" />
    <glyph glyph-name="three.ot" 
d="M45 -40q104 -26 238 -26q74 0 122.5 9.5t78.5 29t46 50.5t26 73t10 72q0 8 -2 29t-23 42.5t-60.5 30.5t-99.5 9h-174l10 53h171q53 0 93 6.5t67.5 22t45 42.5t25.5 68q8 39 8 69q0 10 -2 33t-24 47.5t-63 36t-104 11.5q-66 0 -121.5 -6.5t-103.5 -18.5l10 50
q47 11 101 18t122 7q153 0 200 -50t47 -121q0 -35 -9 -78q-16 -79 -60 -117t-109 -49q69 -14 91.5 -53.5t22.5 -82.5q0 -36 -11 -82q-12 -52 -33 -90t-58.5 -63t-95.5 -36.5t-144 -11.5q-81 0 -140.5 7t-107.5 19z" />
    <glyph glyph-name="four.ot" 
d="M642 356l-97 -456h-58l41 195h-493l10 47l417 560h68l-412 -554h422l44 208h58z" />
    <glyph glyph-name="five.ot" 
d="M44 -35q50 -13 112.5 -19.5t134.5 -6.5q68 0 113.5 11t75 33t45 54t24.5 75q7 35 7 62q0 15 -4 39.5t-28 46.5t-65.5 31.5t-101.5 9.5h-206l85 401h468l-11 -53h-409l-63 -295h145q73 0 125.5 -13t83.5 -42.5t37 -62t6 -53.5q0 -35 -9 -78q-12 -57 -34 -98.5t-60 -69
t-93.5 -40.5t-135.5 -13q-84 0 -145 7t-108 19z" />
    <glyph glyph-name="six.ot" 
d="M357 -16q-87 0 -143.5 23t-84 73.5t-27.5 131.5t25 199q26 116 58.5 194t78.5 125t109 67.5t149 20.5q66 0 115 -7.5t91 -19.5l-11 -54q-41 11 -89 18.5t-107 7.5q-72 0 -124.5 -16.5t-91 -56t-66 -105t-48.5 -163.5q24 10 49 18.5t53 15t60 10t70 3.5q60 0 108 -14
t79 -44.5t38.5 -64.5t7.5 -58q0 -32 -8 -70q-13 -66 -37 -110.5t-59.5 -72t-83.5 -39.5t-111 -12zM413 416q-38 0 -69 -3.5t-58.5 -9.5t-53 -14.5t-52.5 -18.5q-19 -93 -19.5 -125.5t-0.5 -33.5q0 -62 21 -101t65 -56t114 -17q48 0 86 8.5t66 30.5t47 58.5t31 92.5
q6 30 6 54q0 18 -5.5 43.5t-29 48t-61 33t-87.5 10.5z" />
    <glyph glyph-name="seven.ot" 
d="M187 702h570l-10 -46l-573 -756h-75l577 749h-500z" />
    <glyph glyph-name="eight.ot" 
d="M324 -16q-153 0 -203 47t-50 116q0 29 7 62q20 94 66.5 140t121.5 60q-27 8 -48 22.5t-33 37.5t-13.5 43t-1.5 27q0 28 8 63q11 54 32 94.5t57.5 67.5t91.5 40.5t133 13.5q145 0 191 -45.5t46 -111.5q0 -29 -7 -64q-19 -90 -61 -134.5t-110 -58.5q64 -14 87.5 -51.5
t23.5 -82.5q0 -31 -8 -69q-12 -54 -34 -95t-60 -68t-95.5 -40.5t-140.5 -13.5zM414 432q58 0 100 7t71 25.5t47.5 49.5t28.5 79q7 33 7 60q0 7 -1.5 26.5t-21 41t-58.5 32t-102 10.5q-62 0 -105.5 -10.5t-72.5 -32t-45.5 -53.5t-25.5 -74q-7 -32 -7 -56q0 -13 3.5 -33.5
t25 -39t60 -25.5t96.5 -7zM331 40q66 0 112.5 10.5t77 31.5t48 53t26.5 75q7 32 7 57q0 14 -4 36t-27 41t-65 26.5t-103 7.5t-105.5 -7.5t-76 -26.5t-51.5 -51.5t-31 -82.5q-6 -31 -6 -56q0 -10 2 -30.5t23.5 -41.5t63.5 -31.5t109 -10.5z" />
    <glyph glyph-name="nine.ot" 
d="M435 718q87 0 143.5 -23t84 -73.5t28 -92t0.5 -42.5q0 -80 -26 -196q-25 -115 -58 -193.5t-79 -125.5t-109 -67.5t-149 -20.5q-66 0 -115 7.5t-91 19.5l11 54q42 -11 89.5 -18.5t107.5 -7.5q72 0 124 16.5t90.5 56t66 105t48.5 163.5q-24 -10 -49 -18.5t-53 -15t-60 -10
t-70 -3.5q-60 0 -108 14t-78.5 44.5t-38.5 64.5t-8 59q0 31 8 69q14 66 37.5 110.5t59 72t84 39.5t110.5 12zM203 475q-6 -29 -6 -53q0 -19 5.5 -44.5t29 -48t61 -33t86.5 -10.5q38 0 69.5 3.5t59 9.5t53 14.5t51.5 18.5q19 93 19.5 125.5t0.5 33.5q0 62 -21 101t-65 56
t-114 17q-48 0 -85.5 -8.5t-65.5 -30.5t-47 -58.5t-31 -92.5z" />
    <glyph glyph-name="dollar.op" horiz-adv-x="690" 
d="M62 135q38 -13 96.5 -25t124.5 -14l54 251q-70 12 -114 25.5t-66.5 35t-24.5 41t-2 27.5q0 27 8 64q8 36 25.5 61t49 41.5t79 24.5t114.5 9l18 84h51l-18 -85q57 -3 107 -13t85 -21l-10 -53q-37 12 -87 21.5t-106 12.5l-49 -229q69 -12 112.5 -28t66 -39t25 -44t2.5 -31
q0 -26 -7 -59q-9 -45 -28.5 -74t-52.5 -46t-80.5 -23.5t-112.5 -6.5l-22 -102l-50 -1l22 104q-66 2 -125 12.5t-97 24.5zM192 532q-5 -24 -5 -41q0 -6 1.5 -19t18 -27t50.5 -24t91 -20l47 222q-53 -1 -89.5 -6t-60 -16t-36 -28t-17.5 -41zM541 200q5 24 5 41q0 7 -1.5 20.5
t-18.5 29.5t-51 27t-90 21l-52 -244q51 1 87.5 5.5t61.5 16.5t39 32t20 51z" />
    <glyph glyph-name="dollar.op.weight" horiz-adv-x="690" 
d="M62 135q21 -7 49 -14t61 -13t69 -9.5t73 -3.5q57 0 97 4t66.5 15.5t41.5 32t22 53.5q5 22 5 40q0 8 -2.5 23.5t-22 31t-59 27t-104.5 22.5q-77 12 -125 25.5t-72.5 35t-27.5 42.5t-3 32q0 26 8 61q8 36 25.5 61t49 41.5t79 24.5t114.5 9l18 84h51l-18 -85q57 -3 107 -13
t85 -21l-10 -53q-43 14 -102.5 24.5t-125.5 10.5q-58 0 -97 -5t-64.5 -15.5t-38.5 -28t-19 -42.5q-5 -24 -5 -41q0 -8 2 -21.5t21.5 -28t58.5 -24t105 -20.5q77 -12 125 -28t73 -39.5t28 -45t3 -33.5q0 -26 -7 -59q-9 -45 -28.5 -74t-52.5 -46t-80.5 -23.5t-112.5 -6.5
l-22 -102l-50 -1l22 104q-66 2 -125 12.5t-97 24.5z" />
    <glyph glyph-name="Euro.op" horiz-adv-x="690" 
d="M41 281h94q7 70 26 141h-90l10 48h93q21 66 50.5 113t72 77t100.5 44t136 14q31 0 55.5 -2t45.5 -5.5t39 -8t36 -9.5l-12 -54q-35 11 -75.5 18t-94.5 7q-64 0 -111 -11t-81.5 -34.5t-58 -60t-40.5 -88.5h383l-10 -48h-387q-10 -35 -17 -70.5t-11 -70.5h385l-10 -48h-380
q-1 -13 -1 -25q0 -38 8.5 -70.5t34 -56t67.5 -33.5t102 -10q52 0 96 7t79 18l-12 -53q-32 -11 -74 -18.5t-96 -7.5q-71 0 -122 14t-83 44t-45 73t-13 95q0 11 1 23h-100z" />
    <glyph glyph-name="Euro.op.weight" horiz-adv-x="690" 
d="M61 378h90q19 92 47.5 156.5t72.5 105t108 59.5t154 19q31 0 55.5 -2t45.5 -5.5t39 -8t36 -9.5l-12 -54q-35 11 -75.5 18t-94.5 7q-77 0 -129.5 -16t-88.5 -50.5t-58 -88.5t-39 -131h387l-10 -48h-387q-14 -75 -14 -127q0 -5 1 -35.5t24 -65t69 -49.5t118 -15q52 0 96 7
t79 18l-12 -53q-32 -11 -74 -18.5t-96 -7.5q-82 0 -138 19t-86.5 60.5t-34.5 83t-4 61.5q0 54 12 122h-90z" />
    <glyph glyph-name="cent.op" horiz-adv-x="690" 
d="M491 702l-17 -80q62 -2 111 -8.5t86 -16.5l-11 -54q-38 9 -88 15t-110 8l-90 -427q63 1 117 7t99 15l-12 -54q-45 -10 -99.5 -16.5t-116.5 -7.5l-18 -83h-51l18 84q-67 4 -115.5 21.5t-77 52.5t-33 70t-4.5 54q0 41 10 91q15 70 42 117.5t68.5 76t97 41.5t127.5 14l17 80
h50zM149 365q-9 -43 -9 -77q0 -58 33.5 -100.5t147.5 -47.5l91 426q-60 -2 -104.5 -14t-76 -36t-51.5 -61.5t-31 -89.5z" />
    <glyph glyph-name="cent.op.weight" horiz-adv-x="690" 
d="M491 702l-17 -80q62 -2 111 -8.5t86 -16.5l-11 -54q-42 10 -99 16.5t-125 6.5q-67 0 -115.5 -11t-83 -35t-55.5 -62t-33 -93q-9 -44 -9 -78q0 -14 3.5 -42t28.5 -55t70 -39t111 -12q69 0 128 6t107 16l-12 -54q-45 -10 -99.5 -16.5t-117.5 -7.5l-17 -83h-51l18 84
q-67 3 -115.5 21t-77 52.5t-33 69.5t-4.5 54q0 41 10 92q15 70 42 117.5t68.5 76t97 41.5t127.5 14l17 80h50z" />
    <glyph glyph-name="sterling.op" horiz-adv-x="690" 
d="M62 48q58 0 85 26t38 81l37 175h-152l10 48h152l42 196q9 44 27.5 76.5t50.5 55t79 33.5t113 11q61 0 107 -8t81 -17l-11 -54q-36 11 -79.5 18t-97.5 7t-92 -9t-63 -25.5t-39 -39t-20 -50.5l-41 -194h328l-10 -48h-328l-36 -171q-8 -41 -24.5 -68t-45.5 -43h439l-10 -48
h-602l10 48h52z" />
    <glyph glyph-name="yen.op" horiz-adv-x="690" 
d="M78 281h239l30 141h-239l10 48h197l-189 233h63l194 -232l297 231h68l-289 -232h197l-10 -48h-240l-30 -141h240l-10 -48h-240l-49 -233h-59l49 233h-239z" />
    <glyph glyph-name="florin.op" horiz-adv-x="690" 
d="M106 378h230l36 168q11 53 28.5 89.5t44.5 59t65 33t90 10.5q30 0 54 -1.5t48 -4.5l-11 -51q-23 2 -45.5 3.5t-45.5 1.5q-39 0 -67 -7.5t-48.5 -24.5t-33.5 -44t-22 -67l-35 -165h233l-11 -48h-232l-75 -352q-11 -52 -28 -88.5t-43.5 -59.5t-63 -33.5t-87.5 -10.5
q-33 0 -57.5 1.5t-48.5 4.5l12 52q44 -6 90 -6q40 0 68 7.5t47.5 24.5t32 44t21.5 67l74 349h-230z" />
    <glyph glyph-name="zeroslash.op" horiz-adv-x="690" 
d="M417 718q83 0 137.5 -21t83.5 -66t31 -86t2 -52q0 -64 -17 -150q-21 -99 -50.5 -167.5t-71 -111t-97 -61.5t-128.5 -19q-82 0 -137 21t-84 66t-31.5 86t-2.5 53q0 64 18 149q21 99 50.5 167.5t71.5 111t97.5 61.5t127.5 19zM130 351q-14 -66 -16 -97t-2 -41q0 -37 8 -65
l461 457q-46 58 -169 58q-60 0 -105 -17t-78.5 -54t-57 -96.5t-41.5 -144.5zM312 39q60 0 105 17t78.5 54t57 96.5t41.5 144.5q18 80 18 138q0 38 -8 66l-461 -457q23 -31 64.5 -45t104.5 -14z" />
    <glyph glyph-name="zero.op" horiz-adv-x="690" 
d="M417 718q83 0 137.5 -21t83.5 -66t31 -86t2 -52q0 -64 -17 -150q-21 -99 -50.5 -167.5t-71 -111t-97 -61.5t-128.5 -19q-82 0 -137 21t-84 66t-31.5 86t-2.5 53q0 64 18 149q21 99 50.5 167.5t71.5 111t97.5 61.5t127.5 19zM312 39q60 0 105 17t78.5 54t57 96.5
t41.5 144.5q18 84 18 142q0 1 -0.5 31.5t-22.5 68t-65.5 54t-111.5 16.5q-60 0 -105 -17t-78.5 -54t-57 -96.5t-41.5 -144.5q-18 -83 -18 -141q0 -2 0.5 -32.5t22.5 -68t66 -54t111 -16.5z" />
    <glyph glyph-name="one.op" horiz-adv-x="522" 
d="M420 702h54l-149 -702h-58l135 636l-290 -127l-17 50z" />
    <glyph glyph-name="two.op" horiz-adv-x="690" 
d="M417 718q134 0 187 -38t53 -101q0 -21 -5 -45q-5 -25 -14 -46t-25.5 -40t-42 -37t-63.5 -37l-278 -141q-34 -18 -55.5 -32t-34 -28.5t-19 -33t-12.5 -45.5l-10 -46h463l-11 -48h-521l20 92q8 37 18 63t28 47t45 39.5t69 39.5l281 144q30 16 48.5 29.5t29.5 27t16 27.5
t8 31q3 16 3 30q0 44 -39.5 69t-146.5 25q-68 0 -130.5 -7t-113.5 -18l11 54q51 11 108.5 18t132.5 7z" />
    <glyph glyph-name="three.op" horiz-adv-x="690" 
d="M10 -40q104 -26 238 -26q74 0 122.5 9.5t78.5 29t46 50.5t26 73t10 72q0 8 -2 29t-23 42.5t-60.5 30.5t-99.5 9h-174l10 53h171q53 0 93 6.5t67.5 22t45 42.5t25.5 68q8 39 8 69q0 10 -2 33t-24 47.5t-63 36t-104 11.5q-66 0 -121.5 -6.5t-103.5 -18.5l10 50
q47 11 101 18t122 7q153 0 200 -50t47 -121q0 -35 -9 -78q-16 -79 -60 -117t-109 -49q69 -14 91.5 -53.5t22.5 -82.5q0 -36 -11 -82q-12 -52 -33 -90t-58.5 -63t-95.5 -36.5t-144 -11.5q-81 0 -140.5 7t-107.5 19z" />
    <glyph glyph-name="four.op" horiz-adv-x="690" 
d="M607 356l-97 -456h-58l41 195h-493l10 47l417 560h68l-412 -554h422l44 208h58z" />
    <glyph glyph-name="five.op" horiz-adv-x="690" 
d="M9 -35q50 -13 112.5 -19.5t134.5 -6.5q68 0 113.5 11t75 33t45 54t24.5 75q7 35 7 62q0 15 -4 39.5t-28 46.5t-65.5 31.5t-101.5 9.5h-206l85 401h468l-11 -53h-409l-63 -295h145q73 0 125.5 -13t83.5 -42.5t37 -62t6 -53.5q0 -35 -9 -78q-12 -57 -34 -98.5t-60 -69
t-93.5 -40.5t-135.5 -13q-84 0 -145 7t-108 19z" />
    <glyph glyph-name="six.op" horiz-adv-x="690" 
d="M322 -16q-87 0 -143.5 23t-84 73.5t-27.5 131.5t25 199q26 116 58.5 194t78.5 125t109 67.5t149 20.5q66 0 115 -7.5t91 -19.5l-11 -54q-41 11 -89 18.5t-107 7.5q-72 0 -124.5 -16.5t-91 -56t-66 -105t-48.5 -163.5q24 10 49 18.5t53 15t60 10t70 3.5q60 0 108 -14
t79 -44.5t38.5 -64.5t7.5 -58q0 -32 -8 -70q-13 -66 -37 -110.5t-59.5 -72t-83.5 -39.5t-111 -12zM378 416q-38 0 -69 -3.5t-58.5 -9.5t-53 -14.5t-52.5 -18.5q-19 -93 -19.5 -125.5t-0.5 -33.5q0 -62 21 -101t65 -56t114 -17q48 0 86 8.5t66 30.5t47 58.5t31 92.5
q6 30 6 54q0 18 -5.5 43.5t-29 48t-61 33t-87.5 10.5z" />
    <glyph glyph-name="seven.op" horiz-adv-x="690" 
d="M152 702h570l-10 -46l-573 -756h-75l577 749h-500z" />
    <glyph glyph-name="eight.op" horiz-adv-x="690" 
d="M289 -16q-153 0 -203 47t-50 116q0 29 7 62q20 94 66.5 140t121.5 60q-27 8 -48 22.5t-33 37.5t-13.5 43t-1.5 27q0 28 8 63q11 54 32 94.5t57.5 67.5t91.5 40.5t133 13.5q145 0 191 -45.5t46 -111.5q0 -29 -7 -64q-19 -90 -61 -134.5t-110 -58.5q64 -14 87.5 -51.5
t23.5 -82.5q0 -31 -8 -69q-12 -54 -34 -95t-60 -68t-95.5 -40.5t-140.5 -13.5zM379 432q58 0 100 7t71 25.5t47.5 49.5t28.5 79q7 33 7 60q0 7 -1.5 26.5t-21 41t-58.5 32t-102 10.5q-62 0 -105.5 -10.5t-72.5 -32t-45.5 -53.5t-25.5 -74q-7 -32 -7 -56q0 -13 3.5 -33.5
t25 -39t60 -25.5t96.5 -7zM296 40q66 0 112.5 10.5t77 31.5t48 53t26.5 75q7 32 7 57q0 14 -4 36t-27 41t-65 26.5t-103 7.5t-105.5 -7.5t-76 -26.5t-51.5 -51.5t-31 -82.5q-6 -31 -6 -56q0 -10 2 -30.5t23.5 -41.5t63.5 -31.5t109 -10.5z" />
    <glyph glyph-name="nine.op" horiz-adv-x="690" 
d="M400 718q87 0 143.5 -23t84 -73.5t28 -92t0.5 -42.5q0 -80 -26 -196q-25 -115 -58 -193.5t-79 -125.5t-109 -67.5t-149 -20.5q-66 0 -115 7.5t-91 19.5l11 54q42 -11 89.5 -18.5t107.5 -7.5q72 0 124 16.5t90.5 56t66 105t48.5 163.5q-24 -10 -49 -18.5t-53 -15t-60 -10
t-70 -3.5q-60 0 -108 14t-78.5 44.5t-38.5 64.5t-8 59q0 31 8 69q14 66 37.5 110.5t59 72t84 39.5t110.5 12zM168 475q-6 -29 -6 -53q0 -19 5.5 -44.5t29 -48t61 -33t86.5 -10.5q38 0 69.5 3.5t59 9.5t53 14.5t51.5 18.5q19 93 19.5 125.5t0.5 33.5q0 62 -21 101t-65 56
t-114 17q-48 0 -85.5 -8.5t-65.5 -30.5t-47 -58.5t-31 -92.5z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="399" 
d="M305 811q45 0 76.5 -13t48 -42t17.5 -55t1 -30q0 -45 -13 -107q-13 -61 -29.5 -104.5t-41 -71.5t-59 -41.5t-82.5 -13.5q-46 0 -77.5 13t-48 42t-17.5 55t-1 30q0 45 13 107q13 61 30 104.5t41.5 71.5t59 41.5t82.5 13.5zM226 377q34 0 59 10t43 33.5t31.5 60.5t24.5 91
q12 54 12.5 74.5t0.5 23.5q0 33 -9.5 55t-31 32t-55.5 10t-58.5 -10t-42.5 -33.5t-31.5 -60.5t-24.5 -91q-12 -54 -12.5 -74.5t-0.5 -24.5q0 -32 9 -54t30.5 -32t55.5 -10z" />
    <glyph glyph-name="one.numr" horiz-adv-x="311" 
d="M299 802h49l-98 -460h-51l86 403l-156 -70l-17 44z" />
    <glyph glyph-name="two.numr" horiz-adv-x="399" 
d="M302 811q90 0 118 -26t28 -64q0 -15 -4 -33q-5 -21 -11 -35t-17 -25.5t-29 -23t-46 -26.5l-151 -82q-17 -10 -28 -17.5t-17.5 -15.5t-10.5 -18t-7 -24l-8 -37h265l-9 -42h-316l18 87q4 19 10 33t16 25.5t25 21.5t37 22l157 85q20 11 32.5 19t20 16t11 16.5t6.5 21.5
q2 12 2 22q0 25 -17.5 40.5t-82.5 15.5q-36 0 -71 -4.5t-64 -10.5l9 45q29 6 60.5 10t73.5 4z" />
    <glyph glyph-name="three.numr" horiz-adv-x="399" 
d="M67 392q58 -16 137 -16q40 0 66 4.5t42 14.5t24.5 26t13.5 38q5 20 5 36q0 26 -16.5 42.5t-78.5 16.5h-106l8 42h104q56 0 83.5 14.5t35.5 57.5q4 18 4 33q0 28 -17.5 47.5t-86.5 19.5q-39 0 -70.5 -3.5t-61.5 -11.5l9 44q27 6 59 10t74 4q92 0 120.5 -28.5t28.5 -70.5
q0 -20 -5 -44q-9 -45 -33 -66.5t-61 -29.5q38 -10 50 -33t12 -46q0 -20 -6 -45q-7 -31 -20 -52.5t-35.5 -35.5t-57 -20.5t-85.5 -6.5q-48 0 -83 4t-63 12z" />
    <glyph glyph-name="four.numr" horiz-adv-x="399" 
d="M419 604l-55 -262h-51l22 106h-277l9 40l233 314h58l-232 -312h218l24 114h51z" />
    <glyph glyph-name="five.numr" horiz-adv-x="399" 
d="M66 393q29 -8 65.5 -12t77.5 -4q36 0 60.5 5.5t40 16.5t24 28t13.5 40q3 18 3 32q0 32 -20 49.5t-83 17.5h-126l51 236h279l-9 -42h-228l-32 -152h74q88 0 118 -28t30 -72q0 -21 -5 -47q-7 -35 -21 -59.5t-37.5 -39.5t-56.5 -22t-80 -7q-48 0 -85 4.5t-63 11.5z" />
    <glyph glyph-name="six.numr" horiz-adv-x="399" 
d="M236 333q-52 0 -85.5 13.5t-50 42.5t-17 53.5t-0.5 25.5q0 46 14 111q15 65 32.5 109.5t43 71.5t61.5 39t86 12q41 0 71 -4.5t54 -11.5l-10 -44q-24 7 -52 11.5t-62 4.5q-38 0 -65 -8.5t-46 -29t-32 -54t-24 -83.5q26 11 54.5 18t67.5 7q36 0 64.5 -8.5t46 -27t22 -38.5
t4.5 -34q0 -19 -5 -41q-16 -76 -57 -105.5t-115 -29.5zM265 574q-38 0 -65.5 -6.5t-55.5 -17.5q-10 -49 -10.5 -66.5t-0.5 -18.5q0 -32 11 -52t34 -29t59 -9q51 0 79 21t40 79q3 17 3 31q0 31 -21 49.5t-73 18.5z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="399" 
d="M145 802h336l-9 -40l-318 -420h-64l324 418h-279z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="399" 
d="M219 333q-91 0 -122 27t-31 68q0 17 4 37q11 51 36.5 77t67.5 35q-31 11 -42 30.5t-11 41.5q0 18 5 40q6 31 19 54t34.5 38t53.5 22.5t77 7.5q85 0 114.5 -26.5t29.5 -66.5q0 -16 -4 -35q-11 -50 -34.5 -75.5t-61.5 -35.5q35 -9 48 -30t13 -47q0 -17 -4 -39
q-7 -31 -20 -54t-35.5 -38.5t-56 -23t-80.5 -7.5zM269 594q30 0 51.5 4t36.5 13.5t24.5 26t14.5 41.5q4 16 4 29q0 24 -15 42t-80 18q-32 0 -54.5 -5.5t-37 -16.5t-23.5 -27.5t-14 -39.5q-3 -16 -3 -29q0 -27 17.5 -41.5t78.5 -14.5zM223 376q34 0 58 5.5t40 16.5t25 28
t14 40q4 17 4 30q0 29 -20 44.5t-84 15.5q-63 0 -94.5 -18.5t-42.5 -71.5q-4 -16 -4 -30q0 -5 1.5 -16t13 -22t33 -16.5t56.5 -5.5z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="399" 
d="M290 811q52 0 85.5 -13.5t50 -42.5t17 -53.5t0.5 -25.5q0 -46 -14 -111q-15 -65 -32.5 -109.5t-43.5 -71.5t-61.5 -39t-86.5 -12q-40 0 -70.5 4.5t-53.5 11.5l9 44q25 -7 53 -11.5t62 -4.5q38 0 64.5 8.5t45.5 28.5t32.5 53.5t23.5 83.5q-25 -11 -53.5 -17.5t-67.5 -6.5
q-73 0 -105 30t-32 76q0 20 5 43q16 76 56.5 105.5t115.5 29.5zM170 669q-3 -17 -3 -31q0 -31 21 -49.5t73 -18.5q38 0 65.5 6.5t54.5 17.5q10 49 10.5 66.5t0.5 18.5q0 32 -11 52t-34 29t-59 9q-51 0 -78.5 -21t-39.5 -79z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="399" 
d="M233 469q45 0 76.5 -13t48 -42t17.5 -55t1 -30q0 -45 -13 -107q-13 -61 -29.5 -104.5t-41 -71.5t-59 -41.5t-82.5 -13.5q-46 0 -77.5 13t-48 42t-17.5 55t-1 30q0 45 13 107q13 61 30 104.5t41.5 71.5t59 41.5t82.5 13.5zM154 35q34 0 59 10t43 33.5t31.5 60.5t24.5 91
q12 54 12.5 74.5t0.5 23.5q0 33 -9.5 55t-31 32t-55.5 10t-58.5 -10t-42.5 -33.5t-31.5 -60.5t-24.5 -91q-12 -54 -12.5 -74.5t-0.5 -24.5q0 -32 9 -54t30.5 -32t55.5 -10z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="311" 
d="M227 460h49l-98 -460h-51l86 403l-156 -70l-17 44z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="399" 
d="M230 469q90 0 118 -26t28 -64q0 -15 -4 -33q-5 -21 -11 -35t-17 -25.5t-29 -23t-46 -26.5l-151 -82q-17 -10 -28 -17.5t-17.5 -15.5t-10.5 -18t-7 -24l-8 -37h265l-9 -42h-316l18 87q4 19 10 33t16 25.5t25 21.5t37 22l157 85q20 11 32.5 19t20 16t11 16.5t6.5 21.5
q2 12 2 22q0 25 -17.5 40.5t-82.5 15.5q-36 0 -71 -4.5t-64 -10.5l9 45q29 6 60.5 10t73.5 4z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="399" 
d="M-5 50q58 -16 137 -16q40 0 66 4.5t42 14.5t24.5 26t13.5 38q5 20 5 36q0 26 -16.5 42.5t-78.5 16.5h-106l8 42h104q56 0 83.5 14.5t35.5 57.5q4 18 4 33q0 28 -17.5 47.5t-86.5 19.5q-39 0 -70.5 -3.5t-61.5 -11.5l9 44q27 6 59 10t74 4q92 0 120.5 -28.5t28.5 -70.5
q0 -20 -5 -44q-9 -45 -33 -66.5t-61 -29.5q38 -10 50 -33t12 -46q0 -20 -6 -45q-7 -31 -20 -52.5t-35.5 -35.5t-57 -20.5t-85.5 -6.5q-48 0 -83 4t-63 12z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="399" 
d="M347 262l-55 -262h-51l22 106h-277l9 40l233 314h58l-232 -312h218l24 114h51z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="399" 
d="M-6 51q29 -8 65.5 -12t77.5 -4q36 0 60.5 5.5t40 16.5t24 28t13.5 40q3 18 3 32q0 32 -20 49.5t-83 17.5h-126l51 236h279l-9 -42h-228l-32 -152h74q88 0 118 -28t30 -72q0 -21 -5 -47q-7 -35 -21 -59.5t-37.5 -39.5t-56.5 -22t-80 -7q-48 0 -85 4.5t-63 11.5z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="399" 
d="M164 -9q-52 0 -85.5 13.5t-50 42.5t-17 53.5t-0.5 25.5q0 46 14 111q15 65 32.5 109.5t43 71.5t61.5 39t86 12q41 0 71 -4.5t54 -11.5l-10 -44q-24 7 -52 11.5t-62 4.5q-38 0 -65 -8.5t-46 -29t-32 -54t-24 -83.5q26 11 54.5 18t67.5 7q36 0 64.5 -8.5t46 -27t22 -38.5
t4.5 -34q0 -19 -5 -41q-16 -76 -57 -105.5t-115 -29.5zM193 232q-38 0 -65.5 -6.5t-55.5 -17.5q-10 -49 -10.5 -66.5t-0.5 -18.5q0 -32 11 -52t34 -29t59 -9q51 0 79 21t40 79q3 17 3 31q0 31 -21 49.5t-73 18.5z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="399" 
d="M73 460h336l-9 -40l-318 -420h-64l324 418h-279z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="399" 
d="M147 -9q-91 0 -122 27t-31 68q0 17 4 37q11 51 36.5 77t67.5 35q-31 11 -42 30.5t-11 41.5q0 18 5 40q6 31 19 54t34.5 38t53.5 22.5t77 7.5q85 0 114.5 -26.5t29.5 -66.5q0 -16 -4 -35q-11 -50 -34.5 -75.5t-61.5 -35.5q35 -9 48 -30t13 -47q0 -17 -4 -39
q-7 -31 -20 -54t-35.5 -38.5t-56 -23t-80.5 -7.5zM197 252q30 0 51.5 4t36.5 13.5t24.5 26t14.5 41.5q4 16 4 29q0 24 -15 42t-80 18q-32 0 -54.5 -5.5t-37 -16.5t-23.5 -27.5t-14 -39.5q-3 -16 -3 -29q0 -27 17.5 -41.5t78.5 -14.5zM151 34q34 0 58 5.5t40 16.5t25 28
t14 40q4 17 4 30q0 29 -20 44.5t-84 15.5q-63 0 -94.5 -18.5t-42.5 -71.5q-4 -16 -4 -30q0 -5 1.5 -16t13 -22t33 -16.5t56.5 -5.5z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="399" 
d="M218 469q52 0 85.5 -13.5t50 -42.5t17 -53.5t0.5 -25.5q0 -46 -14 -111q-15 -65 -32.5 -109.5t-43.5 -71.5t-61.5 -39t-86.5 -12q-40 0 -70.5 4.5t-53.5 11.5l9 44q25 -7 53 -11.5t62 -4.5q38 0 64.5 8.5t45.5 28.5t32.5 53.5t23.5 83.5q-25 -11 -53.5 -17.5t-67.5 -6.5
q-73 0 -105 30t-32 76q0 20 5 43q16 76 56.5 105.5t115.5 29.5zM98 327q-3 -17 -3 -31q0 -31 21 -49.5t73 -18.5q38 0 65.5 6.5t54.5 17.5q10 49 10.5 66.5t0.5 18.5q0 32 -11 52t-34 29t-59 9q-51 0 -78.5 -21t-39.5 -79z" />
    <hkern u1="&#x23;" g2="nine.op" k="10" />
    <hkern u1="&#x23;" g2="four.op" k="46" />
    <hkern u1="&#x23;" u2="&#x37;" k="-5" />
    <hkern u1="&#x23;" u2="&#x34;" k="35" />
    <hkern u1="&#x28;" u2="&#x237;" k="-50" />
    <hkern u1="&#x28;" u2="j" k="-50" />
    <hkern u1="&#x2a;" u2="X" k="33" />
    <hkern u1="&#x2a;" u2="&#x2f;" k="110" />
    <hkern u1="&#x2f;" u2="&#xdf;" k="20" />
    <hkern u1="&#x2f;" u2="x" k="20" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="120" />
    <hkern u1="&#x31;" u2="&#x20ac;" k="10" />
    <hkern u1="&#x31;" u2="&#x192;" k="20" />
    <hkern u1="&#x31;" u2="&#xb0;" k="3" />
    <hkern u1="&#x31;" u2="&#x33;" k="20" />
    <hkern u1="&#x31;" u2="&#x31;" k="60" />
    <hkern u1="&#x32;" u2="&#x192;" k="60" />
    <hkern u1="&#x32;" u2="&#xa5;" k="12" />
    <hkern u1="&#x32;" u2="&#x3c;" k="50" />
    <hkern u1="&#x32;" u2="&#x37;" k="10" />
    <hkern u1="&#x32;" u2="&#x34;" k="18" />
    <hkern u1="&#x32;" u2="&#x32;" k="10" />
    <hkern u1="&#x32;" u2="&#x31;" k="10" />
    <hkern u1="&#x33;" u2="&#x3e;" k="5" />
    <hkern u1="&#x33;" u2="&#x39;" k="2" />
    <hkern u1="&#x33;" u2="&#x37;" k="10" />
    <hkern u1="&#x33;" u2="&#x34;" k="-2" />
    <hkern u1="&#x33;" u2="&#x33;" k="20" />
    <hkern u1="&#x33;" u2="&#x32;" k="2" />
    <hkern u1="&#x33;" u2="&#x31;" k="20" />
    <hkern u1="&#x34;" u2="&#x39;" k="1" />
    <hkern u1="&#x34;" u2="&#x31;" k="110" />
    <hkern u1="&#x35;" u2="&#x192;" k="8" />
    <hkern u1="&#x35;" u2="&#xb0;" k="22" />
    <hkern u1="&#x35;" u2="&#x39;" k="18" />
    <hkern u1="&#x35;" u2="&#x37;" k="30" />
    <hkern u1="&#x35;" u2="&#x35;" k="10" />
    <hkern u1="&#x35;" u2="&#x33;" k="30" />
    <hkern u1="&#x35;" u2="&#x32;" k="12" />
    <hkern u1="&#x35;" u2="&#x31;" k="70" />
    <hkern u1="&#x37;" u2="&#x192;" k="70" />
    <hkern u1="&#x37;" u2="&#xb0;" k="-20" />
    <hkern u1="&#x37;" u2="&#xa3;" k="50" />
    <hkern u1="&#x37;" u2="&#x3e;" k="90" />
    <hkern u1="&#x37;" u2="&#x3c;" k="140" />
    <hkern u1="&#x37;" u2="&#x35;" k="20" />
    <hkern u1="&#x37;" u2="&#x34;" k="82" />
    <hkern u1="&#x37;" u2="&#x33;" k="10" />
    <hkern u1="&#x37;" u2="&#x32;" k="40" />
    <hkern u1="&#x37;" u2="&#x23;" k="90" />
    <hkern u1="&#x3c;" g2="seven.op" k="18" />
    <hkern u1="&#x3c;" g2="three.op" k="17" />
    <hkern u1="&#x3c;" u2="&#x37;" k="40" />
    <hkern u1="&#x3c;" u2="&#x31;" k="90" />
    <hkern u1="&#x3e;" g2="nine.op" k="8" />
    <hkern u1="&#x3e;" g2="seven.op" k="110" />
    <hkern u1="&#x3e;" g2="five.op" k="8" />
    <hkern u1="&#x3e;" g2="three.op" k="25" />
    <hkern u1="&#x3e;" g2="one.op" k="50" />
    <hkern u1="&#x3e;" u2="&#x37;" k="100" />
    <hkern u1="&#x3e;" u2="&#x33;" k="17" />
    <hkern u1="&#x3e;" u2="&#x31;" k="101" />
    <hkern u1="&#x3f;" u2="X" k="30" />
    <hkern u1="&#x40;" u2="v" k="20" />
    <hkern u1="&#x40;" u2="X" k="20" />
    <hkern u1="&#x40;" u2="V" k="70" />
    <hkern u1="B" g2="bullet.case" k="2" />
    <hkern u1="B" u2="&#x2122;" k="40" />
    <hkern u1="B" u2="&#xae;" k="30" />
    <hkern u1="B" u2="v" k="20" />
    <hkern u1="B" u2="X" k="20" />
    <hkern u1="B" u2="V" k="31" />
    <hkern u1="F" g2="at.case" k="17" />
    <hkern u1="F" u2="&#xbf;" k="166" />
    <hkern u1="F" u2="&#xb0;" k="-3" />
    <hkern u1="F" u2="x" k="20" />
    <hkern u1="F" u2="v" k="10" />
    <hkern u1="F" u2="X" k="17" />
    <hkern u1="F" u2="&#x40;" k="17" />
    <hkern u1="F" u2="&#x3f;" k="-6" />
    <hkern u1="F" u2="&#x2f;" k="103" />
    <hkern u1="P" g2="at.case" k="-3" />
    <hkern u1="P" g2="bullet.case" k="-3" />
    <hkern u1="P" u2="v" k="-3" />
    <hkern u1="P" u2="X" k="52" />
    <hkern u1="P" u2="V" k="11" />
    <hkern u1="P" u2="&#x2f;" k="63" />
    <hkern u1="P" u2="&#x2a;" k="-6" />
    <hkern u1="V" g2="at.case" k="12" />
    <hkern u1="V" g2="bullet.case" k="50" />
    <hkern u1="V" g2="questiondown.case" k="66" />
    <hkern u1="V" u2="&#x2022;" k="70" />
    <hkern u1="V" u2="&#xdf;" k="30" />
    <hkern u1="V" u2="&#xbf;" k="123" />
    <hkern u1="V" u2="&#xb0;" k="-3" />
    <hkern u1="V" u2="&#xa9;" k="30" />
    <hkern u1="V" u2="&#xa1;" k="50" />
    <hkern u1="V" u2="x" k="40" />
    <hkern u1="V" u2="v" k="32" />
    <hkern u1="V" u2="V" k="-3" />
    <hkern u1="V" u2="&#x40;" k="50" />
    <hkern u1="V" u2="&#x2f;" k="60" />
    <hkern u1="V" u2="&#x23;" k="66" />
    <hkern u1="X" g2="at.case" k="80" />
    <hkern u1="X" g2="bullet.case" k="120" />
    <hkern u1="X" u2="&#x2022;" k="40" />
    <hkern u1="X" u2="&#xae;" k="20" />
    <hkern u1="X" u2="&#xa9;" k="40" />
    <hkern u1="X" u2="x" k="-2" />
    <hkern u1="X" u2="v" k="50" />
    <hkern u1="X" u2="&#x40;" k="20" />
    <hkern u1="X" u2="&#x2a;" k="16" />
    <hkern u1="Y" u2="&#xf0;" k="80" />
    <hkern u1="\" u2="&#xdf;" k="20" />
    <hkern u1="\" u2="v" k="60" />
    <hkern u1="\" u2="V" k="100" />
    <hkern u1="v" u2="&#x2022;" k="20" />
    <hkern u1="v" u2="v" k="-4" />
    <hkern u1="v" u2="X" k="50" />
    <hkern u1="v" u2="V" k="40" />
    <hkern u1="v" u2="&#x2f;" k="60" />
    <hkern u1="x" u2="&#x2122;" k="40" />
    <hkern u1="x" u2="&#x2022;" k="40" />
    <hkern u1="x" u2="x" k="-3" />
    <hkern u1="x" u2="\" k="20" />
    <hkern u1="x" u2="X" k="-2" />
    <hkern u1="x" u2="V" k="50" />
    <hkern u1="&#xa1;" u2="V" k="80" />
    <hkern u1="&#xa3;" u2="&#x37;" k="10" />
    <hkern u1="&#xa3;" u2="&#x31;" k="30" />
    <hkern u1="&#xa5;" u2="&#x34;" k="3" />
    <hkern u1="&#xa5;" u2="&#x32;" k="22" />
    <hkern u1="&#xa5;" u2="&#x31;" k="30" />
    <hkern u1="&#xa9;" u2="X" k="40" />
    <hkern u1="&#xa9;" u2="V" k="50" />
    <hkern u1="&#xae;" u2="X" k="20" />
    <hkern u1="&#xb0;" g2="seven.op" k="-20" />
    <hkern u1="&#xb0;" g2="four.op" k="102" />
    <hkern u1="&#xb0;" u2="&#x37;" k="-10" />
    <hkern u1="&#xb0;" u2="&#x34;" k="80" />
    <hkern u1="&#xb0;" u2="&#x32;" k="20" />
    <hkern u1="&#xbf;" u2="&#xdf;" k="50" />
    <hkern u1="&#xbf;" u2="x" k="50" />
    <hkern u1="&#xbf;" u2="v" k="70" />
    <hkern u1="&#xbf;" u2="X" k="40" />
    <hkern u1="&#xbf;" u2="V" k="130" />
    <hkern u1="&#xbf;" u2="&#x237;" k="-100" />
    <hkern u1="&#xbf;" u2="j" k="-90" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="80" />
    <hkern u1="&#xde;" g2="at.case" k="-5" />
    <hkern u1="&#xde;" g2="bullet.case" k="-3" />
    <hkern u1="&#xde;" g2="parenleft.case" k="-30" />
    <hkern u1="&#xde;" u2="&#xbf;" k="30" />
    <hkern u1="&#xde;" u2="v" k="-3" />
    <hkern u1="&#xde;" u2="X" k="55" />
    <hkern u1="&#xde;" u2="V" k="42" />
    <hkern u1="&#xde;" u2="&#x40;" k="-2" />
    <hkern u1="&#xde;" u2="&#x2f;" k="33" />
    <hkern u1="&#xde;" u2="&#x2a;" k="-2" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="30" />
    <hkern u1="&#xdf;" u2="&#xb0;" k="10" />
    <hkern u1="&#xdf;" u2="&#xae;" k="20" />
    <hkern u1="&#xdf;" u2="x" k="10" />
    <hkern u1="&#xdf;" u2="v" k="10" />
    <hkern u1="&#xdf;" u2="\" k="20" />
    <hkern u1="&#xdf;" u2="X" k="5" />
    <hkern u1="&#xdf;" u2="V" k="40" />
    <hkern u1="&#xdf;" u2="&#x2f;" k="20" />
    <hkern u1="&#xdf;" u2="&#x23;" k="-3" />
    <hkern u1="&#xf0;" u2="&#x2122;" k="30" />
    <hkern u1="&#xf0;" u2="&#xb0;" k="30" />
    <hkern u1="&#xf0;" u2="&#xae;" k="20" />
    <hkern u1="&#xf0;" u2="x" k="23" />
    <hkern u1="&#xf0;" u2="v" k="4" />
    <hkern u1="&#xf0;" u2="\" k="40" />
    <hkern u1="&#xf0;" u2="X" k="23" />
    <hkern u1="&#xf0;" u2="V" k="71" />
    <hkern u1="&#xf0;" u2="&#x2a;" k="10" />
    <hkern u1="&#x13d;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x13d;" u2="&#x21a;" k="80" />
    <hkern u1="&#x13d;" u2="&#x17d;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x17b;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x179;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x178;" k="80" />
    <hkern u1="&#x13d;" u2="&#x176;" k="80" />
    <hkern u1="&#x13d;" u2="&#x166;" k="80" />
    <hkern u1="&#x13d;" u2="&#x164;" k="80" />
    <hkern u1="&#x13d;" u2="&#x162;" k="80" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="80" />
    <hkern u1="&#x13d;" u2="Z" k="-20" />
    <hkern u1="&#x13d;" u2="Y" k="80" />
    <hkern u1="&#x13d;" u2="V" k="60" />
    <hkern u1="&#x13d;" u2="T" k="80" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-40" />
    <hkern u1="&#x165;" u2="]" k="-40" />
    <hkern u1="&#x165;" u2="&#x29;" k="-40" />
    <hkern u1="&#x176;" u2="&#xf0;" k="80" />
    <hkern u1="&#x178;" u2="&#xf0;" k="80" />
    <hkern u1="&#x192;" u2="&#x37;" k="20" />
    <hkern u1="&#x192;" u2="&#x34;" k="33" />
    <hkern u1="&#x192;" u2="&#x33;" k="8" />
    <hkern u1="&#x192;" u2="&#x32;" k="13" />
    <hkern u1="&#x192;" u2="&#x31;" k="40" />
    <hkern u1="&#x1ef2;" u2="&#xf0;" k="80" />
    <hkern u1="&#x2022;" u2="x" k="40" />
    <hkern u1="&#x2022;" u2="v" k="20" />
    <hkern u1="&#x2022;" u2="X" k="40" />
    <hkern u1="&#x2022;" u2="V" k="70" />
    <hkern u1="&#x2044;" g2="seven.dnom" k="-40" />
    <hkern u1="&#x2044;" g2="four.dnom" k="20" />
    <hkern u1="&#x20ac;" u2="&#x31;" k="10" />
    <hkern u1="&#x2116;" u2="&#x37;" k="42" />
    <hkern g1="questiondown.case" u2="V" k="80" />
    <hkern g1="bullet.case" u2="X" k="110" />
    <hkern g1="bullet.case" u2="V" k="70" />
    <hkern g1="at.case" u2="X" k="60" />
    <hkern g1="at.case" u2="V" k="45" />
    <hkern g1="sterling.op" g2="nine.op" k="10" />
    <hkern g1="sterling.op" g2="three.op" k="20" />
    <hkern g1="sterling.op" g2="one.op" k="20" />
    <hkern g1="yen.op" g2="nine.op" k="10" />
    <hkern g1="yen.op" g2="four.op" k="3" />
    <hkern g1="florin.op" g2="nine.op" k="10" />
    <hkern g1="florin.op" g2="seven.op" k="30" />
    <hkern g1="florin.op" g2="five.op" k="22" />
    <hkern g1="florin.op" g2="four.op" k="32" />
    <hkern g1="florin.op" g2="three.op" k="20" />
    <hkern g1="florin.op" g2="two.op" k="20" />
    <hkern g1="florin.op" g2="one.op" k="30" />
    <hkern g1="one.op" g2="three.op" k="30" />
    <hkern g1="one.op" g2="one.op" k="30" />
    <hkern g1="two.op" g2="florin.op" k="25" />
    <hkern g1="two.op" u2="&#x3c;" k="50" />
    <hkern g1="three.op" g2="nine.op" k="5" />
    <hkern g1="three.op" g2="one.op" k="20" />
    <hkern g1="three.op" g2="florin.op" k="20" />
    <hkern g1="three.op" g2="yen.op" k="20" />
    <hkern g1="four.op" g2="nine.op" k="10" />
    <hkern g1="four.op" g2="seven.op" k="10" />
    <hkern g1="four.op" g2="one.op" k="110" />
    <hkern g1="four.op" u2="&#xb0;" k="70" />
    <hkern g1="five.op" g2="nine.op" k="20" />
    <hkern g1="five.op" g2="seven.op" k="40" />
    <hkern g1="five.op" g2="one.op" k="50" />
    <hkern g1="five.op" g2="florin.op" k="18" />
    <hkern g1="five.op" u2="&#xb0;" k="10" />
    <hkern g1="five.op" u2="&#x3c;" k="8" />
    <hkern g1="five.op" u2="&#x23;" k="-30" />
    <hkern g1="seven.op" g2="five.op" k="20" />
    <hkern g1="seven.op" g2="four.op" k="80" />
    <hkern g1="seven.op" g2="florin.op" k="70" />
    <hkern g1="seven.op" u2="&#xb0;" k="-20" />
    <hkern g1="seven.op" u2="&#x3e;" k="30" />
    <hkern g1="seven.op" u2="&#x3c;" k="70" />
    <hkern g1="nine.op" g2="nine.op" k="20" />
    <hkern g1="nine.op" g2="seven.op" k="50" />
    <hkern g1="nine.op" g2="five.op" k="20" />
    <hkern g1="nine.op" g2="three.op" k="15" />
    <hkern g1="nine.op" g2="one.op" k="20" />
    <hkern g1="nine.op" u2="&#xb0;" k="20" />
    <hkern g1="two.numr" u2="&#x2044;" k="-40" />
    <hkern g1="four.numr" u2="&#x2044;" k="-30" />
    <hkern g1="seven.numr" u2="&#x2044;" k="60" />
    <hkern g1="germandbls"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="germandbls"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="germandbls"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="germandbls"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="germandbls"
	g2="comma,quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="germandbls"
	g2="hyphen,periodcentered,endash,emdash"
	k="-2" />
    <hkern g1="germandbls"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="2" />
    <hkern g1="germandbls"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="38" />
    <hkern g1="germandbls"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="germandbls"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="germandbls"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="germandbls"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="germandbls"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="1" />
    <hkern g1="germandbls"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="3" />
    <hkern g1="germandbls"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="1" />
    <hkern g1="germandbls"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="1" />
    <hkern g1="six,six.op"
	g2="quotedbl,quotesingle"
	k="53" />
    <hkern g1="six,six.op"
	g2="period,ellipsis"
	k="32" />
    <hkern g1="six,six.op"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="six,six.op"
	g2="three.op"
	k="35" />
    <hkern g1="six,six.op"
	g2="five"
	k="20" />
    <hkern g1="six,six.op"
	g2="three"
	k="30" />
    <hkern g1="six,six.op"
	g2="nine.op"
	k="50" />
    <hkern g1="six,six.op"
	g2="five.op"
	k="20" />
    <hkern g1="six,six.op"
	g2="two"
	k="12" />
    <hkern g1="six,six.op"
	g2="ordfeminine,ordmasculine"
	k="32" />
    <hkern g1="six,six.op"
	g2="cent,cent.weight"
	k="3" />
    <hkern g1="six,six.op"
	g2="one"
	k="70" />
    <hkern g1="six,six.op"
	g2="Euro"
	k="2" />
    <hkern g1="six,six.op"
	g2="one.op"
	k="50" />
    <hkern g1="six,six.op"
	g2="dollar,dollar.weight"
	k="22" />
    <hkern g1="six,six.op"
	g2="degree"
	k="32" />
    <hkern g1="six,six.op"
	g2="seven.op"
	k="55" />
    <hkern g1="six,six.op"
	g2="florin"
	k="20" />
    <hkern g1="six,six.op"
	g2="nine"
	k="32" />
    <hkern g1="six,six.op"
	g2="florin.op"
	k="17" />
    <hkern g1="six,six.op"
	g2="yen"
	k="33" />
    <hkern g1="six,six.op"
	g2="seven"
	k="62" />
    <hkern g1="six,six.op"
	g2="zeroslash.op,zero.op"
	k="5" />
    <hkern g1="six,six.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="70" />
    <hkern g1="six,six.op"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="six,six.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="six,six.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="twosuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="twosuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="two.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="two.op"
	g2="plus,divide,minus"
	k="30" />
    <hkern g1="two.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="25" />
    <hkern g1="four"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="four"
	g2="ordfeminine,ordmasculine"
	k="40" />
    <hkern g1="four"
	g2="cent,cent.weight"
	k="20" />
    <hkern g1="four"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="quotedbl,quotesingle"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="quotedbl,quotesingle"
	g2="three.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="five"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="three"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="nine.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="five.op"
	k="30" />
    <hkern g1="quotedbl,quotesingle"
	g2="two"
	k="30" />
    <hkern g1="quotedbl,quotesingle"
	g2="two.op"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="slash"
	k="180" />
    <hkern g1="quotedbl,quotesingle"
	g2="four.op"
	k="130" />
    <hkern g1="quotedbl,quotesingle"
	g2="four"
	k="70" />
    <hkern g1="quotedbl,quotesingle"
	g2="x"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="zeroslash.op,zero.op"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="quotedbl,quotesingle"
	g2="eight,eight.op"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="60" />
    <hkern g1="quotedbl,quotesingle"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="V"
	k="60" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="X"
	k="100" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="13" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="66" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="guilsinglright.case,guillemotright.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="two"
	g2="cent,cent.weight"
	k="13" />
    <hkern g1="two"
	g2="dollar,dollar.weight"
	k="10" />
    <hkern g1="two"
	g2="plus,divide,minus"
	k="85" />
    <hkern g1="two"
	g2="zero,six,zeroslash,six.op"
	k="10" />
    <hkern g1="two"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="10" />
    <hkern g1="threesuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="threesuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="Euro.op,Euro.op.weight"
	g2="three.op"
	k="8" />
    <hkern g1="nine.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="nine.op"
	g2="period,ellipsis"
	k="32" />
    <hkern g1="nine.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="nine.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-18" />
    <hkern g1="eth"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="eth"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="eth"
	g2="ae,aeacute"
	k="5" />
    <hkern g1="eth"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="eth"
	g2="z,zacute,zdotaccent,zcaron"
	k="3" />
    <hkern g1="eth"
	g2="hyphen,periodcentered,endash,emdash"
	k="-3" />
    <hkern g1="eth"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="eth"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="46" />
    <hkern g1="eth"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="eth"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="30" />
    <hkern g1="eth"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="23" />
    <hkern g1="eth"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="4" />
    <hkern g1="eth"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="2" />
    <hkern g1="eth"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="1" />
    <hkern g1="eth"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="5" />
    <hkern g1="eth"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-1" />
    <hkern g1="eth"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="1" />
    <hkern g1="uni2075"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2075"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="one"
	g2="quotedbl,quotesingle"
	k="18" />
    <hkern g1="one"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="one"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="one"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="12" />
    <hkern g1="five.op"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="five.op"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="five.op"
	g2="ordfeminine,ordmasculine"
	k="30" />
    <hkern g1="five.op"
	g2="cent.op,cent.op.weight"
	k="30" />
    <hkern g1="five.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="five.op"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="five.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="five.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="30" />
    <hkern g1="uni2077"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2077"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="zeroslash.op,zero.op"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="zeroslash.op,zero.op"
	g2="period,ellipsis"
	k="32" />
    <hkern g1="zeroslash.op,zero.op"
	g2="three.op"
	k="40" />
    <hkern g1="zeroslash.op,zero.op"
	g2="nine.op"
	k="5" />
    <hkern g1="zeroslash.op,zero.op"
	g2="five.op"
	k="20" />
    <hkern g1="zeroslash.op,zero.op"
	g2="one.op"
	k="10" />
    <hkern g1="zeroslash.op,zero.op"
	g2="seven.op"
	k="40" />
    <hkern g1="zeroslash.op,zero.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="62" />
    <hkern g1="zeroslash.op,zero.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="three"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="three"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="three"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="three"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="three.op"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="three.op"
	g2="cent.op,cent.op.weight"
	k="20" />
    <hkern g1="three.op"
	g2="Euro.op,Euro.op.weight"
	k="-8" />
    <hkern g1="three.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="three.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="uni2078"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2078"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="v"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="v"
	g2="ae,aeacute"
	k="15" />
    <hkern g1="v"
	g2="guillemotleft,guilsinglleft"
	k="12" />
    <hkern g1="v"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="v"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="v"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="v"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="51" />
    <hkern g1="v"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-1" />
    <hkern g1="v"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-10" />
    <hkern g1="v"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="68" />
    <hkern g1="v"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="v"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-3" />
    <hkern g1="v"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-1" />
    <hkern g1="v"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="v"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="13" />
    <hkern g1="v"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-2" />
    <hkern g1="v"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="v"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="1" />
    <hkern g1="eight,eight.op"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="eight,eight.op"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="eight,eight.op"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="eight,eight.op"
	g2="nine.op"
	k="5" />
    <hkern g1="eight,eight.op"
	g2="one"
	k="10" />
    <hkern g1="eight,eight.op"
	g2="Euro"
	k="-8" />
    <hkern g1="eight,eight.op"
	g2="cent.op,cent.op.weight"
	k="10" />
    <hkern g1="eight,eight.op"
	g2="numbersign"
	k="-10" />
    <hkern g1="eight,eight.op"
	g2="seven"
	k="10" />
    <hkern g1="eight,eight.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="eight,eight.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="Thorn"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="Thorn"
	g2="period,ellipsis"
	k="60" />
    <hkern g1="Thorn"
	g2="guilsinglleft.case,guillemotleft.case"
	k="-5" />
    <hkern g1="Thorn"
	g2="guilsinglright.case,guillemotright.case"
	k="-3" />
    <hkern g1="Thorn"
	g2="AE,AEacute"
	k="43" />
    <hkern g1="Thorn"
	g2="J,Jcircumflex"
	k="13" />
    <hkern g1="Thorn"
	g2="comma,quotesinglbase,quotedblbase"
	k="100" />
    <hkern g1="Thorn"
	g2="hyphen,periodcentered,endash,emdash"
	k="-5" />
    <hkern g1="Thorn"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="Thorn"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-26" />
    <hkern g1="Thorn"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="66" />
    <hkern g1="Thorn"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-5" />
    <hkern g1="Thorn"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-3" />
    <hkern g1="Thorn"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="74" />
    <hkern g1="Thorn"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="Thorn"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-3" />
    <hkern g1="Thorn"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-3" />
    <hkern g1="Thorn"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="22" />
    <hkern g1="Thorn"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-2" />
    <hkern g1="Thorn"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-3" />
    <hkern g1="Thorn"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-3" />
    <hkern g1="Thorn"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-3" />
    <hkern g1="V"
	g2="period,ellipsis"
	k="140" />
    <hkern g1="V"
	g2="ae,aeacute"
	k="70" />
    <hkern g1="V"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="V"
	g2="guillemotleft,guilsinglleft"
	k="62" />
    <hkern g1="V"
	g2="guilsinglleft.case,guillemotleft.case"
	k="43" />
    <hkern g1="V"
	g2="guilsinglright.case,guillemotright.case"
	k="15" />
    <hkern g1="V"
	g2="AE,AEacute"
	k="82" />
    <hkern g1="V"
	g2="J,Jcircumflex"
	k="21" />
    <hkern g1="V"
	g2="guillemotright,guilsinglright"
	k="42" />
    <hkern g1="V"
	g2="comma,quotesinglbase,quotedblbase"
	k="160" />
    <hkern g1="V"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="V"
	g2="hyphen,periodcentered,endash,emdash"
	k="70" />
    <hkern g1="V"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="60" />
    <hkern g1="V"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-2" />
    <hkern g1="V"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="V"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="V"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-2" />
    <hkern g1="V"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-2" />
    <hkern g1="V"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="32" />
    <hkern g1="V"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="32" />
    <hkern g1="V"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="72" />
    <hkern g1="V"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="22" />
    <hkern g1="V"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="54" />
    <hkern g1="V"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="50" />
    <hkern g1="V"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="42" />
    <hkern g1="V"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="41" />
    <hkern g1="V"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="V"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="61" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="V"
	k="42" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="71" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-5" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="72" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="2" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="x"
	k="33" />
    <hkern g1="guillemotright,guilsinglright"
	g2="V"
	k="70" />
    <hkern g1="guillemotright,guilsinglright"
	g2="X"
	k="13" />
    <hkern g1="guillemotright,guilsinglright"
	g2="v"
	k="12" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="88" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="92" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="guillemotright,guilsinglright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="12" />
    <hkern g1="asterisk"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="asterisk"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="asterisk"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="3" />
    <hkern g1="asterisk"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="asterisk"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="asterisk"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="43" />
    <hkern g1="asterisk"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="asterisk"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="F"
	g2="period,ellipsis"
	k="130" />
    <hkern g1="F"
	g2="ae,aeacute"
	k="30" />
    <hkern g1="F"
	g2="AE,AEacute"
	k="102" />
    <hkern g1="F"
	g2="J,Jcircumflex"
	k="48" />
    <hkern g1="F"
	g2="comma,quotesinglbase,quotedblbase"
	k="130" />
    <hkern g1="F"
	g2="z,zacute,zdotaccent,zcaron"
	k="28" />
    <hkern g1="F"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-3" />
    <hkern g1="F"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-2" />
    <hkern g1="F"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-6" />
    <hkern g1="F"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="F"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="21" />
    <hkern g1="F"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="63" />
    <hkern g1="F"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-1" />
    <hkern g1="F"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="10" />
    <hkern g1="onesuperior"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="onesuperior"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="two"
	k="20" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="four.op"
	k="110" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="four"
	k="80" />
    <hkern g1="ordfeminine,ordmasculine"
	g2="seven"
	k="-10" />
    <hkern g1="dollar.op,dollar.op.weight"
	g2="three.op"
	k="25" />
    <hkern g1="dollar.op,dollar.op.weight"
	g2="nine.op"
	k="10" />
    <hkern g1="dollar.op,dollar.op.weight"
	g2="one.op"
	k="70" />
    <hkern g1="seven"
	g2="period,ellipsis"
	k="160" />
    <hkern g1="seven"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="seven"
	g2="ordfeminine,ordmasculine"
	k="-20" />
    <hkern g1="seven"
	g2="cent,cent.weight"
	k="22" />
    <hkern g1="seven"
	g2="dollar,dollar.weight"
	k="22" />
    <hkern g1="seven"
	g2="comma,quotesinglbase,quotedblbase"
	k="180" />
    <hkern g1="seven"
	g2="plus,divide,minus"
	k="160" />
    <hkern g1="seven"
	g2="hyphen,periodcentered,endash,emdash"
	k="60" />
    <hkern g1="seven"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="108" />
    <hkern g1="x"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="x"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="x"
	g2="guillemotleft,guilsinglleft"
	k="33" />
    <hkern g1="x"
	g2="z,zacute,zdotaccent,zcaron"
	k="-2" />
    <hkern g1="x"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="x"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-2" />
    <hkern g1="x"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="60" />
    <hkern g1="x"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="2" />
    <hkern g1="x"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="48" />
    <hkern g1="x"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="x"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-1" />
    <hkern g1="x"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="1" />
    <hkern g1="x"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-3" />
    <hkern g1="x"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="x"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="11" />
    <hkern g1="x"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="15" />
    <hkern g1="x"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="22" />
    <hkern g1="X"
	g2="ae,aeacute"
	k="30" />
    <hkern g1="X"
	g2="guillemotleft,guilsinglleft"
	k="22" />
    <hkern g1="X"
	g2="guilsinglleft.case,guillemotleft.case"
	k="92" />
    <hkern g1="X"
	g2="guilsinglright.case,guillemotright.case"
	k="53" />
    <hkern g1="X"
	g2="AE,AEacute"
	k="-2" />
    <hkern g1="X"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="X"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="102" />
    <hkern g1="X"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="40" />
    <hkern g1="X"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="62" />
    <hkern g1="X"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="62" />
    <hkern g1="X"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="X"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="X"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="18" />
    <hkern g1="X"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-2" />
    <hkern g1="X"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="X"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="50" />
    <hkern g1="X"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="X"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="42" />
    <hkern g1="X"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="60" />
    <hkern g1="B"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="B"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="B"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="43" />
    <hkern g1="B"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="28" />
    <hkern g1="B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="18" />
    <hkern g1="B"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="B"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="9" />
    <hkern g1="B"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="B"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="five"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="five"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="five"
	g2="ordfeminine,ordmasculine"
	k="30" />
    <hkern g1="five"
	g2="dollar,dollar.weight"
	k="12" />
    <hkern g1="five"
	g2="comma,quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="five"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="K,uni0136"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="K,uni0136"
	g2="ae,aeacute"
	k="60" />
    <hkern g1="K,uni0136"
	g2="degree"
	k="30" />
    <hkern g1="K,uni0136"
	g2="guillemotleft,guilsinglleft"
	k="42" />
    <hkern g1="K,uni0136"
	g2="guilsinglleft.case,guillemotleft.case"
	k="123" />
    <hkern g1="K,uni0136"
	g2="guilsinglright.case,guillemotright.case"
	k="73" />
    <hkern g1="K,uni0136"
	g2="v"
	k="100" />
    <hkern g1="K,uni0136"
	g2="at.case"
	k="90" />
    <hkern g1="K,uni0136"
	g2="bullet"
	k="100" />
    <hkern g1="K,uni0136"
	g2="bullet.case"
	k="120" />
    <hkern g1="K,uni0136"
	g2="copyright"
	k="70" />
    <hkern g1="K,uni0136"
	g2="registered"
	k="30" />
    <hkern g1="K,uni0136"
	g2="at"
	k="20" />
    <hkern g1="K,uni0136"
	g2="questiondown.case"
	k="43" />
    <hkern g1="K,uni0136"
	g2="germandbls"
	k="12" />
    <hkern g1="K,uni0136"
	g2="asterisk"
	k="18" />
    <hkern g1="K,uni0136"
	g2="hyphen,periodcentered,endash,emdash"
	k="102" />
    <hkern g1="K,uni0136"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="140" />
    <hkern g1="K,uni0136"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="60" />
    <hkern g1="K,uni0136"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="36" />
    <hkern g1="K,uni0136"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="K,uni0136"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="100" />
    <hkern g1="K,uni0136"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="70" />
    <hkern g1="K,uni0136"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="K,uni0136"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="32" />
    <hkern g1="K,uni0136"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="32" />
    <hkern g1="K,uni0136"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="75" />
    <hkern g1="K,uni0136"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="50" />
    <hkern g1="K,uni0136"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="72" />
    <hkern g1="K,uni0136"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="20" />
    <hkern g1="K,uni0136"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="70" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="V"
	k="23" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="X"
	k="70" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="56" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="guilsinglleft.case,guillemotleft.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="10" />
    <hkern g1="period,ellipsis"
	g2="quotedbl,quotesingle"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="three.op"
	k="10" />
    <hkern g1="period,ellipsis"
	g2="nine.op"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="five.op"
	k="30" />
    <hkern g1="period,ellipsis"
	g2="one"
	k="212" />
    <hkern g1="period,ellipsis"
	g2="four"
	k="35" />
    <hkern g1="period,ellipsis"
	g2="one.op"
	k="210" />
    <hkern g1="period,ellipsis"
	g2="V"
	k="140" />
    <hkern g1="period,ellipsis"
	g2="seven.op"
	k="120" />
    <hkern g1="period,ellipsis"
	g2="seven"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="v"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="asterisk"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="zeroslash.op,zero.op"
	k="32" />
    <hkern g1="period,ellipsis"
	g2="eight,eight.op"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-3" />
    <hkern g1="period,ellipsis"
	g2="zero,six,zeroslash,six.op"
	k="65" />
    <hkern g1="period,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="130" />
    <hkern g1="period,ellipsis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="150" />
    <hkern g1="period,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="80" />
    <hkern g1="period,ellipsis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="period,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="period,ellipsis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="period,ellipsis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="50" />
    <hkern g1="period,ellipsis"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="27" />
    <hkern g1="slash"
	g2="period,ellipsis"
	k="130" />
    <hkern g1="slash"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="slash"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="slash"
	g2="hyphen,periodcentered,endash,emdash"
	k="50" />
    <hkern g1="slash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-3" />
    <hkern g1="slash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-3" />
    <hkern g1="slash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="slash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="22" />
    <hkern g1="slash"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="slash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="slash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="60" />
    <hkern g1="slash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="slash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="22" />
    <hkern g1="slash"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="seven.op"
	g2="period,ellipsis"
	k="180" />
    <hkern g1="seven.op"
	g2="zeroslash.op,zero.op"
	k="10" />
    <hkern g1="seven.op"
	g2="comma,quotesinglbase,quotedblbase"
	k="190" />
    <hkern g1="seven.op"
	g2="plus,divide,minus"
	k="98" />
    <hkern g1="seven.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="30" />
    <hkern g1="seven.op"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="30" />
    <hkern g1="P"
	g2="period,ellipsis"
	k="100" />
    <hkern g1="P"
	g2="ae,aeacute"
	k="5" />
    <hkern g1="P"
	g2="guilsinglright.case,guillemotright.case"
	k="-2" />
    <hkern g1="P"
	g2="AE,AEacute"
	k="95" />
    <hkern g1="P"
	g2="J,Jcircumflex"
	k="20" />
    <hkern g1="P"
	g2="guillemotright,guilsinglright"
	k="-3" />
    <hkern g1="P"
	g2="comma,quotesinglbase,quotedblbase"
	k="170" />
    <hkern g1="P"
	g2="hyphen,periodcentered,endash,emdash"
	k="-2" />
    <hkern g1="P"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="P"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-5" />
    <hkern g1="P"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="23" />
    <hkern g1="P"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-6" />
    <hkern g1="P"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-6" />
    <hkern g1="P"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="17" />
    <hkern g1="P"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-3" />
    <hkern g1="P"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-3" />
    <hkern g1="P"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="43" />
    <hkern g1="P"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-2" />
    <hkern g1="P"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-5" />
    <hkern g1="P"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-3" />
    <hkern g1="P"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-2" />
    <hkern g1="cent,cent.weight"
	g2="three"
	k="17" />
    <hkern g1="cent,cent.weight"
	g2="one"
	k="10" />
    <hkern g1="cent,cent.weight"
	g2="seven"
	k="10" />
    <hkern g1="dcaron,lcaron"
	g2="x"
	k="-50" />
    <hkern g1="dcaron,lcaron"
	g2="V"
	k="-136" />
    <hkern g1="dcaron,lcaron"
	g2="X"
	k="-108" />
    <hkern g1="dcaron,lcaron"
	g2="AE,AEacute"
	k="32" />
    <hkern g1="dcaron,lcaron"
	g2="J,Jcircumflex"
	k="-8" />
    <hkern g1="dcaron,lcaron"
	g2="v"
	k="-30" />
    <hkern g1="dcaron,lcaron"
	g2="germandbls"
	k="-62" />
    <hkern g1="dcaron,lcaron"
	g2="asterisk"
	k="-108" />
    <hkern g1="dcaron,lcaron"
	g2="parenright"
	k="-120" />
    <hkern g1="dcaron,lcaron"
	g2="bracketright,braceright"
	k="-140" />
    <hkern g1="dcaron,lcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="-28" />
    <hkern g1="dcaron,lcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-122" />
    <hkern g1="dcaron,lcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-140" />
    <hkern g1="dcaron,lcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-50" />
    <hkern g1="dcaron,lcaron"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-100" />
    <hkern g1="dcaron,lcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-133" />
    <hkern g1="dcaron,lcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-140" />
    <hkern g1="dcaron,lcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-30" />
    <hkern g1="dcaron,lcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-28" />
    <hkern g1="dcaron,lcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="3" />
    <hkern g1="dcaron,lcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="-70" />
    <hkern g1="dcaron,lcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="dcaron,lcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-78" />
    <hkern g1="dcaron,lcaron"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-40" />
    <hkern g1="dcaron,lcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-62" />
    <hkern g1="dcaron,lcaron"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="-66" />
    <hkern g1="dcaron,lcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="8" />
    <hkern g1="dcaron,lcaron"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,uni2116"
	k="-72" />
    <hkern g1="four.op"
	g2="quotedbl,quotesingle"
	k="60" />
    <hkern g1="four.op"
	g2="ordfeminine,ordmasculine"
	k="50" />
    <hkern g1="four.op"
	g2="plus,divide,minus"
	k="40" />
    <hkern g1="four.op"
	g2="hyphen,periodcentered,endash,emdash"
	k="-10" />
    <hkern g1="four.op"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="uni2074"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2074"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="cent.op,cent.op.weight"
	g2="three.op"
	k="20" />
    <hkern g1="cent.op,cent.op.weight"
	g2="one.op"
	k="10" />
    <hkern g1="uni2076"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2076"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="colon,semicolon"
	g2="V"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="seven"
	k="20" />
    <hkern g1="colon,semicolon"
	g2="eight,eight.op"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="100" />
    <hkern g1="colon,semicolon"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="50" />
    <hkern g1="colon,semicolon"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="quotedbl,quotesingle"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="three.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="nine.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="five.op"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="one"
	k="152" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="one.op"
	k="160" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="V"
	k="120" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="X"
	k="20" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="seven.op"
	k="90" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="seven"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="v"
	k="40" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="asterisk"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-3" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="zero,six,zeroslash,six.op"
	k="27" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="130" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="80" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="130" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="70" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="60" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="30" />
    <hkern g1="comma,quotesinglbase,quotedblbase"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="1" />
    <hkern g1="uni2070,uni2079"
	g2="period,ellipsis"
	k="150" />
    <hkern g1="uni2070,uni2079"
	g2="comma,quotesinglbase,quotedblbase"
	k="150" />
    <hkern g1="zero,nine,zeroslash"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="period,ellipsis"
	k="25" />
    <hkern g1="zero,nine,zeroslash"
	g2="three"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="two"
	k="10" />
    <hkern g1="zero,nine,zeroslash"
	g2="one"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="degree"
	k="2" />
    <hkern g1="zero,nine,zeroslash"
	g2="seven"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="zero,nine,zeroslash"
	g2="plus,divide,minus"
	k="20" />
    <hkern g1="zero,nine,zeroslash"
	g2="hyphen,periodcentered,endash,emdash"
	k="-30" />
    <hkern g1="zero,nine,zeroslash"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="zero,nine,zeroslash"
	g2="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	k="20" />
    <hkern g1="plus,divide,minus"
	g2="three.op"
	k="50" />
    <hkern g1="plus,divide,minus"
	g2="five"
	k="17" />
    <hkern g1="plus,divide,minus"
	g2="three"
	k="50" />
    <hkern g1="plus,divide,minus"
	g2="nine.op"
	k="8" />
    <hkern g1="plus,divide,minus"
	g2="five.op"
	k="30" />
    <hkern g1="plus,divide,minus"
	g2="two"
	k="20" />
    <hkern g1="plus,divide,minus"
	g2="one"
	k="180" />
    <hkern g1="plus,divide,minus"
	g2="four.op"
	k="30" />
    <hkern g1="plus,divide,minus"
	g2="one.op"
	k="100" />
    <hkern g1="plus,divide,minus"
	g2="seven.op"
	k="140" />
    <hkern g1="plus,divide,minus"
	g2="seven"
	k="102" />
    <hkern g1="plus,divide,minus"
	g2="zero,six,zeroslash,six.op"
	k="12" />
    <hkern g1="registered"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="registered"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-5" />
    <hkern g1="registered"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-3" />
    <hkern g1="registered"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-2" />
    <hkern g1="registered"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="registered"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="90" />
    <hkern g1="registered"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="three.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="nine.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="five.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="two"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="two.op"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="slash"
	k="150" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="four.op"
	k="160" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="four"
	k="110" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="one.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="V"
	k="-3" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="v"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="zeroslash.op,zero.op"
	k="70" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="comma,quotesinglbase,quotedblbase"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="eight,eight.op"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="z,zacute,zdotaccent,zcaron"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="zero,six,zeroslash,six.op"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-3" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="40" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="30" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="70" />
    <hkern g1="questiondown"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="questiondown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="120" />
    <hkern g1="questiondown"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="questiondown"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="123" />
    <hkern g1="questiondown"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="110" />
    <hkern g1="questiondown"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="40" />
    <hkern g1="questiondown"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="70" />
    <hkern g1="questiondown"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="60" />
    <hkern g1="questiondown"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="30" />
    <hkern g1="questiondown"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="questiondown"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="70" />
    <hkern g1="questiondown"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="52" />
    <hkern g1="questiondown"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="60" />
    <hkern g1="questiondown"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="60" />
    <hkern g1="questiondown"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="50" />
    <hkern g1="questiondown"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="60" />
    <hkern g1="questiondown"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,uni2116"
	k="60" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="period,ellipsis"
	k="112" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="slash"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="V"
	k="32" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="X"
	k="52" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="degree"
	k="-3" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="v"
	k="-2" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="bullet"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="registered"
	k="-2" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="germandbls"
	k="2" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="asterisk"
	k="-3" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="132" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="2" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="trademark"
	k="40" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="61" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-3" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="55" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="22" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-2" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="2" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="33" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-3" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-1" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="11" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="V"
	k="30" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="X"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="J,Jcircumflex"
	k="3" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="bullet"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="bullet.case"
	k="-2" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="registered"
	k="3" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="comma,quotesinglbase,quotedblbase"
	k="40" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-2" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="trademark"
	k="32" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="backslash"
	k="30" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-2" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="29" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="8" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="1" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="9" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-2" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="Euro"
	g2="zero,six,zeroslash,six.op"
	k="10" />
    <hkern g1="degree"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-2" />
    <hkern g1="degree"
	g2="zero,six,zeroslash,six.op"
	k="-18" />
    <hkern g1="degree"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="3" />
    <hkern g1="degree"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-5" />
    <hkern g1="degree"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-3" />
    <hkern g1="degree"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-2" />
    <hkern g1="degree"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="degree"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="90" />
    <hkern g1="degree"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="2" />
    <hkern g1="degree"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="ae,aeacute"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="V"
	k="70" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="numbersign"
	k="-20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="bullet"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-2" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="trademark"
	k="50" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="backslash"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="110" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="77" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="1" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="bullet.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="52" />
    <hkern g1="bullet.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="95" />
    <hkern g1="bullet.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="92" />
    <hkern g1="bullet.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="bullet.case"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="bullet.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="questiondown.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-3" />
    <hkern g1="questiondown.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="70" />
    <hkern g1="questiondown.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="questiondown.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="questiondown.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-20" />
    <hkern g1="questiondown.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="questiondown.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="period,ellipsis"
	k="-3" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="x"
	k="-2" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="numbersign"
	k="-3" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="J,Jcircumflex"
	k="-12" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="v"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="bullet.case"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="-2" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="60" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="question"
	k="-5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-2" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="-2" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="f,longs,f_f,f.1"
	g2="quotedbl,quotesingle"
	k="-18" />
    <hkern g1="f,longs,f_f,f.1"
	g2="period,ellipsis"
	k="92" />
    <hkern g1="f,longs,f_f,f.1"
	g2="ae,aeacute"
	k="10" />
    <hkern g1="f,longs,f_f,f.1"
	g2="slash"
	k="28" />
    <hkern g1="f,longs,f_f,f.1"
	g2="x"
	k="-2" />
    <hkern g1="f,longs,f_f,f.1"
	g2="V"
	k="-44" />
    <hkern g1="f,longs,f_f,f.1"
	g2="degree"
	k="-47" />
    <hkern g1="f,longs,f_f,f.1"
	g2="guillemotleft,guilsinglleft"
	k="13" />
    <hkern g1="f,longs,f_f,f.1"
	g2="numbersign"
	k="20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="v"
	k="-3" />
    <hkern g1="f,longs,f_f,f.1"
	g2="bullet"
	k="20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="registered"
	k="-54" />
    <hkern g1="f,longs,f_f,f.1"
	g2="asterisk"
	k="-45" />
    <hkern g1="f,longs,f_f,f.1"
	g2="comma,quotesinglbase,quotedblbase"
	k="92" />
    <hkern g1="f,longs,f_f,f.1"
	g2="z,zacute,zdotaccent,zcaron"
	k="-1" />
    <hkern g1="f,longs,f_f,f.1"
	g2="hyphen,periodcentered,endash,emdash"
	k="20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="trademark"
	k="-59" />
    <hkern g1="f,longs,f_f,f.1"
	g2="backslash"
	k="-89" />
    <hkern g1="f,longs,f_f,f.1"
	g2="question"
	k="-45" />
    <hkern g1="f,longs,f_f,f.1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-50" />
    <hkern g1="f,longs,f_f,f.1"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-15" />
    <hkern g1="f,longs,f_f,f.1"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-37" />
    <hkern g1="f,longs,f_f,f.1"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-50" />
    <hkern g1="f,longs,f_f,f.1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-35" />
    <hkern g1="f,longs,f_f,f.1"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-3" />
    <hkern g1="f,longs,f_f,f.1"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-2" />
    <hkern g1="f,longs,f_f,f.1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="33" />
    <hkern g1="f,longs,f_f,f.1"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-15" />
    <hkern g1="f,longs,f_f,f.1"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-20" />
    <hkern g1="f,longs,f_f,f.1"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="1" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="three.op"
	k="60" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="three"
	k="25" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="nine.op"
	k="-10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="one"
	k="170" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="four"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="x"
	k="50" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="one.op"
	k="28" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="V"
	k="70" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="X"
	k="50" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="seven.op"
	k="100" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="seven"
	k="70" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="v"
	k="10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="zeroslash.op,zero.op"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="eight,eight.op"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="zero,six,zeroslash,six.op"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="96" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="90" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="8" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-2" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-20" />
    <hkern g1="hyphen,periodcentered,endash,emdash"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-3" />
    <hkern g1="numbersign"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="numbersign"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-2" />
    <hkern g1="numbersign"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-3" />
    <hkern g1="numbersign"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="10" />
    <hkern g1="numbersign"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="40" />
    <hkern g1="numbersign"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="85" />
    <hkern g1="numbersign"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="numbersign"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="32" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="V"
	k="70" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="X"
	k="88" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="115" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="100" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="28" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-2" />
    <hkern g1="hyphen.case,endash.case,emdash.case,periodcentered.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-3" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="ae,aeacute"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="slash"
	k="28" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="x"
	k="-1" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="V"
	k="32" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="X"
	k="22" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="degree"
	k="-3" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="guillemotleft,guilsinglleft"
	k="12" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="numbersign"
	k="5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="v"
	k="-3" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="bullet"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="at"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="100" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="trademark"
	k="22" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="backslash"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="51" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-1" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-2" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-2" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="21" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="13" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-2" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="1" />
    <hkern g1="at.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="90" />
    <hkern g1="at.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="40" />
    <hkern g1="at.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="at.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="at.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-2" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="period,ellipsis"
	k="133" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="ae,aeacute"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="slash"
	k="140" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="x"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="V"
	k="-2" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="X"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="degree"
	k="-5" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guillemotleft,guilsinglleft"
	k="75" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="numbersign"
	k="78" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guilsinglleft.case,guillemotleft.case"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="AE,AEacute"
	k="150" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="J,Jcircumflex"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guillemotright,guilsinglright"
	k="72" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="v"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="at.case"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bullet"
	k="130" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bullet.case"
	k="90" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="copyright"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="registered"
	k="-3" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="at"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="questiondown.case"
	k="34" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="germandbls"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="comma,quotesinglbase,quotedblbase"
	k="142" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="bracketright,braceright"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="z,zacute,zdotaccent,zcaron"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="hyphen,periodcentered,endash,emdash"
	k="90" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-2" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="trademark"
	k="-3" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="backslash"
	k="-3" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="question"
	k="-8" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-5" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-2" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="questiondown"
	k="135" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="exclamdown"
	k="45" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="82" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="72" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="40" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="37" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="80" />
    <hkern g1="question"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-5" />
    <hkern g1="question"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="55" />
    <hkern g1="exclamdown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="93" />
    <hkern g1="exclamdown"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="70" />
    <hkern g1="exclamdown"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="34" />
    <hkern g1="exclamdown"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="exclamdown"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="ae,aeacute"
	k="40" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="x"
	k="-2" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="V"
	k="70" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="bullet"
	k="50" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="copyright"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="hyphen,periodcentered,endash,emdash"
	k="70" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="trademark"
	k="30" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="backslash"
	k="40" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="112" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="75" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="11" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="27" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-2" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="29" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="19" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="21" />
    <hkern g1="k,uni0137,kgreenlandic,f_k,f_f_k"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="41" />
    <hkern g1="exclamdown.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="period,ellipsis"
	k="113" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ae,aeacute"
	k="130" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="colon,semicolon"
	k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="slash"
	k="95" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="x"
	k="51" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="V"
	k="-2" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotleft,guilsinglleft"
	k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="numbersign"
	k="85" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guilsinglleft.case,guillemotleft.case"
	k="66" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guilsinglright.case,guillemotright.case"
	k="14" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="AE,AEacute"
	k="106" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J,Jcircumflex"
	k="23" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotright,guilsinglright"
	k="54" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="v"
	k="34" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at.case"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="bullet"
	k="118" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="bullet.case"
	k="56" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="copyright"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at"
	k="92" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="germandbls"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="asterisk"
	k="3" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="comma,quotesinglbase,quotedblbase"
	k="113" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="83" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen,periodcentered,endash,emdash"
	k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="backslash"
	k="-3" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-6" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="56" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="16" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="34" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="34" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="questiondown"
	k="136" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="exclamdown"
	k="93" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="98" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="26" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="66" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="73" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="70" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="48" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="33" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="3" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="81" />
    <hkern g1="bullet"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="136" />
    <hkern g1="bullet"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="130" />
    <hkern g1="bullet"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="bullet"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="bullet"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="bullet"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="bullet"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="quotedbl,quotesingle"
	k="170" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="period,ellipsis"
	k="-3" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="slash"
	k="-3" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="V"
	k="98" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="X"
	k="-5" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="degree"
	k="130" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="numbersign"
	k="-6" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="guilsinglleft.case,guillemotleft.case"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="AE,AEacute"
	k="-6" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="J,Jcircumflex"
	k="-13" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="v"
	k="50" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="at.case"
	k="44" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="bullet"
	k="84" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="bullet.case"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="copyright"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="registered"
	k="150" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="asterisk"
	k="153" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="comma,quotesinglbase,quotedblbase"
	k="-5" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="z,zacute,zdotaccent,zcaron"
	k="-4" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="hyphen,periodcentered,endash,emdash"
	k="46" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-5" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="117" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="trademark"
	k="180" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="backslash"
	k="110" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="question"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="142" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="100" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="178" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="65" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="45" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="-3" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-3" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="38" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="slash"
	k="42" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="x"
	k="1" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="V"
	k="22" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="X"
	k="32" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="J,Jcircumflex"
	k="2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="guillemotright,guilsinglright"
	k="-2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="at.case"
	k="-2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="bullet"
	k="-2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="asterisk"
	k="-3" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="comma,quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="hyphen,periodcentered,endash,emdash"
	k="-3" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-3" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="trademark"
	k="30" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="backslash"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="45" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-1" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-3" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="37" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="questiondown"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="3" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-2" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="period,ellipsis"
	k="80" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ae,aeacute"
	k="45" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="colon,semicolon"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="slash"
	k="43" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="V"
	k="-2" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="numbersign"
	k="43" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guilsinglleft.case,guillemotleft.case"
	k="32" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guilsinglright.case,guillemotright.case"
	k="-4" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="AE,AEacute"
	k="82" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex"
	k="6" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotright,guilsinglright"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="at.case"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bullet.case"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="copyright"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="registered"
	k="-2" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="at"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="germandbls"
	k="3" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="90" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,periodcentered,endash,emdash"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="28" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="trademark"
	k="-3" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-2" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="questiondown"
	k="80" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="exclamdown"
	k="8" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="22" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="32" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,ntilde,dotlessi,kgreenlandic,nacute,uni0146,ncaron,eng,uni0157,uni0237"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="1" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="30" />
    <hkern g1="backslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="118" />
    <hkern g1="backslash"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="40" />
    <hkern g1="backslash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="140" />
    <hkern g1="backslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="72" />
    <hkern g1="backslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="23" />
    <hkern g1="backslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="backslash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="backslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="backslash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="backslash"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="40" />
    <hkern g1="backslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="70" />
    <hkern g1="backslash"
	g2="b,h,i,j,k,l,igrave,iacute,icircumflex,idieresis,thorn,hcircumflex,hbar,itilde,imacron,ibreve,iogonek,ij,jcircumflex,uni0137,lacute,uni013C,lcaron,ldot,lslash,racute,rcaron,i.trk"
	k="32" />
    <hkern g1="backslash"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="period,ellipsis"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="ae,aeacute"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="slash"
	k="6" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="V"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="X"
	k="12" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="degree"
	k="-2" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="v"
	k="-1" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="bullet"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="2" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="backslash"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="60" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-1" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="50" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-2" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="1" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-12" />
    <hkern g1="copyright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="90" />
    <hkern g1="copyright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="copyright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="copyright"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="copyright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="32" />
    <hkern g1="at"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="113" />
    <hkern g1="at"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="at"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="at"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="period,ellipsis"
	k="-2" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="ae,aeacute"
	k="5" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="V"
	k="50" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="numbersign"
	k="-3" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="guillemotright,guilsinglright"
	k="-2" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="bullet"
	k="27" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="hyphen,periodcentered,endash,emdash"
	k="25" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-3" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="backslash"
	k="20" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="78" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-1" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="65" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="11" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-2" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="1" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="9" />
    <hkern g1="bracketleft,braceleft"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="ae,aeacute"
	k="15" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="x"
	k="2" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="V"
	k="67" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="X"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="degree"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="v"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="germandbls"
	k="1" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="asterisk"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="hyphen,periodcentered,endash,emdash"
	k="-2" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="trademark"
	k="50" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="backslash"
	k="50" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="105" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="2" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="21" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="11" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="2" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="1" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="2" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="three.op"
	k="30" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="one"
	k="110" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="one.op"
	k="50" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="seven.op"
	k="50" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="seven"
	k="130" />
    <hkern g1="equal,logicalnot,plusminus,multiply,approxequal,notequal"
	g2="zero,six,zeroslash,six.op"
	k="20" />
    <hkern g1="trademark"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="trademark"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="80" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="period,ellipsis"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="slash"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="x"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="V"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="X"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="J,Jcircumflex"
	k="2" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="v"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="bullet.case"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="registered"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="asterisk"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="comma,quotesinglbase,quotedblbase"
	k="50" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="hyphen,periodcentered,endash,emdash"
	k="-2" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="2" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="trademark"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="backslash"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="50" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="46" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="11" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="slash"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="V"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="X"
	k="31" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="degree"
	k="-3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="guillemotleft,guilsinglleft"
	k="-5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="numbersign"
	k="-3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="registered"
	k="-3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="asterisk"
	k="-3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="comma,quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="hyphen,periodcentered,endash,emdash"
	k="-6" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="trademark"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="backslash"
	k="22" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="41" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-2" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="35" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="1" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,Ccedilla.1"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-2" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="slash"
	k="-2" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="V"
	k="52" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="numbersign"
	k="-3" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="registered"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="comma,quotesinglbase,quotedblbase"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="z,zacute,zdotaccent,zcaron"
	k="-1" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="trademark"
	k="20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="71" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-2" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="47" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="31" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="-1" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-3" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="1" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="-2" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-1" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron,ccedilla.1"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="quotedbl,quotesingle"
	k="80" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="x"
	k="-3" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="V"
	k="72" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="X"
	k="-2" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="degree"
	k="120" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="numbersign"
	k="-3" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guilsinglleft.case,guillemotleft.case"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="guilsinglright.case,guillemotright.case"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="AE,AEacute"
	k="-5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="J,Jcircumflex"
	k="-10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="v"
	k="51" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="at.case"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="bullet"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="bullet.case"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="copyright"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="registered"
	k="110" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="asterisk"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="67" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="trademark"
	k="120" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="backslash"
	k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="question"
	k="50" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="104" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="109" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="42" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="42" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="21" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-3" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="31" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="21" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="35" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="x"
	k="-1" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="numbersign"
	k="-3" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="AE,AEacute"
	k="-3" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="J,Jcircumflex"
	k="-2" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="v"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="at.case"
	k="17" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="-3" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-3" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-3" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="f,longs,f_f,f_f_i,f_f_l,f.1,f_b,f_h,f_j,f_k,fl,f_f_b,f_f_h,f_f_j,f_f_k"
	k="8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="1" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="quotedbl,quotesingle"
	k="60" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="period,ellipsis"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="slash"
	k="-2" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="x"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="V"
	k="80" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="X"
	k="-2" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="degree"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="numbersign"
	k="-5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="guillemotright,guilsinglright"
	k="-2" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="v"
	k="2" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="registered"
	k="60" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="asterisk"
	k="30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="hyphen,periodcentered,endash,emdash"
	k="-3" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="trademark"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="backslash"
	k="70" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="100" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="78" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="2" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="2" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-3" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-1" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-2" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="V"
	k="59" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="degree"
	k="30" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="v"
	k="11" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="registered"
	k="50" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="asterisk"
	k="40" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-2" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="trademark"
	k="80" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="backslash"
	k="40" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="106" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="81" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="22" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="11" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng,f_h,f_f_h"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="period,ellipsis"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="slash"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="x"
	k="21" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="X"
	k="20" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="J,Jcircumflex"
	k="3" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="guillemotright,guilsinglright"
	k="2" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="hyphen,periodcentered,endash,emdash"
	k="-2" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-2" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-1" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="1" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="1" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-1" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="1" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="period,ellipsis"
	k="50" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="slash"
	k="62" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="x"
	k="22" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="V"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="X"
	k="42" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="degree"
	k="2" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="guilsinglright.case,guillemotright.case"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="J,Jcircumflex"
	k="2" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="v"
	k="1" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="at.case"
	k="-2" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="asterisk"
	k="-10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="comma,quotesinglbase,quotedblbase"
	k="60" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="1" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="hyphen,periodcentered,endash,emdash"
	k="-20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="2" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="hyphen.case,endash.case,emdash.case,periodcentered.case"
	k="-3" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="trademark"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="backslash"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="46" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-1" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="57" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="1" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="1" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-2" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-1" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="period,ellipsis"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="x"
	k="21" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="V"
	k="72" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="X"
	k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="degree"
	k="80" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="numbersign"
	k="-3" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="v"
	k="21" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="registered"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="asterisk"
	k="50" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="comma,quotesinglbase,quotedblbase"
	k="30" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="hyphen,periodcentered,endash,emdash"
	k="-3" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="2" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="trademark"
	k="70" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="backslash"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="94" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="napostrophe,quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="21" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="s,sacute,scircumflex,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,germandbls.cap"
	k="1" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="1" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Ccedilla.1"
	k="-1" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute,f_b,f_f_b"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,uni0123,omacron,obreve,ohungarumlaut,oe,aringacute,oslashacute,ccedilla.1"
	k="-1" />
    <hkern g1="d,i,j,l,igrave,iacute,icircumflex,idieresis,dcroat,itilde,imacron,ibreve,iogonek,ij,jcircumflex,lacute,uni013C,lslash,f_f_i,f_f_l,i.trk,f_j,fl,f_f_j"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="3" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="V"
	k="40" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-2" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="trademark"
	k="30" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="backslash"
	k="10" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="81" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,uni0123,dotlessi,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute,uni0237"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
  </font>
</defs></svg>

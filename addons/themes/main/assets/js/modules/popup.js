const targetTime = new Date('2025-06-23T00:00:00+07:00');
const popupOverlay = document.getElementById('popup-overlay');
const btnClose = document.getElementById('popup-close');

function updateTimer() {
    const now = new Date();
    let distance = targetTime - now;

    if (distance < 0) distance = 0;

    const days = Math.floor(distance / (1000 * 60 * 60 * 24));
    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((distance % (1000 * 60)) / 1000);

    document.getElementById('timer-days').textContent = days;
    document.getElementById('timer-hours').textContent = String(hours).padStart(2, '0');
    document.getElementById('timer-minutes').textContent = String(minutes).padStart(2, '0');
    document.getElementById('timer-seconds').textContent = String(seconds).padStart(2, '0');

    if (distance <= 0) {
        clearInterval(timerInterval);
        if (popupOverlay) popupOverlay.style.display = 'none';
    }
}

const timerInterval = setInterval(updateTimer, 1000);
updateTimer();

if (btnClose) {
    btnClose.onclick = function() {
        if (popupOverlay) popupOverlay.style.display = 'none';
        clearInterval(timerInterval);
    };
}

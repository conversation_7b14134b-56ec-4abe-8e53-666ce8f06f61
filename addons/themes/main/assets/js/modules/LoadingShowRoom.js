export class LoadingShowRoom {
    constructor() {
        this.events();
    }
    events() {
        this.loadingShowRoom();
    }
    loadingShowRoom() {
        $(document).ready(function () {
            $(document).on("change", "#matp_id", function () {
                var url = $(this).data("url");
                $.ajax({
                    headers: {
                        "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
                    },
                    url: url,
                    data: {
                        matp_id: $(this).val(),
                    },
                    method: "GET",
                    dataType: "json",
                    success: function (data) {
                        $('#change-show-room').html(data.data.template);
                        $('.change-show-room').html(data.data.template);
                        if(data.data.type){
                            $(".btn-booking").removeClass('button-booking-disable');
                            $(".btn-booking").removeAttr("disabled");
                        } else{
                            $(".btn-booking").addClass('button-booking-disable');
                            $(".btn-booking").attr("disabled", "disabled");
                        }
                    },
                    error: function (xhr, thrownError) { },
                });
            });

            // show_room_id

            $(document).on("change", "#matp_id_test", function () {
                var url = $(this).data("url");
                $.ajax({
                    headers: {
                        "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
                    },
                    url: url,
                    data: {
                        matp_id: $(this).val(),
                    },
                    method: "GET",
                    dataType: "json",
                    success: function (data) {
                        $('#change-show-room').html(data.data.template);
                        $('.change-show-room').html(data.data.template);

                        // button submit mổ khi check
                        if(data.data.type){
                            console.log();
                            if ($('[name="car_id"]').val() !== 'Chọn phiên bản') {
                                $(".btn-booking").removeClass('button-booking-disable').removeAttr("disabled");
                            } else{
                                $(".btn-booking").addClass('button-booking-disable').attr("disabled", "disabled");
                            }
                        } else{
                            $(".btn-booking").addClass('button-booking-disable').attr("disabled", "disabled");
                        }
                    },
                    error: function (xhr, thrownError) { },
                });
            });


            $(document).on("change", ".show_room_id", function () {
                var url = $(this).data("url");
                $.ajax({
                    headers: {
                        "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
                    },
                    url: url,
                    data: {
                        show_room_id: $(this).val(),
                    },
                    method: "GET",
                    dataType: "json",
                    success: function (data) {
                        // $('.agency-list.checkbox-custom').append(data.data.template);
                        $('#change-google-map').html(data.data.templateGoogleMap);
                    },
                    error: function (xhr, thrownError) { },
                });
            });
            function formatPrice(number) {
                return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            }
            $(document).on("change", "#change_car", function () {
                var url = $(this).data("url");
                $.ajax({
                    headers: {
                        "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
                    },
                    url: url,
                    data: {
                        car_id: $(this).val(),
                        matp_id : $('#matp_id').val()
                    },
                    method: "GET",
                    dataType: "json",
                    success: function (data) {
                        if(data.type === 'success'){

                            $('.price_car').text(formatPrice(data.price) + ' VNĐ');
                            $('input#price_car').val(data.price);

                            var  PhanTramPhiTruocBa = data.phi_truoc_ba;
                            var  PhiDangKyBienSo =data.phi_dang_ky_bien_so;
                            var PhiDangKiem =data.phi_dang_kiem_xe;
                            var priceCar =   $('input#price_car').val();
                            var PhiTruocBa = (priceCar * PhanTramPhiTruocBa) / 100;

                            var totalPrice = parseFloat(PhiTruocBa + Number(priceCar) +PhiDangKiem + PhiDangKyBienSo);
                            // var formattedPriceCar = formatNumber(priceCar); //
                            var formattedPriceLicensePlate = formatNumber(PhiTruocBa); // phí trước bạ
                            var formattedPriceEntry = formatNumber(PhiDangKyBienSo);
                            var formattedPriceInspection = formatNumber(PhiDangKiem);


                            var formattedTotalPrice = formatNumber(totalPrice);
                            $('.price_license_plate').text(formattedPriceLicensePlate + ' VNĐ');
                            $('.price_entry').text(formattedPriceEntry + ' VNĐ');
                            $('.price_inspection').text(formattedPriceInspection + ' VNĐ');
                            $('.total_price').text(formattedTotalPrice + ' VNĐ');

                            $("input[name='price_license_plate']").val(PhiTruocBa);
                            $("input[name='price_entry']").val(PhiDangKyBienSo);
                            $("input[name='price_inspection']").val(PhiDangKiem);
                            $("input[name='total_price']").val(totalPrice);
                        } else{
                            $('.price_car').text(formatPrice(data.data.price) + ' VNĐ');
                            $('input#price_car').val(data.data.price);
                        }
                    },
                    error: function (xhr, thrownError) { },
                });
            });


             $(document).on("change", "#matp_id", function () {
                $.ajax({
                    headers: {
                        "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
                    },
                    url: carPriceUrl,
                    data: {
                        matp_id: $(this).val(),
                    },
                    method: "GET",
                    dataType: "json",
                    success: function (data) {
                        if(data.type === 'success'){
                            var  PhanTramPhiTruocBa = data.phi_truoc_ba;

                            var  PhiDangKyBienSo =data.phi_dang_ky_bien_so;
                            var PhiDangKiem =data.phi_dang_kiem_xe;
                            var priceCar =   $('input#price_car').val();
                            var PhiTruocBa = (priceCar * PhanTramPhiTruocBa) / 100;

                            var totalPrice = parseFloat(PhiTruocBa + Number(priceCar) +PhiDangKiem + PhiDangKyBienSo);
                            var formattedPriceLicensePlate = formatNumber(PhiTruocBa); // phí trước bạ
                            var formattedPriceEntry = formatNumber(PhiDangKyBienSo);
                            var formattedPriceInspection = formatNumber(PhiDangKiem);


                            var formattedTotalPrice = formatNumber(totalPrice);
                            $('.price_license_plate').text(formattedPriceLicensePlate + ' VNĐ');
                            $('.price_entry').text(formattedPriceEntry + ' VNĐ');
                            $('.price_inspection').text(formattedPriceInspection + ' VNĐ');
                            $('.total_price').text(formattedTotalPrice + ' VNĐ');

                            $("input[name='price_license_plate']").val(PhiTruocBa);
                            $("input[name='price_entry']").val(PhiDangKyBienSo);
                            $("input[name='price_inspection']").val(PhiDangKiem);
                            $("input[name='total_price']").val(totalPrice);
                        }
                    },
                    error: function (xhr, thrownError) { },
                });

            });

            $(document).on("change", "#matp_id", function () {
                $.ajax({
                    headers: {
                        "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
                    },
                    url: carPriceUrl,
                    data: {
                        matp_id: $(this).val(),
                    },
                    method: "GET",
                    dataType: "json",
                    success: function (data) {
                        if(data.type === 'success'){
                            var  PhanTramPhiTruocBa = data.phi_truoc_ba;

                            var  PhiDangKyBienSo =data.phi_dang_ky_bien_so;
                            var PhiDangKiem =data.phi_dang_kiem_xe;
                            var priceCar =   $('input#price_car').val();
                            var PhiTruocBa = (priceCar * PhanTramPhiTruocBa) / 100;

                            var totalPrice = parseFloat(PhiTruocBa + Number(priceCar) +PhiDangKiem + PhiDangKyBienSo);

                            // var formattedPriceCar = formatNumber(priceCar); //
                            var formattedPriceLicensePlate = formatNumber(PhiTruocBa); // phí trước bạ
                            var formattedPriceEntry = formatNumber(PhiDangKyBienSo);
                            var formattedPriceInspection = formatNumber(PhiDangKiem);


                            var formattedTotalPrice = formatNumber(totalPrice);
                            $('.price_license_plate').text(formattedPriceLicensePlate + ' VNĐ');
                            $('.price_entry').text(formattedPriceEntry + ' VNĐ');
                            $('.price_inspection').text(formattedPriceInspection + ' VNĐ');
                            $('.total_price').text(formattedTotalPrice + ' VNĐ');

                            $("input[name='price_license_plate']").val(PhiTruocBa);
                            $("input[name='price_entry']").val(PhiDangKyBienSo);
                            $("input[name='price_inspection']").val(PhiDangKiem);
                            $("input[name='total_price']").val(totalPrice);
                        }
                    },
                    error: function (xhr, thrownError) { },
                });

            });
            function formatNumber(number) {
                return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            }

            $(document).on('click', '.close-popup', function (){
                $('.fixed-bg').hide();
                $('.animated-modal-tk').hide();
                $('.camon').hide();
                $('body').removeClass('active-popup');
            });

            $(document).on('click', '.active-popup', function (){
                $('.fixed-bg').hide();
                $('.animated-modal-tk').hide();
                $('.camon').hide();
                $('body').removeClass('active-popup');
            });

            $('[name="car_id"]').on('change', function() {
                if ($(this).val() !== 'Chọn phiên bản') {
                    if (!blank($('.show_room_id').val())) {
                        console.log('ok next');
                        $(".btn-booking").removeClass('button-booking-disable').removeAttr("disabled");
                    } else {
                        $(".btn-booking").addClass('button-booking-disable').attr("disabled", "disabled");
                    }
                } else {
                    $(".btn-booking").addClass('button-booking-disable').attr("disabled", "disabled");
                }
            });

            function blank(value) {
                return value == null || value.trim() === '';
            }

        });
    }

}

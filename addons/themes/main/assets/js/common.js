import Home from "./modules/home";
import SwiperSlide from "./modules/swiper";
import Equipment from "./modules/section-equipment";
import Section4 from "./modules/section4";
import Popup from "./modules/popup";
import { LoadingShowRoom } from "./modules/LoadingShowRoom";

(() => {
    if (!!document.querySelector(".container-custom")) {
        const init = () =>
            document.documentElement.style.setProperty(
                "--container-padding",
                `${
                    (document.querySelector("body").offsetWidth -
                        document.querySelector(".container-custom")
                            .offsetWidth) /
                    2
                }px`,
            );
        init();
        window.addEventListener("resize", init);
    }
})();
$(document).ready(function () {
    new Home();
    new SwiperSlide();
    new Equipment();
    new Section4();
    new LoadingShowRoom();
    new Popup();
});
$(document).on("click", '.menu__wapper a[href^="#"]', function (event) {
    event.preventDefault();
    $("html, body").animate(
        {
            scrollTop: $($.attr(this, "href")).offset().top,
        },
        500,
    );
});

$("a.scroll-top").click(function (event) {
    event.preventDefault();
    $("html, body").animate({ scrollTop: 0 }, 500);
});

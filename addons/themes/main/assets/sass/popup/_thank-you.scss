._thank-you {
    .modal_thank-you {
        display: flex;
        justify-content: center;
        align-items: center;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.7);

    }

    .modal-content {
        width: 1188px;
        height: 816px;
        position: relative;
        background: url('../images/popup/bg2.jpg') center center/cover no-repeat;
        border-radius: 28px;
        padding: 48px 36px 32px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        max-width: 95vw;
        text-align: center;
        overflow: hidden;
        @include maxWidth(991) {
           height: 630px;
        }
        .close {
            position: absolute;
            top: 24px;
            right: 24px;
            font-size: 36px;
            color: $color-white;
            opacity: 0.7;
            cursor: pointer;
            z-index: 2;
            transition: opacity 0.2s;

            &:hover {
                opacity: 1;
            }
        }

        .logo {
            margin-bottom: 20px;

            img {
                width: 310px;
                height: 227px;
                display: block;
                margin: 0 auto;
                @include maxWidth(991) {
                    width: 190px;
                    height: unset;
                }
            }
        }

        .congrats {
            font-size: 2.5rem;
            font-weight: bold;
            color: $_gold;
            letter-spacing: 2px;
            margin-bottom: 16px;
        }

        .main-desc {
            font-size: 18px;
            font-family: $VN-PeugeotNew-Bold;
            color: $_light-blue;
            margin-bottom: 6px;
            @include maxWidth(991) {
                font-size: 14px;
            }
        }

        .sub-desc {
            font-family: $VN-PeugeotNew-Regular;
            font-size: 14px;
            color: $_light-blue;
            margin-bottom: 28px;
            @include maxWidth(991) {
                font-size: 10px;
            }
        }

        .order-label {
            font-size: 18px;
            font-family: $VN-PeugeotNew-Regular;
            color: $color-white;
            margin-bottom: 20px;
        }

        .order-box {
            .order-P {
                width: max-content;
                margin: auto;
                position: relative;
                @include maxWidth(991) {
                    width : unset;
                    img{
                        width: 100%;
                    }
                }
            }


            .order-left,
            .order-center,
            .order-right {
                display: flex;
                align-items: center;
                justify-content: center;

            }

            .order-left {
                background: linear-gradient(90deg, #e5e5e5 80%, #c5c5c5 100%);
                color: #232323;
                padding: 18px 14px 12px 18px;
                flex-direction: column;
                min-width: 140px;
                text-align: left;
                justify-content: center;

                .years {
                    font-size: 2.2rem;
                    font-weight: bold;
                    letter-spacing: 2px;
                    margin-bottom: 2px;
                }

                .label {
                    font-size: 1.1rem;
                    font-weight: 500;
                    letter-spacing: 1px;
                }
            }

            .order-center {
                background: linear-gradient(90deg, #e5e5e5 80%, #c5c5c5 100%);
                min-width: 70px;
                padding: 0 10px;

                img {
                    width: 44px;
                    height: auto;
                    display: block;
                }
            }

            .order-right {
                position: absolute;
                right: 30px;
                top: 50%;
                transform: translateY(-50%);
                font-family: $VN-PeugeotNew-Bold;
                font-style: normal;
                font-weight: 700;
                font-size: 52px;
                line-height: 70px;
                text-align: center;
                background: linear-gradient(180deg, #FFFFFF 0%, #CFCFCF 45.38%, #B3B3B3 84.29%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                text-fill-color: transparent;
                @include maxWidth(991) {
                    right: 5%;
                }
                @include maxWidth(481) {
                    left: 32%;
                }
            }
        }

        .more-info {
            margin-bottom: 20px;
            margin-top: 20px;
            font-size: 15px;
            font-family: $VN-PeugeotNew-Bold;
            color: $color-white;
            @include maxWidth(991) {
                font-size: 10px;
            }
        }

        .cta-btn {
            display: inline-block;
            background: linear-gradient(90deg, #0074E8 0%, #0454A4 100%);
            color: #fff;
            font-size: 14px;
            border: none;
            font-family: $VN-PeugeotNew-Bold;
            border-radius: 8px;
            padding: 16px 80px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            transition: background 0.2s;
            text-decoration: none;
            width: 50%;
            margin: 0 auto 20px auto;
            i {
                margin-left: 12px;
            }
            @include maxWidth(991) {
                width: 100%;
            }

            &:hover {
                background: linear-gradient(90deg, $_secondary 60%, $_primary 100%);
            }
        }

        .socials {
            margin-top: 10px;
            display: flex;
            justify-content: center;
            gap: 32px;

            a {
                color: #b0b8c1;
                font-size: 2rem;
                transition: color 0.2s;

                &:hover {
                    color: $_primary;
                }
            }
        }

        .modal-bgcar {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            top: 0;
            z-index: 0;
            opacity: 0.55;
            background: url('../images/popup/bg2.jpg') no-repeat center bottom/cover;
            filter: grayscale(0.2) blur(0.5px) brightness(0.85);
            pointer-events: none;
        }
    }

    @media (max-width: 991px) {
        .modal-content {
            padding: 30px 2vw 18px;
        }

        .order-box {
        }

        .cta-btn {
            padding: 16px 18vw;
        }
    }

    @media (max-width: 480px) {
        .order-box {
            flex-direction: column;
            min-width: 0;
        }

        .order-left,
        .order-center,
        .order-right {
            min-width: 0;
            width: 100%;
            justify-content: center;
        }

        .cta-btn {
            padding: 14px 4vw;
            font-size: 1rem;
        }
    }
}

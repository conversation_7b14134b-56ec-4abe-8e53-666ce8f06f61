.register-content{
    background-image:url('../images/comingsoon/re.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    .register-form {
        max-width: 588px;
        margin: 0 40px;
        padding: 30px;
        @include maxWidth(991) {
            margin: 0 0px;
            padding: 20px;
        }
        border-radius: 12px;
        //position: absolute;
        //top: 50%;
        //transform: translateY(-50%);
        //left: 60px;
        h1 {
            font-size: 25px;
            margin-bottom: 0.5rem;
            letter-spacing: 1px;
            font-family: $VN-PeugeotNew-Bold;
            align-items: center;
            text-align: center;
            text-transform: uppercase;
            background: linear-gradient(142.06deg, #C8A16E -51.7%, #754C24 -0.35%, #FFDEA4 41.8%, #8E653A 78.54%, #8B6439 123.96%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;

        }
        h2 {
            font-size: 23px;
            font-family: $VN-PeugeotNew-Bold;
            margin-bottom: 30px;
            letter-spacing: 1px;
            color: white;
            text-align: center;
        }
        .desc {
            font-family: $VN-PeugeotNew-Regular;
            margin-bottom: 0.5rem;
            color: #ccc;
            font-size: 13px;
            text-align: center;
        }
        .desc span {
            background: linear-gradient(142.06deg, #C8A16E -51.7%, #754C24 -0.35%, #FFDEA4 41.8%, #8E653A 78.54%, #8B6439 123.96%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-family: $VN-PeugeotNew-Bold;
            font-size: 17px;
        }
        .desc2 {
            font-family: $VN-PeugeotNew-Bold;
            margin-bottom: 30px;
            color: #ccc;
            font-size: 13px;
            text-align: center;
        }
        form input, form select {
            width: 100%;
            padding: 15px 10px;
            margin-bottom: 18px;
            //border: solid 1px #787B80;
            background: #222;
            color: #fff;
            outline: none;
            box-sizing: border-box;
            border: 1px solid transparent;
            background: linear-gradient(to right, #393938, #393938) padding-box, linear-gradient(to right, #8F673B, #FFDEA4) border-box;
        }
        .re-name{
            border: solid 1px #787b80;
        }
        form input::placeholder {
            font-family: $VN-PeugeotNew-Regular;
            font-size: 11px;
            background: linear-gradient(142.06deg, #C8A16E -51.7%, #754C24 -0.35%, #FFDEA4 41.8%, #8E653A 78.54%, #8B6439 123.96%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;

        }
        .re-name::placeholder{
            color: #787b80;
            background: unset;
            -webkit-background-clip: unset;
            -webkit-text-fill-color: unset;
            background-clip: unset;
        }
        .color-options {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
            align-items: center;
        }
        .color-circle {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            border: 2px solid #fff;
            cursor: pointer;
            display: inline-block;
            transition: border 0.2s;
        }
        .color-circle.selected {
            border: 3px solid #2196f3;
        }
        .color-black { background: #111; }
        .color-red { background: #c00; }
        .color-blue { background: #25aef5; }
        .color-silver { background: #e0e0e0; }
        .color-white { background: #fff; }
        .submit-btn {
            width: 100%;
            padding: 14px 0;
            background: linear-gradient(180deg, #FFDEA4 0%, #8F673B 100%);
            color: black;
            border: none;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.2s;
            font-family: $VN-PeugeotNew-Bold;

        }
        .submit-btn:hover {
            background: #1769aa;
        }
        @media (max-width: 700px) {
            .container {
                position: static;
                margin: 24px auto;
                width: 95%;
                left: 0;
            }
        }
        .input-custom{
            position: relative;
            .__text{
                position: absolute;
                top:3px;
                left: 5px;
                font-family: $VN-PeugeotNew-Regular;
                font-size: 11px;
                background: linear-gradient(142.06deg, #C8A16E -51.7%, #754C24 -0.35%, #FFDEA4 41.8%, #8E653A 78.54%, #8B6439 123.96%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }
        }
    }

}

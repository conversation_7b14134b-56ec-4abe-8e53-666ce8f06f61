.popup-overlay {
    position: fixed;
    top: 0; left: 0; width: 100vw; height: 100vh;
    background: rgba(15, 22, 35, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.popup-main {
    width: 1188px;
    height: 704px;
    background: url('../images/popup/bg-popup.png') center center/cover no-repeat;
    border-radius: 40px;
    position: relative;
    padding: 90px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    color: #fff;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    top: 24px;
    right: 24px;
    background: none;
    border: none;
    font-size: 2.6rem;
    color: #fff;
    opacity: 0.8;
    cursor: pointer;
    z-index: 2;
    transition: opacity 0.2s;
}
.popup-close:hover { opacity: 1; }

.popup-title {

    text-align: center;
    font-size: 42px;
    font-family: $VN-PeugeotNew-Bold;
    font-weight: 700;
    letter-spacing: 1.5px;
    margin-bottom: 16px;
    background: linear-gradient(136.72deg, #8A9BF4 -8.73%, #FFFFFF 58.91%, #0074E8 101.15%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.popup-desc {
    text-align: center;
    font-size: 16px;
    font-family: $VN-PeugeotNew-Regular;
    color: #e0e6ee;
    margin-bottom: 38px;
    line-height: 1.5;
    span{
        color: #BADDFF;
    }
}

.popup-timer {
    display: flex;
    justify-content: center;
    align-items: center;
    width: max-content;
    margin: 0 auto 40px auto;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5.43656px);
    border-radius: 17px;
}
.timer-block {

    border-radius: 14px;
    padding: 18px 34px;
    margin: 0 4px;
    min-width: 90px;
    text-align: center;
    span {
        font-size: 32px;
        font-family: $VN-PeugeotNew-Bold;
        display: block;
        letter-spacing: 1px;
        margin-bottom: 15px;
    }

    div {
        font-size: 16px;
        margin-top: 2px;
        font-family: $VN-PeugeotNew-Regular;
    }
}

.timer-sep {
    font-size: 2.4rem;
    font-weight: bold;
    color: #fff;
    margin: 0 8px;
}

.popup-note {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    color: #fff;
    margin-bottom: 30px;
    text-shadow: 0 2px 8px rgba(0,0,0,0.25);
}
.popup-icon {
    margin-right: 8px;
    font-size: 1.25rem;
}

.popup-actions {
    display: flex;
    gap: 28px;
    justify-content: center;
    margin-bottom: 34px;
}
.btn-outline {
    background: transparent;
    border: 1px solid #fff;
    color: #fff;
    padding: 14px 44px;
    font-size: 14px;
    font-family: $VN-PeugeotNew-Bold;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
    display: flex;
    align-items: center;
    gap: 10px;
}
.btn-outline:hover {
    background: #fff;
    color: #1a2236;
}
.btn-primary {
    background: #0076ff;
    border: none;
    color: #fff;
    padding: 14px 44px;
    font-size: 14px;
    font-family: $VN-PeugeotNew-Bold;
    cursor: pointer;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    gap: 10px;
}


.btn-primary:hover {
    background: #005bb5;
}

.popup-socials {
    display: flex;
    justify-content: center;
    gap: 32px;
    margin-top: 18px;
}
.popup-socials img {
    width: 38px;
    height: 38px;
    opacity: 0.85;
    transition: opacity 0.2s;
    filter: grayscale(0.2);
}
.popup-socials img:hover {
    opacity: 1;
    filter: grayscale(0);
}

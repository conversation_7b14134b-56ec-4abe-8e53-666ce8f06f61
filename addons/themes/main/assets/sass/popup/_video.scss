.video-container{
    background-image:url('../images/comingsoon/video.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    .video-title{
        color: #fff;
        text-align: center;
        font-family: $VN-PeugeotNew-Bold;
        font-style: normal;
        font-size: 42px;
        text-transform: uppercase;
        span{
            text-align: center;
            font-family: $VN-PeugeotNew-Bold;
            font-style: normal;
            font-size: 42px;
            text-transform: uppercase;
            background: linear-gradient(180deg, #FFDEA4 0%, #8F673B 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        padding-top: 30px;
        padding-bottom: 20px;
    }
    .video-desc{
        font-family: $VN-PeugeotNew-Bold;
        font-style: normal;
        font-weight: 700;
        font-size: 13px;
        line-height: 19px;
        text-transform: uppercase;
        color: #fff;
        text-align: center;
        span{
            background: linear-gradient(180deg, #FFDEA4 0%, #8F673B 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    }
    .video-wrapper{
        width: 100%;
        padding: 30px 100px 60px;

        .video-relative{
            position: relative;
            video{
                width: 100%;
                border-radius: 24px;
            }
            .video-play{
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 100;
            }
            button{
                border: unset;
                background: transparent;
                padding: 0 30px;
                img{
                    height: 45px;
                    width: 45px;
                }
            }
            #play-pause{
                padding: 0 10px;
            }
            #next{
                padding-left: 60px;
            }
            .controls {
                margin: 20px;
                padding: 20px;
                position: absolute;
                bottom: 10px;
                left: 0;
                right: 0;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: space-between;
                color: white;
                border-radius: 30px;
            }
            #progress {
                flex: 1;
                margin: 0 10px;
                cursor: pointer;
                appearance: none;
                background: #a4a3a2;
                height: 10px;
                border-radius: 10px;
                transition: background 0.3s ease;
                -webkit-appearance: none;
            }

            #progress::-webkit-slider-thumb {
                appearance: none;
                width: 12px;
                height: 12px;
                background: white;
                border-radius: 50%;
            }
            //#progress::-webkit-slider-thumb {
            //    -webkit-appearance: none;
            //    height: 12px;
            //    width: 12px;
            //    background: white;
            //    border-radius: 50%;
            //    border: none;
            //    cursor: pointer;
            //    margin-top: -5px; /* căn giữa thumb */
            //}
            //
            //#progress::-moz-range-thumb {
            //    height: 12px;
            //    width: 12px;
            //    background: white;
            //    border-radius: 50%;
            //    border: none;
            //    cursor: pointer;
            //}
        }


    }
}

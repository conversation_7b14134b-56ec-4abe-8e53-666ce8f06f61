.popup-overlay {
    //position: absolute;
    //top: 0;
    //left: 0;
    background-image:url('../images/comingsoon/bn.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: 100vw;
    //height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    .popup-main {
        //width: 1188px;
        //height: 704px;
        border-radius: 40px;
        position: relative;
        padding: 40px 90px 0;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        color: $color-white;
        overflow: hidden;
        @include maxWidth(991) {
            height: max-content;
            padding: 10px;
            margin: 5px;
        }
        .logo-banner{
            text-align: center;
            margin-bottom: 20px;
            img{
                width: 220px;
            }
        }
        .text-coming{
            color: transparent;
            -webkit-text-stroke: 1.5px #8E653A;
            font-family: $VN-PeugeotNew-Bold;
            font-size: 54px;
            margin-bottom: 20px;
            text-align: center;
            @include maxWidth(991) {
                font-size: 30px;
            }

        }


        .popup-close {
            position: absolute;
            top: 24px;
            right: 24px;
            background: none;
            border: none;
            font-size: 2.6rem;
            color: $color-white;
            opacity: 0.8;
            cursor: pointer;
            z-index: 2;
            transition: opacity 0.2s;
            @include maxWidth(991) {
                top: 5px;
                right: 10px;
            }
            &:hover {
                opacity: 1;
            }
        }

        .popup-title {
            text-align: center;
            font-size: 48px;
            font-family: $VN-PeugeotNew-Bold;
            font-weight: 700;
            letter-spacing: 1.5px;
            margin-bottom: 25px;
            //background: linear-gradient(136.72deg, #8A9BF4 -8.73%, $color-white 58.91%, #0074E8 101.15%);
            //-webkit-background-clip: text;
            //-webkit-text-fill-color: transparent;
            //background-clip: text;
            /* Coming Soon */

            text-transform: uppercase;
            background: linear-gradient(142.06deg, #C8A16E -51.7%, #754C24 -0.35%, #FFDEA4 41.8%, #8E653A 78.54%, #8B6439 123.96%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;

            @include maxWidth(991) {
                font-size: 14px;
                background: unset;
                -webkit-background-clip: unset;
                -webkit-text-fill-color: unset;
                background-clip: unset;
                margin-bottom: 15px;
                margin-top: 0px;
            }
        }

        .popup-desc {
            text-align: center;
            font-size: 24px;
            font-family: $VN-PeugeotNew-Regular;
            color: #e0e6ee;
            margin-bottom: 10px;
            line-height: 1.5;
            @include maxWidth(991) {
                font-size: 12px;
                margin-bottom: 20px
            }
            .popup-desc-small{
                font-size: 18px;
            }

            span {
                background: linear-gradient(to bottom, #FFDEA4, #8F673B);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;

            }
            span:nth-child(1) {
                //font-family: $VN-PeugeotNew-Bold;
            }
        }
        .popup-desc-small {
            text-align: center;
            font-size: 18px;
            font-family: $VN-PeugeotNew-Regular;
            color: #e0e6ee;
            margin-bottom: 38px;
            line-height: 1.5;
            @include maxWidth(991) {
                font-size: 12px;
                margin-bottom: 20px
            }
            .popup-desc-small{
                font-size: 18px;
            }

            span {
                background: linear-gradient(to bottom, #FFDEA4, #8F673B);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;

            }
            span:nth-child(1) {
                font-family: $VN-PeugeotNew-Bold;
            }
        }

        .popup-timer {
            display: flex;
            justify-content: center;
            align-items: center;
            width: max-content;
            margin: 0 auto 20px;
            border: 2px solid transparent;
            border-radius: 20px;
            font-size: 20px;
            color: white;
            background:
                linear-gradient(to right, #050e1d, #040913) padding-box,
                linear-gradient(to right, #8F673B, #FFDEA4) border-box;
            @include maxWidth(991) {
                margin-bottom: 20px
            }
            .timer-block {

                border-radius: 14px;
                padding: 18px 34px;
                margin: 0 4px;
                width: 175px;
                text-align: center;
                &:nth-child(1){
                    margin-left: 50px;
                    @include maxWidth(991) {
                        margin-left: 10px;
                    }
                }

                @include maxWidth(991) {
                    border-radius: 14px;
                    padding: 10px 10px;
                    margin: 0 4px;
                    width: max-content;
                    text-align: center;
                }

                span {
                    font-family: $VN-PeugeotNew-Bold;
                    display: block;
                    letter-spacing: 1px;
                    font-size: 58px;
                    text-align: center;
                    background: linear-gradient(142.06deg, #C8A16E -51.7%, #754C24 -0.35%, #FFDEA4 41.8%, #8E653A 78.54%, #8B6439 123.96%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    @include maxWidth(991) {
                        font-size: 25px;
                    }
                }

                div {
                    margin-top: 2px;
                    font-family: $VN-PeugeotNew-Regular;
                    font-size: 20px;
                    text-align: center;
                    background: linear-gradient(142.06deg, #C8A16E -51.7%, #754C24 -0.35%, #FFDEA4 41.8%, #8E653A 78.54%, #8B6439 123.96%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    @include maxWidth(991) {
                        font-size: 12px;
                    }
                }
            }
            .timer-block-end{
                margin-right: 50px;
                @include maxWidth(991) {
                    margin-right: 10px;
                }
            }
            .timer-sep {
                font-size: 2.4rem;
                font-weight: bold;
                color: $color-white;
                margin: 0 8px;
            }
        }

        .popup-note {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: $color-white;
            margin-bottom: 30px;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
            @include maxWidth(991) {
                font-size: 10px;
            }
            .popup-icon {
                margin-right: 8px;
                font-size: 1.25rem;
            }
        }

        .popup-actions {
            display: flex;
            gap: 28px;
            justify-content: center;
            margin-bottom: 250px;
            @include maxWidth(991) {
                flex-direction: column;
                margin-bottom: 50px;
            }

            .btn-outline {
                background:
                    linear-gradient(to right, black, black) padding-box,
                    linear-gradient(to right, #8F673B, #FFDEA4) border-box;
                border: 2px solid transparent;
                padding: 14px 44px;
                font-size: 14px;
                font-family: $VN-PeugeotNew-Bold;
                cursor: pointer;
                transition: background 0.2s, color 0.2s;
                display: flex;
                align-items: center;
                gap: 10px;
                span{
                    text-transform: capitalize;
                    background: linear-gradient(142.06deg, #C8A16E -51.7%, #754C24 -0.35%, #FFDEA4 41.8%, #8E653A 78.54%, #8B6439 123.96%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                }

                &:hover {
                    background: $color-white;
                    color: #1a2236;
                }
                @include maxWidth(991) {
                    margin: auto;
                    padding: 14px 38px;
                }
            }

            .btn-primary {
                background: linear-gradient(142.06deg, #C8A16E -51.7%, #754C24 -0.35%, #FFDEA4 41.8%, #8E653A 78.54%, #8B6439 123.96%);
                border: none;
                color: black;
                padding: 14px 44px;
                font-size: 14px;
                font-family: $VN-PeugeotNew-Bold;
                cursor: pointer;
                transition: background 0.2s;
                display: flex;
                align-items: center;
                gap: 10px;
                @include maxWidth(991) {
                    margin: auto;
                }
                &:hover {
                    background: #005bb5;
                }
            }
        }

        .popup-socials {
            display: flex;
            justify-content: center;
            gap: 32px;
            margin-top: 18px;

            img {
                width: 38px;
                height: 38px;
                opacity: 0.85;
                transition: opacity 0.2s;
                filter: grayscale(0.2);

                &:hover {
                    opacity: 1;
                    filter: grayscale(0);
                }
            }
        }
    }
}
.coming-soon-line{
    width: 100%;
    background: linear-gradient(107.24deg, #C8A16E -366.26%, #754C24 -120.03%, #FFDEA4 82.05%, #8E653A 258.22%, #8B6439 475.99%);
    overflow: hidden;
    white-space: nowrap;
    box-sizing: border-box;
    border: 1px solid #ccc;

    span{
        color: black;
        font-size: 20px;
        text-transform: uppercase;
        display: inline-block;
        padding-left: 100%;
        animation: scroll-left 20s linear infinite;
        font-weight: 500;
        padding: 8px;
        @keyframes scroll-left {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-100%);
            }
        }
    }
}

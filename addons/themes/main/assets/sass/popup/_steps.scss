.steps-container{
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.7);
    .timeline-container {
        width: 1188px;
        height: 816px;
        position: relative;
        background: url('../images/popup/bg3.jpg') center center/cover no-repeat;
        border-radius: 28px;
        padding: 50px 90px 50px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        max-width: 95vw;
        text-align: center;
        overflow: hidden;
        @include maxWidth(1164) {
            height: 600px;
            padding: 20px;
        }
        @media (max-width: 768px) {
            overflow: scroll;
        }
        .close{
            position: absolute;
            top: 24px;
            right: 24px;
            @include maxWidth(1164) {
                top: 12px;
                right: 5px;
            }
        }
        ._content{
            .main-title {
                font-size: 30px;
                font-family: $VN-PeugeotNew-Bold;
                margin-bottom: 10px;
                color: white;
                span{
                    color: #6DB6FF;
                }
                @include maxWidth(1164) {
                    font-size: 14px;
                    margin-top: 30px;
                }
            }

            .sub-title {
                font-size: 23px;
                font-family: $VN-PeugeotNew-Bold;
                color: white;
                margin-bottom: 20px;
                @include maxWidth(1164) {
                    font-size: 12px;
                }
            }

            .limited-text {
                font-size: 13px;
                color: white;
                margin-bottom: 5px;
                font-family: $VN-PeugeotNew-Bold;
                @include maxWidth(1164) {
                    font-size: 10px;
                }
            }

            .quantity-text {
                font-size: 13px;
                color: #6DB6FF;
                font-family: $VN-PeugeotNew-Bold;
                margin-bottom: 20px;
                @include maxWidth(1164) {
                    font-size: 10px;
                }
            }

        }


        .timeline-steps {
            display: flex;
            justify-content: space-between;
            position: relative;
            z-index: 1;

            @media (max-width: 768px) {
                flex-direction: column;
                gap: 60px;
            }
        }

        .step {
            flex: 1;
            text-align: center;
            max-width: 230px;
            position: relative;

            .step-number {
                font-size: 48px;
                font-family: $VN-PeugeotNew-Bold;
                margin-bottom: 20px;
                @media (max-width: 1164px) {
                    font-size: 30px;
                }
            }

            .step-icon {
                width: 120px;
                height: 120px;
                border-radius: 50%;
                margin: 0 auto 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);

                svg {
                    width: 50px;
                    height: 50px;
                    fill: #333;
                }
                @include maxWidth(1164) {
                    width: 120px;
                    height: 50px;
                    img{
                        width: 70%;
                    }
                }
            }

            .step-dot {
                position: absolute;
                top: 140px;
                left: 50%;
                transform: translateX(-50%);
                width: 16px;
                height: 16px;
                border-radius: 50%;
                z-index: 2;
                @include maxWidth(1164) {
                    top: 70px;
                }
            }

            .step-content {
                background: white;
                color: #333;
                padding: 75px 20px 31px;
                margin-top: 70px;
                position: relative;
                clip-path: polygon(0 30%, 50% 0, 100% 30%, 100% 100%, 0 100%);
                min-height: 180px;
                height: 240px;
                @include maxWidth(1164) {
                    padding: 65px 10px 30px;
                    margin-top: 50px;
                    min-height: 175px;
                    height: 220px;
                }

                @media (max-width: 768px) {
                    clip-path: none;
                    border-radius: 8px;
                    min-height: auto;
                    height: max-content;
                    padding: 20px;
                }

                .step-title {
                    font-size: 16px;
                    font-family: $VN-PeugeotNew-Bold;
                    color: #333;
                    margin-bottom: 15px;
                    text-transform: uppercase;
                    line-height: 1.2;
                }

                .step-description {
                    color: #666;
                    line-height: 1.5;
                    font-size: 13px;
                }
            }

            .step-footer {
                margin-top: 12px;
                height: 14px;
                background: transparent;
            }

            &:nth-child(1) {
                .step-number {
                    color: #87CEEB;
                }

                .step-icon {
                    border-top: 8px solid #87CEEB;
                    @include maxWidth(1164) {
                        border-top: unset;
                    }
                }

                .step-dot {
                    background-color: #87CEEB;
                }

                .step-footer {
                    background: #87CEEB;
                }
            }

            &:nth-child(2) {
                .step-number {
                    color: #e74c69;
                }

                .step-icon {
                    border-top: 8px solid #e74c69;
                    @include maxWidth(1164) {
                        border-top: unset;
                    }
                }

                .step-dot {
                    background-color: #e74c69;
                }

                .step-footer {
                    background: #e74c69;
                }
            }

            &:nth-child(3) {
                .step-number {
                    color: #FFF;
                }

                .step-icon {
                    border-top: 8px solid #FFF;
                    @include maxWidth(1164) {
                        border-top: unset;
                    }
                }

                .step-dot {
                    background-color: #FFF;
                }

                .step-footer {
                    background: #fff;
                }
            }

            &:nth-child(4) {
                .step-number {
                    color: #1E90FF;
                }

                .step-icon {
                    border-top: 8px solid #1E90FF;
                    @include maxWidth(1164) {
                        border-top: unset;
                    }
                }

                .step-dot {
                    background-color: #1E90FF;
                }

                .step-footer {
                    background: #1E90FF;
                }
            }

            @media (max-width: 768px) {
                max-width: 100%;
                border: solid 1px #fff7f7;
                padding: 8px;
                border-radius: 10px;
                .step-dot {
                    display: none;

                }
            }
        }

        .timeline-line {
            position: absolute;
            top: 454px;
            left: 90px;
            right: 90px;
            height: 2px;
            background: repeating-linear-gradient(
                    to right,
                    #FFF 0px,
                    #FFF 8px,
                    transparent 8px,
                    transparent 16px
            );
            z-index: 0;
            @include maxWidth(1164) {
                top: 307px;
            }
            @media (max-width: 768px) {
                display: none;
            }
        }
    }


}



.steps-container{
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.7);
    .timeline-container {
        width: 1188px;
        height: 816px;
        position: relative;
        background: url('../images/popup/bg-popup.png') center center/cover no-repeat;
        border-radius: 28px;
        padding: 48px 36px 32px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        max-width: 95vw;
        text-align: center;
        overflow: hidden;
        ._content{
            .main-title {
                font-size: 42px;
                font-family: $VN-PeugeotNew-Bold;
                margin-bottom: 10px;
                background: linear-gradient(90deg, #4a9eff, #ffffff);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            .sub-title {
                font-size: 24px;
                font-family: $VN-PeugeotNew-Bold;
                color: white;
                margin-bottom: 20px;
            }

            .limited-text {
                font-size: 14px;
                color: #4a9eff;
                margin-bottom: 5px;
            }

            .quantity-text {
                font-size: 16px;
                color: #4a9eff;
                font-family: $VN-PeugeotNew-Bold;
            }

        }


        .timeline-steps {
            display: flex;
            justify-content: space-between;
            position: relative;
            z-index: 1;

            @media (max-width: 768px) {
                flex-direction: column;
                gap: 60px;
            }
        }

        .step {
            flex: 1;
            text-align: center;
            max-width: 280px;
            position: relative;

            .step-number {
                font-size: 48px;
                font-family: $VN-PeugeotNew-Bold;
                margin-bottom: 20px;
            }

            .step-icon {
                width: 120px;
                height: 120px;
                border-radius: 50%;
                margin: 0 auto 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
                background: white;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);

                svg {
                    width: 50px;
                    height: 50px;
                    fill: #333;
                }
            }

            .step-dot {
                position: absolute;
                top: 172px;
                left: 50%;
                transform: translateX(-50%);
                width: 16px;
                height: 16px;
                border-radius: 50%;
                z-index: 2;
            }

            .step-content {
                background: white;
                color: #333;
                padding: 20px 20px 30px;
                margin-top: 125px;
                position: relative;
                border-radius: 0 0 8px 8px;
                clip-path: polygon(0 15%, 50% 0, 100% 15%, 100% 100%, 0 100%);
                min-height: 180px;

                @media (max-width: 768px) {
                    clip-path: none;
                    border-radius: 8px;
                    min-height: auto;
                }

                .step-title {
                    font-size: 18px;
                    font-family: $VN-PeugeotNew-Bold;
                    color: #333;
                    margin-bottom: 15px;
                    text-transform: uppercase;
                    line-height: 1.2;
                }

                .step-description {
                    color: #666;
                    line-height: 1.5;
                    font-size: 14px;
                }
            }

            .step-footer {
                margin-top: -8px;
                height: 8px;
                background: transparent;
            }

            &:nth-child(1) {
                .step-number {
                    color: #87CEEB;
                }

                .step-icon {
                    border-top: 8px solid #87CEEB;
                }

                .step-dot {
                    background-color: #87CEEB;
                }

                .step-footer {
                    background: #87CEEB;
                }
            }

            &:nth-child(2) {
                .step-number {
                    color: #FF69B4;
                }

                .step-icon {
                    border-top: 8px solid #FF69B4;
                }

                .step-dot {
                    background-color: #FF69B4;
                }

                .step-footer {
                    background: #FF69B4;
                }
            }

            &:nth-child(3) {
                .step-number {
                    color: #FFF;
                }

                .step-icon {
                    border-top: 8px solid #FFF;
                }

                .step-dot {
                    background-color: #FFF;
                }

                .step-footer {
                    background: transparent;
                }
            }

            &:nth-child(4) {
                .step-number {
                    color: #1E90FF;
                }

                .step-icon {
                    border-top: 8px solid #1E90FF;
                }

                .step-dot {
                    background-color: #1E90FF;
                }

                .step-footer {
                    background: #1E90FF;
                }
            }

            @media (max-width: 768px) {
                max-width: 100%;

                .step-dot {
                    display: none;
                }
            }
        }

        .timeline-line {
            position: absolute;
            top: 279px;
            left: 60px;
            right: 60px;
            height: 2px;
            background: repeating-linear-gradient(
                    to right,
                    #FFF 0px,
                    #FFF 8px,
                    transparent 8px,
                    transparent 16px
            );
            z-index: 0;

            @media (max-width: 768px) {
                display: none;
            }
        }
    }


}



:root {
    --container-padding: 0px;
}

body {
    line-height: 1.5;
}

html,
body {
    margin: 0;
    padding: 0;
    min-width: 320px;
    font-family: $VN-PeugeotNew-Regular;
    font-size: var(--c-text-base);
    line-height: 1.5;
}
.uppercaseText {
    text-transform: uppercase !important;
}

.container-custom {
    width: 90%;
    margin: 0 auto;
    max-width: 1920px;
    padding-right: 0;
    padding-left: 0;
    @include maxWidth(1366) {
        width: 95%;
        padding-right: 0;
        padding-left: 0;
    }
    @include maxWidth(767) {
        width: calc(100% - 24px);
        padding-right: 0;
        padding-left: 0;
    }
}
.peugoet-btn {
    &-text {
        max-width: 300px;
        width: 100%;
        color: $color-white;
        text-transform: uppercase;
        text-align: center;
        display: block;
        padding: 2rem 5rem;
        background-color: $color-primary;
        border: 1px solid $color-primary;
        white-space: nowrap;

        &:hover {
            color: $color-white;
            background-color: $color-black;
            border: 1px solid $color-black;
        }
    }
    &-transparent {
        background-color: transparent;
        border: 1px solid $color-white;
        &:hover {
            background-color: $color-primary;
            border: 1px solid $color-primary;
        }
    }
}
.padding-bt {
    padding-bottom: 100px;
}

.section-to-section {
    padding-bottom: 10rem;
    @include maxWidth(768) {
        padding-bottom: 5rem;
    }
    @include maxWidth(420) {
        padding-bottom: 2.5rem;
    }
}
.mb-10rem {
    margin-bottom: 10rem;
    @include maxWidth(768) {
        margin-bottom: 5rem;
    }
    @include maxWidth(480) {
        margin-bottom: 2.5rem;
    }
}
.pt-8rem {
    padding-top: 10rem;
    @include maxWidth(768) {
        padding-top: 5rem;
    }
    @include maxWidth(480) {
        padding-top: 2.5rem;
    }
}
.item-to-item {
    padding-bottom: 5rem;
    @include maxWidth(768) {
        padding-bottom: 2.5rem;
    }
    @include maxWidth(480) {
        padding-bottom: 1.5rem;
    }
}
.desktop {
    @include maxWidth(1024) {
        display: none;
    }
    display: block;
}
.mobile {
    display: none;
    @include maxWidth(1024) {
        display: block;
    }
}

.lh-base {
    line-height: 1.5;
}
.checkbox-custom {
    input {
        margin-bottom: unset;
    }
    input[type="radio"] {
        position: relative;
        border: 2px solid #000000;
        border-radius: 2px;
        background: none;
        cursor: pointer;
        line-height: 0;
        margin: 0 0.6em 0 0;
        outline: 0;
        padding: 0 !important;
        vertical-align: text-top;
        min-height: 30px;
        min-width: 30px;
        -webkit-appearance: none;
        opacity: 0.5;
    }

    input[type="radio"]:hover {
        opacity: 1;
    }

    input[type="radio"]:checked {
        background-color: transparent;
        border: 2px solid #08a7eb;
        opacity: 1;
    }
    input[type="radio"]:checked::before {
        content: "";
        position: absolute;
        right: 50%;
        top: 50%;
        width: 5px;
        height: 10px;
        border: solid #08a7eb;
        border-width: 0 2px 2px 0;
        margin: -1px -1px -1px -1px;
        transform: rotate(45deg) translate(-50%, -50%);
        z-index: 2;
    }
}

.cursor-pointer {
    cursor: pointer;
}
::placeholder {
    color: #000;
    opacity: 1; /* Firefox */
}

::-ms-input-placeholder {
    /* Edge 12 -18 */
    color: #000;
}
.sup-title {
    margin-bottom: 2.5rem;
    @include maxWidth(480) {
        margin-bottom: 1.5rem;
    }
}
.lh-xs {
    line-height: 1.2;
    @include maxWidth(991) {
        line-height: 1.5;
    }
}

#overlay {
    background-image: url('../images/bg-loading.jpeg');
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

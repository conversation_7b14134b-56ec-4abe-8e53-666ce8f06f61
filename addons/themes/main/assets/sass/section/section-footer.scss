.footer {
    background-color: $color-black;
    &__wapper {
        &-header {
            &-list {
                display: flex;
                margin: 0;
                padding: 0;
                @include maxWidth(767) {
                    flex-direction: column;
                }
                & > li {
                    flex: 1;
                    padding: 2.7rem 0;
                    border-left: 1px solid $color-description;
                    border-bottom: 1px solid $color-description;
                    @include maxWidth(767) {
                        border-left: 0;
                    }
                    &:first-child {
                        border-left: 0;
                    }
                    .footer__wapper-header-item {
                        color: $color-white;
                        text-transform: uppercase;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        @include maxWidth(767) {
                            justify-content: flex-start;
                            padding-left: 24px;
                        }
                        .socie__icon {
                            margin-right: 15px;
                            .socie__icon_footer {
                                object-fit: contain !important;
                            }
                        }
                    }
                }
            }
        }
        &-main {
            text-align: center;
            padding: 100px 0 40px;
            @include maxWidth(767) {
                padding: 50px 0 20px;
            }
            &-title {
                padding-bottom: 2rem;
                & > a {
                    text-transform: uppercase;
                    font-family: $VN-PeugeotNew-Bold;
                    background: linear-gradient(142.06deg, #C8A16E -51.7%, #754C24 -0.35%, #FFDEA4 41.8%, #8E653A 78.54%, #8B6439 123.96%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;


                }
            }
            &-info {
                &-phone {
                    font-family: $VN-PeugeotNew-Bold;
                    background: linear-gradient(180deg, #FFDEA4 0%, #8F673B 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    text-transform: uppercase;
                    &:hover {
                        color: $color-white;
                    }
                }
                &-list {
                    display: flex;
                    justify-content: center;
                    column-gap: 30px;
                    padding: 20px 0 0;
                    margin: 0;
                    align-items: center;
                    li > a > img {
                        object-fit: contain;
                    }
                }
            }
            &-logo {
                padding-top: 40px;
                @include maxWidth(767) {
                    padding-top: 50px;
                }
            }
            &-policy {
                display: flex;
                padding-top: 2rem;
                justify-content: center;
                @include maxWidth(767) {
                    flex-direction: column;
                }
                &-item {
                    &:first-child {
                        padding-right: 1rem;
                        border-right: 1px solid;
                        @include maxWidth(767) {
                            border-right: 0;
                            padding-right: 0;
                        }
                    }
                    &:last-child {
                        padding-left: 1rem;
                        @include maxWidth(767) {
                            border-right: 0;
                            padding-left: 0;
                        }
                    }
                    & > a {
                        color: $color-description;
                    }
                }
            }
        }
    }
}
// .footer__socie {
//     position: fixed;
//     top: 50%;
//     right: 25px;
//     transform: translateY(-50%);
//     z-index: 99;
//     & > ul {
//         display: flex;
//         flex-direction: column;
//         gap: 10px;
//         padding: 0;
//         margin: 0;

//         li {
//             a {
//                 display: flex;
//                 // transition: 5s;
//                 transition: transform 2s;
//                 &:hover {
//                     .socie__wapper {
//                         padding: 0 0 0 20px;
//                         & > p {
//                             display: block;
//                             width: 150px;
//                         }
//                     }
//                 }
//                 .socie__wapper {
//                     display: flex;
//                     flex-direction: row;
//                     color: $color-white;
//                     align-items: center;
//                     position: relative;
//                     background-color: $color-description;
//                     border-radius: 100px;
//                     & > p {
//                         width: 0;
//                         transition: width 2s;
//                         display: none;
//                         margin: 0;
//                         white-space: nowrap;
//                     }
//                 }
//                 .socie__icon {
//                     height: 100%;
//                     display: flex;
//                     width: 50px;
//                     height: 50px;
//                     justify-content: center;
//                     align-items: center;
//                     background-color: $color-black;
//                     border-radius: 50%;
//                     border: 1px solid $color-white;
//                 }
//             }
//         }
//     }
// }

.fixed-button {
    position: fixed;
    z-index: 10;
    right: 20px;
    @media (max-width: 480px) {
        right: 7px;
    }
    top: 45%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    a {
        text-decoration: none;
    }
    .item-bt {
        background: $color-description;
        border-radius: 25px;
        width: 50px;
        @media (max-width: 480px) {
            width: 40px;
        }
        margin-bottom: 10px;
        &:hover {
            width: 200px;
            padding: 0px 0px 0px 20px;
            transition: all 0.6s ease;
        }
    }
    .wrap-buttonn {
        display: flex;
        align-items: center;
        color: #fff;
        justify-content: space-between;
        background: #06141f;
        width: 175px;
        border-radius: 25px;
        height: 40px;
        @media (max-width: 480px) {
            height: 40px;
        }
        padding: 0px 15px 0 15px;
        p {
            margin-top: 2px;
            font-size: 10px;
        }
        @media (max-width: 1024px) {
            border-radius: 50%;
            width: 40px;
            height: 40px;
            @media (max-width: 480px) {
                width: 40px;
                height: 40px;
            }
            padding: 0px 15px 0 14px;
            p {
                display: none;
            }
        }
    }

    .wrap-button {
        display: flex;
        align-items: center;
        color: #fff;
        justify-content: space-between;
        p.show-p {
            text-transform: uppercase;
            display: none;
            animation: slide-ngang 0.9s ease-out;
            font-size: 10px;
            margin: 0;
            white-space: nowrap;
        }
        p.bttun-to-top {
            font-size: 12px;
        }
        .img-bt {
            background: #05141f;
            padding: 5px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            border: 1px solid $color-white;
            @media (max-width: 481px) {
                width: 40px;
                height: 40px;
            }
            img {
                filter: invert(1);
            }
        }
        .img-bt2 {
            img {
                filter: invert(0);
                width: 90% !important;
            }
        }
    }
    .item-bt:hover .show-p {
        display: block;
    }
}

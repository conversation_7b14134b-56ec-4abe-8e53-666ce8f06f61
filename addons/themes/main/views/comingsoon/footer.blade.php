@php
    $titles = json_decode(theme_option('footer_sup_title'), true);
    $list_icons = json_decode(theme_option('footer_list_icon'), true);
    $policys = json_decode(theme_option('footer_policy'), true);
    $contents = json_decode(theme_option('footer_content'), true);

@endphp

<footer class="footer">
    <div class="footer__wapper">
        <div class="footer__wapper-header">
            <ul class="footer__wapper-header-list">
                @if (!blank($contents))
                    @foreach ($contents as $content)
                        <li>
                            <a href="{{ $content[2]['value'] ? $content[2]['value'] : '' }}"
                                class="footer__wapper-header-item c-text-base VN-PeugeotNew-Bold">
                                <div class="socie__icon"class="footer__wapper-header-item-icon">
                                    <img class="socie__icon_footer" width="25" height="20"
                                        src="{{ get_image_url($content[0]['value']) }}" alt="">
                                </div>
                                {{ $content[1]['value'] ? $content[1]['value'] : '' }}
                            </a>
                        </li>
                    @endforeach
                @endif
            </ul>
        </div>
        <div class="footer__wapper-main">
            <div class="container-custom">
                <h2 class="c-text-3xl VN-PeugeotNew-Bold footer__wapper-main-title">
                    @if (!blank($titles))
                        @foreach ($titles as $key => $title)
                            <a href="{{ $title[1]['value'] ? $title[1]['value'] : '' }}">

                                {{ $title[0]['value'] ? $title[0]['value'] : '' }}
                            </a>
                        @endforeach
                    @endif
                </h2>
                <div class="footer__wapper-main-info">
                    @if (!blank(theme_option('footer_hotline')))
                        <a href="tel:{{ theme_option('footer_hotline') }}"
                            class="c-text-md  VN-PeugeotNew-Bold footer__wapper-main-info-phone">
                            hotline: {{ theme_option('footer_hotline') }}
                        </a>
                    @endif
                    <ul class="footer__wapper-main-info-list">
                        @if (!blank($list_icons))
                            @foreach ($list_icons as $key => $list_icon)
                                <li>
                                    <a href="{{ $list_icon[1]['value'] ? $list_icon[1]['value'] : '' }}">
                                        <img width="28" height="27"
                                            src="{{ $list_icon[0]['value'] ? get_image_url($list_icon[0]['value']) : '' }}"
                                            alt="">
                                    </a>
                                </li>
                            @endforeach
                        @endif

                    </ul>
                </div>
                <div class="footer__wapper-main-logo">
                    <img width="90" height="auto" src="{{ Theme::asset()->url('images/comingsoon/lg.png') }}"
                        alt="logo">
                </div>
                <div class="footer__wapper-main-policy">
                    @if (!blank($policys))
                        @foreach ($policys as $policy)
                            <div class="footer__wapper-main-policy-item">
                                <a href="" class="c-text-base ">{!! $policy[0]['value'] ? $policy[0]['value'] : '' !!}</a>
                            </div>
                        @endforeach
                    @endif
                </div>
            </div>
        </div>
    </div>

</footer>
<div class="fixed-button VN-PeugeotNew-Bold">
    <div class="item-bt">
        <a href="javascript:;" role="button" data-toggle="modal" data-target="#XeLaiThu" alt="Register"
            title="Register" class="click_scroll">
            <div class="wrap-button">
                <p class="c-text-base show-p  VN-PeugeotNew-Bold text-uppercase ">đăng ký lái thử</p>
                <div class="img-bt img-bt2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="24" viewBox="0 0 26 24"
                        fill="none">
                        <path
                            d="M20.8459 2.57978C18.5297 0.809673 15.6674 -0.103628 12.758 0.00935756C9.85801 -0.0942127 7.01453 0.809673 4.70774 2.57037C1.72303 5.01839 0 8.6716 0 12.5319C0 18.6426 4.8019 24 7.70186 24H17.8612C20.7611 24 25.563 18.6426 25.563 12.5319C25.563 8.6716 23.84 5.01839 20.8553 2.57978M5.40449 3.44601C7.51355 1.85479 10.1122 1.03564 12.758 1.13922C15.4131 1.02623 18.0212 1.84538 20.1303 3.45542C22.5877 5.47034 24.1225 8.38913 24.3861 11.5527L19.0475 10.8842C18.106 9.68847 16.7596 8.36089 15.931 8.15375C14.8859 7.92778 13.8219 7.82421 12.758 7.84304C11.694 7.82421 10.6395 7.92778 9.60379 8.15375C8.77523 8.36089 7.39115 9.68847 6.49668 10.8842L1.11103 11.5527C1.36524 8.37972 2.90938 5.45151 5.37624 3.44601M1.77953 16.4205L8.25738 15.6108L10.1405 17.2962V22.8796H7.65479C6.02591 22.8796 3.10711 20.1491 1.77953 16.4205ZM12.758 22.8701H11.2703V16.7877L8.634 14.415L1.4594 15.3189C1.23343 14.4621 1.11103 13.5865 1.0922 12.7014L7.10869 11.9388L7.24992 11.741C7.94666 10.7148 8.85997 9.86736 9.93333 9.25536C10.8749 9.04822 11.8447 8.95406 12.8145 8.97289C13.7843 8.95406 14.7541 9.04822 15.705 9.25536C16.7313 9.87678 17.6164 10.7148 18.2754 11.7128L18.4167 11.9105L24.4332 12.6638C24.4049 13.5582 24.2825 14.4433 24.0566 15.3095L16.9102 14.415L14.2645 16.7877V22.8701H12.758ZM17.88 22.8701H15.432V17.2868L17.3151 15.6014L23.7553 16.4111C22.4277 20.1773 19.5089 22.8701 17.88 22.8701Z"
                            fill="white" />
                    </svg>
                </div>
            </div>
        </a>
    </div>
    <div class="item-bt">
        <a href="javascript:;" role="button" data-toggle="modal" data-target="#DatXeTrucTuyen" alt="Brochure"
            target="_blank" title="Brochure">
            <div class="wrap-button">
                <p class="c-text-base show-p  VN-PeugeotNew-Bold text-uppercase ">yêu cầu báo giá</p>
                <div class="img-bt">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="24" viewBox="0 0 24 24"
                        fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="feather feather-dollar-sign">
                        <line x1="12" y1="1" x2="12" y2="23"></line>
                        <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                    </svg>
                </div>
            </div>
        </a>
    </div>
    <div class="item-bt">
        <a class=""
            href="https://www.peugeotvietnam.vn/content/dam/peugeot/vietnam/catalog/E-BROCHURE-PEUGEOT-408.pdf"
            target="_blank">
            <div class="wrap-button">
                <p class="VN-PeugeotNew-Bold show-p font16">E-Brochure</p>
                <div class="img-bt ">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="20" height="24"
                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
                    </svg>

                </div>
            </div>
        </a>
    </div>
    <div class="">
        <a class="" href="#" class="scroll-top">
            <div class="wrap-button">
                <div class="">
                    <div class="img-bt">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="24" viewBox="0 0 27 15"
                            fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M-7.11698e-08 13.3718L1.62818 15L13.3603 3.25635L24.9769 14.8614L26.6051 13.2333L13.3603 3.69678e-07L-7.11698e-08 13.3718Z"
                                fill="white" />
                        </svg>
                    </div>
                </div>
            </div>
        </a>
    </div>
</div>

{{--<div>--}}
{{--    <img class="w-100 img-fluid" src="{{ Theme::asset()->url('images/comingsoon/bn.png') }}" alt="">--}}
{{--</div>--}}
<div class="popup-overlay" id="popup-overlay">
    <div class="popup-main">
        <div class="logo-banner">
            <img src="{{ Theme::asset()->url('images/comingsoon/logo.png') }}" alt="">
        </div>
        <div class="text-coming">
            COMING SOON

        </div>
        <h1 class="popup-title">PEUGEOT 408 LEGEND EDITION</h1>
        <p class="popup-desc">
            <span class="popup-desc-bold">Phiên bản giới hạn chỉ 215 xe</span> – Biểu tượng của sự tinh tế và khác biệt.<br>
        </p>
        <p class="popup-desc-small">
            <span>Sở hữu Peugeot 408 Legend Edition</span> – mẫu SUV Coupe độc nhất, với số hiệu cá nhân từ 001 đến 215.
        </p>
        <div class="popup-timer" id="popup-timer">
            <div class="timer-block">
                <span id="timer-days"></span>
                <div>Ngày</div>
            </div>
            <div class="timer-sep">:</div>
            <div class="timer-block">
                <span id="timer-hours"></span>
                <div>Giờ</div>
            </div>
            <div class="timer-sep">:</div>
            <div class="timer-block">
                <span id="timer-minutes"></span>
                <div>Phút</div>
            </div>
            <div class="timer-sep">:</div>
            <div class="timer-block timer-block-end">
                <span id="timer-seconds"></span>
                <div>Giây</div>
            </div>
        </div>
        <div class="popup-note">
            <span class="popup-icon">📩</span>
            Đăng ký ngay để nhận ưu tiên đặt trước và thông tin ra mắt sớm nhất.
        </div>
        <div class="popup-actions">
            <button class="btn-outline">
                <span>Tìm Hiểu Thêm </span><span><svg width="25" height="24" viewBox="0 0 25 24" fill="none"
                                                      xmlns="http://www.w3.org/2000/svg">
                <path d="M9.57031 6L15.5703 12L9.57031 18" stroke="url(#paint0_linear_223_6729)" stroke-width="2"/>
                <defs>
                <linearGradient id="paint0_linear_223_6729" x1="21.5989" y1="2.05173" x2="1.87835" y2="14.6978"
                                gradientUnits="userSpaceOnUse">
                <stop offset="0.0531915" stop-color="#C8A16E"/>
                <stop offset="0.3059" stop-color="#754C24"/>
                <stop offset="0.5133" stop-color="#FFDEA4"/>
                <stop offset="0.6941" stop-color="#8E653A"/>
                <stop offset="0.9176" stop-color="#8B6439"/>
                </linearGradient>
                </defs>
                </svg>

                </span>
            </button>
            <button class="btn-primary">Đăng Ký Ngay <span><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 6L15 12L9 18" stroke="#05141F" stroke-width="2"/>
                </svg>

                </span>
            </button>
        </div>
    </div>
</div>
<div class="coming-soon-line">
    <span>Coming Soon  |  Coming Soon  |  Coming Soon  |    Coming Soon  |   Coming Soon  |    Coming Soon  |  Coming Soon  |  Coming Soon  |    Coming Soon  |   Coming Soon  |    Coming Soon</span>
</div>




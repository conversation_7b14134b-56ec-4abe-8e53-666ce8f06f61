<div class="register-content">
{{--    <div>--}}
{{--        <img class="w-100 img-fluid" src="{{ Theme::asset()->url('images/comingsoon/re.jpg') }}" alt="">--}}
{{--    </div>--}}
    <div class="register-form">
        <h1>PEUGEOT 408 LEGEND EDITION</h1>
        <h2>PHIÊN BẢN GIỚI HẠN</h2>
        <div class="desc">
            Trải nghiệm thiết kế SUV Coupe độc đáo, tiện nghi đẳng cấp<br>
            <span>chỉ 215 xe với đánh số thứ tự đặc biệt.</span>
        </div>
        <div class="desc2">
            Đăng ký ngay để trở thành khách hàng ưu tiên sở hữu!
        </div>
        <form action="{{ route('public.submitContact') }}" method="POST">
            @csrf
            <input class="re-name" name="name" type="text" placeholder="Họ và tên *" required>
            <div class="input-custom">
                <input type="email" name="email" placeholder="" required>
                <div class="__text">
                    Email*
                </div>
            </div>
            <div class="input-custom">
                <input type="tel" name="phone" placeholder="" required>
                <div class="__text">
                    Số điện thoại?
                </div>
            </div>
{{--            --}}{{-- Hidden fields for validation --}}
{{--            <input type="hidden" name="car_id" value="1">--}}
{{--            <input type="hidden" name="showroom_id" value="1">--}}
{{--            <input type="hidden" name="type_response" value="1">--}}

            <button class="submit-btn" type="submit" id="submit-btn"> Đăng Ký Ngay <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 6L15 12L9 18" stroke="#05141F" stroke-width="2"/>
                </svg>
            </button>
        </form>
    </div>

</div>
<script>
    // $(document).ready(function() {
    //     console.log('Form script loaded');
    //
    //     // Debug: Log when form is submitted
    //     $('form').on('submit', function(e) {
    //         console.log('Form submitted!');
    //         console.log('Form data:', $(this).serialize());
    //         console.log('Form action:', $(this).attr('action'));
    //
    //         // Let the form submit normally (don't prevent default)
    //         // e.preventDefault();
    //     });
    //
    //     // Debug: Log when button is clicked
    //     $('#submit-btn').on('click', function(e) {
    //         console.log('Submit button clicked!');
    //     });
    // });
</script>


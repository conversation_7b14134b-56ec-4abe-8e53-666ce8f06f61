<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Peugeot 408 Legend Edition - Sở hữu ngay</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
<div class="container">
    <button class="close-btn">&times;</button>

    <div class="header">
        <h1>04 BƯỚC ĐỂ SỞ HỮU NGAY <span class="highlight">PHIÊN BẢN GIỚI HẠN</span></h1>
        <h2>PEUGEOT 408 LEGEND EDITION</h2>
        <p class="subtitle">CHỈ GIÀNH CHO<br><span class="limited">215 CHỦ NHÂN MÁY MẮN NHẤT</span></p>
    </div>

    <div class="car-image">
        <img src="peugeot-408.png" alt="Peugeot 408 Legend Edition">
    </div>

    <div class="steps-container">
        <div class="progress-bar">
            <div class="progress-line"></div>
            <div class="step-indicator active" data-step="1"></div>
            <div class="step-indicator" data-step="2"></div>
            <div class="step-indicator" data-step="3"></div>
            <div class="step-indicator" data-step="4"></div>
        </div>

        <div class="steps">
            <div class="step active">
                <div class="step-number">01</div>
                <div class="step-icon">
                    <img src="register-icon.svg" alt="Đăng ký">
                </div>
                <div class="step-content">
                    <h3>ĐĂNG KÝ</h3>
                    <p>Điền thông tin liên hệ để trở thành một trong những người đầu tiên nhận quyền ưu tiên.</p>
                </div>
            </div>

            <div class="step">
                <div class="step-number">02</div>
                <div class="step-icon">
                    <img src="verify-icon.svg" alt="Xác nhận">
                </div>
                <div class="step-content">
                    <h3>XÁC NHẬN THÔNG TIN</h3>
                    <p>Chuyên viên tư vấn sẽ liên hệ để xác nhận nhu cầu và giải đáp mọi thắc mắc.</p>
                </div>
            </div>

            <div class="step">
                <div class="step-number">03</div>
                <div class="step-icon">
                    <img src="booking-icon.svg" alt="Đặt cọc">
                </div>
                <div class="step-content">
                    <h3>ĐẶT CỌC</h3>
                    <p>Hoàn tất đặt cọc để giữ chỗ sở hữu một trong 215 xe giới hạn được đánh số riêng.</p>
                </div>
            </div>

            <div class="step">
                <div class="step-number">04</div>
                <div class="step-icon">
                    <img src="delivery-icon.svg" alt="Nhận xe">
                </div>
                <div class="step-content">
                    <h3>NHẬN XE</h3>
                    <p>Trải nghiệm phiên bản Legend Edition được cá nhân hóa và bàn giao tận tay.</p>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Arial', sans-serif;
        background: linear-gradient(135deg, #1a2332 0%, #2d3e50 100%);
        color: white;
        min-height: 100vh;
    }

    .container {
        position: relative;
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px 20px;
        border-radius: 20px;
        background: linear-gradient(135deg, #1e2a3a 0%, #34495e 100%);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .close-btn {
        position: absolute;
        top: 20px;
        right: 30px;
        background: none;
        border: none;
        color: white;
        font-size: 30px;
        cursor: pointer;
        opacity: 0.7;
        transition: opacity 0.3s ease;
    }

    .close-btn:hover {
        opacity: 1;
    }

    .header {
        text-align: center;
        margin-bottom: 40px;
    }

    .header h1 {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
        background: linear-gradient(45deg, #3498db, #74b9ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .highlight {
        color: #74b9ff;
    }

    .header h2 {
        font-size: 1.8rem;
        color: white;
        margin-bottom: 20px;
        font-weight: normal;
    }

    .subtitle {
        font-size: 1rem;
        color: #bdc3c7;
    }

    .limited {
        color: #74b9ff;
        font-weight: bold;
    }

    .car-image {
        text-align: center;
        margin: 40px 0;
        position: relative;
    }

    .car-image img {
        max-width: 100%;
        height: auto;
        filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.3));
    }

    .steps-container {
        margin-top: 60px;
    }

    .progress-bar {
        position: relative;
        margin-bottom: 40px;
        height: 4px;
    }

    .progress-line {
        width: 100%;
        height: 2px;
        background: #34495e;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
    }

    .step-indicator {
        position: absolute;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #34495e;
        top: 50%;
        transform: translateY(-50%);
        transition: all 0.3s ease;
    }

    .step-indicator:nth-child(2) { left: 0; background: #3498db; }
    .step-indicator:nth-child(3) { left: 33.33%; background: #e74c3c; }
    .step-indicator:nth-child(4) { left: 66.66%; background: #95a5a6; }
    .step-indicator:nth-child(5) { left: 100%; background: #3498db; }

    .steps {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 30px;
        margin-top: 40px;
    }

    .step {
        text-align: center;
        position: relative;
    }

    .step-number {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 20px;
        opacity: 0.3;
        transition: all 0.3s ease;
    }

    .step:nth-child(1) .step-number { color: #3498db; opacity: 1; }
    .step:nth-child(2) .step-number { color: #e74c3c; }
    .step:nth-child(3) .step-number { color: #95a5a6; }
    .step:nth-child(4) .step-number { color: #3498db; }

    .step-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: white;
        margin: 0 auto 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .step-icon img {
        width: 40px;
        height: 40px;
    }

    .step-content {
        background: white;
        color: #2c3e50;
        padding: 25px 20px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        min-height: 140px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .step-content h3 {
        font-size: 1.1rem;
        font-weight: bold;
        margin-bottom: 10px;
        color: #2c3e50;
    }

    .step-content p {
        font-size: 0.9rem;
        line-height: 1.4;
        color: #7f8c8d;
    }

    .step:nth-child(1) .step-content {
        border-bottom: 4px solid #3498db;
    }

    .step:nth-child(2) .step-content {
        border-bottom: 4px solid #e74c3c;
    }

    .step:nth-child(3) .step-content {
        border-bottom: 4px solid #95a5a6;
    }

    .step:nth-child(4) .step-content {
        border-bottom: 4px solid #3498db;
    }

    @media (max-width: 768px) {
        .steps {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .header h1 {
            font-size: 1.8rem;
        }

        .header h2 {
            font-size: 1.4rem;
        }

        .step-number {
            font-size: 2rem;
        }

        .container {
            margin: 10px;
            padding: 20px;
        }
    }

    @media (max-width: 480px) {
        .header h1 {
            font-size: 1.5rem;
        }

        .step-icon {
            width: 60px;
            height: 60px;
        }

        .step-icon img {
            width: 30px;
            height: 30px;
        }
    }

</style>

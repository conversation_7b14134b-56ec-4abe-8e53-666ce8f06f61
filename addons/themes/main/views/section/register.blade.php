<div class="register-content d-none">
    <div>
        <img class="w-100 img-fluid" src="{{ Theme::asset()->url('images/popup/bgre.jpg') }}" alt="">
    </div>
    <div class="register-form">
        <h1>PEUGEOT 408 LEGEND EDITION</h1>
        <h2>PHIÊN BẢN GIỚI HẠN</h2>
        <div class="desc">
            Trải nghiệm thiết kế SUV Coupe độc đáo, tiện nghi đẳng cấp<br>
            <span>chỉ 215 xe với đánh số thứ tự đặc biệt.</span>
        </div>
        <div class="desc2">
            Đăng ký ngay để trở thành khách hàng ưu tiên sở hữu!
        </div>
        <form>
            <input type="text" placeholder="Họ và tên *" required>
            <input type="email" placeholder="Email*" required>
            <input type="tel" placeholder="Số điện thoại? +12 345 678 90" required>
            <input type="number" min="1" max="205" placeholder="Nhập Số xe bạn muốn 1-205">
            <select>
                <option>Chọn phiên bản</option>
            </select>
            <div class="color-options">
                <div class="color-circle color-black selected" title="Đen"></div>
                <div class="color-circle color-red" title="Đỏ"></div>
                <div class="color-circle color-blue" title="Xanh"></div>
                <div class="color-circle color-silver" title="Bạc"></div>
                <div class="color-circle color-white" title="Trắng"></div>
            </div>
            <button class="submit-btn" type="submit">Đăng Ký Ngay</button>
        </form>
    </div>

</div>
<script>
    const circles = document.querySelectorAll('.color-circle');
    circles.forEach(circle => {
        circle.addEventListener('click', function() {
            circles.forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');
        });
    });
</script>

